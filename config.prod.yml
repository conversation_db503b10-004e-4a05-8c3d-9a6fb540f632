region: eu-west-2
s3:
  email-bucket: app-rentancy-com-email-bucket
  document-bucket: com-rentancy-documentsprod-prod
lambda:
  appsync-service: appSyncService
  securityGroup: sg-036a944df12e57b7f
  subnet: subnet-03ace7205d6f62698
mainService:
  endpoint: http://api.prod.uk.loftyworks.systems
dynamodb:
  invoice-line-item-stream-arn: arn:aws:dynamodb:eu-west-2:169675308590:table/InvoiceLineItem-7ghe6kcqrrg37ohck7dmvk7zaa-prod/stream/2021-02-18T16:27:44.258
sqs:
  xero-invoices-queue-inbound-arn: arn:aws:sqs:eu-west-2:169675308590:xero-invoices-queue-inbound
  xero-invoices-callback-queue-arn: arn:aws:sqs:eu-west-2:169675308590:xero-invoices-callback-queue