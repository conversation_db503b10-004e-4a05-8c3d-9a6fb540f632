service: integrations
frameworkVersion: "3"

plugins:
  - serverless-apigw-binary

resources:
  Resources:
    ReadModelParentPropertySummary:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ReadModelParentPropertySummary-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S
        KeySchema:
          - AttributeName: id
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST
  Outputs:
    ApiGatewayId:
      Value:
        Ref: ApiGatewayRestApi
      Export:
        Name: IntegrationsAPIGatewayId-${self:provider.stage}

custom:
  incomeExcelSenderFunctionName:
    prerelease: ${self:service}-${self:provider.stage}-income-arrears-excel-sender
    default: ${self:service}-${self:provider.stage}-income-arrears-summary-excel-sender
  orgStripeChargeIdFunctionName:
    prerelease: ${self:service}-${self:provider.stage}-org-stripe-charge-number-generator
    default: ${self:service}-${self:provider.stage}-organisation-stripe-charge-number-generator
  orgStripeInvoicePdfFunctionName:
    prerelease: ${self:service}-${self:provider.stage}-org-stripe-charge-invoice-pdf
    default: ${self:service}-${self:provider.stage}-organisation-stripe-charge-invoice-pdf
  eventBridge:
    eventBusName: "loftyworks-domain-events-${opt:stage}"
    eventSource: "lw.property-management"
  rentancy-domain:
    dev: dev.rentancy.com
    devuk: dev.rentancy.com
    stageuk: stageuk.rentancy.com
    prod: app.rentancy.com
    produs: produs.rentancy.io
    devus: devus.rentancy.io
    prerelease: lettings.stage.eu.loftyworks.com
    prodeu: app.rentancy.com
  rentancy-primary-organisation-id:
    dev: rentancydevelopment
    devuk: rentancydevelopment
    stageuk: rentancystageuk
    prod: rentancyinternal2
    produs: rentancyprodus
    devus: rentancydevus
    prerelease: todo
    prodeu: todo
  revenue-report-sender-emails:
    dev: <EMAIL>
    devuk: <EMAIL>
    stageuk: <EMAIL>
    prod: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
    produs: <EMAIL>
    devus: <EMAIL>
    prerelease: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
    prodeu: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
  organisation-report-sender-emails:
    dev: <EMAIL>,<EMAIL>,<EMAIL>
    devuk: <EMAIL>,<EMAIL>,<EMAIL>
    stageuk: <EMAIL>,<EMAIL>,<EMAIL>
    prod: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
    produs: <EMAIL>
    devus: <EMAIL>
    prerelease: todo
    prodeu: todo
  organisation-stripe-charge-report-emails:
    dev: <EMAIL>
    devuk: <EMAIL>
    stageuk: <EMAIL>
    prod: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
    devus: todo
    produs: todo
    prerelease: todo
    prodeu: todo
  organisation-daily-signup-report-emails:
    dev: <EMAIL>
    devuk: <EMAIL>
    stageuk: <EMAIL>
    prod: <EMAIL>,<EMAIL>,<EMAIL>
    devus: todo
    produs: todo
    prerelease: todo
    prodeu: todo
  monthly-journal-report-sender-emails:
    dev: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
    devuk: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
    stageuk: <EMAIL>
    prod: <EMAIL>,<EMAIL>,<EMAIL>
    produs: <EMAIL>
    devus: <EMAIL>
    prerelease: todo
    prodeu: todo
  apigwBinary:
    types:
      - "application/octet-stream"
  rentancySuppMail:
    dev: <EMAIL>
    devuk: <EMAIL>
    stageuk: <EMAIL>
    prod: <EMAIL>
    produs: <EMAIL>
    devus: <EMAIL>
    prerelease: todo
    prodeu: todo
  appsync:
    api-output:
      prod: 7ghe6kcqrrg37ohck7dmvk7zaa
      dev: o7ndtvliqnfdpouk7ybpefcb7u
      devuk: zc4ddtrzinaxxoqjlv5irtqbh4
      stageuk: hudtpmi3ebcnlfncsbaeqo7nwy
      produs: x533udwbyfdazdcp5t3xv2ghbm
      devus: n5trebw2xbajvluuq7kdhgzk6i
      prerelease: hhmlvxrpevhrdld2jmcx2dr3x4
      prodeu: gkyzrmicdjh55hv7qfs4hsvbvu
  lambda:
    appsync-service: ${file(./config.${opt:stage, 'dev'}.yml):lambda.appsync-service}-${self:provider.stage}
  cognito:
    user-pool:
      dev: arn:aws:cognito-idp:${self:provider.cognito_region}:${aws:accountId}:userpool/eu-west-2_zL6axz6tQ
      devuk: arn:aws:cognito-idp:${self:provider.cognito_region}:${aws:accountId}:userpool/eu-west-2_Tm05gPOz2
      stageuk: arn:aws:cognito-idp:${self:provider.cognito_region}:${aws:accountId}:userpool/eu-west-2_cQ6Icbk92
      prod: arn:aws:cognito-idp:${self:provider.cognito_region}:${aws:accountId}:userpool/eu-west-2_ye9ZPay62
      produs: arn:aws:cognito-idp:${self:provider.cognito_region}:${aws:accountId}:userpool/us-west-1_8FspwhEXz
      devus: arn:aws:cognito-idp:${self:provider.cognito_region}:${aws:accountId}:userpool/us-west-2_srTkAjYKS
    userPoolArn: ${self:custom.cognito.user-pool.${self:provider.stage}, '${self:custom.cognito.user-pool.devuk}'}
  s3:
    email-bucket: ${file(./config.${opt:stage, 'dev'}.yml):s3.email-bucket}
    email-bucket-prefix:
      dev: dev-inbox
      devuk: dev-inbox
      stageuk: stageuk-inbox
      prod: inbox
      devus: devus-inbox
      prerelease: stageuk-inbox
      prodeu: prodeu-inbox
    rentancy-document-uploads: ${file(./config.${opt:stage, 'dev'}.yml):s3.document-bucket}
    rentancy-email-attachments-bucket: rentancy-email-attachments-${self:provider.stage}
  sqs:
    integrations-queue: integrations-queue-${self:provider.stage}
    integrations-queue-dlq: integrations-queue-${self:provider.stage}-dlq
    xero-update-queue: xero-update-queue-${self:provider.stage}
    xero-update-queue-dlq: xero-update-queue-${self:provider.stage}-dlq
    xero-contacts-queue: xero-contacts-queue-${self:provider.stage}
    xero-contacts-queue-dlq: xero-contacts-queue-${self:provider.stage}-dlq
    xero-invoices-queue: xero-invoices-queue-${self:provider.stage}
    xero-invoices-queue-dlq: xero-invoices-queue-${self:provider.stage}-dlq
    xero-invoices-queue-outbound: xero-invoices-queue-outbound
    xero-invoices-queue-outbound-dlq: xero-invoices-queue-outbound-dlq
    xero-invoices-callback-queue: xero-invoices-callback-queue
    xero-invoices-callback-queue-dlq: xero-invoices-callback-queue-dlq
    xero-transactions-queue: xero-transactions-queue-${self:provider.stage}
    xero-transactions-queue-dlq: xero-transactions-queue-${self:provider.stage}-dlq
    xero-accounts-queue: xero-accounts-queue-${self:provider.stage}
    xero-accounts-queue-dlq: xero-accounts-queue-${self:provider.stage}-dlq
    xero-over-payment-queue: xero-over-payment-queue-${self:provider.stage}
    xero-over-payment-queue-dlq: xero-over-payment-queue-${self:provider.stage}-dlq
    xero-payments-queue: xero-payments-queue-${self:provider.stage}
    xero-payments-queue-dlq: xero-payments-queue-${self:provider.stage}-dlq
    xero-bank-transfer-queue: xero-bank-transfer-queue-${self:provider.stage}
    xero-bank-transfer-queue-dlq: xero-bank-transfer-queue-${self:provider.stage}-dlq
    xero-journal-queue: xero-journal-queue-${self:provider.stage}
    xero-journal-queue-dlq: xero-journal-queue-${self:provider.stage}-dlq
    mailchimp-queue: mailchimp-queue-${self:provider.stage}
    mailchimp-queue-dlq: mailchimp-queue-${self:provider.stage}-dlq
    tenancy-invoice-queue: tenancy-invoice-queue-${self:provider.stage}
    tenancy-invoice-queue-dlq: tenancy-invoice-queue-${self:provider.stage}-dlq
    commission-invoice-queue: commission-invoice-queue-${self:provider.stage}
    commission-invoice-queue-dlq: commission-invoice-queue-${self:provider.stage}-dlq
    overseas-resident-bill-queue: overseas-resident-bill-queue-${self:provider.stage}
    overseas-resident-bill-queue-dlq: overseas-resident-bill-queue-${self:provider.stage}-dlq
    portfolio-invoice-queue: portfolio-invoice-queue-${self:provider.stage}
    portfolio-invoice-queue-dlq: portfolio-invoice-queue-${self:provider.stage}-dlq
    email-notification-queue: email-notification-queue-${self:provider.stage}
    email-notification-queue-dlq: email-notification-queue-${self:provider.stage}-dlq
    email-template-notification-queue: send-template-email-queue-${self:provider.stage}
    email-template-notification-queue-dlq: send-template-email-queue-${self:provider.stage}-dlq
    email-invoice-queue: email-invoice-queue-${self:provider.stage}
    email-invoice-queue-dlq: email-invoice-queue-${self:provider.stage}-dlq
    whats-app-queue: whats-app-queue-${self:provider.stage}
    whats-app-queue-dlq: whats-app-queue-${self:provider.stage}-dlq
    whats-app-integration-queue: whats-app-integration-queue-${self:provider.stage}
    whats-app-integration-queue-dlq: whats-app-integration-queue-${self:provider.stage}-dlq
    email-message-queue: email-message-queue-${self:provider.stage}
    email-message-queue-dlq: email-message-queue-${self:provider.stage}-dlq
    property-tracking-code-queue: property-tracking-code-queue-${self:provider.stage}
    property-tracking-code-queue-dlq: property-tracking-code-queue-${self:provider.stage}-dlq
    landlord-bill-queue: landlord-bill-queue-${self:provider.stage}
    landlord-bill-queue-dlq: landlord-bill-queue-${self:provider.stage}-dlq
    async-property-balance-report-queue: async-property-balance-report-queue-${self:provider.stage}
    async-property-balance-report-queue-dlq: async-property-balance-report-queue-${self:provider.stage}-dlq
    client-balance-report-queue: client-balance-report-queue-${self:provider.stage}
    client-balance-report-queue-dlq: client-balance-report-queue-${self:provider.stage}-dlq
    organisation-property-data-exporter-queue: organisation-property-data-exporter-queue-${self:provider.stage}
    organisation-property-data-exporter-queue-dlq: organisation-property-data-exporter-queue-${self:provider.stage}-dlq
    client-statement-report-queue: client-statement-report-queue-${self:provider.stage}
    client-statement-report-queue-dlq: client-statement-report-queue-${self:provider.stage}-dlq
    client-general-report-queue: client-general-report-queue-${self:provider.stage}
    client-general-report-queue-dlq: client-general-report-queue-${self:provider.stage}-dlq
    cash-balance-report-queue: cash-balance-report-queue-${self:provider.stage}
    cash-balance-report-queue-dlq: cash-balance-report-queue-${self:provider.stage}-dlq
    bacs-report-queue: bacs-report-queue-${self:provider.stage}
    bacs-report-queue-dlq: bacs-report-queue-${self:provider.stage}-dlq
    overseas-resident-report-queue: overseas-resident-report-queue-${self:provider.stage}
    overseas-resident-report-queue-dlq: overseas-resident-report-queue-${self:provider.stage}-dlq
    landlord-commission-queue: landlord-commission-queue-${self:provider.stage}
    landlord-commission-queue-dlq: landlord-commission-queue-${self:provider.stage}-dlq
    tenancy-schedule-queue: tenancy-schedule-queue-${self:provider.stage}
    tenancy-schedule-queue-dlq: tenancy-schedule-queue-${self:provider.stage}-dlq
    xero-webhook-queue: xero-webhook-queue-${self:provider.stage}
    xero-webhook-queue-dlq: xero-webhook-queue-${self:provider.stage}-dlq
    supplier-landlord-statement-report-queue: supplier-landlord-statement-report-queue-${self:provider.stage}
    supplier-landlord-statement-report-queue-dlq: supplier-landlord-statement-report-queue-${self:provider.stage}-dlq
    landlord-bill-update-queue: landlord-bill-update-queue-${self:provider.stage}
    landlord-bill-update-queue-dlq: landlord-bill-update-queue-${self:provider.stage}-dlq
    bulk-payout-queue: bulk-payout-queue-${self:provider.stage}
    bulk-payout-queue-dlq: bulk-payout-queue-${self:provider.stage}-dlq
    journal-bill-sender-queue: journal-bill-sender-queue-${self:provider.stage}
    journal-bill-sender-queue-dlq: journal-bill-sender-queue-${self:provider.stage}-dlq
    read-model-parent-property-summary-queue: read-model-parent-property-summary-queue-${self:provider.stage}


  dynamodb:
    email-message-table: EmailMessage-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    email-attachment-table: EmailAttachment-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    organisation-table: Organisation-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    organisation-stripe-charge-table: OrganisationStripeCharges-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    tenancy-table: Tenancy-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    tenancy-settings-table: TenancySettings-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    user-table: User-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    organisation-user-table: OrganisationUser-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    organisation-user-index: gsi-OrgranisationUsers
    document-table: Document-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    document-organisation-index: gsi-OrganisationDocuments
    user-email-index: gsi-ByCognitoEmail
    user-xero-index: gsi-ByXeroId
    user-table-denormalized-emails-index: gsi-ByDenormalizedEmails
    user-organisation-index: gsi-ByOrganisation
    integration-table: Integration-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    user-id-index: gsi-ByCognitoId
    integration-index: gsi-OrganisationUserIntegration
    integration-organisation-index: gsi-OrganisationIntegrations
    integration-state-index: gsi-ByState
    integration-tenant-index: gsi-TenantIntegration
    invoice-table: Invoice-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    invoice-allocation-table: InvoiceAllocation-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    invoice-organisation-index: gsi-OrganisationInvoices
    invoice-allocation-property-index: gsi-ByPropertyId
    invoice-allocation-index: gsi-InvoiceAllocations
    invoice-index: gsi-ByInvoiceId
    invoice-line-item-table: InvoiceLineItem-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    invoice-line-item-index: gsi-ByLineItemId
    invoice-line-item-invoice-index: gsi-InvoiceLineItems
    invoice-line-item-transaction-index: gsi-TransactionLineItems
    invoice-line-item-organisation-index: gsi-OrganisationLineItems
    transaction-table: Transaction-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    account-table: Account-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    transaction-index: gsi-ByTransactionId
    transaction-organisation-index: gsi-OrganisationTransactions
    account-index: gsi-ByAccountId
    account-organisation-index: gsi-OrganisationAccounts
    tenancy-reference-index: gsi-ByReference
    property-reference-index: gsi-ByReference
    tenancy-invoice-table: InvoiceTenancy-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    invoice-property-table: InvoiceProperty-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    property-table: Property-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    parent-property-table: ParentPropertyEntity-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    parent-property-property-index: gsi-ParentPropertyEntityProperty
    property-organisation-index: gsi-OrganisationProperties
    payment-table: Payment-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    payment-index: gsi-ByPaymentId
    payment-organisation-index: gsi-OrganisationPayments
    transfer-table: Transfer-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    transfer-index: gsi-ByTransferId
    tenancy-organisation-index: gsi-OrganisationTenancies
    tenancy-property-index: gsi-PropertyTenancies
    tenancy-settings-index: gsi-OrganisationSettings
    property-invoice-index: gsi-PropertyInvoices
    property-invoice-invoice-index: gsi-InvoiceProperties
    tenancy-invoice-index: gsi-TenancyInvoices
    tenancy-invoice-invoice-index: gsi-InvoiceTenancies
    statement-table: Statement-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    statement-index: gsi-OrganisationStatements
    statement-landlord-bill-index: gsi-LandlordBillStatements
    statement-property-index: gsi-PropertyStatements
    rent-invoice-history: RentInvoiceHistory-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    rent-invoice-history-tenancy-index: gsi-RentInvoiceHistoryTenancy
    rent-invoice-history-xero-index: gsi-ByXeroId
    address-table: Address-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    address-parent-index: gsi-ByParentIdAndParentType
    address-organisation-index: gsi-OrganisationAddresses
    task-table: Task-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    task-organisation-index: gsi-OrgranisationTasks
    column-table: Column-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    conversation-table: Conversation-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    message-table: Message-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    message-index: gsi-ByWhatsAppId
    property-budget-table: PropertyBudget-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    property-budget-property-index: gsi-PropertyBudget
    xero-journal-table: XeroJournal-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    xero-journal-index: gsi-JournalId
    landlord-bill-table: LandlordBill-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    landlord-bill-organisation-index: gsi-ByReference
    landlord-bill-invoice-index: gsi-ByOriginalInvoiceId
    invoice-webhook-events-table: InvoiceWebhookEvents-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    invoice-line-item-tracking-name-index: gsi-ByTrackingName
    over-payment-table: OverPayment-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    over-payment-index: gsi-ByOverPaymentId
    over-payment-organisation-index: gsi-OrganisationOverPayments
    read-model-parent-property-summary: ReadModelParentPropertySummary-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
  mailchimp:
    api-key:
      prod: ${ssm:/integrations/prod/mailchimp/api-key}
      dev: ${ssm:/integrations/dev/mailchimp/api-key}
      stageuk: ${ssm:/integrations/dev/mailchimp/api-key}
      #produs: ${ssm:/integrations/produs/mailchimp/api-key}
      #devus: ${ssm:/integrations/devus/mailchimp/api-key}
    base-uri:
      prod: https://us10.api.mailchimp.com/3.0
      dev: https://us1.api.mailchimp.com/3.0
      stageuk: https://us1.api.mailchimp.com/3.0
      produs: https://us1.api.mailchimp.com/3.0
      devus: https://us1.api.mailchimp.com/3.0
  xero:
    authorization-url: https://login.xero.com/identity/connect/authorize
    token-url: https://identity.xero.com/connect/token
    scopes: openid,email,profile,offline_access,accounting.settings,accounting.transactions,accounting.contacts,accounting.reports.read,accounting.attachments,accounting.journals.read
    openid-scopes: openid,email,profile
    api-root-path: https://api.xero.com
    ui-root-path: https://go.xero.com
    webhooks-secret: ${ssm:/integrations/${self:provider.stage}/xero/webhooks-secret}
    redirect-url: ${ssm:/integrations/${self:provider.stage}/xero/redirect-url}
    app-client-id: ${ssm:/integrations/${self:provider.stage}/xero/app-client-id}
    app-client-secret: ${ssm:/integrations/${self:provider.stage}/xero/app-client-secret}
  jasper-server:
    rootUrl: http://ec2-35-178-161-29.eu-west-2.compute.amazonaws.com
  stripe:
    charge-currency:
      dev: gbp
      devuk: gbp
      stageuk: gbp
      prod: gbp
      devus: todo
      produs: todo
      prerelease: gbp
      prodeu: gbp
    property-charge-rate:
      dev: 150 # It.s 1.5 * 100
      devuk: 150 # It.s 1.5 * 100
      stageuk: 150
      prod: 150 # It.s 1.5 * 100
      devus: todo
      produs: todo
      prerelease: 150
      prodeu: 150
    charge-vat-rate:
      dev: 0.2
      devuk: 0.2
      stageuk: 0.2
      prod: 0.2
      devus: todo
      produs: todo
      prerelease: 0.2
      prodeu: 0.2
    minimum-charge:
      dev: 2500 # It's 25 * 100
      devuk: 2500 # It's 25 * 100
      stageuk: 2500
      prod: 2500 # It's 25 * 100
      devus: todo
      produs: todo
      prerelease: 2500
      prodeu: 2500
  twilio:
    account-sid:
      prod: AC469d0bf4e62a93e573b31e29560401fe
      dev: ACb31a2a00556b9b12d336ad76ba11fe68
      devuk: ACb31a2a00556b9b12d336ad76ba11fe68
      stageuk: ACb31a2a00556b9b12d336ad76ba11fe68
      produs: ACb31a2a00556b9b12d336ad76ba11fe68
      devus: ACb31a2a00556b9b12d336ad76ba11fe68
      prerelease: ACb31a2a00556b9b12d336ad76ba11fe68
      prodeu: ACb31a2a00556b9b12d336ad76ba11fe68
    auth-token:
      prod: ${ssm:/integrations/prod/twilio/auth-token}
      dev: ${ssm:/integrations/dev/twilio/auth-token}
      devuk: ${ssm:/integrations/dev/twilio/auth-token}
      stageuk: ${ssm:/integrations/dev/twilio/auth-token}
      produs: ${ssm:/integrations/produs/twilio/auth-token}
      devus: ${ssm:/integrations/devus/twilio/auth-token}
      prerelease: ${ssm:/integrations/dev/twilio/auth-token}
      prodeu: ${ssm:/integrations/dev/twilio/auth-token}
  rentancy-error-channel:
    dev: bbc1c110-fd24-11ec-9c8f-41c5115dadc5
    stageuk: bbc1c110-fd24-11ec-9c8f-41c5115dadc5
    prod: 205d1a50-0803-11ed-bac9-fd04947354bd
    produs: bbc1c110-fd24-11ec-9c8f-41c5115dadc5
    devus: bbc1c110-fd24-11ec-9c8f-41c5115dadc5
    prerelease: bbc1c110-fd24-11ec-9c8f-41c5115dadc5
    prodeu: bbc1c110-fd24-11ec-9c8f-41c5115dadc5
  authorizers:
    dev:
      arn: ${self:custom.cognito.userPoolArn}
      type: COGNITO_USER_POOLS
    devuk:
      arn: ${self:custom.cognito.userPoolArn}
      type: COGNITO_USER_POOLS
    stageuk:
      arn: ${self:custom.cognito.userPoolArn}
      type: COGNITO_USER_POOLS
    prod:
      arn: ${self:custom.cognito.userPoolArn}
      type: COGNITO_USER_POOLS
    devus:
      arn: ${self:custom.cognito.userPoolArn}
      type: COGNITO_USER_POOLS
    stageus:
      arn: ${self:custom.cognito.userPoolArn}
      type: COGNITO_USER_POOLS
    produs:
      arn: ${self:custom.cognito.userPoolArn}
      type: COGNITO_USER_POOLS
    prerelease:
      arn: arn:aws:lambda:${self:provider.region}:${aws:accountId}:function:custom-authentication-${self:provider.stage}-CustomAuthorizer
      type: request
      identitySource: method.request.header.Cookie
    prodeu:
      arn: arn:aws:lambda:${self:provider.region}:${aws:accountId}:function:custom-authentication-prod-CustomAuthorizer
      type: request
      identitySource: method.request.header.Cookie

provider:
  name: aws
  region: ${file(./config.${opt:stage, 'dev'}.yml):region}
  cognito_region: ${file(./config.${opt:stage, 'dev'}.yml):region}
  ddb_region: ${file(./config.${opt:stage, 'dev'}.yml):region}
  lambda-region: ${file(./config.${opt:stage, 'dev'}.yml):region}
  uploads_bucket_region: ${file(./config.${opt:stage, 'dev'}.yml):region}
  notifications_region: ${file(./config.${opt:stage, 'dev'}.yml):region}
  runtime: java11
  versionFunctions: false
  stage: ${opt:stage, 'dev'}

  environment:
    REGION: ${self:provider.region}
    MAIN_SERVICE_ENDPOINT: ${file(config.${opt:stage, 'dev'}.yml):mainService.endpoint}
    EVENT_SOURCE: ${self:custom.eventBridge.eventSource}
    EVENT_BUS_NAME: ${self:custom.eventBridge.eventBusName}

  vpc:
    securityGroupIds:
      - ${file(config.${opt:stage, 'dev'}.yml):lambda.securityGroup}
    subnetIds:
      - ${file(config.${opt:stage, 'dev'}.yml):lambda.subnet}

  iamRoleStatements:
    - Effect: "Allow"
      Action:
        - "sns:*"
      Resource:
        - "*"
    - Effect: "Allow"
      Action:
        - "sqs:*"
      Resource:
        - "*"
    - Effect: "Allow"
      Action:
        - "s3:*"
      Resource:
        - "*"
    - Effect: "Allow"
      Action:
        - "ses:*"
      Resource:
        - "*"
    - Effect: "Allow"
      Action:
        - "appsync:GraphQL"
      Resource:
        - "*"
    - Effect: "Allow"
      Action:
        - "dynamodb:*"
      Resource:
        - "*"
    - Effect: "Allow"
      Action:
        - "lambda:*"
      Resource:
        - "*"
    - Effect: "Allow"
      Action:
        - events:PutEvents
      Resource:
        - Arn:
          Fn::Sub: "arn:aws:events:${AWS::Region}:${AWS::AccountId}:event-bus/${self:custom.eventBridge.eventBusName}"

# you can add packaging information here
package:
  artifact: build/distributions/integrations.zip

functions:
  xero-oauth2-client:
    handler: com.rentancy.integrations.handlers.XeroOauth2Wrapper
    timeout: 30
    events:
      - http:
          path: xero/auth
          method: get
          request:
            parameters:
              querystrings:
                startDate: true
                organisationId: true
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
      - http:
          path: xero/appstore/auth
          method: get
      - http:
          path: xero/disconnect
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
      - http:
          path: xero/exchange
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            schema:
              application/json: ${file(xero-exchange.json)}
      - http:
          path: xero/callback
          method: get
          request:
            parameters:
              querystrings:
                code: true
                state: true
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_AUTHORIZATION_URL: ${self:custom.xero.authorization-url}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      REDIRECT_URL: ${self:custom.xero.redirect-url}
      XERO_SCOPES: ${self:custom.xero.scopes}
      XERO_OPENID_SCOPES: ${self:custom.xero.openid-scopes}
      DDB_REGION: ${self:provider.ddb_region}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      USER_COGNITOID_INDEX: ${self:custom.dynamodb.user-id-index}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      RENTANCY_DOMAIN: ${self:custom.rentancy-domain.${self:provider.stage}}
  xero-webhooks:
    handler: com.rentancy.integrations.handlers.XeroWebHooks
    timeout: 900
    events:
      - http:
          path: xero/webhooks/contacts
          method: post
          request:
            schema:
              application/json: ${file(xero-webhook-response.json)}
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_WEBHOOKS_SECRET: ${self:custom.xero.webhooks-secret}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      DDB_REGION: ${self:provider.ddb_region}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      XERO_CONTACTS_QUEUE: ${self:custom.sqs.xero-contacts-queue}
      XERO_INVOICES_QUEUE: ${self:custom.sqs.xero-invoices-queue}
      XERO_INVOICES_QUEUE_OUTBOUND: ${self:custom.sqs.xero-invoices-queue-outbound}
      XERO_INVOICES_CALLBACK_QUEUE: ${self:custom.sqs.xero-invoices-callback-queue}
      XERO_WEBHOOK_QUEUE: ${self:custom.sqs.xero-webhook-queue}
  xero-accounts-update:
    handler: com.rentancy.integrations.handlers.XeroAccountDataFetcher
    timeout: 30
    events:
      - http:
          path: xero/accounts/update
          method: post
          request:
            parameters:
              querystrings:
                organisationId: true
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_WEBHOOKS_SECRET: ${self:custom.xero.webhooks-secret}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      DDB_REGION: ${self:provider.ddb_region}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      XERO_ACCOUNTS_QUEUE: ${self:custom.sqs.xero-accounts-queue}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      XERO_CONTACTS_QUEUE: ${self:custom.sqs.xero-contacts-queue}
      XERO_INVOICES_QUEUE: ${self:custom.sqs.xero-invoices-queue}
      XERO_INVOICES_QUEUE_OUTBOUND: ${self:custom.sqs.xero-invoices-queue-outbound}
      XERO_INVOICES_CALLBACK_QUEUE: ${self:custom.sqs.xero-invoices-callback-queue}
      XERO_WEBHOOK_QUEUE: ${self:custom.sqs.xero-webhook-queue}
      ACCOUNT_TABLE: ${self:custom.dynamodb.account-table}
      ACCOUNT_ORGANISATION_INDEX: ${self:custom.dynamodb.account-organisation-index}
      USER_TABLE_DENORMALIZED_EMAILS_INDEX: ${self:custom.dynamodb.user-table-denormalized-emails-index}

  xero-api-client:
    handler: com.rentancy.integrations.handlers.XeroApiClientWrapper
    timeout: 30
    events:
      - schedule:
          rate: rate(2 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: xero/api/invoice
          method: post
          contentHandling: CONVERT_TO_BINARY
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            schema:
              application/json: ${file(xero-invoice-api.json)}
      - http:
          path: xero/api/invoices/update
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                statementId: false
      - http:
          path: public/xero/api/invoice
          method: post
          contentHandling: CONVERT_TO_BINARY
          request:
            parameters:
              querystrings:
                organisationId: true
                invoiceId: true
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      USER_COGNITOID_INDEX: ${self:custom.dynamodb.user-id-index}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_INDEX: ${self:custom.dynamodb.invoice-index}
      INVOICE_LINE_ITEM_INDEX: ${self:custom.dynamodb.invoice-line-item-index}
      TRANSACTION_TABLE: ${self:custom.dynamodb.transaction-table}
      ACCOUNT_TABLE: ${self:custom.dynamodb.account-table}
      ACCOUNT_INDEX: ${self:custom.dynamodb.account-index}
      TRANSACTION_INDEX: ${self:custom.dynamodb.transaction-index}
      STATEMENT_TABLE: ${self:custom.dynamodb.statement-table}
      STATEMENT_INDEX: ${self:custom.dynamodb.statement-index}
      LANDLORD_BILL_TABLE: ${self:custom.dynamodb.landlord-bill-table}
      LANDLORD_BILL_ORGANISATION_INDEX: ${self:custom.dynamodb.landlord-bill-organisation-index}
  xero-list-invoice-attachments:
    handler: com.rentancy.integrations.handlers.ListInvoiceAttachmentsHandler
    timeout: 30
    events:
      - schedule:
          rate: rate(5 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /xero/api/invoices
          method: get
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_INDEX: ${self:custom.dynamodb.invoice-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
  landlord-bill-sender:
    handler: com.rentancy.integrations.handlers.LandlordBillSender
    timeout: 30
    events:
      - schedule:
          rate: cron(1 0 * * ? *)
          enabled: true
          input:
            source: DAILY_RUN
      - schedule:
          rate: rate(2 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /landlordbill/{id}
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                statementId: false
      - http:
          path: /v2/landlordbill
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                organisationId: true
      - http:
          path: /v3/landlordbill
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                organisationId: true
      - http:
          path: /landlordbills
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                statementId: true
      - http:
          path: /payout-process/v1/payout
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                organisationId: true
      - http:
          path: /payout-process/v1/calculate
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      USER_COGNITOID_INDEX: ${self:custom.dynamodb.user-id-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      LANDLORD_BILL_TABLE: ${self:custom.dynamodb.landlord-bill-table}
      LANDLORD_BILL_ORGANISATION_INDEX: ${self:custom.dynamodb.landlord-bill-organisation-index}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      TENANCY_INVOICE_TABLE: ${self:custom.dynamodb.tenancy-invoice-table}
      TENANCY_INVOICE_INDEX: ${self:custom.dynamodb.tenancy-invoice-index}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      PARENT_PROPERTY_TABLE: ${self:custom.dynamodb.parent-property-table}
      TENANCY_REFERENCE_INDEX: ${self:custom.dynamodb.tenancy-reference-index}
      RENT_INVOICE_HISTORY_TABLE: ${self:custom.dynamodb.rent-invoice-history}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
      STATEMENT_TABLE: ${self:custom.dynamodb.statement-table}
      STATEMENT_INDEX: ${self:custom.dynamodb.statement-index}
  property-bill-sender:
    handler: com.rentancy.integrations.handlers.PropertyBillSenderHandler
    timeout: 30
    events:
      - schedule:
          rate: rate(5 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /landlordbill
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      LANDLORD_BILL_TABLE: ${self:custom.dynamodb.landlord-bill-table}
      LANDLORD_BILL_ORGANISATION_INDEX: ${self:custom.dynamodb.landlord-bill-organisation-index}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      TENANCY_INVOICE_TABLE: ${self:custom.dynamodb.tenancy-invoice-table}
      TENANCY_INVOICE_INDEX: ${self:custom.dynamodb.tenancy-invoice-index}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      PARENT_PROPERTY_TABLE: ${self:custom.dynamodb.parent-property-table}
      TENANCY_REFERENCE_INDEX: ${self:custom.dynamodb.tenancy-reference-index}
      RENT_INVOICE_HISTORY_TABLE: ${self:custom.dynamodb.rent-invoice-history}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
      ACCOUNT_ORGANISATION_INDEX: ${self:custom.dynamodb.account-organisation-index}
      ACCOUNT_TABLE: ${self:custom.dynamodb.account-table}

  deposit-bill-sender:
    handler: com.rentancy.integrations.handlers.DepositBillSenderHandler
    timeout: 30
    events:
      - schedule:
          rate: rate(5 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /deposit/transferbill
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
      - http:
          path: /deposit/return
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      LANDLORD_BILL_TABLE: ${self:custom.dynamodb.landlord-bill-table}
      LANDLORD_BILL_ORGANISATION_INDEX: ${self:custom.dynamodb.landlord-bill-organisation-index}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      TENANCY_INVOICE_TABLE: ${self:custom.dynamodb.tenancy-invoice-table}
      TENANCY_INVOICE_INDEX: ${self:custom.dynamodb.tenancy-invoice-index}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      PARENT_PROPERTY_TABLE: ${self:custom.dynamodb.parent-property-table}
      TENANCY_REFERENCE_INDEX: ${self:custom.dynamodb.tenancy-reference-index}
      RENT_INVOICE_HISTORY_TABLE: ${self:custom.dynamodb.rent-invoice-history}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
      ACCOUNT_ORGANISATION_INDEX: ${self:custom.dynamodb.account-organisation-index}
      ACCOUNT_TABLE: ${self:custom.dynamodb.account-table}
  create-tenancy-invoice:
    handler: com.rentancy.integrations.handlers.TenancyInvoiceSenderHandler
    timeout: 30
    events:
      - schedule:
          rate: rate(5 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /tenancyinvoice
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      LANDLORD_BILL_TABLE: ${self:custom.dynamodb.landlord-bill-table}
      LANDLORD_BILL_ORGANISATION_INDEX: ${self:custom.dynamodb.landlord-bill-organisation-index}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      TENANCY_INVOICE_TABLE: ${self:custom.dynamodb.tenancy-invoice-table}
      TENANCY_INVOICE_INDEX: ${self:custom.dynamodb.tenancy-invoice-index}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      TENANCY_REFERENCE_INDEX: ${self:custom.dynamodb.tenancy-reference-index}
      RENT_INVOICE_HISTORY_TABLE: ${self:custom.dynamodb.rent-invoice-history}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
  get-deposit-management-data:
    handler: com.rentancy.integrations.handlers.DepositManagementHandler
    timeout: 30
    events:
      - schedule:
          rate: rate(5 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /deposits/tenancy
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      LANDLORD_BILL_TABLE: ${self:custom.dynamodb.landlord-bill-table}
      LANDLORD_BILL_ORGANISATION_INDEX: ${self:custom.dynamodb.landlord-bill-organisation-index}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      ORGANISATION_USER_TABLE: ${self:custom.dynamodb.organisation-user-table}
      ORGANISATION_USER_INDEX: ${self:custom.dynamodb.organisation-user-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      TENANCY_INVOICE_TABLE: ${self:custom.dynamodb.tenancy-invoice-table}
      TENANCY_INVOICE_INDEX: ${self:custom.dynamodb.tenancy-invoice-index}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      TENANCY_REFERENCE_INDEX: ${self:custom.dynamodb.tenancy-reference-index}
      RENT_INVOICE_HISTORY_TABLE: ${self:custom.dynamodb.rent-invoice-history}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      PROPERTY_ORGANISATION_INDEX: ${self:custom.dynamodb.property-organisation-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
  get-deposit-management-data-export:
    handler: com.rentancy.integrations.handlers.DepositManagementExportHandler
    timeout: 30
    events:
      - schedule:
          rate: rate(5 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /deposits/export
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      LANDLORD_BILL_TABLE: ${self:custom.dynamodb.landlord-bill-table}
      LANDLORD_BILL_ORGANISATION_INDEX: ${self:custom.dynamodb.landlord-bill-organisation-index}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      ORGANISATION_USER_TABLE: ${self:custom.dynamodb.organisation-user-table}
      ORGANISATION_USER_INDEX: ${self:custom.dynamodb.organisation-user-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      TENANCY_INVOICE_TABLE: ${self:custom.dynamodb.tenancy-invoice-table}
      TENANCY_INVOICE_INDEX: ${self:custom.dynamodb.tenancy-invoice-index}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      TENANCY_REFERENCE_INDEX: ${self:custom.dynamodb.tenancy-reference-index}
      RENT_INVOICE_HISTORY_TABLE: ${self:custom.dynamodb.rent-invoice-history}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      PROPERTY_ORGANISATION_INDEX: ${self:custom.dynamodb.property-organisation-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
  landlord-report:
    handler: com.rentancy.integrations.handlers.LandlordReportHandler
    timeout: 30
    events:
      - schedule:
          rate: rate(2 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /landlord-report
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                propertyId: true
                startDate: true
                endDate: true

    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      DDB_REGION: ${self:provider.ddb_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
  run-async-lambda:
    handler: com.rentancy.integrations.handlers.AsyncLambdaInvoker
    timeout: 30
    events:
      - schedule:
          rate: rate(5 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /run/job
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                jobName: true

    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      DDB_REGION: ${self:provider.ddb_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      PROPERTY_BALANCE_REPORT_QUEUE_NAME: ${self:custom.sqs.async-property-balance-report-queue}
      CLIENT_BALANCE_REPORT_QUEUE_NAME: ${self:custom.sqs.client-balance-report-queue}
      ORGANISATION_PROPERTY_DATA_EXPORTER_QUEUE_NAME: ${self:custom.sqs.organisation-property-data-exporter-queue}
      CLIENT_STATEMENT_REPORT_QUEUE_NAME: ${self:custom.sqs.client-statement-report-queue}
      CLIENT_GENERAL_REPORT_QUEUE_NAME: ${self:custom.sqs.client-general-report-queue}
      TENANCY_SCHEDULE_REPORT_QUEUE_NAME: ${self:custom.sqs.tenancy-schedule-queue}
      CASH_BALANCE_REPORT_QUEUE_NAME: ${self:custom.sqs.cash-balance-report-queue}
      BACS_REPORT_QUEUE_NAME: ${self:custom.sqs.bacs-report-queue}
      OVERSEAS_RESIDENT_REPORT_QUEUE_NAME: ${self:custom.sqs.overseas-resident-report-queue}
      SUPPLIER_LANDLORD_REPORT_QUEUE_NAME: ${self:custom.sqs.supplier-landlord-statement-report-queue}
      BULK_PAYOUT_QUEUE_NAME: ${self:custom.sqs.bulk-payout-queue}
  tenant-statement-report:
    handler: com.rentancy.integrations.handlers.TenantStatementReportHandler
    timeout: 30
    events:
      - schedule:
          rate: rate(5 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /tenant-report-pdf
          method: post
          contentHandling: CONVERT_TO_BINARY
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                organisationId: true
                tenantId: true
                startDate: true
                endDate: true
                format: true
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      DDB_REGION: ${self:provider.ddb_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      TENANCY_REFERENCE_INDEX: ${self:custom.dynamodb.tenancy-reference-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      RENTANCY_DOCUMENT_UPLOADS: ${file(./config.${opt:stage, 'dev'}.yml):s3.document-bucket}
      PROPERTY_ORGANISATION_INDEX: ${self:custom.dynamodb.property-organisation-index}
      EMAIL_ATTACHMENT_BUCKET: ${self:custom.s3.rentancy-email-attachments-bucket}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      TENANCY_SETTINGS_INDEX: ${self:custom.dynamodb.tenancy-settings-index}

  landlord-report-pdf:
    handler: com.rentancy.integrations.handlers.LandlordReportPdfHandler
    timeout: 30
    events:
      - schedule:
          rate: rate(5 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /landlord-report-pdf
          method: post
          contentHandling: CONVERT_TO_BINARY
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                propertyId: true
                startDate: true
                endDate: true
                format: true
                type: false
                approved: false
                landlordBillId: false

    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      DDB_REGION: ${self:provider.ddb_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      USER_COGNITOID_INDEX: ${self:custom.dynamodb.user-id-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      RENTANCY_DOCUMENT_UPLOADS: ${file(./config.${opt:stage, 'dev'}.yml):s3.document-bucket}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      STATEMENT_TABLE: ${self:custom.dynamodb.statement-table}
      STATEMENT_INDEX: ${self:custom.dynamodb.statement-index}
  property-extract-report:
    handler: com.rentancy.integrations.handlers.PropertyExtractReportHandler
    timeout: 30
    events:
      - schedule:
          rate: rate(5 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /property-extract-report
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                propertyId: true
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      DDB_REGION: ${self:provider.ddb_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      USER_COGNITOID_INDEX: ${self:custom.dynamodb.user-id-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      RENTANCY_DOCUMENT_UPLOADS: ${file(./config.${opt:stage, 'dev'}.yml):s3.document-bucket}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      STATEMENT_TABLE: ${self:custom.dynamodb.statement-table}
      STATEMENT_INDEX: ${self:custom.dynamodb.statement-index}

  statement-report-sender:
    handler: com.rentancy.integrations.handlers.StatementReportSender
    timeout: 30
    events:
      - schedule:
          rate: rate(2 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /generate-statement-report
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
      - http:
          path: /send-statement-report
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                landlordBillId: true
      - http:
          path: /statement-report
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                statementId: true
                skip: true

    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      PROPERTY_ORGANISATION_INDEX: ${self:custom.dynamodb.property-organisation-index}
      DDB_REGION: ${self:provider.ddb_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      USER_COGNITOID_INDEX: ${self:custom.dynamodb.user-id-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      EMAIL_ATTACHMENT_BUCKET: ${self:custom.s3.rentancy-email-attachments-bucket}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      RENTANCY_DOCUMENT_UPLOADS: ${file(./config.${opt:stage, 'dev'}.yml):s3.document-bucket}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      STATEMENT_TABLE: ${self:custom.dynamodb.statement-table}
      STATEMENT_INDEX: ${self:custom.dynamodb.statement-index}
      STATEMENT_LANDLORD_BILL_INDEX: ${self:custom.dynamodb.statement-landlord-bill-index}
      LANDLORD_BILL_TABLE: ${self:custom.dynamodb.landlord-bill-table}
      LANDLORD_BILL_ORGANISATION_INDEX: ${self:custom.dynamodb.landlord-bill-organisation-index}
      PARENT_PROPERTY_TABLE: ${self:custom.dynamodb.parent-property-table}

  tenancy-summary:
    handler: com.rentancy.integrations.handlers.TenancySummary
    timeout: 30
    events:
      - schedule:
          rate: rate(5 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /tenancy/{tenancyId}/summary
          method: get
          authorizer: ${self:custom.authorizers.${self:provider.stage}}

    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      DDB_REGION: ${self:provider.ddb_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      TENANCY_INVOICE_TABLE: ${self:custom.dynamodb.tenancy-invoice-table}
      TENANCY_INVOICE_INDEX: ${self:custom.dynamodb.tenancy-invoice-index}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
  user-summary:
    handler: com.rentancy.integrations.handlers.UserSummaryHandler
    timeout: 30
    events:
      - schedule:
          rate: rate(5 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /user/summary
          method: get
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                organisationId: true
                ledgerCode: true
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      DDB_REGION: ${self:provider.ddb_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      TENANCY_INVOICE_TABLE: ${self:custom.dynamodb.tenancy-invoice-table}
      TENANCY_INVOICE_INDEX: ${self:custom.dynamodb.tenancy-invoice-index}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
  property-summary:
    handler: com.rentancy.integrations.handlers.PropertySummary
    timeout: 30
    events:
      - schedule:
          rate: rate(5 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /property/{propertyId}/summary
          method: get
          authorizer: ${self:custom.authorizers.${self:provider.stage}}

    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      DDB_REGION: ${self:provider.ddb_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      TENANCY_INVOICE_TABLE: ${self:custom.dynamodb.tenancy-invoice-table}
      TENANCY_INVOICE_INDEX: ${self:custom.dynamodb.tenancy-invoice-index}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
  parent-property-summary:
    handler: com.rentancy.integrations.handlers.ParentPropertySummary
    timeout: 30
    events:
      - schedule:
          rate: rate(5 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /parent-property/{parentPropertyId}/summary
          method: get
          authorizer: ${self:custom.authorizers.${self:provider.stage}}

    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      DDB_REGION: ${self:provider.ddb_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      TENANCY_INVOICE_TABLE: ${self:custom.dynamodb.tenancy-invoice-table}
      TENANCY_INVOICE_INDEX: ${self:custom.dynamodb.tenancy-invoice-index}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      PARENT_PROPERTY_TABLE: ${self:custom.dynamodb.parent-property-table}
      PROPERTY_PARENT_PROPERTY_INDEX: ${self:custom.dynamodb.parent-property-property-index}
      READ_MODEL_PARENT_PROPERTY_SUMMARY_TABLE: ${self:custom.dynamodb.read-model-parent-property-summary}
      READ_MODEL_PARENT_PROPERTY_SUMMARY_QUEUE: ${self:custom.sqs.read-model-parent-property-summary-queue}
  parent-property-expenses:
    handler: com.rentancy.integrations.handlers.ParentPropertyExpenses
    timeout: 30
    events:
      - schedule:
          rate: rate(5 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /parent-property/{parentPropertyId}/expenses
          method: get
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                limit: true
                page: true
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      DDB_REGION: ${self:provider.ddb_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      INVOICE_LINE_ITEM_TRACKING_NAME_INDEX: ${self:custom.dynamodb.invoice-line-item-tracking-name-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      TENANCY_INVOICE_TABLE: ${self:custom.dynamodb.tenancy-invoice-table}
      TENANCY_INVOICE_INDEX: ${self:custom.dynamodb.tenancy-invoice-index}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      PARENT_PROPERTY_TABLE: ${self:custom.dynamodb.parent-property-table}
      PROPERTY_PARENT_PROPERTY_INDEX: ${self:custom.dynamodb.parent-property-property-index}
  income-arrears-summary:
    handler: com.rentancy.integrations.handlers.IncomeArrearsSummaryHandler
    memorySize: 7200
    timeout: 30
    events:
      - schedule:
          rate: rate(5 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /arrears
          method: get
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                organisationId: true
                filter: false
                sortBy: false
                sortOrder: false
                page: false
                limit: false
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      PROPERTY_ORGANISATION_INDEX: ${self:custom.dynamodb.property-organisation-index}
      DDB_REGION: ${self:provider.ddb_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      ORGANISATION_USER_TABLE: ${self:custom.dynamodb.organisation-user-table}
      ORGANISATION_USER_INDEX: ${self:custom.dynamodb.organisation-user-index}
      TENANCY_INVOICE_TABLE: ${self:custom.dynamodb.tenancy-invoice-table}
      TENANCY_INVOICE_INDEX: ${self:custom.dynamodb.tenancy-invoice-index}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
  income-arrears-summary-excel-sender:
    handler: com.rentancy.integrations.handlers.IncomeArrearsSummarySender
    name: ${self:custom.incomeExcelSenderFunctionName.${self:provider.stage}, self:custom.incomeExcelSenderFunctionName.default}
    memorySize: 7200
    timeout: 60
    events:
      - schedule:
          rate: rate(5 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /arrears/send
          method: get
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                organisationId: true
                receiverId: true
                filter: false
      - http:
          path: /arrears/report
          method: get
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                organisationId: true
                filter: false
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      PROPERTY_ORGANISATION_INDEX: ${self:custom.dynamodb.property-organisation-index}
      DDB_REGION: ${self:provider.ddb_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      ORGANISATION_USER_TABLE: ${self:custom.dynamodb.organisation-user-table}
      ORGANISATION_USER_INDEX: ${self:custom.dynamodb.organisation-user-index}
      TENANCY_INVOICE_TABLE: ${self:custom.dynamodb.tenancy-invoice-table}
      TENANCY_INVOICE_INDEX: ${self:custom.dynamodb.tenancy-invoice-index}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      RENTANCY_DOCUMENT_UPLOADS: ${file(./config.${opt:stage, 'dev'}.yml):s3.document-bucket}
      EMAIL_ATTACHMENT_BUCKET: ${self:custom.s3.rentancy-email-attachments-bucket}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
  property-ledgers-summary:
    handler: com.rentancy.integrations.handlers.PropertyLedgersSummary
    timeout: 30
    events:
      - schedule:
          rate: rate(5 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /property/ledgers/summary
          method: get
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                organisationId: true
                propertyId: true
                tableType: true
                format: true
                startDate: true
                endDate: true
                filterTenancyReference: false
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      PROPERTY_ORGANISATION_INDEX: ${self:custom.dynamodb.property-organisation-index}
      DDB_REGION: ${self:provider.ddb_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_TRACKING_NAME_INDEX: ${self:custom.dynamodb.invoice-line-item-tracking-name-index}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      ORGANISATION_USER_TABLE: ${self:custom.dynamodb.organisation-user-table}
      ORGANISATION_USER_INDEX: ${self:custom.dynamodb.organisation-user-index}
      TENANCY_INVOICE_TABLE: ${self:custom.dynamodb.tenancy-invoice-table}
      TENANCY_INVOICE_INDEX: ${self:custom.dynamodb.tenancy-invoice-index}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      TRANSACTION_TABLE: ${self:custom.dynamodb.transaction-table}
      TRANSACTION_ORGANISATION_INDEX: ${self:custom.dynamodb.transaction-organisation-index}
      OVER_PAYMENT_TABLE: ${self:custom.dynamodb.over-payment-table}
      OVER_PAYMENT_ORGANISATION_INDEX: ${self:custom.dynamodb.over-payment-organisation-index}
      RENTANCY_DOCUMENT_UPLOADS: ${file(./config.${opt:stage, 'dev'}.yml):s3.document-bucket}
      ACCOUNT_TABLE: ${self:custom.dynamodb.account-table}
      ACCOUNT_ORGANISATION_INDEX: ${self:custom.dynamodb.account-organisation-index}

  client-balance-summary:
    handler: com.rentancy.integrations.handlers.ClientBalanceSummaryHandler
    timeout: 30
    memorySize: 7200
    events:
      - schedule:
          rate: rate(5 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /client/summary
          method: get
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                organisationId: true
      - http:
          path: /property/summary
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                organisationId: true
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      DDB_REGION: ${self:provider.ddb_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      TENANCY_INVOICE_TABLE: ${self:custom.dynamodb.tenancy-invoice-table}
      TENANCY_INVOICE_INDEX: ${self:custom.dynamodb.tenancy-invoice-index}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      PROPERTY_ORGANISATION_INDEX: ${self:custom.dynamodb.property-organisation-index}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      TENANCY_SETTINGS_INDEX: ${self:custom.dynamodb.tenancy-settings-index}

  document-report-pdf:
    handler: com.rentancy.integrations.handlers.DocumentReportPdfHandler
    timeout: 30
    events:
      - schedule:
          rate: rate(5 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /document-report-pdf
          method: post
          contentHandling: CONVERT_TO_BINARY
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                organisationId: true
                startDate: true
                endDate: true
                format: true
                sortBy: true

    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      DDB_REGION: ${self:provider.ddb_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      DOCUMENT_TABLE: ${self:custom.dynamodb.document-table}
      DOCUMENT_ORGANISATION_INDEX: ${self:custom.dynamodb.document-organisation-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      RENTANCY_DOCUMENT_UPLOADS: ${file(./config.${opt:stage, 'dev'}.yml):s3.document-bucket}

  task-report-pdf:
    handler: com.rentancy.integrations.handlers.TaskReportPdfHandler
    timeout: 30
    events:
      - schedule:
          rate: rate(5 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /task-report-pdf
          method: post
          contentHandling: CONVERT_TO_BINARY
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                organisationId: true
                startDate: true
                endDate: true
                format: true
                sortBy: true

    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      DDB_REGION: ${self:provider.ddb_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      DOCUMENT_TABLE: ${self:custom.dynamodb.document-table}
      DOCUMENT_ORGANISATION_INDEX: ${self:custom.dynamodb.document-organisation-index}
      RENTANCY_DOCUMENT_UPLOADS: ${file(./config.${opt:stage, 'dev'}.yml):s3.document-bucket}
      TASK_TABLE: ${self:custom.dynamodb.task-table}
      TASK_ORGANISATION_INDEX: ${self:custom.dynamodb.task-organisation-index}
      COLUMN_TABLE: ${self:custom.dynamodb.column-table}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
  manual-invoice:
    handler: com.rentancy.integrations.handlers.ManualInvoiceHandler
    timeout: 30
    events:
      - schedule:
          rate: rate(3 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /xero/api/invoice/tenancy/{tenancyId}
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      USER_COGNITOID_INDEX: ${self:custom.dynamodb.user-id-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      XERO_UI_ROOT_PATH: ${self:custom.xero.ui-root-path}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      ACCOUNT_ORGANISATION_INDEX: ${self:custom.dynamodb.account-organisation-index}
      ACCOUNT_TABLE: ${self:custom.dynamodb.account-table}
  pay-with-float:
    handler: com.rentancy.integrations.handlers.PayWithFloatHandler
    timeout: 30
    events:
      - http:
          path: /paywithfloat
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      PROPERTY_REFERENCE_INDEX: ${self:custom.dynamodb.property-reference-index}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      USER_COGNITOID_INDEX: ${self:custom.dynamodb.user-id-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      XERO_UI_ROOT_PATH: ${self:custom.xero.ui-root-path}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      ACCOUNT_ORGANISATION_INDEX: ${self:custom.dynamodb.account-organisation-index}
      ACCOUNT_TABLE: ${self:custom.dynamodb.account-table}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
  journal-forecast-report:
    handler: com.rentancy.integrations.handlers.JournalForecastReportHandler
    timeout: 30
    memorySize: 7100
    events:
      - schedule:
          rate: rate(3 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /forecast-report
          method: post
          request:
            parameters:
              querystrings:
                organisationId: true
                startDate: true
                periodLength: true
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      PROPERTY_ORGANISATION_INDEX: ${self:custom.dynamodb.property-organisation-index}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_ORGANISATION_INDEX: ${self:custom.dynamodb.user-organisation-index}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      XERO_UI_ROOT_PATH: ${self:custom.xero.ui-root-path}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ACCOUNT_ORGANISATION_INDEX: ${self:custom.dynamodb.account-organisation-index}
      ACCOUNT_TABLE: ${self:custom.dynamodb.account-table}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      TENANCY_SETTINGS_INDEX: ${self:custom.dynamodb.tenancy-settings-index}
  tenancy-invoice-collector:
    handler: com.rentancy.integrations.handlers.TenancyInvoiceCollector
    timeout: 900
    events:
      - schedule:
          rate: cron(0 4 * * ? *)
          enabled: true
          input:
            sourceType: RENT_INVOICE
      - schedule:
          rate: cron(20 4 * * ? *)
          enabled: true
          input:
            sourceType: COMMISSION_BILL
      - schedule:
          rate: cron(40 4 * * ? *)
          enabled: true
          input:
            sourceType: LANDLORD_COMMISSION_BILL
      - schedule:
          rate: cron(0 5 * * ? *)
          enabled: true
          input:
            sourceType: OVERSEAS_RESIDENT_BILL
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      TENANCY_INVOICE_QUEUE: ${self:custom.sqs.tenancy-invoice-queue}
      COMMISSION_INVOICE_QUEUE: ${self:custom.sqs.commission-invoice-queue}
      RENT_INVOICE_HISTORY_TABLE: ${self:custom.dynamodb.rent-invoice-history}
      RENT_INVOICE_HISTORY_INDEX: ${self:custom.dynamodb.rent-invoice-history-tenancy-index}
      LANDLORD_COMMISSION_QUEUE: ${self:custom.sqs.landlord-commission-queue}
      OVERSEAS_RESIDENT_BILL_QUEUE: ${self:custom.sqs.overseas-resident-bill-queue}
      TENANCY_SETTINGS_INDEX: ${self:custom.dynamodb.tenancy-settings-index}

  org-tenancy-journal-collecting:
    handler: com.rentancy.integrations.handlers.OrgTenancyJournalCollectingHandler
    timeout: 900
    events:
      - http:
          path: auto-journal/run-now
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      DDB_REGION: ${self:provider.ddb_region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      JOURNAL_BILL_SENDER_QUEUE: ${self:custom.sqs.journal-bill-sender-queue}

  tenancy-journal-collector:
    handler: com.rentancy.integrations.handlers.TenancyJournalCollector
    timeout: 900
    events:
      - schedule:
          rate: cron(13 1 * * ? *)
          enabled: true
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}`
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      TENANCY_INVOICE_QUEUE: ${self:custom.sqs.tenancy-invoice-queue}
      COMMISSION_INVOICE_QUEUE: ${self:custom.sqs.commission-invoice-queue}
      RENT_INVOICE_HISTORY_TABLE: ${self:custom.dynamodb.rent-invoice-history}
      RENT_INVOICE_HISTORY_INDEX: ${self:custom.dynamodb.rent-invoice-history-tenancy-index}
      LANDLORD_COMMISSION_QUEUE: ${self:custom.sqs.landlord-commission-queue}
      OVERSEAS_RESIDENT_BILL_QUEUE: ${self:custom.sqs.overseas-resident-bill-queue}
      JOURNAL_BILL_SENDER_QUEUE: ${self:custom.sqs.journal-bill-sender-queue}
  whatsapp-handler:
    handler: com.rentancy.integrations.handlers.WhatsAppHandler
    timeout: 30
    events:
      - http:
          path: /whatsapp/message
          method: post
      - http:
          path: /whatsapp/status
          method: post
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      ORGANISATION_USER_TABLE: ${self:custom.dynamodb.organisation-user-table}
      ORGANISATION_USER_INDEX: ${self:custom.dynamodb.organisation-user-index}
      CONVERSATION_TABLE: ${self:custom.dynamodb.conversation-table}
      MESSAGE_TABLE: ${self:custom.dynamodb.message-table}
      MESSAGE_INDEX: ${self:custom.dynamodb.message-index}
      USER_COGNITOID_INDEX: ${self:custom.dynamodb.user-id-index}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      TWILIO_ACCOUNT_SID: ${self:custom.twilio.account-sid.${self:provider.stage}}
      TWILIO_AUTH_TOKEN: ${self:custom.twilio.auth-token.${self:provider.stage}}
      RENTANCY_DOCUMENT_UPLOADS: ${file(./config.${opt:stage, 'dev'}.yml):s3.document-bucket}
      CUSTOM_WHATSAPP_ORGANISATION_NAMES: vickerscapitalltd:Tenant Manager
  data-exporter:
    handler: com.rentancy.integrations.handlers.DataExporterHandler
    timeout: 30
    memorySize: 3600
    events:
      - schedule:
          rate: rate(2 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: /contacts/export
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                organisationId: true
                format: true
      - http:
          path: /properties/export
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                organisationId: true
                format: true
      - http:
          path: /contracts/export
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                organisationId: true
                format: true
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      PROPERTY_ORGANISATION_INDEX: ${self:custom.dynamodb.property-organisation-index}
      DDB_REGION: ${self:provider.ddb_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_ORGANISATION_INDEX: ${self:custom.dynamodb.user-organisation-index}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      ADDRESS_ORGANISATION_INDEX: ${self:custom.dynamodb.address-organisation-index}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      ORGANISATION_USER_TABLE: ${self:custom.dynamodb.organisation-user-table}
      ORGANISATION_USER_INDEX: ${self:custom.dynamodb.organisation-user-index}
      TENANCY_SETTINGS_INDEX: ${self:custom.dynamodb.tenancy-settings-index}

  invoice-poster:
    handler: com.rentancy.integrations.handlers.InvoicePoster
    timeout: 30
    events:
      - schedule:
          rate: cron(0 10 * * ? *)
          enabled: true
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      LANDLORD_BILL_TABLE: ${self:custom.dynamodb.landlord-bill-table}
      LANDLORD_BILL_ORGANISATION_INDEX: ${self:custom.dynamodb.landlord-bill-organisation-index}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      TENANCY_INVOICE_TABLE: ${self:custom.dynamodb.tenancy-invoice-table}
      TENANCY_INVOICE_INDEX: ${self:custom.dynamodb.tenancy-invoice-index}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      TENANCY_REFERENCE_INDEX: ${self:custom.dynamodb.tenancy-reference-index}
      RENT_INVOICE_HISTORY_TABLE: ${self:custom.dynamodb.rent-invoice-history}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
  organisation-report-sender:
    handler: com.rentancy.integrations.handlers.OrganisationReportSenderHandler
    timeout: 30
    events:
      - schedule:
          rate: cron(0 0 * * ? *)
          enabled: true
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      ORGANISATION_REPORT_SENDER_EMAILS: ${self:custom.organisation-report-sender-emails.${self:provider.stage}}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      EMAIL_ATTACHMENT_BUCKET: ${self:custom.s3.rentancy-email-attachments-bucket}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      DDB_REGION: ${self:provider.ddb_region}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      LAMBDA_REGION: ${self:provider.lambda-region}
      PRIMARY_ORGANISATION_ID: ${self:custom.rentancy-primary-organisation-id.${self:provider.stage}}
  organisation-stripe-charge-report-sender:
    handler: com.rentancy.integrations.handlers.OrganisationStripeChargeReportSenderHandler
    timeout: 30
    events:
      - schedule:
          rate: cron(0 9 * * ? *)
          enabled: true
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      ORGANISATION_STRIPE_CHARGE_TABLE: ${self:custom.dynamodb.organisation-stripe-charge-table}
      ORGANISATION_STRIPE_CHARGE_REPORT_EMAILS: ${self:custom.organisation-stripe-charge-report-emails.${self:provider.stage}}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      EMAIL_ATTACHMENT_BUCKET: ${self:custom.s3.rentancy-email-attachments-bucket}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      DDB_REGION: ${self:provider.ddb_region}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      LAMBDA_REGION: ${self:provider.lambda-region}
      PRIMARY_ORGANISATION_ID: ${self:custom.rentancy-primary-organisation-id.${self:provider.stage}}
  organisation-daily-signup-report-sender:
    handler: com.rentancy.integrations.handlers.OrganisationDailySignupReportSenderHandler
    timeout: 30
    events:
      - schedule:
          rate: cron(0 9 * * ? *)
          enabled: true
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      ORGANISATION_DAILY_SIGNUP_REPORT_EMAILS: ${self:custom.organisation-daily-signup-report-emails.${self:provider.stage}}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      EMAIL_ATTACHMENT_BUCKET: ${self:custom.s3.rentancy-email-attachments-bucket}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      DDB_REGION: ${self:provider.ddb_region}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      LAMBDA_REGION: ${self:provider.lambda-region}
      PRIMARY_ORGANISATION_ID: ${self:custom.rentancy-primary-organisation-id.${self:provider.stage}}
      PROPERTY_ORGANISATION_INDEX: ${self:custom.dynamodb.property-organisation-index}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
  organisation-stripe-charge-number-generator:
    handler: com.rentancy.integrations.handlers.OrganisationStripeChargeNumberGeneratorHandler
    name: ${self:custom.orgStripeChargeIdFunctionName.${self:provider.stage}, self:custom.orgStripeChargeIdFunctionName.default}
    timeout: 30
    events:
      - schedule:
          rate: cron(1 0 * * ? *)
          enabled: true
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      ORGANISATION_STRIPE_CHARGE_TABLE: ${self:custom.dynamodb.organisation-stripe-charge-table}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      EMAIL_ATTACHMENT_BUCKET: ${self:custom.s3.rentancy-email-attachments-bucket}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      DDB_REGION: ${self:provider.ddb_region}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      LAMBDA_REGION: ${self:provider.lambda-region}
      PRIMARY_ORGANISATION_ID: ${self:custom.rentancy-primary-organisation-id.${self:provider.stage}}

  organisation-stripe-charge-invoice-pdf:
    handler: com.rentancy.integrations.handlers.OrganisationStripeChargeInvoicePdf
    name: ${self:custom.orgStripeInvoicePdfFunctionName.${self:provider.stage}, self:custom.orgStripeInvoicePdfFunctionName.default}
    timeout: 30
    events:
      - http:
          path: /organisation/charges/{chargeId}/pdf
          method: get
          contentHandling: CONVERT_TO_BINARY
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                organisationId: true
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      ORGANISATION_STRIPE_CHARGE_TABLE: ${self:custom.dynamodb.organisation-stripe-charge-table}
      ORGANISATION_STRIPE_CHARGE_REPORT_EMAILS: ${self:custom.organisation-stripe-charge-report-emails.${self:provider.stage}}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      EMAIL_ATTACHMENT_BUCKET: ${self:custom.s3.rentancy-email-attachments-bucket}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      DDB_REGION: ${self:provider.ddb_region}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      LAMBDA_REGION: ${self:provider.lambda-region}
      PRIMARY_ORGANISATION_ID: ${self:custom.rentancy-primary-organisation-id.${self:provider.stage}}
      STRIPE_MINIMUM_CHARGE_RATE: ${self:custom.stripe.minimum-charge.${self:provider.stage}}
      STRIPE_CHARGE_CURRENCY: ${self:custom.stripe.charge-currency.${self:provider.stage}}
      STRIPE_PROPERTY_CHARGE_RATE: ${self:custom.stripe.property-charge-rate.${self:provider.stage}}
      STRIPE_VAT_CHARGE_RATE: ${self:custom.stripe.charge-vat-rate.${self:provider.stage}}

  revenue-report-sender:
    handler: com.rentancy.integrations.handlers.RevenueReportSenderHelper
    timeout: 30
    events:
      - schedule:
          rate: cron(1 0 ? * 2 *)
          enabled: true
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      LANDLORD_BILL_TABLE: ${self:custom.dynamodb.landlord-bill-table}
      LANDLORD_BILL_ORGANISATION_INDEX: ${self:custom.dynamodb.landlord-bill-organisation-index}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      TENANCY_INVOICE_TABLE: ${self:custom.dynamodb.tenancy-invoice-table}
      TENANCY_INVOICE_INDEX: ${self:custom.dynamodb.tenancy-invoice-index}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      TENANCY_REFERENCE_INDEX: ${self:custom.dynamodb.tenancy-reference-index}
      RENT_INVOICE_HISTORY_TABLE: ${self:custom.dynamodb.rent-invoice-history}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
      PRIMARY_ORGANISATION_ID: ${self:custom.rentancy-primary-organisation-id.${self:provider.stage}}
      REVENUE_REPORT_SENDER_EMAILS: ${self:custom.revenue-report-sender-emails.${self:provider.stage}}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      EMAIL_ATTACHMENT_BUCKET: ${self:custom.s3.rentancy-email-attachments-bucket}
  whats-app-request-mail-sender:
    handler: com.rentancy.integrations.handlers.WhatsAppRequestHandler
    timeout: 30
    events:
      - http:
          path: whatsapp/integration
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                organisationId: true
                userId: true
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      DDB_REGION: ${self:provider.ddb_region}
      RENTANCY_SUPPORT_EMAIL: ${self:custom.rentancySuppMail.${self:provider.stage}}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      WHATS_APP_INTEGRATION_QUEUE: ${self:custom.sqs.whats-app-integration-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
  xero-contact-synchronizer:
    handler: com.rentancy.integrations.handlers.XeroContactSynchronizer
    timeout: 900
    events:
      - schedule:
          rate: cron(0 1 * * ? *)
          enabled: true
      - schedule:
          rate: cron(50 2 * * ? *)
          enabled: true
      - schedule:
          rate: cron(0 13 * * ? *)
          enabled: true
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      ORGANISATION_USER_TABLE: ${self:custom.dynamodb.organisation-user-table}
      ORGANISATION_USER_INDEX: ${self:custom.dynamodb.organisation-user-index}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      ADDRESS_ORGANISATION_INDEX: ${self:custom.dynamodb.address-organisation-index}
  xero-invoice-synchronizer:
    handler: com.rentancy.integrations.handlers.XeroInvoiceSynchronizer
    timeout: 30
    events:
      - schedule:
          rate: rate(3 minutes)
          enabled: true
          input:
            source: WARMUP
      - http:
          path: xero/invoice/synch
          method: post
          request:
            parameters:
              querystrings:
                organisationId: true
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      USER_TABLE_DENORMALIZED_EMAILS_INDEX: ${self:custom.dynamodb.user-table-denormalized-emails-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      ORGANISATION_USER_TABLE: ${self:custom.dynamodb.organisation-user-table}
      ORGANISATION_USER_INDEX: ${self:custom.dynamodb.organisation-user-index}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
  preflight-response:
    handler: com.rentancy.integrations.handlers.PreflightResponseHandler
    environment:
      ENV: ${self:provider.stage}
    events:
      - http:
          path: /{proxy+}
          method: options
  early-invoice-generation:
    handler: com.rentancy.integrations.handlers.EarlyInvoiceGenerationHandler
    events:
      - http:
          path: /early-invoice
          method: post
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
    environment:
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      COMMISSION_INVOICE_QUEUE: ${self:custom.sqs.commission-invoice-queue}
      DDB_REGION: ${self:provider.ddb_region}
      EMAIL_ATTACHMENT_BUCKET: ${self:custom.s3.rentancy-email-attachments-bucket}
      EMAIL_INVOICE_QUEUE: ${self:custom.sqs.email-invoice-queue}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      EMAIL_TEMPLATE_NOTIFICATION_QUEUE: ${self:custom.sqs.email-template-notification-queue}
      ENV: ${self:provider.stage}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      LAMBDA_REGION: ${self:provider.lambda-region}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      OVERSEAS_RESIDENT_BILL_QUEUE: ${self:custom.sqs.overseas-resident-bill-queue}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      REGION: ${self:provider.region}
      RENTANCY_DOMAIN: ${self:custom.rentancy-domain.${self:provider.stage}}
      RENTANCY_SUPPORT_EMAIL: ${self:custom.rentancySuppMail.${self:provider.stage}}
      RENT_INVOICE_HISTORY_INDEX: ${self:custom.dynamodb.rent-invoice-history-tenancy-index}
      RENT_INVOICE_HISTORY_TABLE: ${self:custom.dynamodb.rent-invoice-history}
      RENT_INVOICE_HISTORY_XERO_INDEX: ${self:custom.dynamodb.rent-invoice-history-xero-index}
      TENANCY_INVOICE_QUEUE: ${self:custom.sqs.tenancy-invoice-queue}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      TENANCY_SETTINGS_INDEX: ${self:custom.dynamodb.tenancy-settings-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      XERO_UI_ROOT_PATH: ${self:custom.xero.ui-root-path}
  rent-invoice-dates-query:
    handler: com.rentancy.integrations.handlers.RentInvoiceDatesQueryHandler
    events:
      - http:
          path: /rent-invoice-dates
          method: get
          authorizer: ${self:custom.authorizers.${self:provider.stage}}
          request:
            parameters:
              querystrings:
                tenancyId: true
                referenceDate: false
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      RENT_INVOICE_HISTORY_TABLE: ${self:custom.dynamodb.rent-invoice-history}
      RENT_INVOICE_HISTORY_INDEX: ${self:custom.dynamodb.rent-invoice-history-tenancy-index}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      EMAIL_INVOICE_QUEUE: ${self:custom.sqs.email-invoice-queue}
      XERO_UI_ROOT_PATH: ${self:custom.xero.ui-root-path}
      RENTANCY_DOMAIN: ${self:custom.rentancy-domain.${self:provider.stage}}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      TENANCY_SETTINGS_INDEX: ${self:custom.dynamodb.tenancy-settings-index}
      EMAIL_TEMPLATE_NOTIFICATION_QUEUE: ${self:custom.sqs.email-template-notification-queue}
      RENTANCY_SUPPORT_EMAIL: ${self:custom.rentancySuppMail.${self:provider.stage}}
  rent-invoice-dates-query-internal:
    handler: com.rentancy.integrations.handlers.RentInvoiceDatesQueryHandlerInternal
    events:
      - http:
          path: /internal/rent-invoice-dates
          method: get
          authorizer: aws_iam
          request:
            parameters:
              querystrings:
                tenancyId: true
                referenceDate: false
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      RENT_INVOICE_HISTORY_TABLE: ${self:custom.dynamodb.rent-invoice-history}
      RENT_INVOICE_HISTORY_INDEX: ${self:custom.dynamodb.rent-invoice-history-tenancy-index}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      EMAIL_INVOICE_QUEUE: ${self:custom.sqs.email-invoice-queue}
      XERO_UI_ROOT_PATH: ${self:custom.xero.ui-root-path}
      RENTANCY_DOMAIN: ${self:custom.rentancy-domain.${self:provider.stage}}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      TENANCY_SETTINGS_INDEX: ${self:custom.dynamodb.tenancy-settings-index}
      EMAIL_TEMPLATE_NOTIFICATION_QUEUE: ${self:custom.sqs.email-template-notification-queue}
      RENTANCY_SUPPORT_EMAIL: ${self:custom.rentancySuppMail.${self:provider.stage}}

