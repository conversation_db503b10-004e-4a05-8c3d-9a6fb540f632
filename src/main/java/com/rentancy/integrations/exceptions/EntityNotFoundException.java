package com.rentancy.integrations.exceptions;

import org.apache.commons.lang.StringUtils;

public class EntityNotFoundException extends RuntimeException {

    public <T> EntityNotFoundException(Class<T> entity, String identifierName, Object identifierValue) {
        super(String.format("%s with [%s=%s] not found",
                String.join(" ", StringUtils.splitByCharacterTypeCamelCase(entity.getName())),
                identifierName,
                identifierValue.toString())
        );
    }

    public <T> EntityNotFoundException(Class<T> entity, Object id) {
        super(String.format("%s with [ID=%s] not found", entity.getName(), id.toString()));
    }
}
