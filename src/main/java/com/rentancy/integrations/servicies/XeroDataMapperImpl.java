package com.rentancy.integrations.servicies;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.*;
import com.rentancy.integrations.pojos.Invoice.XeroInvoicePayment;
import com.rentancy.integrations.pojos.OverPayment.OverPaymentStatuses;
import com.rentancy.integrations.pojos.XeroOverPayments.XeroOverPayment;
import com.rentancy.integrations.pojos.XeroTransactions.BatchPayment;
import com.rentancy.integrations.servicies.persistence.InvoiceService;
import com.rentancy.integrations.servicies.persistence.OrganisationService;
import com.rentancy.integrations.servicies.persistence.PropertyService;
import com.rentancy.integrations.servicies.persistence.UserService;
import com.xero.models.accounting.Address;
import com.xero.models.accounting.Contact;
import com.xero.models.accounting.Journal;
import com.xero.models.accounting.JournalLine;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.rentancy.integrations.servicies.TenancyService.DEPOSIT_LEDGER_CODE_NAMES;
import static com.rentancy.integrations.util.JSONUtils.wrappedToJsonString;
import static com.rentancy.integrations.util.Utils.*;
import static java.util.stream.Collectors.toUnmodifiableList;
import static java.util.stream.Collectors.toUnmodifiableSet;

@RequiredArgsConstructor
public class XeroDataMapperImpl implements XeroDataMapper {

    private static final Logger log = LogManager.getLogger(XeroDataMapperImpl.class);

    private static final String PAID_STATUS = "PAID";

    private final OrganisationService organisationService;
    private final InvoiceService invoiceService;
    private final UserService userService;
    private final PropertyService propertyService;
    private final ReportService reportService;
    private final IntegrationService integrationService;
    private final SQSClient sqsClient;
    private final Config config;


    @Override
    public void mapInvoice(String organisation, String tenant, XeroInvoices.XeroInvoice xeroInvoice, User contactUser) {
        var mapReference = new AtomicBoolean(true);
        var invoiceId = xeroInvoice.getInvoiceId();
        var reference = xeroInvoice.getReference();
        var number = xeroInvoice.getNumber();
        var organisationItem = organisationService.getOrganisation(organisation);
        var integration = integrationService.findIntegration(tenant, Integration.IntegrationService.XERO);

        var depositLedgerCodes = getDepositLedgerCodes(organisationItem);
        var isDepositInvoice = xeroInvoice.getLineItems()
                .stream()
                .filter(item -> Objects.nonNull(item.getAccountCode()))
                .anyMatch(item -> depositLedgerCodes.contains(item.getAccountCode()));
        var isIncomeDeposit = "ACCREC".equals(xeroInvoice.getType()) && isDepositInvoice;

        var propertyTrackingName = findMappedTrackingCategory(organisationItem, "Property");
        var trackingName = xeroInvoice.getLineItems()
                .stream()
                .map(XeroInvoices.XeroInvoiceLineItem::getTracking)
                .flatMap(List::stream)
                .filter(tracking -> tracking.getTrackingName().equals(propertyTrackingName))
                .map(XeroInvoices.TrackingCategory::getTrackingOption)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
        boolean balanceTransfer = Optional.ofNullable(contactUser)
                .filter(user -> Objects.nonNull(organisationItem.getBalanceTransferContactUserId()))
                .map(user -> user.getId().equals(organisationItem.getBalanceTransferContactUserId()))
                .orElse(false);


        log.info("Selected tracking name:" + trackingName);

        var invoice = Invoice
                .builder()
                .invoiceId(invoiceId)
                .number(number)
                .reference(reference)
                .type(xeroInvoice.getType())
                .status(xeroInvoice.getStatus())
                .contactId(Optional.ofNullable(xeroInvoice.getContact()).map(contact -> contact.getContactID().toString()).orElse(null))
                .contactFname(contactUser.getFname())
                .contactSname(contactUser.getSname())
                .contactCompanyName(contactUser.getCompanyName())
                .lineAmountTypes(xeroInvoice.getLineAmountTypes())
                .subTotal(xeroInvoice.getSubTotal())
                .totalTax(xeroInvoice.getTotalTax())
                .total(xeroInvoice.getTotal())
                .amountDue(xeroInvoice.getAmountDue())
                .amountPaid(xeroInvoice.getAmountPaid())
                .totalDiscount(xeroInvoice.getTotalDiscount())
                .amountCredited(xeroInvoice.getAmountCredited())
                .currency(xeroInvoice.getCurrency())
                .organisation(organisation)
                .tenant(tenant)
                .sentToContact(xeroInvoice.isSentToContact())
                .hasAttachments(xeroInvoice.isHasAttachments())
                .expectedPaymentDate(toInstant(xeroInvoice.getExpectedPaymentDate()))
                .plannedPaymentDate(toInstant(xeroInvoice.getPlannedPaymentDate()))
                .fullyPaidOnDate(toInstant(xeroInvoice.getFullyPaidOnDate()))
                .date(toInstant(xeroInvoice.getDate()))
                .dueDate(toInstant(xeroInvoice.getDueDate()))
                .lineItems(mapLineItems(xeroInvoice.getLineItems(), propertyTrackingName, balanceTransfer))
                .payments(mapXeroInvoicePayments(xeroInvoice.getPayments()))
                .trackingOptionName(trackingName)
                .warnings(List.of())
                .balanceTransfer(balanceTransfer)
                .build();

        if (invoice.getStatus().equals("VOIDED") || invoice.getStatus().equals("DELETED")) {
            Optional.ofNullable(invoiceService.findInvoiceWithOrganisationAndInvoiceId(organisation, invoiceId))
                    .ifPresent(item -> {
                        propertyService.deletePropertyInvoiceWithInvoiceId(item.getId());
                        propertyService.deleteTenancyInvoicesWithInvoiceId(item.getId());
                        invoiceService.deletePaymentsWithOrganisationIdAndInvoiceId(organisation, invoiceId);
                        invoiceService.deleteInvoice(item);
                        var deletedIds = invoiceService.deleteLandlordBillsWithOriginalInvoiceId(item.getId());
                        deletedIds.forEach(reportService::deleteByLandlordBillId);

                        if (isIncomeDeposit && StringUtils.hasText(invoice.getReference())) {
                            propertyService.findTenancyWithOrganisationAndReference(organisation, invoice.getReference())
                                    .ifPresent(tenancy -> propertyService.updateTenancyDepositStatus(tenancy.getId(), null));
                        }
                    });
            return;
        }

        var isBill = com.xero.models.accounting.Invoice.TypeEnum.ACCPAY.getValue().equals(invoice.getType());
        var searchBy = isBill ? number : reference;
        var excludedLedgerCodes = getExcludedLedgerCodes(organisationItem);
        var rentIncomeLedgerCode = getRentIncomeLedgerCode(organisationItem);
        var id = Optional.ofNullable(invoiceService.findInvoiceWithOrganisationAndInvoiceId(organisation, invoiceId))
                .map(item -> {
                    var itemId = item.getId();
                    var itemSearchBy = isBill ? item.getNumber() : item.getReference();
                    mapReference.set(Objects.nonNull(searchBy) && !searchBy.equals(itemSearchBy));
                    log.info("Invoice found - " + itemId);
                    invoiceService.updateInvoice(invoice.toBuilder().id(itemId).build(), organisation);

                    var referenceRemoved = (Objects.isNull(searchBy) || searchBy.isEmpty()) && Objects.nonNull(itemSearchBy);
                    if (referenceRemoved) {
                        log.info("Reference was removed, deleting related props/contracts");
                        propertyService.deletePropertyInvoiceWithInvoiceId(item.getId());
                        propertyService.deleteTenancyInvoicesWithInvoiceId(item.getId());
                        invoiceService.deletePaymentsWithOrganisationIdAndInvoiceId(organisation, invoiceId);
                        invoiceService.deleteInvoice(item);
                        var deletedIds = invoiceService.deleteLandlordBillsWithOriginalInvoiceId(item.getId());
                        deletedIds.forEach(reportService::deleteByLandlordBillId);
                    }

                    Optional<Tenancy> tenancyOpt = StringUtils.hasText(invoice.getReference()) ?
                            propertyService.findTenancyWithOrganisationAndReference(organisation, invoice.getReference()) : Optional.empty();

                    tenancyOpt.ifPresent(tenancy -> {
                        if (!isIncomeDeposit) {
                            return;
                        }

                        if (referenceRemoved) {
                            propertyService.updateTenancyDepositStatus(tenancy.getId(), null);
                            return;
                        }
                        var isFullyPaid = PAID_STATUS.equals(invoice.getStatus());
                        var tenancyDepositStatus = isFullyPaid ? Tenancy.DepositStatus.REGISTERING : Tenancy.DepositStatus.RECEIVING;
                        propertyService.updateTenancyDepositStatus(tenancy.getId(), tenancyDepositStatus);
                    });

                    var currentPaidAmount = new BigDecimal(getOrDefault(invoice.getAmountPaid(), "0.00"));
                    var previousPaidAmount = new BigDecimal(getOrDefault(item.getAmountPaid(), "0.00"));
                    var originalPaidAmount = currentPaidAmount.subtract(previousPaidAmount);
                    log.info("Original paid amount - {}", originalPaidAmount);
                    if (originalPaidAmount.doubleValue() != 0) {
                        tenancyOpt.ifPresent(tenancy -> {
                            var settings = tenancy.getSettings();

                            log.info("Checking for mgmt queue update - {}", settings);
                            log.info("Rent income code - {}", rentIncomeLedgerCode);
                            log.info("Filter result - {}", invoice.getLineItems().stream().filter(lineItem -> lineItem.getAccountCode() != null).anyMatch(lineItem -> rentIncomeLedgerCode.equals(lineItem.getAccountCode())));
                            if (settings.getFeeType().equals("PERCENTAGE_OF_AMOUNT_RECEIVED") && invoice.getLineItems().stream().filter(lineItem -> lineItem.getAccountCode() != null).anyMatch(lineItem -> rentIncomeLedgerCode.equals(lineItem.getAccountCode()))) {
                                var payload = TenancyInvoiceSenderPayload.builder()
                                        .tenant(tenant)
                                        .token(integration.getAccessToken())
                                        .organisationId(organisation)
                                        .tenancies(List.of(tenancy.getId()))
                                        .journalingTenantAmount(Map.of())
                                        .paidAmount(originalPaidAmount)
                                        .xeroInvoiceId(invoiceId)
                                        .build();
                                sqsClient.enqueue(config.getCommissionInvoiceQueue(), wrappedToJsonString(payload));
                            }
                        });
                    }

                    if (!item.getStatus().equals(PAID_STATUS) && invoice.getStatus().equals(PAID_STATUS) && !isBill) {
                        log.info("Excluded ledger codes - {}", excludedLedgerCodes);
                        var paidAmount = invoice.getLineItems()
                                .stream()
                                .filter(lineItem -> Objects.nonNull(lineItem.getAccountCode()))
                                .filter(lineItem -> !excludedLedgerCodes.contains(lineItem.getAccountCode()))
                                .map(lineItem -> new BigDecimal(getOrDefault(lineItem.getLineAmount(), "0.00")).add(new BigDecimal(getOrDefault(lineItem.getTaxAmount(), "0.00"))))
                                .reduce(BigDecimal.ZERO, BigDecimal::add);

                        if (!paidAmount.equals(BigDecimal.ZERO)) {
                            tenancyOpt.ifPresent(tenancy -> {
                                sqsClient.enqueue(config.getLandlordBillQueue(), wrappedToJsonString(new LandlordInvoicePaidCommand(tenancy, paidAmount.toString(), itemId)));
                            });
                        }
                    }

                    return itemId;
                }).orElseGet(() -> {
                    var itemId = invoiceService.createInvoice(invoice);
                    Optional<Tenancy> tenancyOpt = StringUtils.hasText(invoice.getReference()) ?
                            propertyService.findTenancyWithOrganisationAndReference(organisation, invoice.getReference()) : Optional.empty();
                    tenancyOpt.ifPresent(tenancy -> {
                        if (isIncomeDeposit) {
                            var isFullyPaid = PAID_STATUS.equals(invoice.getStatus());
                            var tenancyDepositStatus = isFullyPaid ? Tenancy.DepositStatus.REGISTERING : Tenancy.DepositStatus.RECEIVING;
                            propertyService.updateTenancyDepositStatus(tenancy.getId(), tenancyDepositStatus);
                        }
                    });

                    if (invoice.getAmountPaid() != null) {
                        var paid = new BigDecimal(invoice.getAmountPaid());
                        if (paid.doubleValue() > 0) {
                            tenancyOpt.ifPresent(tenancy -> {
                                var settings = tenancy.getSettings();

                                log.info("Checking for mgmt queue create - {}", settings);
                                log.info("Rent income code - {}", rentIncomeLedgerCode);
                                log.info("Filter result - {}", invoice.getLineItems().stream().filter(lineItem -> lineItem.getAccountCode() != null).anyMatch(lineItem -> rentIncomeLedgerCode.equals(lineItem.getAccountCode())));
                                if (settings.getFeeType().equals("PERCENTAGE_OF_AMOUNT_RECEIVED") && invoice.getLineItems().stream().filter(lineItem -> lineItem.getAccountCode() != null).anyMatch(lineItem -> rentIncomeLedgerCode.equals(lineItem.getAccountCode()))) {

                                    var payload = TenancyInvoiceSenderPayload.builder()
                                            .tenant(tenant)
                                            .token(integration.getAccessToken())
                                            .organisationId(organisation)
                                            .tenancies(List.of(tenancy.getId()))
                                            .journalingTenantAmount(Map.of())
                                            .paidAmount(paid)
                                            .xeroInvoiceId(invoiceId)
                                            .build();

                                    sqsClient.enqueue(config.getCommissionInvoiceQueue(), wrappedToJsonString(payload));
                                }
                            });
                        }
                    }

                    if (invoice.getStatus().equals(PAID_STATUS) && !isBill) {
                        var paidAmount = invoice.getLineItems()
                                .stream()
                                .filter(lineItem -> Objects.nonNull(lineItem.getAccountCode()))
                                .filter(lineItem -> !excludedLedgerCodes.contains(lineItem.getAccountCode()))
                                .map(lineItem -> new BigDecimal(getOrDefault(lineItem.getLineAmount(), "0.00")).add(new BigDecimal(getOrDefault(lineItem.getTaxAmount(), "0.00"))))
                                .reduce(BigDecimal.ZERO, BigDecimal::add);

                        tenancyOpt.ifPresent(tenancy -> {
                            sqsClient.enqueue(config.getLandlordBillQueue(), wrappedToJsonString(new LandlordInvoicePaidCommand(tenancy, paidAmount.toString(), itemId)));
                        });
                    }

                    log.info("Invoice created - " + itemId);
                    return itemId;
                });

        if (Objects.nonNull(searchBy) && !searchBy.isEmpty() && !searchBy.isBlank()) {
            log.info("Reference - " + reference + " searchBy " + searchBy);
            // if invoice => search by reference, else search by invoice.number=property.reference
            // amount == bill.total
            var properties = propertyService.findPropertyWithOrganisationAndReference(organisation, getPropertyReference(searchBy));
            if (properties.isPresent()) {
                log.info("Properties - " + properties);
                properties.ifPresent(property -> {
                    if (mapReference.get()) {
                        propertyService.deletePropertyInvoiceWithInvoiceId(id);
                        propertyService.createPropertyInvoice(organisation, property.getId(), id);
                    }

                    var lastFinalizedStatement = reportService.findLastFinalizedStatement(property.getId());

                    log.info("FinalizedStatement present - {}", lastFinalizedStatement.isPresent());

                    lastFinalizedStatement
                            .filter(statement -> statement.getTo().isAfter(invoice.getDate()))
                            .ifPresent(statement -> {
                                var warnings = List.of(
                                        String.join(" ", property.getAddressLine1(), ":", "Statement date", formatDate(statement.getTo().toString()))
                                );
                                invoiceService.updateWarnings(id, warnings);

                                log.info("Updated warnings - {}", warnings);
                            });
                });
                if (isBill && mapReference.get()) {
                    properties.ifPresent(property -> {
                        invoiceService.createAllocation(id, invoice.getTotal(), property.getId(), property.getOrganisation());
                        sqsClient.enqueue(config.getLandlordBillUpdateQueue(), wrappedToJsonString(new LandlordBillUpdateCommand(property.getId())));
                    });
                    invoiceService.addBillProperties(id, properties.get());
                }
            } else {
                if (mapReference.get()) {
                    var tenancies = propertyService.findTenancyWithOrganisationAndReference(organisation, searchBy);
                    log.info("Tenancies - " + tenancies);
                    propertyService.deleteTenancyInvoicesWithInvoiceId(id);
                    tenancies.ifPresent(tenancy -> propertyService.createTenancyInvoice(tenancy.getId(), id));
                }
            }
        }
    }

    private List<XeroInvoicePayment> mapXeroInvoicePayments(List<XeroInvoices.XeroPayments> payments) {
        return Optional.ofNullable(payments)
            .stream()
            .flatMap(Collection::stream)
            .map(payment -> XeroInvoicePayment.builder()
                .date(toInstant(payment.getDate()))
                .amount(payment.getAmount())
                .paymentId(payment.getPaymentId())
                .batchPaymentId(payment.getBatchPaymentId())
                .reference(payment.getReference())
                .build())
            .collect(toUnmodifiableList());
    }

    private String getPropertyReference(String number) {
        var parts = number.split("/");
        return parts[0];
    }

    private List<String> getExcludedLedgerCodes(Organisation organisation) {
        return organisation
                .getLedgerCodes()
                .stream()
                .filter(ledgerCode -> List.of(
                        "Deposits",
                        "Reservations Income",
                        "Service Charge Income",
                        "Other Income",
                        "Reserve 1",
                        "Reserve 2",
                        "Reserve 3",
                        "Reserve 4",
                        "Reserve 5",
                        "Tenant Balances",
                        "Supplier Balances",
                        "Landlord Balances",
                        "Suspense Balances"
                    ).contains(ledgerCode.getName()))
                .map(Organisation.LedgerCode::getCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toUnmodifiableList());
    }

    private String getRentIncomeLedgerCode(Organisation organisation) {
        return organisation
                .getLedgerCodes()
                .stream()
                .filter(ledgerCode -> "Rent Income".equals(ledgerCode.getName()))
                .filter(ledgerCode -> Objects.nonNull(ledgerCode.getCode()))
                .findAny()
                .map(Organisation.LedgerCode::getCode)
                .orElse("200");
    }

    private Set<String> getDepositLedgerCodes(Organisation organisation) {
        return organisation
                .getLedgerCodes()
                .stream()
                .filter(ledgerCode -> DEPOSIT_LEDGER_CODE_NAMES.contains(ledgerCode.getName()))
                .map(Organisation.LedgerCode::getCode)
                .filter(Objects::nonNull)
                .collect(toUnmodifiableSet());
    }

    @Override
    public void mapAccount(String organisation, String tenant, XeroAccounts.XeroAccount xeroAccount) {
        var accountId = xeroAccount.getAccountId();
        Optional.ofNullable(invoiceService.findAccountsWithOrganisationIdAndAccountId(organisation, accountId))
                .ifPresentOrElse(item -> {
                    var id = item.getId();
                    log.info("Account found - " + id);
                    invoiceService.updateAccount(item
                            .toBuilder()
                            .tenant(tenant)
                            .name(xeroAccount.getName())
                            .code(xeroAccount.getCode())
                            .type(xeroAccount.getType())
                            .build());
                }, () -> {
                    var account = Account
                            .builder()
                            .accountId(accountId)
                            .code(xeroAccount.getCode())
                            .name(xeroAccount.getName())
                            .type(xeroAccount.getType())
                            .organisation(organisation)
                            .tenant(tenant)
                            .build();
                    var id = invoiceService.createAccount(account);
                    log.info("Account created - " + id);
                });
    }

    @Override
    public void mapTransaction(String organisation, String tenant, XeroTransactions.XeroTransaction xeroTransaction, User contactUser) {
        var transactionId = xeroTransaction.getTransactionId();
        var organisationItem = organisationService.getOrganisation(organisation);
        var propertyTrackingName = findMappedTrackingCategory(organisationItem, "Property");

        var balanceTransfer = Optional.ofNullable(contactUser)
            .filter(user -> Objects.nonNull(organisationItem.getBalanceTransferContactUserId()))
            .map(user -> user.getId().equals(organisationItem.getBalanceTransferContactUserId()))
            .orElse(false)
            .booleanValue();

        var transaction = Transaction
                .builder()
                .transactionId(transactionId)
                .prePaymentId(xeroTransaction.getPrePaymentId())
                .overPaymentId(xeroTransaction.getOverPaymentId())
                .type(xeroTransaction.getType())
                .contactId(Optional.ofNullable(xeroTransaction.getContact()).map(contact -> contact.getContactID().toString()).orElse(null))
                .reconciled(xeroTransaction.isReconciled())
                .hasAttachments(xeroTransaction.isHasAttachments())
                .date(toInstant(xeroTransaction.getDate()))
                .reference(xeroTransaction.getReference())
                .currency(xeroTransaction.getCurrency())
                .currencyRate(xeroTransaction.getCurrencyRate())
                .url(xeroTransaction.getUrl())
                .status(xeroTransaction.getStatus())
                .lineAmountTypes(xeroTransaction.getLineAmountTypes())
                .subTotal(xeroTransaction.getSubTotal())
                .totalTax(xeroTransaction.getTotalTax())
                .total(xeroTransaction.getTotal())
                .updatedAt(toInstant(xeroTransaction.getUpdatedDate()))
                .organisation(organisation)
                .tenant(tenant)
                .accountId(Optional.ofNullable(xeroTransaction.getAccount()).map(XeroAccounts.XeroAccount::getAccountId).orElse(null))
                .lineItems(mapLineItems(xeroTransaction.getLineItems(), propertyTrackingName, balanceTransfer))
                .balanceTransfer(balanceTransfer)
                .batchPaymentId(Optional.ofNullable(xeroTransaction.getBatchPayment()).map(BatchPayment::getBatchPaymentId).orElse(null))
                .build();

        if (transaction.getStatus().equals("VOIDED") || transaction.getStatus().equals("DELETED")) {
            Optional.ofNullable(invoiceService.findTransactionWithOrganisationIdAndAccountId(organisation, transactionId))
                    .ifPresent(item -> {
                        invoiceService.deleteTransaction(item.getId());
                    });
            return;
        }

        Optional.ofNullable(invoiceService.findTransactionWithOrganisationIdAndAccountId(organisation, transactionId))
                .ifPresentOrElse(item -> {
                    var id = item.getId();
                    log.info("Transaction found - " + id);
                    invoiceService.updateTransaction(transaction.toBuilder().id(id).build());
                }, () -> {
                    var id = invoiceService.createTransaction(transaction);
                    log.info("Transaction created - " + id);
                });
    }

    @Override
    public User mapContact(String organisation, Contact contact) {
        var id = contact.getContactID().toString();
        var email = contact.getEmailAddress();
        var phones = contact.getPhones();
        var name = contact.getName();
        var fname = Optional.ofNullable(contact.getFirstName())
                .filter(n -> !n.isEmpty())
                .orElse("");
        var sname = Optional.ofNullable(contact.getLastName())
                .filter(n -> !n.isEmpty())
                .orElse("");

        var userBuilder = User
                .builder()
                .xeroId(id)
                .fname(fname)
                .sname(sname)
                .companyName(name)
                .type("NEW")
                .currentOrganisation(organisation);

        contact.getAddresses().forEach(addr -> {
            if (addr.getAddressType() == Address.AddressTypeEnum.POBOX) {
                userBuilder
                        .homeAddress(User.UserAddress
                                .builder()
                                .addressLine1(addr.getAddressLine1())
                                .addressLine2(addr.getAddressLine2())
                                .addressLine3(addr.getAddressLine3())
                                .city(addr.getCity())
                                .country(addr.getCountry())
                                .state(addr.getRegion())
                                .postcode(addr.getPostalCode())
                                .build());
            } else if (addr.getAddressType() == Address.AddressTypeEnum.STREET) {
                userBuilder
                        .postalAddress(User.UserAddress
                                .builder()
                                .addressLine1(addr.getAddressLine1())
                                .addressLine2(addr.getAddressLine2())
                                .addressLine3(addr.getAddressLine3())
                                .city(addr.getCity())
                                .country(addr.getCountry())
                                .state(addr.getRegion())
                                .postcode(addr.getPostalCode())
                                .build());
            }
        });

        Optional
                .ofNullable(phones)
                .ifPresent(p -> userBuilder.phones(p
                        .stream()
                        .filter(phone -> Objects.nonNull(phone.getPhoneNumber()) && (!phone.getPhoneNumber().isEmpty() || !phone.getPhoneNumber().isBlank()))
                        .map(phone -> new User.Phone(phone.getPhoneType().getValue(), phone.getPhoneNumber()))
                        .collect(toUnmodifiableList())));

        var user = userBuilder.build();

        var userWithXeroId = userService.findUserWithXeroId(id);
        if (userWithXeroId != null) {
            log.info("User with xero id exists - {}", id);
            return userWithXeroId;
        }

        if (email != null && !email.isBlank()) {
            var userWithCognitoEmail = userService.findUserWithEmail(email);
            if (userWithCognitoEmail != null) {
                log.info("User with cognito email exists - {}", email);
                userService.updateXeroDetails(userWithCognitoEmail.getId(), id);
                return userWithCognitoEmail;
            }

            var userWithRegularEmail = userService.findUserWithOptionalEmail(email);
            if (userWithRegularEmail != null) {
                log.info("User with email exists - {}", email);
                userService.updateXeroDetails(userWithRegularEmail.getId(), id);
                return userWithRegularEmail;
            }

            user.setEmails(List.of(new User.Email("WORK", email)));
        }

        var userWithCompanyName = userService.findUserWithCompanyName(organisation, name);
        if (userWithCompanyName != null) {
            log.info("User with company name exists - {}", name);
            userService.updateXeroDetails(userWithCompanyName.getId(), id);
            return userWithCompanyName;
        }

        var userId = userService.createUser(user);
        user.setId(userId);
        log.info("User created - " + userId);

        return user;
    }

    @Override
    public void mapPayment(String organisation, String tenant, XeroPayments.XeroPayment xeroPayment) {
        var paymentId = xeroPayment.getPaymentId();

        if (xeroPayment.getStatus().equals("VOIDED") || xeroPayment.getStatus().equals("DELETED")) {
            Optional.ofNullable(invoiceService.findPaymentWithOrganisationAndPaymentId(organisation, paymentId))
                    .ifPresent(item -> {
                        invoiceService.deletePayment(item.getId());
                    });
            if (Objects.nonNull(xeroPayment.getInvoice())) {
                var invoice
                        = invoiceService.findPaymentWithOrganisationAndPaymentId(organisation, xeroPayment.getInvoice().getInvoiceId());

                Optional.ofNullable(invoice).ifPresent(inv -> {
                    var deletedIds = invoiceService.deleteLandlordBillsWithOriginalInvoiceId(inv.getId());
                    deletedIds.forEach(reportService::deleteByLandlordBillId);
                });
            }
            return;
        }

        Optional.ofNullable(invoiceService.findPaymentWithOrganisationAndPaymentId(organisation, paymentId))
                .ifPresentOrElse(payment -> {
                    log.info("Payment found - " + payment);
                    invoiceService.updatePayment(payment.toBuilder().tenant(tenant).build());
                }, () -> {
                    var payment = Payment
                            .builder()
                            .organisationId(organisation)
                            .tenant(tenant)
                            .paymentId(xeroPayment.getPaymentId())
                            .batchPaymentId(xeroPayment.getBatchPaymentId())
                            .bankAmount(xeroPayment.getBankAmount())
                            .amount(xeroPayment.getAmount())
                            .accountId(xeroPayment.getAccount().getAccountId())
                            .invoiceId(xeroPayment.getInvoice().getInvoiceId())
                            .currencyRate(xeroPayment.getCurrencyRate())
                            .date(toInstant(xeroPayment.getDate()))
                            .hasAccount(xeroPayment.isHasAccount())
                            .isReconciled(xeroPayment.isReconciled())
                            .paymentType(xeroPayment.getPaymentType())
                            .reference(xeroPayment.getReference())
                            .status(xeroPayment.getStatus())
                            .build();
                    var id = invoiceService.createPayment(payment);
                    log.info("Payment created - " + id);
                });
    }

    @Override
    public void mapTransfer(String organisation, String tenant, XeroBankTransfers.XeroBankTransfer xeroTransfer) {
        var transferId = xeroTransfer.getBankTransferId();
        Optional.ofNullable(invoiceService.findTransferWithOrganisationAndTransferId(organisation, transferId))
                .ifPresentOrElse(transfer -> log.info("Transfer found - {}", transfer), () -> {
                    var transfer = Transfer
                            .builder()
                            .organisationId(organisation)
                            .tenant(tenant)
                            .transferId(transferId)
                            .fromAccount(xeroTransfer.getFromBankAccount().getAccountId())
                            .accountId(xeroTransfer.getToBankAccount().getAccountId())
                            .amount(xeroTransfer.getAmount())
                            .fromTransaction(xeroTransfer.getFromBankTransactionID())
                            .transactionId(xeroTransfer.getToBankTransactionId())
                            .date(toInstant(xeroTransfer.getDate()))
                            .build();
                    var id = invoiceService.createTransfer(transfer);
                    log.info("Transfer created - {}", id);
                });
    }

    @Override
    public void mapJournal(String organisation, String tenant, Journal xeroJournal) {
        var journalId = xeroJournal.getJournalID();
        var journal = com.rentancy.integrations.pojos.Journal
                .builder()
                .journalId(journalId.toString())
                .tenant(tenant)
                .organisation(organisation)
                .sourceId(xeroJournal.getSourceID().toString())
                .sourceType(xeroJournal.getSourceType().name())
                .reference(xeroJournal.getReference())
                .number(xeroJournal.getJournalNumber())
                .date(toInstant(xeroJournal.getJournalDate()))
                .createdDate(toInstant(xeroJournal.getCreatedDateUTC()))
                .journalLines(mapToLines(xeroJournal.getJournalLines()))
                .build();
        Optional.ofNullable(invoiceService.findJournalWithOrganisationAndJournalId(organisation, journalId))
                .ifPresentOrElse(item -> {
                    log.info("Journal found - {}", item);
                    invoiceService.updateJournal(journal.toBuilder().id(item.getId()).build());
                }, () -> {
                    var id = invoiceService.createJournal(journal);
                    log.info("Journal created - {}", id);
                });
    }

    @Override
    public void mapOverPayment(String organisation, XeroOverPayment xeroOverPayment) {
        if (Objects.equals(xeroOverPayment.getStatus(), OverPaymentStatuses.VOIDED.name())) {
            invoiceService.findOverPayment(organisation, xeroOverPayment.getOverPaymentId())
                .map(OverPayment::getId)
                .ifPresent(invoiceService::deleteOverPayment);

            log.info("Over Payment deleted {}", xeroOverPayment.getOverPaymentId());
            return;
        }

        var overPayment = OverPayment.builder()
            .id(UUID.randomUUID().toString())
            .overPaymentId(xeroOverPayment.getOverPaymentId())
            .type(xeroOverPayment.getType())
            .reference(xeroOverPayment.getReference())
            .remainingCredit(xeroOverPayment.getRemainingCredit())
            .contactId(Optional.ofNullable(xeroOverPayment.getContact()).map(contact -> contact.getContactID().toString()).orElse(null))
            .status(OverPaymentStatuses.valueOf(xeroOverPayment.getStatus()))
            .lineAmountTypes(xeroOverPayment.getLineAmountTypes())
            .subTotal(xeroOverPayment.getSubTotal())
            .totalTax(xeroOverPayment.getTotalTax())
            .total(xeroOverPayment.getTotal())
            .currency(xeroOverPayment.getCurrency())
            .organisationId(organisation)
            .date(toInstant(xeroOverPayment.getDate()))
            .build();

        invoiceService.findOverPayment(organisation, xeroOverPayment.getOverPaymentId())
            .ifPresentOrElse(item -> {
                log.info("Over Payment found - {}", item);

                invoiceService.updateOverPayment(overPayment.toBuilder().id(item.getId()).build());
            }, () -> {
                invoiceService.createOverPayment(overPayment);

                log.info("Over Payment created - {}", overPayment.getId());
            });
    }

    private List<com.rentancy.integrations.pojos.Journal.JournalLine> mapToLines(List<JournalLine> journalLines) {
        return journalLines
                .stream()
                .map(journalLine -> com.rentancy.integrations.pojos.Journal.JournalLine
                        .builder()
                        .lineId(journalLine.getJournalLineID().toString())
                        .accountId(journalLine.getAccountID().toString())
                        .accountCode(journalLine.getAccountCode())
                        .accountName(journalLine.getAccountName())
                        .accountType(journalLine.getAccountType().name())
                        .netAmount(BigDecimal.valueOf(journalLine.getNetAmount()).toString())
                        .grossAmount(BigDecimal.valueOf(journalLine.getGrossAmount()).toString())
                        .taxAmount(BigDecimal.valueOf(journalLine.getTaxAmount()).toString())
                        .taxName(journalLine.getTaxName())
                        .taxType(journalLine.getTaxType())
                        .trackingCategories(journalLine
                                .getTrackingCategories()
                                .stream()
                                .map(trackingCategory -> com.rentancy.integrations.pojos.Journal.TrackingCategory
                                        .builder()
                                        .categoryId(trackingCategory.getTrackingCategoryID().toString())
                                        .optionId(trackingCategory.getTrackingOptionID().toString())
                                        .name(trackingCategory.getName())
                                        .build())
                                .collect(toUnmodifiableList())
                        ).build())
                .collect(toUnmodifiableList());
    }


    private List<Invoice.LineItem> mapLineItems(List<XeroInvoices.XeroInvoiceLineItem> lineItems, String propertyTrackingName, boolean balanceTransfer) {
        return lineItems
                .stream()
                .map(xeroInvoiceLineItem -> Invoice.LineItem
                        .builder()
                        .lineItemId(xeroInvoiceLineItem.getLineItemId())
                        .description(xeroInvoiceLineItem.getDescription())
                        .quantity(xeroInvoiceLineItem.getQuantity())
                        .unitAmount(xeroInvoiceLineItem.getUnitAmount())
                        .itemCode(xeroInvoiceLineItem.getItemCode())
                        .accountCode(xeroInvoiceLineItem.getAccountCode())
                        .taxType(xeroInvoiceLineItem.getTaxType())
                        .taxAmount(xeroInvoiceLineItem.getTaxAmount())
                        .lineAmount(xeroInvoiceLineItem.getLineAmount())
                        .discountRate(xeroInvoiceLineItem.getDiscountRate())
                        .discountAmount(xeroInvoiceLineItem.getDiscountAmount())
                        .balanceTransfer(balanceTransfer)
                        .trackingName(xeroInvoiceLineItem.getTracking()
                                .stream()
                                .filter(tracking -> tracking.getTrackingName().equals(propertyTrackingName))
                                .findFirst()
                                .map(XeroInvoices.TrackingCategory::getTrackingOption)
                                .orElse(null))
                        .build())
                .collect(toUnmodifiableList());
    }
}
