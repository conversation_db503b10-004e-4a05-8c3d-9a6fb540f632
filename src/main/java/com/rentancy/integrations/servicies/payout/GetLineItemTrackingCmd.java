package com.rentancy.integrations.servicies.payout;

import com.rentancy.integrations.pojos.Organisation;
import com.rentancy.integrations.pojos.User;
import com.rentancy.integrations.pojos.XeroCredentials;
import com.xero.models.accounting.TrackingCategories;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class GetLineItemTrackingCmd {
    XeroCredentials xeroCredentials;
    String propertyId;
    Organisation organisation;
    User landlord;
    TrackingCategories trackingCategories;
}
