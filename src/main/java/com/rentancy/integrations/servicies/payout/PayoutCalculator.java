package com.rentancy.integrations.servicies.payout;

import lombok.Builder;
import lombok.Value;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class PayoutCalculator {

    public PayoutCalculationResponseDto calculate(PayoutCalculationRequestDto requestDto, ListLandlordBalancesGroupedByLandlordResult landlordBalances) {
        var flatLineItems = getFlatLineItems(landlordBalances);
        var includedLineItems = flatLineItems;
        if (!requestDto.getLineItems().isEmpty()) {
            includedLineItems = flatLineItems.stream().filter(item -> requestDto.getLineItems().contains(item.getInvoiceId())).collect(Collectors.toList());
        }

        var propertyIdLineItemsMap = new HashMap<String, List<LandlordBalancesLineItem>>();
        includedLineItems.forEach(item -> {
            var ppId = item.getPropertyId();
            propertyIdLineItemsMap.computeIfAbsent(ppId, (k) -> new ArrayList<>()).add(item);
        });

        var splitPerProperty = new HashMap<String, List<PayoutCalculationResponseDto.PayoutForLandlord>>();
        var flatItems = new ArrayList<>(Optional.ofNullable(requestDto.getFloatAdjustments()).orElse(List.of()));
        for (var entry : propertyIdLineItemsMap.entrySet()) {
            var requestedLineItems = entry.getValue();
            var propertyId = entry.getKey();
            var propertyTotal = calculateTotalPayout(requestedLineItems);

            // find float adjustment for this property
            var floatAdjustmentAmount = flatItems.stream().filter(i -> i.getPropertyId().equals(propertyId)).map(i -> i.getFloatAdjustment()).findFirst().orElse(BigDecimal.ZERO);

            var totalMinusFloat = propertyTotal.subtract(floatAdjustmentAmount);
            var split = calculateSplitPerLandlord(propertyId, landlordBalances, totalMinusFloat, floatAdjustmentAmount);
            splitPerProperty.put(propertyId, split);
        }

        // Property : List of Items -> Landlord : List of Items
        var landlordToPayoutListMap = new HashMap<String, List<PayoutCalculationResponseDto.PayoutForProperty>>();
        for (var entry : splitPerProperty.entrySet()) {
            var list = entry.getValue();
            for (PayoutCalculationResponseDto.PayoutForLandlord item : list) {
                landlordToPayoutListMap.computeIfAbsent(item.getLandlordId(), (k) -> new ArrayList<>()).addAll(item.getPayoutForPropertyList());
            }
        }

        var firstLandlordItem = safeGet(landlordBalances.getItems(), 0, LandlordBalancesItem.builder().unfundedItems(0).clientBalance(BigDecimal.ZERO).build());

        return PayoutCalculationResponseDto.builder()
                .payoutForLandlordList(landlordToPayoutListMap.entrySet().stream().map((a) -> new PayoutCalculationResponseDto.PayoutForLandlord(a.getKey(), a.getValue())).collect(Collectors.toList()))
                .propertyAvailableFloats(landlordBalances.getItems().stream().flatMap(l -> l.getFloatSummary().stream()).map(i -> PayoutCalculationResponseDto.PropertyAvailableFloat.builder()
                        .available(i.getAvailable())
                        .current(i.getCurrentFloat())
                        .target(i.getTargetFloat())
                        .propertyId(i.getPropertyId())
                        .build()
                ).collect(Collectors.toList()))
                .unfundedItems(firstLandlordItem.getUnfundedItems())
                .clientBalance(firstLandlordItem.getClientBalance())
                .from(requestDto.from)
                .to(requestDto.to)

                .build();
    }

    private List<LandlordBalancesLineItem> getFlatLineItems(ListLandlordBalancesGroupedByLandlordResult landlordBalances) {
        return landlordBalances.getItems().stream().flatMap(a -> {
            var incomes = Optional.ofNullable(a.getIncomes()).orElse(List.of()).stream().map(i -> i.withIncome(true));
            var expenses = Optional.ofNullable(a.getExpenses()).orElse(List.of()).stream().map(e -> e.withIncome(false));
            var broughtForward = Optional.ofNullable(a.getItemsBroughtForward()).orElse(List.of()).stream();
            return Stream.concat(Stream.concat(incomes, expenses), broughtForward);
        }).collect(Collectors.toList());
    }

    private List<PayoutCalculationResponseDto.PayoutForLandlord> calculateSplitPerLandlord(String propertyId, ListLandlordBalancesGroupedByLandlordResult landlordBalances, BigDecimal totalMinusFloat, BigDecimal floatAdjustmentAmount) {
        return landlordBalances
                .getItems()
                .stream()
                .flatMap(i -> i.getLandlordPercentages().stream().map(l -> PayoutCommandValidator.LandlordSplit.builder()
                        .landlordId(l.getLandlordId())
                        .value(calculateSplit(l.getPercentage(), totalMinusFloat))
                        .floatAdjustmentAmount(calculateSplit(l.getPercentage(), floatAdjustmentAmount)).build())
                )
                .map(value ->
                        new PayoutCalculationResponseDto.PayoutForLandlord(value.getLandlordId(), List.of(new PayoutCalculationResponseDto.PayoutForProperty(propertyId, value.getValue(), value.getFloatAdjustmentAmount())))
                )
                .collect(Collectors.toList());
    }

    private static BigDecimal calculateTotalPayout(List<LandlordBalancesLineItem> requestedLineItems) {
        var incomes = BigDecimal.ZERO;
        var expenses = BigDecimal.ZERO;
        for (LandlordBalancesLineItem landlordBalancesLineItem : requestedLineItems) {
            if (landlordBalancesLineItem.getIncome()) {
                incomes = incomes.add(landlordBalancesLineItem.getAmount().abs());
            } else {
                expenses = expenses.add(landlordBalancesLineItem.getAmount().abs());
            }
        }

        return incomes.subtract(expenses);
    }

    @Value
    @Builder
    static class LandlordSplit {
        String landlordId;
        BigDecimal value;
        BigDecimal floatAdjustmentAmount;
    }

    private BigDecimal calculateSplit(BigDecimal percentage, BigDecimal amount) {
        return percentage.setScale(2, RoundingMode.HALF_EVEN).divide(BigDecimal.valueOf(100)).multiply(amount).setScale(2, RoundingMode.DOWN);
    }

    private <T> T safeGet(List<T> list, int index, T defaultValue) {
        if (list.size() <= index) return defaultValue;
        else return list.get(index);
    }

}
