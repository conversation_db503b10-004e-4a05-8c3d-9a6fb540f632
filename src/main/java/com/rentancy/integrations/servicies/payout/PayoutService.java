package com.rentancy.integrations.servicies.payout;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.servicies.LambdaClient;
import com.rentancy.integrations.servicies.PortfolioFactory;
import com.rentancy.integrations.servicies.PortfolioService;

public class PayoutService {

    private final PayoutCommandValidator payoutCommandValidator;
    private final PayoutCalculator payoutCalculator;
    private final LandlordBalancesDownloader landlordBalancesDownloader;
    private final PortfolioService portfolioService;

    PayoutService(PayoutCommandValidator payoutCommandValidator, PayoutCalculator payoutCalculator, LandlordBalancesDownloader landlordBalancesDownloader, PortfolioService portfolioService) {
        this.payoutCommandValidator = payoutCommandValidator;
        this.payoutCalculator = payoutCalculator;
        this.landlordBalancesDownloader = landlordBalancesDownloader;
        this.portfolioService = portfolioService;
    }

    public static PayoutService create(Config config) {
        LambdaClient lambdaClient = new LambdaClient(config);
        LandlordBalancesDownloader downloader = new LandlordBalancesDownloader(lambdaClient, config);
        PayoutCommandValidator validator = new PayoutCommandValidator();
        PayoutCalculator calculator = new PayoutCalculator();
        PortfolioService portfolioService = PortfolioFactory.getPortfolioService(config);
        return new PayoutService(validator, calculator, downloader, portfolioService);
    }

    public PayoutCommandValidator.LandlordPayoutValidationResult payout(String organisationId, PayoutCommandV1 command) {
        var landlordBalances = landlordBalancesDownloader.downloadLandlordBalances(LandlordBalancesDownloader.BalancesDownloadData.builder()
                .landlordId(command.landlordId)
                .organisationId(organisationId)
                .from(command.from)
                .to(command.to)
                .build());

        var calculatedPayout = payoutCalculator.calculate(PayoutCalculationRequestDto.fromPayoutCommand(command, organisationId), landlordBalances);

        var validation = payoutCommandValidator.validate(command, landlordBalances, calculatedPayout);

        if (!validation.errors.isEmpty()) {
            return validation;
        }

        portfolioService.sendLandlordBillV3(organisationId, command.request);
        return validation;
    }

    public PayoutCalculationResponseDto calculatePayout(String organisationId, PayoutCalculationRequestDto request) {
        var landlordBalances = landlordBalancesDownloader.downloadLandlordBalances(LandlordBalancesDownloader.BalancesDownloadData.builder()
                .landlordId(request.landlordId)
                .organisationId(organisationId)
                .to(request.to)
                .from(request.from)
                .build());

        return payoutCalculator.calculate(request, landlordBalances);
    }


}
