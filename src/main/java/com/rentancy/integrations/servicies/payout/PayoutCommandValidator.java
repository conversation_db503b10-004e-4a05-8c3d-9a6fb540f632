package com.rentancy.integrations.servicies.payout;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Value;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


public class PayoutCommandValidator {

    public LandlordPayoutValidationResult validate(PayoutCommandV1 command, ListLandlordBalancesGroupedByLandlordResult landlordBalances, PayoutCalculationResponseDto calculations) {
        var landlordBillsRequestV3 = command.request;

        var propertyFloatsMap = new HashMap<String, PayoutCalculationResponseDto.PropertyAvailableFloat>();
        var landlordPropertyPayoutMap = new HashMap<String, PayoutCalculationResponseDto.PayoutForProperty>();
        calculations.getPropertyAvailableFloats().forEach(adj -> propertyFloatsMap.put(adj.getPropertyId(), adj));
        calculations.getPayoutForLandlordList().forEach(p -> p.getPayoutForPropertyList().forEach(a -> {
            landlordPropertyPayoutMap.put(a.getPropertyId() + p.getLandlordId(), a);
        }));

        List<PayoutValidationError> errors = new ArrayList<>();

        landlordBillsRequestV3.getItems().forEach(item -> {
            item.getPropertyItems().forEach(propertyItem -> {
                var availableFloat = propertyFloatsMap.get(propertyItem.getPropertyId());
                if (availableFloat == null) {
                    errors.add(new AvailableFloatNotFoundForPropertyError(item.getLandlordId(), propertyItem.getPropertyId()));
                    return;
                }
                if (propertyItem.getFloatAdjustmentAmount().signum() == -1) {
                    errors.add(new FloatAdjustmentIsNegativeError(item.getLandlordId(), propertyItem.getPropertyId(), propertyItem.getFloatAdjustmentAmount()));
                }
                if (availableFloat.getAvailable().compareTo(propertyItem.getFloatAdjustmentAmount()) < 0) {
                    errors.add(new FloatAdjustmentIsMoreThanAvailableError(item.getLandlordId(), propertyItem.getPropertyId(), propertyItem.getFloatAdjustmentAmount(), availableFloat.getAvailable()));
                }

                var calculatedAmount = landlordPropertyPayoutMap.get(propertyItem.getPropertyId() + item.getLandlordId());

                if (Objects.nonNull(calculatedAmount)) {
                    if (calculatedAmount.getPayoutAmount().compareTo(propertyItem.getAmount()) != 0) {
                        errors.add(new RequestedPayoutDifferentFromCalculatedError(item.getLandlordId(), propertyItem.getPropertyId(), calculatedAmount.getPayoutAmount(), propertyItem.getAmount()));
                    }
                }
            });
        });

        var flatLineItems = getFlatLineItems(landlordBalances).stream().map(li -> li.getInvoiceId()).collect(Collectors.toSet());
        var requestedLineItems = new HashSet<>(landlordBillsRequestV3.getLineItemIds());
        requestedLineItems.removeAll(flatLineItems);
        requestedLineItems.forEach(id -> errors.add(new LineItemNotFoundButRequestedError(id)));

        return new LandlordPayoutValidationResult(errors);
    }

    private List<LandlordBalancesLineItem> getFlatLineItems(ListLandlordBalancesGroupedByLandlordResult landlordBalances) {
        return landlordBalances.getItems().stream().flatMap(a -> {
            var incomes = Optional.ofNullable(a.getIncomes()).orElse(List.of()).stream().map(i -> i.withIncome(true));
            var expenses = Optional.ofNullable(a.getExpenses()).orElse(List.of()).stream().map(e -> e.withIncome(false));
            var broughtForward = Optional.ofNullable(a.getItemsBroughtForward()).orElse(List.of()).stream();
            return Stream.concat(Stream.concat(incomes, expenses), broughtForward);
        }).collect(Collectors.toList());
    }

    @Value
    @Builder
    static class LandlordSplit {
        String landlordId;
        BigDecimal value;
        BigDecimal floatAdjustmentAmount;
    }

    @Value
    public static class LandlordPayoutValidationResult {
        public boolean success;
        public List<PayoutValidationError> errors;

        public LandlordPayoutValidationResult(List<PayoutValidationError> errors) {
            this.success = errors.isEmpty();
            this.errors = errors;
        }
    }

    public static abstract class PayoutValidationError {
        String type;

        String getType() {
            return this.type;
        }
    }

    @EqualsAndHashCode(callSuper = true)
    @Value
    static class FloatAdjustmentIsNegativeError extends PayoutValidationError {
        String type = "FloatAdjustmentIsNegativeError";
        String landlordId;
        String propertyId;
        BigDecimal floatAdjustmentAmount;
    }

    @EqualsAndHashCode(callSuper = true)
    @Value
    static class FloatAdjustmentIsMoreThanAvailableError extends PayoutValidationError {
        String type = "FloatAdjustmentIsMoreThanAvailableError";
        String landlordId;
        String propertyId;
        BigDecimal floatAdjustmentAmount;
        BigDecimal availableAmount;
    }

    @EqualsAndHashCode(callSuper = true)
    @Value
    static class AvailableFloatNotFoundForPropertyError extends PayoutValidationError {
        String type = "AvailableFloatNotFoundForPropertyError";
        String landlordId;
        String propertyId;
    }

    @EqualsAndHashCode(callSuper = true)
    @Value
    static class RequestedPayoutDifferentFromCalculatedError extends PayoutValidationError {
        String type = "RequestedPayoutDifferentFromCalculatedError";
        String landlordId;
        String propertyId;
        BigDecimal calculatedPayout;
        BigDecimal requestedPayout;
    }

    @EqualsAndHashCode(callSuper = true)
    @Value
    static class LineItemNotFoundButRequestedError extends PayoutValidationError {
        String type = "LineItemNotFoundButWasRequestedError";
        String lineItemId;
    }

}
