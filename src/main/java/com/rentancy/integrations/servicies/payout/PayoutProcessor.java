package com.rentancy.integrations.servicies.payout;

import com.rentancy.integrations.exceptions.LedgerCodeNotFoundException;
import com.rentancy.integrations.exceptions.PropertyNotFoundException;
import com.rentancy.integrations.exceptions.UserNotFoundException;
import com.rentancy.integrations.pojos.Organisation;
import com.rentancy.integrations.pojos.Organisation.LedgerCode;
import com.rentancy.integrations.pojos.*;
import com.rentancy.integrations.pojos.User;
import com.rentancy.integrations.pojos.RentInvoiceHistory.RentHistoryType;
import com.rentancy.integrations.servicies.XeroClient;
import com.rentancy.integrations.servicies.XeroInvoiceFactory;
import com.rentancy.integrations.servicies.persistence.InvoiceService;
import com.rentancy.integrations.servicies.persistence.PropertyService;
import com.rentancy.integrations.servicies.persistence.UserService;
import com.rentancy.integrations.util.Utils;
import com.xero.models.accounting.Invoice;
import com.xero.models.accounting.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.rentancy.integrations.pojos.RentInvoiceHistory.RentHistoryType.FLOAT_ADJUSTMENT_LANDLORD_BILL;
import static com.rentancy.integrations.pojos.RentInvoiceHistory.RentHistoryType.LANDLORD_BILL;
import static com.rentancy.integrations.util.JSONUtils.wrappedToJsonString;
import static com.rentancy.integrations.util.Utils.findMappedTrackingCategory;
import static java.util.stream.Collectors.toUnmodifiableList;

@AllArgsConstructor
@Slf4j
public class PayoutProcessor {

    protected final static String DEFAULT_LANDLORD_LEDGER_CODE = "328";
    protected final static String NONE_TAX_TYPE = "NONE";

    private final XeroClient xeroClient;
    private final UserService userService;
    private final InvoiceService invoiceService;
    private final PropertyService propertyService;

    public void processPayout(ProcessLandlordPayoutCmd cmd) {
        var xeroCredentials = cmd.getXeroCredentials();
        var organisation = cmd.getOrganisation();
        var contacts = cmd.getContacts();
        var trackingCategories = cmd.getTrackingCategories();
        var landlordItem = cmd.getLandlordItem();
        var bill = cmd.getLandlordBill();

        var landlord = Optional.ofNullable(userService.findUser(landlordItem.getLandlordId(), false))
                .orElseThrow(() -> new UserNotFoundException(landlordItem.getLandlordId()));
        var balanceTransferUser = Optional.ofNullable(userService.findUser(organisation.getBalanceTransferContactUserId(), false))
                .orElseThrow(() -> new UserNotFoundException(organisation.getBalanceTransferContactUserId()));

        List<LineItem> payoutLineItems = new ArrayList<>();
        List<LineItem> floatAdjustmentLineItems = new ArrayList<>();

        landlordItem.getPropertyItems().forEach(item -> {
            var getLineItemTrackingCmd = GetLineItemTrackingCmd.builder()
                    .xeroCredentials(xeroCredentials)
                    .landlord(landlord)
                    .propertyId(item.getPropertyId())
                    .organisation(organisation)
                    .trackingCategories(trackingCategories)
                    .build();
            // TODO: tracking resolution feels like a separate responsibility and should be extracted to a separate component
            var tracking = findTracking(getLineItemTrackingCmd);

            payoutLineItems.add(createLandlordPropertyPayoutLineItem(organisation, tracking, item.getAmount()));
            Optional.ofNullable(item.getFloatAdjustmentAmount())
                    .map(amount -> createLandlordPropertyFloatAdjustmentLineItems(amount, tracking, organisation))
                    .ifPresent(floatAdjustmentLineItems::addAll);
        });

        var organisationCurrency = organisation.getCurrency();
        var landlordContact = getXeroContact(contacts, landlord);
        var payoutInvoice = XeroInvoiceFactory.createAuthorisedBill(landlordContact, organisationCurrency, payoutLineItems);
        log.info("Issuing payout invoice...: {}", wrappedToJsonString(payoutInvoice));

        var balanceTransferContact = getXeroContact(contacts, balanceTransferUser);
        var floatAdjustmentInvoice = XeroInvoiceFactory.createAuthorisedBill(balanceTransferContact, organisationCurrency, floatAdjustmentLineItems);
        log.info("Issuing float adjustment invoice...: {}", wrappedToJsonString(floatAdjustmentInvoice));
        if (payoutInvoice.getInvoiceNumber().equals(floatAdjustmentInvoice.getInvoiceNumber())) {
            throw new DuplicateInvoiceNumberGeneratedException();
        }

        var invoiceHistory = callXeroAPI(
                CreateXeroInvoicesCmd.builder()
                        .invoices(List.of(payoutInvoice, floatAdjustmentInvoice))
                        .organisationId(organisation.getId())
                        .landlordId(landlord.getId())
                        .xeroCredentials(xeroCredentials)
                        .build()
        ).stream()
                .map(it -> ProcessInvoiceResponseCmd.builder()
                        .invoice(it)
                        .xeroCredentials(xeroCredentials)
                        .organisationId(organisation.getId())
                        .landlordId(landlord.getId())
                        .type(invoiceToRentHistoryType(
                                it.getInvoiceNumber(),
                                floatAdjustmentInvoice.getInvoiceNumber(),
                                payoutInvoice.getInvoiceNumber()
                        ))
                        .bill(bill)
                        .build()
                )
                .map(this::processInvoiceResponse)
                .collect(Collectors.toList());

        invoiceService.saveInvoiceHistory(invoiceHistory);
    }

    private RentHistoryType invoiceToRentHistoryType(
            String invoiceNo,
            String floatAdjustmentInvoiceNo,
            String payoutInvoiceNo) {
        if (invoiceNo.equals(floatAdjustmentInvoiceNo)) {
            return FLOAT_ADJUSTMENT_LANDLORD_BILL;
        } else if (invoiceNo.equals(payoutInvoiceNo)) {
            return LANDLORD_BILL;
        } else {
            throw new XeroResponseMissingInvoiceException(invoiceNo);
        }
    }

    private List<Invoice> callXeroAPI(CreateXeroInvoicesCmd cmd) {
        try {
            var validInvoices = cmd.getInvoices().stream()
                    .filter(it -> !it.getLineItems().isEmpty())
                    .collect(Collectors.toList());

            return xeroClient.createInvoicesWithRetry(
                    validInvoices,
                    cmd.getXeroCredentials().getTenantId(),
                    cmd.getXeroCredentials().getAccessToken()
            );
        } catch (Exception e) {
            var errorMessage = "Xero client thrown exception: " + e.getMessage();
            log.error(errorMessage, e);
            invoiceService.saveInvoiceHistory(List.of(
                    RentInvoiceHistory.builder()
                            .againstUser(cmd.getLandlordId())
                            .type(FLOAT_ADJUSTMENT_LANDLORD_BILL)
                            .organisationId(cmd.getOrganisationId())
                            .successful(false)
                            .message(errorMessage)
                            .build(),
                    RentInvoiceHistory.builder()
                            .againstUser(cmd.getLandlordId())
                            .type(LANDLORD_BILL)
                            .organisationId(cmd.getOrganisationId())
                            .successful(false)
                            .message(errorMessage)
                            .build()
            ));

            return Collections.emptyList();
        }
    }

    private RentInvoiceHistory processInvoiceResponse(ProcessInvoiceResponseCmd cmd) {
        var invoiceId = cmd.getInvoice().getInvoiceID();
        if (invoiceId == null || cmd.getInvoice().getHasErrors()) {
            var validationErrors = collectXeroValidationErrors(cmd.getInvoice());
            log.error("Processing of invoice with no. {} resulted in an error: {}", cmd.getInvoice().getInvoiceNumber(), validationErrors);
            return RentInvoiceHistory.builder()
                    .againstUser(cmd.getLandlordId())
                    .type(cmd.getType())
                    .organisationId(cmd.getOrganisationId())
                    .successful(false)
                    .message(validationErrors)
                    .build();
        }

        if (cmd.getType() == LANDLORD_BILL) {
            // Probably it's a legacy piece of code with no effect on our process {
            var landlordBill = cmd.getBill();
            landlordBill.setInvoiceId(invoiceId.toString());
            landlordBill.setDateRaised(Instant.now().toString());
            invoiceService.updateLandlordBill(landlordBill);
            // }
        }

        return RentInvoiceHistory.builder()
                .againstUser(cmd.getLandlordId())
                .type(cmd.getType())
                .organisationId(cmd.getOrganisationId())
                .xeroId(invoiceId.toString())
                .successful(true)
                .build();
    }

    private String collectXeroValidationErrors(Invoice invoice) {
        var validationErrors = invoice.getValidationErrors().stream()
                .map(ValidationError::getMessage)
                .collect(Collectors.joining(";"));
        return "InvoiceNumber=" + invoice.getInvoiceNumber() + ";" + validationErrors;
    }

    private LineItem createLandlordPropertyPayoutLineItem(
            Organisation organisation,
            List<LineItemTracking> tracking,
            BigDecimal amount) {
        var landlordPaymentLedgerCode = Optional.of(organisation)
                .map(Organisation::getLedgerCodes)
                .map(this::getLandlordPaymentLedgerCode)
                .orElse(DEFAULT_LANDLORD_LEDGER_CODE);

        return new LineItem()
                .quantity(BigDecimal.ONE.doubleValue())
                .unitAmount(amount.movePointRight(2).intValue() * 1.0 / 100)
                .accountCode(landlordPaymentLedgerCode)
                .taxType(NONE_TAX_TYPE)
                .tracking(tracking)
                .description("Client Payment " + new SimpleDateFormat("dd MMM yyyy").format(Date.from(Instant.now())));
    }

    private List<LineItemTracking> findTracking(GetLineItemTrackingCmd cmd) {
        var trackingCategories = cmd.getTrackingCategories();
        var propertyId = cmd.getPropertyId();
        var optionName = Optional.ofNullable(propertyService.getParentProperty(propertyId))
                .map(ParentProperty::getReference)
                .orElseGet(() -> {
                    var property = Optional.ofNullable(propertyService.getProperty(propertyId))
                            .orElseThrow(() -> new PropertyNotFoundException(propertyId));
                    return property.getReference();
                });
        var propertyTrackingName = findMappedTrackingCategory(cmd.getOrganisation(), "Property");
        var contactsTrackingName = findMappedTrackingCategory(cmd.getOrganisation(), "Contacts");
        var tracking = new ArrayList<>(getTrackingItem(trackingCategories, cmd.getXeroCredentials(), propertyTrackingName, optionName));
        Optional.of(cmd.getLandlord())
                .map(Utils::formatUser)
                .ifPresent(name -> tracking.addAll(getTrackingItem(trackingCategories, cmd.getXeroCredentials(), contactsTrackingName, name)));
        return tracking;
    }

    private List<LineItem> createLandlordPropertyFloatAdjustmentLineItems(
            BigDecimal amount,
            List<LineItemTracking> tracking,
            Organisation organisation) {
        return List.of(
                new LineItem()
                        .taxType(NONE_TAX_TYPE)
                        .description("Debit Rent")
                        .quantity(1.0)
                        .unitAmount(amount.multiply(BigDecimal.valueOf(-1)).doubleValue())
                        .accountCode(findLedgerCodeByName(organisation, "Rent Income").getCode())
                        .tracking(tracking),
                new LineItem()
                        .taxType(NONE_TAX_TYPE)
                        .description("Credit Float")
                        .quantity(1.0)
                        .unitAmount(amount.doubleValue())
                        .accountCode(findLedgerCodeByName(organisation, "Float Balance").getCode())
                        .tracking(tracking)
        );
    }

    private String getLandlordPaymentLedgerCode(List<LedgerCode> ledgerCodes) {
        return getLenderCodes(
                ledgerCodes,
                ledgerCode -> ledgerCode.getName().equals("Landlord Payments")
        )
                .stream()
                .findAny()
                .orElse(DEFAULT_LANDLORD_LEDGER_CODE);
    }

    private LedgerCode findLedgerCodeByName(Organisation organisation, String name) {
        return organisation.getLedgerCodes().stream()
                .filter(ledgerCode -> name.equals(ledgerCode.getName()))
                .findAny()
                .orElseThrow(() -> new LedgerCodeNotFoundException("Could not find ledger code with name " + name));
    }

    private List<String> getLenderCodes(List<LedgerCode> ledgerCodes,
                                        Predicate<LedgerCode> predicate) {
        return ledgerCodes
                .stream()
                .filter(predicate)
                .map(LedgerCode::getCode)
                .filter(Objects::nonNull)
                .collect(toUnmodifiableList());
    }

    private Contact getXeroContact(Contacts allContacts, User user) {
        return allContacts
                .getContacts()
                .stream()
                .filter(xeroContact -> xeroContact.getContactID().toString().equals(user.getXeroId()))
                .findAny()
                .orElseThrow(() -> new IllegalStateException("Xero contact not found for " + user.getCompanyName()));
    }

    private List<LineItemTracking> getTrackingItem(TrackingCategories trackingCategories,
                                                   XeroCredentials xeroCredentials,
                                                   String categoryName,
                                                   String optionName) {
        try {
            var categoryOption = createTrackingCategoryOption(trackingCategories, xeroCredentials, optionName, categoryName);
            return List.of(new LineItemTracking()
                    .name(categoryName)
                    .option(optionName)
                    .trackingCategoryID(categoryOption.getCategoryId())
                    .trackingOptionID(categoryOption.getOptionId()));
        } catch (Exception e) {
            log.error("Failed to get Tracking item - {}", categoryName, e);
            return List.of();
        }
    }

    private TrackingCategoryOption createTrackingCategoryOption(TrackingCategories trackingCategories,
                                                                XeroCredentials xeroCredentials,
                                                                String optionName,
                                                                String categoryName) {
        var tenantId = xeroCredentials.getTenantId();
        var token = xeroCredentials.getAccessToken();

        return Optional
                .ofNullable(findTrackingCategory(trackingCategories, categoryName))
                .map(trackingCategory -> {
                    var categoryId = trackingCategory.getTrackingCategoryID();
                    var optionId = trackingCategory
                            .getOptions()
                            .stream()
                            .filter(trackingOption -> trackingOption.getName().equals(optionName))
                            .findAny()
                            .map(TrackingOption::getTrackingOptionID)
                            .orElseGet(() -> xeroClient.createTrackingCategoryOptionWithRetry(tenantId, token, categoryId.toString(), optionName));

                    return new TrackingCategoryOption(categoryId, optionId);
                }).orElseGet(() -> {
                    var result = xeroClient.createTrackingCategoryWithRetry(tenantId, token, categoryName);
                    var propertyTrackingCategory = findTrackingCategory(result, categoryName);
                    var categoryId = propertyTrackingCategory.getTrackingCategoryID();
                    var optionId = xeroClient.createTrackingCategoryOptionWithRetry(tenantId, token, categoryId.toString(), optionName);

                    return new TrackingCategoryOption(categoryId, optionId);
                });
    }

    private TrackingCategory findTrackingCategory(TrackingCategories categories, String name) {
        return categories
                .getTrackingCategories()
                .stream()
                .filter(trackingCategory -> trackingCategory.getName().equals(name))
                .findAny()
                .orElse(null);
    }
}
