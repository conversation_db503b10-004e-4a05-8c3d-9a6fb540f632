package com.rentancy.integrations.servicies.payout;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.servicies.LambdaClient;
import com.rentancy.integrations.util.JSONUtils;
import lombok.Builder;
import lombok.Value;

import java.util.List;
import java.util.Map;

import static com.rentancy.integrations.util.JSONUtils.wrappedToJsonString;

public class LandlordBalancesDownloader {

    private final LambdaClient lambdaClient;
    private final String lambdaName;

    public LandlordBalancesDownloader(LambdaClient lambdaClient, Config config) {
        this.lambdaClient = lambdaClient;
        this.lambdaName = "integrationService-" + config.getEnv();
    }

    public ListLandlordBalancesGroupedByLandlordResult downloadLandlordBalances(BalancesDownloadData data) {
        var query = "listLandlordBalancesGroupedByLandlord";

        var arguments = Map.of("organisationId", data.organisationId, "landlordId", data.landlordId, "from", data.from, "to", data.to, "limit", 10000, "page", 1);
        var result = this.lambdaClient.invoke(lambdaName,
                wrappedToJsonString(Map.of(
                                "fieldName", query,
                                "arguments", arguments,
                                "identity", Map.of("claims",
                                        Map.of("custom:organisationId", data.organisationId),
                                        "username", data.landlordId,
                                        "groups", List.of("USER")
                                )
                        )
                )
        );
        return JSONUtils.wrappedDeserializePayload(result, ListLandlordBalancesGroupedByLandlordResult.class);
    }

    @Value
    @Builder
    public static class BalancesDownloadData {
        String organisationId;
        String landlordId;
        String from;
        String to;
    }
}
