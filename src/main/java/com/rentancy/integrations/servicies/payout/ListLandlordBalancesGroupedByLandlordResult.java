package com.rentancy.integrations.servicies.payout;

import lombok.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Value
@Data
@AllArgsConstructor
@NoArgsConstructor(force = true)
@Builder
public class ListLandlordBalancesGroupedByLandlordResult {
    int total;
    List<LandlordBalancesItem> items;
}

@Value
@Data
@AllArgsConstructor
@NoArgsConstructor(force = true)
@Builder
class LandlordBalancesItem {
    String id;
    String landlordId;
    String landlordName;
    int propertyCount;
    int tenancyCount;
    int unfundedItems;
    BigDecimal clientBalance;
    @Builder.Default
    List<LandlordBalancesLandlordPercentages> landlordPercentages = List.of();
    @Builder.Default
    List<LandlordBalancesFloatSummary> floatSummary = List.of();
    @Builder.Default
    List<LandlordBalancesLineItem> incomes = List.of();
    @Builder.Default
    List<LandlordBalancesLineItem> expenses = List.of();
    @Builder.Default
    List<LandlordBalancesLineItem> itemsBroughtForward = List.of();
}

@Value
@Data
@AllArgsConstructor
@NoArgsConstructor(force = true)
@Builder
class LandlordBalancesLandlordPercentages {
    String landlordName;
    BigDecimal percentage;
    String landlordId;
}

@Value
@Data
@AllArgsConstructor
@NoArgsConstructor(force = true)
@Builder
class LandlordBalancesFloatSummary {
    String property;
    String propertyId;
    BigDecimal available;
    BigDecimal currentFloat;
    BigDecimal targetFloat;
    String __typename;
}

@Value
@Data
@AllArgsConstructor
@NoArgsConstructor(force = true)
@Builder
class LandlordBalancesLineItem {
    String id;
    String property;
    String description;
    BigDecimal amount;
    @With
    Boolean income;
    String propertyId;
    String date;
    String invoiceId;
}