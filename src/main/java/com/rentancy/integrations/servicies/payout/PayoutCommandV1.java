package com.rentancy.integrations.servicies.payout;

import com.rentancy.integrations.pojos.LandlordBillsRequestV3;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.Value;

@Value
@AllArgsConstructor
@NoArgsConstructor(force = true)
@Builder
public class PayoutCommandV1 {
    public String from;
    public String to;
    public String landlordId;
    public LandlordBillsRequestV3 request;
}
