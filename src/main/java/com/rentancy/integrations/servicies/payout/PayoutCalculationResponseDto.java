package com.rentancy.integrations.servicies.payout;

import lombok.*;

import java.math.BigDecimal;
import java.util.List;

@Value
@AllArgsConstructor
@Builder
public class PayoutCalculationResponseDto {
    BigDecimal clientBalance;
    Integer unfundedItems;
    String from;
    String to;
    @Builder.Default
    List<PayoutForLandlord> payoutForLandlordList = List.of();
    @Builder.Default
    List<PropertyAvailableFloat> propertyAvailableFloats = List.of();

    @Data
    @Value
    @AllArgsConstructor
    @Builder
    public static class PayoutForLandlord {
        public String landlordId;
        public List<PayoutForProperty> payoutForPropertyList;
    }

    @Value
    @AllArgsConstructor
    @Builder
    public static class PayoutForProperty {
        public String propertyId;
        public BigDecimal payoutAmount;
        public BigDecimal floatAdjustmentAmount;
    }

    @Value
    @AllArgsConstructor
    @Builder
    public static class PropertyAvailableFloat {
        String propertyId;
        BigDecimal available;
        BigDecimal current;
        BigDecimal target;
    }
}
