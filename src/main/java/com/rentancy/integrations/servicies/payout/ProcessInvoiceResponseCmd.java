package com.rentancy.integrations.servicies.payout;

import com.rentancy.integrations.pojos.LandlordBill;
import com.rentancy.integrations.pojos.RentInvoiceHistory.RentHistoryType;
import com.rentancy.integrations.pojos.XeroCredentials;
import com.xero.models.accounting.Invoice;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class ProcessInvoiceResponseCmd {
    String organisationId;
    String landlordId;
    Invoice invoice;
    LandlordBill bill;
    XeroCredentials xeroCredentials;
    RentHistoryType type;
}
