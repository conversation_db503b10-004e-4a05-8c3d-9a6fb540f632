package com.rentancy.integrations.servicies.payout;

import com.rentancy.integrations.pojos.LandlordBill;
import com.rentancy.integrations.pojos.LandlordBillsRequestV3.LandlordBillsRequestV3Item;
import com.rentancy.integrations.pojos.Organisation;
import com.rentancy.integrations.pojos.XeroCredentials;
import com.xero.models.accounting.Contacts;
import com.xero.models.accounting.TrackingCategories;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class ProcessLandlordPayoutCmd {
    XeroCredentials xeroCredentials;
    Organisation organisation;
    Contacts contacts;
    TrackingCategories trackingCategories;
    LandlordBillsRequestV3Item landlordItem;
    LandlordBill landlordBill;
}
