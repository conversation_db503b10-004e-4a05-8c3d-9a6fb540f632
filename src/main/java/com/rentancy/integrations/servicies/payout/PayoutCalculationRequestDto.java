package com.rentancy.integrations.servicies.payout;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PayoutCalculationRequestDto {
    public String organisationId;
    public String landlordId;
    public String from;
    public String to;
    @Builder.Default
    public List<String> lineItems = List.of();
    @Builder.Default
    public List<FloatAdjustment> floatAdjustments = List.of();

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class FloatAdjustment {
        public String propertyId;
        public BigDecimal floatAdjustment;
    }

    public static PayoutCalculationRequestDto fromPayoutCommand(PayoutCommandV1 command, String organisationId) {
        return PayoutCalculationRequestDto.builder()
                .from(command.getFrom())
                .to(command.getTo())
                .organisationId(organisationId)
                .landlordId(command.getLandlordId())
                .lineItems(command.getRequest().getLineItemIds())
                .floatAdjustments(
                        command.getRequest().getItems().stream().flatMap(
                                a -> a.getPropertyItems().stream().map(
                                        g -> new FloatAdjustment(g.getPropertyId(), g.getFloatAdjustmentAmount())
                                )
                        ).collect(Collectors.toList())
                )
                .build();
    }

}


