package com.rentancy.integrations.servicies;

import com.rentancy.integrations.pojos.Integration;
import com.rentancy.integrations.pojos.Integration.IntegrationMetadata;

import java.time.Instant;
import java.util.List;

public interface IntegrationService {
    void findOrCreateIntegration(String organisationId,
                                 String userId,
                                 String url,
                                 String state,
                                 Instant startDate,
                                 Integration.IntegrationService type);

    Integration findIntegration(String organisationId, String userId, Integration.IntegrationService type);

    Integration findConnectedIntegration(String organisationId, String userId, Integration.IntegrationService type);

    Integration findConnectedIntegrationByOrgId(String organisationId);

    List<Integration> findConnectedIntegrations(Integration.IntegrationService type);

    Integration findIntegration(String tenantId, Integration.IntegrationService type);

    Integration findIntegrationByOrganisationId(String organisationId, Integration.IntegrationService type);

    Integration findByOrganisationWithTokenRefresh(String organisationId, Integration.IntegrationService type);

    Integration findIntegrationItem(String tenantId, Integration.IntegrationService type);

    void updateIntegration(Integration integration);

    void updateIntegrationStatus(String organisationId, Integration.IntegrationStatus status, Integration.IntegrationService type);

    void createMetadata(String integrationId, IntegrationMetadata metadata);

    void updateMetadata(String integrationId, IntegrationMetadata metadata);

    void deleteIntegration(String id);

    String updateIntegrationCallXeroCounter(String tenant, String remainingCalls);
}
