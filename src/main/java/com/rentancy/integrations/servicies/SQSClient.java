package com.rentancy.integrations.servicies;

import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.AmazonSQSClientBuilder;
import com.amazonaws.services.sqs.model.GetQueueUrlResult;
import com.amazonaws.services.sqs.model.SendMessageResult;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class SQSClient {

    private static final Logger log = LogManager.getLogger(SQSClient.class);

    private final AmazonSQS sqs;

    public SQSClient() {
        this.sqs = AmazonSQSClientBuilder
                .standard()
                .build();
    }

    public void enqueue(String queueName, String payload) {
        try {
            GetQueueUrlResult queueUrl = sqs.getQueueUrl(queueName);
            SendMessageResult result = sqs.sendMessage(queueUrl.getQueueUrl(), payload);
            log.info("Message sent - " + queueName);
        } catch (Exception e) {
            log.error("Unable to send message", e);
        }
    }

    public void enqueue(String queueName, String specificRegion, String payload) {
        try {
            var customSQS = AmazonSQSClientBuilder
                    .standard()
                    .withRegion(specificRegion)
                    .build();

            var queueUrl = customSQS.getQueueUrl(queueName);
            var result = customSQS.sendMessage(queueUrl.getQueueUrl(), payload);
            log.info("Message sent - " + result.getMessageId());
        } catch (Exception e) {
            log.error("Unable to send message + " + queueName + " " + specificRegion, e);
        }
    }
}
