package com.rentancy.integrations.servicies;

import com.amazonaws.util.StringUtils;
import com.rentancy.integrations.pojos.*;
import com.rentancy.integrations.servicies.persistence.DepositManagement;
import com.rentancy.integrations.servicies.persistence.InvoiceService;
import com.rentancy.integrations.servicies.persistence.PropertyService;
import com.rentancy.integrations.servicies.persistence.UserService;
import com.rentancy.integrations.util.Utils;
import lombok.RequiredArgsConstructor;
import org.springframework.lang.Nullable;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.rentancy.integrations.util.Utils.getPrimaryTenantIds;

@RequiredArgsConstructor
public class DepositManagementServiceImpl2 implements DepositManagement {
    private final PropertyService propertyService;
    private final InvoiceService invoiceService;
    private final TenancyService tenancyService;
    private final UserService userService;

    @Override
    public DepositItemsResponse getAllTypeDeposits(DepositManagementPayload body) {
        String organisationId = body.getOrganisationId();

        // Fetch data
        List<Tenancy> tenancies = fetchTenancies(organisationId, body.getTenancyStatus());
        List<Property> properties = propertyService.findPropertiesWithOrganisation(organisationId);
        List<Invoice.LineItem> invoiceLineItems = invoiceService.findOrganisationLineItems(organisationId, null, null);

        // Process data
        List<Tenancy> filteredTenancies = filterTenancies(tenancies, body);
        Map<String, Tenancy> tenancyMap = createTenancyMap(filteredTenancies);
        Map<String, Property> propertyMap = createPropertyMap(properties);
        Map<String, User> tenantMap = createTenantMap(getPrimaryTenantIds(filteredTenancies));
        Map<String, Invoice.LineItem> invoiceLineItemsMap = createInvoiceLineItemsMap(invoiceLineItems);
        List<Invoice.LineItem> filteredInvoiceLineItems = invoiceLineItemsMap.values().stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toUnmodifiableList());

        // Group line items by deposit status
        // KEY - DepositStatus.ALL will always be empty here
        Map<DepositManagementPayload.DepositStatus, List<Invoice.LineItem>> lineItemsByDepositStatus =
                groupLineItemsByDepositStatus(filteredTenancies, filteredInvoiceLineItems);
        // insert ALL to MAP
        lineItemsByDepositStatus.put(DepositManagementPayload.DepositStatus.ALL, groupedLineItemsWithAllDepositStatus(filteredTenancies, filteredInvoiceLineItems));

        var registering = lineItemsByDepositStatus.get(DepositManagementPayload.DepositStatus.REGISTERING);
        var refunding = lineItemsByDepositStatus.get(DepositManagementPayload.DepositStatus.REFUNDING);
        var receiving = lineItemsByDepositStatus.get(DepositManagementPayload.DepositStatus.RECEIVING);

        // add negations to LIST
        lineItemsByDepositStatus.put(DepositManagementPayload.DepositStatus.NOT_RECEIVING, concatAsCopy(registering, refunding));
        lineItemsByDepositStatus.put(DepositManagementPayload.DepositStatus.NOT_REGISTERING, concatAsCopy(receiving, refunding));
        lineItemsByDepositStatus.put(DepositManagementPayload.DepositStatus.NOT_REFUNDING, concatAsCopy(registering, receiving));

        // Build response
        return buildDepositResponse(lineItemsByDepositStatus, propertyMap, tenancyMap, tenantMap, body);
    }

    private List<Invoice.LineItem> concatAsCopy(
            List<Invoice.LineItem> list1,
            List<Invoice.LineItem> list2
    ) {
        List<Invoice.LineItem> newList = new ArrayList<>();

        newList.addAll(Optional.ofNullable(list1).orElse(List.of()));
        newList.addAll(Optional.ofNullable(list2).orElse(List.of()));

        return newList;
    }

    private List<Tenancy> fetchTenancies(String organisationId, @Nullable String status) {
        // DEV NOTE: LET-301: filter tenancies by status: nullable
        return propertyService.findTenancies(organisationId, status, false).stream()
                .filter(tenancy -> Objects.nonNull(tenancy.getPrimaryTenant()))
                .collect(Collectors.toUnmodifiableList());
    }

    private List<Tenancy> filterTenancies(List<Tenancy> tenancies, DepositManagementPayload body) {
        List<Tenancy> filtered = new ArrayList<>(tenancies);

        // DEV NOTE: LET-301: filter tenancies by tenant name: nullable
        Optional.ofNullable(body.getPrimaryTenantId()).ifPresent(tenantId ->
                filtered.removeIf(tenancy -> !tenancy.getPrimaryTenant().equals(tenantId)));

        // DEV NOTE: LET-301: filter tenancies by depositFrom and depositTo: nullable
        Optional.ofNullable(body.getDepositFrom()).ifPresent(depositFrom ->
                filtered.removeIf(tenancy -> tenancy.getDeposit().compareTo(depositFrom) < 0));

        Optional.ofNullable(body.getDepositTo()).ifPresent(depositTo ->
                filtered.removeIf(tenancy -> tenancy.getDeposit().compareTo(depositTo) > 0));

        return List.copyOf(filtered);
    }

    private Map<String, Tenancy> createTenancyMap(List<Tenancy> tenancies) {
        return tenancies.stream()
                .collect(Collectors.toUnmodifiableMap(
                        Tenancy::getReference,
                        tenancy -> tenancy,
                        (t1, t2) -> t1));
    }

    private Map<String, Property> createPropertyMap(List<Property> properties) {
        return properties.stream()
                .collect(Collectors.toUnmodifiableMap(
                        Property::getId,
                        property -> property,
                        (p1, p2) -> p1));
    }

    private Map<String, User> createTenantMap(Set<String> tenantIds) {
        return userService.findUsers(tenantIds).stream()
                .collect(Collectors.toUnmodifiableMap(
                        User::getId,
                        user -> user));
    }

    private Map<String, Invoice.LineItem> createInvoiceLineItemsMap(List<Invoice.LineItem> lineItems) {
        return lineItems.stream()
                .filter(item -> Objects.nonNull(item.getInvoiceId()) &&
                        Objects.nonNull(tenancyService.getParentReference(item)))
                .collect(Collectors.groupingBy(
                        tenancyService::getParentReference,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.isEmpty() ? null : list.get(0)
                        )
                ));
    }

    private Map<DepositManagementPayload.DepositStatus, List<Invoice.LineItem>> groupLineItemsByDepositStatus(
            List<Tenancy> tenancies, List<Invoice.LineItem> invoiceLineItems) {
        Map<String, Tenancy> tenancyMap = createTenancyMap(tenancies);

        return invoiceLineItems.stream()
                .filter(item -> Objects.nonNull(item.getParentReference()))
                .filter(item -> tenancyMap.containsKey(item.getParentReference()))
                .flatMap(item -> tenancyService.getDashboardDepositStatusForTenancy(tenancyMap.get(item.getParentReference()))
                        .stream()
                        .map(status -> Map.entry(status, item)))
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.mapping(Map.Entry::getValue, Collectors.toList())));
    }

    private List<Invoice.LineItem> groupedLineItemsWithAllDepositStatus(
            List<Tenancy> tenancies, List<Invoice.LineItem> invoiceLineItems) {
        Map<String, Tenancy> tenancyMap = createTenancyMap(tenancies);

        return invoiceLineItems.stream()
                .filter(item -> Objects.nonNull(item.getParentReference()))
                .filter(item -> tenancyMap.containsKey(item.getParentReference()))
                .flatMap(item -> tenancyService.getDashboardDepositStatusForTenancy(tenancyMap.get(item.getParentReference()))
                        .stream()
                        .map(status -> Map.entry(status, item)))
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());
    }

    private DepositItemsResponse buildDepositResponse(
            Map<DepositManagementPayload.DepositStatus, List<Invoice.LineItem>> lineItemsByStatus,
            Map<String, Property> propertyMap,
            Map<String, Tenancy> tenancyMap,
            Map<String, User> tenantMap,
            DepositManagementPayload body) {
        DepositItemsResponse.DepositItemsResponseBuilder builder = DepositItemsResponse.builder();

        for (DepositManagementPayload.DepositStatus status : List.of(
                DepositManagementPayload.DepositStatus.RECEIVING,
                DepositManagementPayload.DepositStatus.NOT_RECEIVING,
                DepositManagementPayload.DepositStatus.REGISTERING,
                DepositManagementPayload.DepositStatus.NOT_REGISTERING,
                DepositManagementPayload.DepositStatus.REFUNDING,
                DepositManagementPayload.DepositStatus.NOT_REFUNDING,
                DepositManagementPayload.DepositStatus.ALL)) {
            List<DepositItemsResponse.DepositManagementBody> deposits = mapToDepositManagementBody(
                    lineItemsByStatus.getOrDefault(status, List.of()),
                    propertyMap,
                    tenancyMap,
                    tenantMap);

            applyDepositFilters(builder, status, deposits, body, tenancyMap, lineItemsByStatus);
        }

        return builder.build();
    }

    private void applyDepositFilters(
            DepositItemsResponse.DepositItemsResponseBuilder builder,
            DepositManagementPayload.DepositStatus status,
            List<DepositItemsResponse.DepositManagementBody> deposits,
            DepositManagementPayload body,
            Map<String, Tenancy> tenancyMap,
            Map<DepositManagementPayload.DepositStatus, List<Invoice.LineItem>> lineItemsByStatus) {
        if (body.getDepositStatus() != null && !status.equals(body.getDepositStatus())) {
            return;
        }

        List<DepositItemsResponse.DepositManagementBody> filtered = filterDeposits(deposits, body);

        switch (status) {
            case RECEIVING: {
                List<DepositItemsResponse.DepositManagementBody> sortedReceiving = sortByDueDate(filtered);
                builder.receivedDepositManagementItems(paginate(sortedReceiving, body.getPage(), body.getLimit()))
                        .receivedDepositManagementItemsCount(deposits.size())
                        .depositsOwed(deposits.size());
                break;
            }

            case REGISTERING: {
                builder.registeredDepositManagementItems(paginate(filtered, body.getPage(), body.getLimit()))
                        .depositsToRegister(countDepositsToRegister(deposits, tenancyMap))
                        .depositsToTransfer(countDepositsToTransfer(deposits, tenancyMap))
                        .registeredDepositManagementItemsCount(deposits.size());
                break;
            }

            case REFUNDING: {
                List<DepositItemsResponse.DepositManagementBody> sortedRefunding = sortByContractEndDate(filtered);
                builder.refundDepositManagementItems(paginate(sortedRefunding, body.getPage(), body.getLimit()))
                        .depositsToProcess(countDepositsToProcess(lineItemsByStatus, tenancyMap))
                        .refundDepositManagementItemsCount(deposits.size());
                break;
            }

            case NOT_RECEIVING: {
                List<DepositItemsResponse.DepositManagementBody> sortedRefunding = sortByContractEndDate(filtered);
                builder
                        // REGISTERING
                        .registeredDepositManagementItems(paginate(filtered, body.getPage(), body.getLimit()))
                        .depositsToRegister(countDepositsToRegister(deposits, tenancyMap))
                        .depositsToTransfer(countDepositsToTransfer(deposits, tenancyMap))
                        .registeredDepositManagementItemsCount(deposits.size())
                        // REFUNDING
                        .refundDepositManagementItems(paginate(sortedRefunding, body.getPage(), body.getLimit()))
                        .depositsToProcess(countDepositsToProcess(lineItemsByStatus, tenancyMap))
                        .refundDepositManagementItemsCount(deposits.size());
                break;
            }

            case NOT_REGISTERING: {
                List<DepositItemsResponse.DepositManagementBody> sortedReceiving = sortByDueDate(filtered);
                List<DepositItemsResponse.DepositManagementBody> sortedRefunding = sortByContractEndDate(filtered);
                builder
                        // RECEIVING
                        .receivedDepositManagementItems(paginate(sortedReceiving, body.getPage(), body.getLimit()))
                        .receivedDepositManagementItemsCount(deposits.size())
                        .depositsOwed(deposits.size())
                        // REFUNDING
                        .refundDepositManagementItems(paginate(sortedRefunding, body.getPage(), body.getLimit()))
                        .depositsToProcess(countDepositsToProcess(lineItemsByStatus, tenancyMap))
                        .refundDepositManagementItemsCount(deposits.size());
                break;
            }

            case NOT_REFUNDING: {
                List<DepositItemsResponse.DepositManagementBody> sortedReceiving = sortByDueDate(filtered);
                builder
                        // RECEIVING
                        .receivedDepositManagementItems(paginate(sortedReceiving, body.getPage(), body.getLimit()))
                        .receivedDepositManagementItemsCount(deposits.size())
                        .depositsOwed(deposits.size())
                        // REGISTERING
                        .registeredDepositManagementItems(paginate(filtered, body.getPage(), body.getLimit()))
                        .depositsToRegister(countDepositsToRegister(deposits, tenancyMap))
                        .depositsToTransfer(countDepositsToTransfer(deposits, tenancyMap))
                        .registeredDepositManagementItemsCount(deposits.size());
                break;
            }

            case ALL: {
                List<DepositItemsResponse.DepositManagementBody> sortedReceiving = sortByDueDate(filtered);
                List<DepositItemsResponse.DepositManagementBody> sortedRefunding = sortByContractEndDate(filtered);
                builder
                        // RECEIVING
                        .receivedDepositManagementItems(paginate(sortedReceiving, body.getPage(), body.getLimit()))
                        .receivedDepositManagementItemsCount(deposits.size())
                        .depositsOwed(deposits.size())
                        // REGISTERING
                        .registeredDepositManagementItems(paginate(filtered, body.getPage(), body.getLimit()))
                        .depositsToRegister(countDepositsToRegister(deposits, tenancyMap))
                        .depositsToTransfer(countDepositsToTransfer(deposits, tenancyMap))
                        .registeredDepositManagementItemsCount(deposits.size())
                        // REFUNDING
                        .refundDepositManagementItems(paginate(sortedRefunding, body.getPage(), body.getLimit()))
                        .depositsToProcess(countDepositsToProcess(lineItemsByStatus, tenancyMap))
                        .refundDepositManagementItemsCount(deposits.size());
                break;
            }

            default:
                throw new IllegalStateException("Unknown deposit status: " + status);
        }
    }

    private List<DepositItemsResponse.DepositManagementBody> filterDeposits(
            List<DepositItemsResponse.DepositManagementBody> deposits,
            DepositManagementPayload body) {
        return deposits.stream()
                .filter(item -> Objects.isNull(body.getPropertyReference()) ||
                        body.getPropertyReference().equals(item.getPropertyReference()))
                .filter(item -> Objects.isNull(body.getContractReference()) ||
                        body.getContractReference().equals(item.getContractReference()))
                .filter(item -> Objects.isNull(body.getDueDate()) ||
                        body.getDueDate().equals(item.getDueDate()))
                .filter(item -> Objects.isNull(body.getRegistered()) ||
                        body.getRegistered() == item.isRegistered())
                .filter(item -> Objects.isNull(body.getTransferred()) ||
                        body.getTransferred() == item.isTransferred())
                .filter(item -> Objects.isNull(body.getContractEndDate()) ||
                        body.getContractEndDate().equals(item.getContractEndDate()))
                .filter(item -> Objects.isNull(body.getReleased()) ||
                        body.getReleased() == item.isReleased())
                .collect(Collectors.toUnmodifiableList());
    }

    private List<DepositItemsResponse.DepositManagementBody> sortByDueDate(
            List<DepositItemsResponse.DepositManagementBody> deposits) {
        return deposits.stream()
                .sorted(Comparator.comparing(DepositItemsResponse.DepositManagementBody::getDueDateInstant))
                .collect(Collectors.toUnmodifiableList());
    }

    private List<DepositItemsResponse.DepositManagementBody> sortByContractEndDate(
            List<DepositItemsResponse.DepositManagementBody> deposits) {
        return deposits.stream()
                .sorted(Comparator.comparing(DepositItemsResponse.DepositManagementBody::getContractEndDate,
                        Comparator.reverseOrder()))
                .collect(Collectors.toUnmodifiableList());
    }

    private List<DepositItemsResponse.DepositManagementBody> paginate(
            List<DepositItemsResponse.DepositManagementBody> deposits, int page, int limit) {
        int startIndex = Math.min(limit * page, deposits.size());
        int endIndex = Math.min(limit * (page + 1), deposits.size());
        return deposits.subList(startIndex, endIndex);
    }

    private long countDepositsToRegister(
            List<DepositItemsResponse.DepositManagementBody> items,
            Map<String, Tenancy> tenancyMap) {
        return items.stream()
                .filter(item -> !tenancyMap.get(item.getTenancyReference()).isDepositRegistered())
                .count();
    }

    private long countDepositsToTransfer(
            List<DepositItemsResponse.DepositManagementBody> items,
            Map<String, Tenancy> tenancyMap) {
        return items.stream()
                .filter(item -> !tenancyMap.get(item.getTenancyReference()).isDepositTransferred())
                .count();
    }

    private long countDepositsToProcess(
            Map<DepositManagementPayload.DepositStatus, List<Invoice.LineItem>> lineItemsByStatus,
            Map<String, Tenancy> tenancyMap) {
        return lineItemsByStatus.values().stream()
                .flatMap(List::stream)
                .filter(item -> {
                    Tenancy tenancy = tenancyMap.get(item.getParentReference());
                    return !tenancy.isDepositReturned() &&
                            tenancy.isDepositTransferred() &&
                            tenancy.isDepositRegistered() &&
                            isContractEndedWithin14Days(tenancy.getEndDate());
                })
                .count();
    }

    private boolean isContractEndedWithin14Days(String endDate) {
        if (endDate == null) return false;
        Instant contractEnd = Instant.parse(endDate);
        Instant now = Instant.now();
        Instant fourteenDaysLater = now.plus(Duration.ofDays(14));
        return contractEnd.isBefore(fourteenDaysLater);
    }

    private List<DepositItemsResponse.DepositManagementBody> mapToDepositManagementBody(
            List<Invoice.LineItem> lineItems,
            Map<String, Property> propertyMap,
            Map<String, Tenancy> tenancyMap,
            Map<String, User> tenantMap) {
        return lineItems.stream()
                .filter(item -> {
                    Tenancy tenancy = tenancyMap.get(item.getParentReference());
                    return tenancy != null && propertyMap.containsKey(tenancy.getProperty());
                })
                .map(item -> createDepositManagementBody(item, propertyMap, tenancyMap, tenantMap))
                .collect(Collectors.toUnmodifiableList());
    }

    private DepositItemsResponse.DepositManagementBody createDepositManagementBody(
            Invoice.LineItem lineItem,
            Map<String, Property> propertyMap,
            Map<String, Tenancy> tenancyMap,
            Map<String, User> tenantMap) {
        Tenancy tenancy = tenancyMap.get(lineItem.getParentReference());
        Property property = propertyMap.get(tenancy.getProperty());
        User user = tenantMap.get(tenancy.getPrimaryTenant());
        String dueDate = Optional.of(lineItem)
                .map(Invoice.LineItem::getParentDueDate)
                .map(String::valueOf)
                .orElse(null);

        String tenantEmail = Optional.ofNullable(user.getCognitoEmail())
                .orElse(user.getEmails().stream()
                        .findAny()
                        .map(User.Email::getEmail)
                        .orElse(null));

        return DepositItemsResponse.DepositManagementBody.builder()
                .tenancyId(tenancy.getId())
                .propertyReference(property.getReference())
                .propertyDisplayAddress(getDisplayAddress(property))
                .tenancyProtectionScheme(tenancy.getDepositProtectionScheme())
                .tenancyStatus(tenancy.getStatus())
                .tenancyStartDate(tenancy.getStartDate())
                .contractReference(tenancy.getReference())
                .tenantName(tenancy.getPrimaryTenantName())
                .tenantEmail(tenantEmail)
                .tenantId(user.getId())
                .propertyId(property.getId())
                .lineItemId(lineItem.getId())
                .inDispute(tenancy.isDepositDisputed())
                .depositValue(new BigDecimal(tenancy.getDeposit()).movePointLeft(2).toString())
                .transferred(tenancy.isDepositTransferred())
                .released(tenancy.isDepositReleased())
                .tenancyReference(tenancy.getReference())
                .dateRegistered(tenancy.getDateDepositRegistered())
                .registered(tenancy.isDepositRegistered())
                .dueDate(Utils.formatDate(dueDate))
                .dueDateInstant(Utils.convertDateToInstant(dueDate))
                .depositReturned(tenancy.isDepositReturned())
                .contractEndDate(Utils.formatDate(tenancy.getEndDate()))
                .contractEndDateInstant(Utils.convertDateToInstant(tenancy.getEndDate()))
                .build();
    }

    private static String getDisplayAddress(Property property) {
        return Stream.of(property.getAddressLine1(), property.getAddressLine2(), property.getAddressLine3(), property.getCity(), property.getPostcode()).filter(StringUtils::hasValue).collect(Collectors.joining(", "));
    }
}
