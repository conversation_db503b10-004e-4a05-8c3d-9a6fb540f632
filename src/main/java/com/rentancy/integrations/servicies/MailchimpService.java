package com.rentancy.integrations.servicies;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.MailChimpMember;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicHeader;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * integrations
 *
 * <AUTHOR>
 * @since 28/03/2021
 */
public class MailchimpService {

    private static final Logger log = LogManager.getLogger(MailchimpService.class);

    private final Config config;
    private final HttpClient client;
    private final ObjectMapper mapper;

    public MailchimpService(Config config, HttpClient client) {
        this.config = config;
        this.client = client;
        this.mapper = new ObjectMapper();
    }

    public boolean createOrUpdateMember(String listId, MailChimpMember member) {
        final HttpPut put;
        try {
            put = new HttpPut(buildMemberURL(listId, member.getEmailAddress()).toString());
            put.setHeader(new BasicHeader("Authorization", String.format("Bearer %s", config.getMailChimpApiKey())));
            put.setHeader(new BasicHeader("Accept", "application/json"));
            put.setHeader(new BasicHeader("Content-Type", "application/json"));
            put.setEntity(new StringEntity(mapper.writeValueAsString(member), StandardCharsets.UTF_8));
            final HttpResponse response = client.execute(put);
            return response.getStatusLine().getStatusCode() == HttpStatus.SC_OK;
        } catch (NoSuchAlgorithmException e) {
            log.error("Cannot update MailChimp due to a missing MD5 algorithm. Where is it? This is bad!", e);
            return false;
        } catch (MalformedURLException e) {
            log.error("Cannot update MailChimp. The constructed URL is invalid. This is a " +
                    "fundamental problem and should be fixed ASAP", e);
            return false;
        } catch (ClientProtocolException e) {
            log.error("There was a problem creating the HTTP client", e);
            return false;
        } catch (IOException e) {
            log.error("There was a problem reading or writing the request to MailChimp", e);
            return false;
        }
    }

    private URL buildMemberURL(String listId, String email) throws NoSuchAlgorithmException, MalformedURLException {
        String subscriberHash = generatemd5Digest(email);
        String path = String.format("%s/lists/%s/members/%s", config.getMailChimpBaseUri().toURL().getPath(), listId, subscriberHash);
        return new URL(
                config.getMailChimpBaseUri().toURL().getProtocol(),
                config.getMailChimpBaseUri().toURL().getHost(),
                config.getMailChimpBaseUri().toURL().getPort(),
                path, null);
    }

    private String generatemd5Digest(String str) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        md.update(str.toLowerCase().getBytes(StandardCharsets.UTF_8));
        byte[] byteData = md.digest();
        StringBuilder sb = new StringBuilder();
        for (byte byteDatum : byteData) sb.append(Integer.toString((byteDatum & 0xff) + 0x100, 16).substring(1));
        return sb.toString();
    }
}
