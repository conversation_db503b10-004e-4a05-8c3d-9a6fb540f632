package com.rentancy.integrations.servicies;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.exceptions.GenerateClientSummaryException;
import com.rentancy.integrations.pojos.*;
import com.rentancy.integrations.servicies.persistence.*;
import com.rentancy.integrations.servicies.persistence.DocumentService;
import com.rentancy.integrations.servicies.rent.NextInvoiceDateCalculator;
import com.rentancy.integrations.util.JSONUtils;
import com.rentancy.integrations.util.PDFUtils;
import com.rentancy.integrations.util.Utils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.util.StringUtils;

import javax.annotation.Nullable;
import javax.imageio.ImageIO;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.rentancy.integrations.servicies.TenancyService.*;
import static com.rentancy.integrations.util.JSONUtils.*;
import static com.rentancy.integrations.util.Utils.*;
import static java.lang.String.join;
import static java.time.ZoneOffset.UTC;
import static java.time.temporal.ChronoUnit.DAYS;
import static java.time.temporal.ChronoUnit.SECONDS;
import static java.util.Collections.emptyList;
import static java.util.stream.Collectors.*;


@RequiredArgsConstructor
public class ReportServiceImpl implements ReportService {

    private static final Logger log = LogManager.getLogger(ReportServiceImpl.class);

    private static final String DEFAULT_VALUE = "";
    private static final String DELIMITER = ", ";
    private static final String YPP_STATEMENT_VERSION = "YPP_STATEMENT_VERSION";

    private final Config config;
    private final PropertyService propertyService;
    private final OrganisationService organisationService;
    private final InvoiceService invoiceService;
    private final UserService userService;
    private final DocumentService documentService;
    private final BoardService boardService;
    private final JasperServerClient jasperClient;
    private final TenancyService tenancyService;
    private final SQSClient sqsClient;
    private final S3Client s3Client;
    private final AppSyncServiceProvider appSyncServiceProvider;
    private final ReportRepository reportRepository;
    private final DataExporterService dataExporterService;
    private final NextInvoiceDateCalculator nextInvoiceDateCalculator = new NextInvoiceDateCalculator();


    static long getCurrentTime() {
        return System.currentTimeMillis();
    }

    @Override
    public byte[] generateLandlordReportPdf(String propertyId, String startDate, String endDate, String format, @Nullable StatementType statementType) {
        var report = generateLandlordReport(propertyId, startDate, endDate);
        var bucket = config.getRentancyDocumentUploads();
        var updatedReport = report.toBuilder()
                .tenancies(report.getTenancies().stream().filter(tenancy -> !tenancy.getInvoices().isEmpty()).collect(toUnmodifiableList()))
                .build();

        var parameters = toLandlordPdfData(updatedReport, statementType);

        log.info(JSONUtils.wrappedToJsonString(parameters));
        var property = propertyService.getProperty(propertyId);

        byte[] dataForUpload;

        if (report.getLandlordStatementTemplateType() == null) {
            dataForUpload = jasperClient.generateLandlordReport(parameters, format);
        } else if (report.getLandlordStatementTemplateType().equals("landlordStatementTemplate")) {
            dataForUpload = jasperClient.generateLandlordTemplateReport(parameters, format);
        } else if (report.getLandlordStatementTemplateType().equals("landlordStatementWithoutFirstColumns")) {
            dataForUpload = jasperClient.generateLandlordTemplateWithoutFirstColumnsReport(parameters, format);
        } else {
            throw new RuntimeException("Unknown landlord statement template type " + report.getLandlordStatementTemplateType());
        }

        var clientName = Optional.ofNullable(report.getLandlordName()).orElse("unknown client");
        var folderName = s3Client.uploadPdf(bucket, dataForUpload);
        var title = String.join(" ", "Property Statement", property.getAddressLine1(), clientName);
        sendReportMessage(property.getOrganisation(), folderName, startDate, endDate, title);
        return dataForUpload;
    }

    private Stream<Invoice.LineItem> getLineItemsByTrackingNameAndLedgerCode(List<Invoice.LineItem> items, String trackingName, List<String> ledgerCodes) {
        return items.stream()
                .filter(lineItem -> Objects.nonNull(lineItem.getTrackingName()) && Objects.nonNull(lineItem.getAccountCode()))
                .filter(lineItem -> lineItem.getTrackingName().equals(trackingName) && (ledgerCodes == null || ledgerCodes.contains(lineItem.getAccountCode())));
    }

    private BigDecimal getLandlordItemsTotal(List<LandlordReportItem> items,
                                             Function<LandlordReportItem, BigDecimal> func) {
        return items
                .stream()
                .map(func)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * Landlord statements
     * Opening balance - property.getOpeningBalance()
     * Closing balance - Opening balance + income - all expenses
     * Float - property.getMinimumBalance()
     * Client payable - Closing balance - float
     */
    @Override
    public LandlordReport generateLandlordReport(String propertyId, String startDate, String endDate) {
        var property = propertyService.getProperty(propertyId);
        var organisation = organisationService.getOrganisation(property.getOrganisation());
        var tenancies = propertyService.findPropertyTenancies(propertyId, false);
        log.info("Found tenancies {}, Organisation {}", tenancies, organisation);

        var previousStartDate = "1990-01-01T00:00:00Z";
        var previousEndDate = Instant.parse(startDate).minus(1, SECONDS).minus(1, DAYS).toString();
        var now = Instant.now();
        var today = now.atOffset(UTC)
                .with(LocalTime.of(23, 59, 59, now.getNano()))
                .toInstant();
        var ledgerCodes = organisation.getLedgerCodes();
        var depositLedgerCodes = getDepositLedgerCodes(ledgerCodes);
        var lineItems = invoiceService.findOrganisationLineItems(property.getOrganisation(), startDate, endDate)
                .stream().filter(lineItem -> !InvoiceStatus.DRAFT.name().equals(lineItem.getParentStatus())).collect(toUnmodifiableList());
        var previousLineItems = invoiceService.findOrganisationLineItems(property.getOrganisation(), previousStartDate, previousEndDate)
                .stream().filter(lineItem -> !InvoiceStatus.DRAFT.name().equals(lineItem.getParentStatus())).collect(toUnmodifiableList());
        var reference = property.getReference();
        var tenancySummary = tenancyService.calculatePropertyTenanciesSummary(organisation, property, tenancies, lineItems, today);
        var previousTenancySummary = tenancyService.calculatePropertyTenanciesSummary(organisation, property, tenancies, previousLineItems, today);

        var expenseLedgerCodeNames = tenancyService.getBillLedgerCodeNames(organisation);
        var expenses = tenancyService.calculateAllExpenses(lineItems, reference, depositLedgerCodes);
        var previousExpenses = tenancyService.calculateAllExpenses(previousLineItems, reference, depositLedgerCodes);
        var paidLineItems = tenancySummary.getTenancyPaidLineItems();
        // We want to display old unpaid line items in arrears as well
        var inArrearsItems = Stream.of(previousTenancySummary, tenancySummary)
                .map(TenancySummary::getInArrearsLineItems)
                .map(Map::values)
                .flatMap(Collection::stream)
                .flatMap(Collection::stream)
                .filter(lineItem -> Objects.nonNull(lineItem.getInvoiceId()))
                .map(this::getNotPaidAmountLandlordReportItem)
                .collect(toUnmodifiableList());
        var expenseLineItems = expenses.getLineItems();
        var paidIncome = tenancySummary.getPaidIncome();
        var previousPaidIncome = previousTenancySummary.getPaidIncome();
        var openingBalance = previousPaidIncome.add(property.getOpeningBalance()).subtract(previousExpenses.getTotalExpenses());
        var minimumBalance = property.getMinimumBalance();
        var closingBalance = paidIncome.add(openingBalance).subtract(expenses.getTotalExpenses());
        var clientPayable = closingBalance.subtract(minimumBalance);
        var tenancyReport = paidLineItems.keySet().stream().map(tenancyReference -> {
            var paidLineItemsTenancy = paidLineItems.get(tenancyReference);
            var tenancy = tenancies
                    .stream()
                    .filter(tenancy1 -> tenancy1.getReference().equals(tenancyReference))
                    .findAny()
                    .orElseThrow();
            return LandlordReportTenancy
                    .builder()
                    .reference(tenancyReference)
                    .title(tenancy.getTitle())
                    .endDate(tenancy.getEndDate())
                    .invoices(paidLineItemsTenancy
                            .stream()
                            .filter(lineItem -> tenancyService.getLineItemPaidAmount(lineItem).compareTo(BigDecimal.ZERO) != 0)
                            .map(this::getPaidAmountLandlordReportItem)
                            .collect(toUnmodifiableList()))
                    .build();
        }).collect(toUnmodifiableList());
        var accountExpenses = expenseLineItems
                .stream()
                .collect(groupingBy(com.rentancy.integrations.pojos.Invoice.LineItem::getAccountCode));
        var expensesReportUnsorted = accountExpenses.keySet()
                .stream()
                .map(account -> {
                    var bills = accountExpenses.get(account)
                            .stream()
                            .map(this::toLandlordReportItem)
                            .collect(toUnmodifiableList());
                    return LandlordReportExpenses.builder()
                            .account(getLedgerName(ledgerCodes, account))
                            .bills(bills)
                            .build();
                }).collect(toList());

        var landlordPaymentExpenses = expensesReportUnsorted
                .stream()
                .filter(item -> "Landlord Payments".equals(item.getAccount()))
                .collect(toList());
        var expensesReport = expensesReportUnsorted
                .stream()
                .filter(item -> !"Landlord Payments".equals(item.getAccount()))
                .collect(toList());
        expensesReport.addAll(landlordPaymentExpenses);

        var rentalIncomeInvoices = tenancyReport.stream()
                .map(LandlordReportTenancy::getInvoices)
                .flatMap(Collection::stream)
                .collect(toUnmodifiableList());
        var managementLedgerCodes = getLedgerCodes(ledgerCodes, ledgerCode -> ledgerCode.getName().equals("Management Fees"));
        var managementInvoices = getLineItemsByTrackingNameAndLedgerCode(lineItems, property.getReference(), managementLedgerCodes)
                .map(this::toLandlordReportItem)
                .collect(toUnmodifiableList());

        var allExpendatureInvoices = getLineItemsByTrackingNameAndLedgerCode(lineItems, property.getReference(), null)
                .filter(lineItem -> !lineItem.isIncome())
                .filter(lineItem -> lineItem.getAccountCode() == null || !managementLedgerCodes.contains(lineItem.getAccountCode()))
                .map(this::toLandlordReportItem)
                .collect(toUnmodifiableList());

        var propertyFloats = BigDecimal.ZERO.equals(property.getMinimumBalance()) ? List.<LandlordReportItem>of() : List.of(LandlordReportItem.builder()
                .reference(property.getReference())
                .from(Optional.ofNullable(property.getLandlords()).flatMap(t -> t.stream().findFirst()).orElse(null))
                .description(DEFAULT_VALUE)
                .creationDate(DEFAULT_VALUE)
                .status(DEFAULT_VALUE)
                .accountCode(DEFAULT_VALUE)
                .subTotal(property.getMinimumBalance())
                .vat(BigDecimal.ZERO)
                .total(property.getMinimumBalance())
                .build());

        var landlord = Optional.ofNullable(property.getPrimaryLandlordId())
                .map(id -> userService.findUser(id, true))
                .orElseGet(() -> {
                    return Optional
                            .ofNullable(property.getLandlords())
                            .map(List::stream)
                            .flatMap(Stream::findFirst)
                            .map(id -> userService.findUser(id, true))
                            .orElse(null);
                });
        var landlordName = formatUser(landlord);
        var landlordAddress = Optional
                .ofNullable(landlord)
                .map(user -> Optional.ofNullable(user.getPostalAddress()).orElse(user.getHomeAddress()))
                .map(this::getUserAddressDescription)
                .orElse(DEFAULT_VALUE);
        var address = getPropertyDescription(property);
        var invoiceSum = tenancyReport
                .stream().flatMap(landlordReportTenancy -> landlordReportTenancy.getInvoices().stream())
                .map(LandlordReportItem::getSubTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        var invoiceVatSum = tenancyReport
                .stream().flatMap(landlordReportTenancy -> landlordReportTenancy.getInvoices().stream())
                .map(LandlordReportItem::getVat)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        var invoiceTotalSum = tenancyReport
                .stream().flatMap(landlordReportTenancy -> landlordReportTenancy.getInvoices().stream())
                .map(LandlordReportItem::getTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        var billSum = expensesReport
                .stream().flatMap(landlordReportExpenses -> landlordReportExpenses.getBills().stream())
                .map(LandlordReportItem::getSubTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        var billVatSum = expensesReport
                .stream().flatMap(landlordReportExpenses -> landlordReportExpenses.getBills().stream())
                .map(LandlordReportItem::getVat)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        var billTotalSum = expensesReport
                .stream().flatMap(landlordReportTenancy -> landlordReportTenancy.getBills().stream())
                .map(LandlordReportItem::getTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        var periodSubTotal = invoiceSum.subtract(billSum);
        var periodVat = invoiceVatSum.subtract(billVatSum);
        var periodTotal = invoiceTotalSum.subtract(billTotalSum);

        var dateFormatter = new SimpleDateFormat("dd/MM/yyyy");

        var totalIncomeSubtotal = getLandlordItemsTotal(rentalIncomeInvoices, LandlordReportItem::getSubTotal);
        var totalIncomeVat = getLandlordItemsTotal(rentalIncomeInvoices, LandlordReportItem::getVat);
        var totalIncomeTotal = getLandlordItemsTotal(rentalIncomeInvoices, LandlordReportItem::getTotal);

        var totalExpenditureSubtotal = getLandlordItemsTotal(managementInvoices, LandlordReportItem::getSubTotal).add(getLandlordItemsTotal(allExpendatureInvoices, LandlordReportItem::getSubTotal));
        var totalExpenditureVat = getLandlordItemsTotal(managementInvoices, LandlordReportItem::getVat).add(getLandlordItemsTotal(allExpendatureInvoices, LandlordReportItem::getVat));
        var totalExpenditureTotal = getLandlordItemsTotal(managementInvoices, LandlordReportItem::getTotal).add(getLandlordItemsTotal(allExpendatureInvoices, LandlordReportItem::getTotal));

        var subTotalSubTotal = totalIncomeSubtotal.subtract(totalExpenditureSubtotal);
        var subTotalVat = totalIncomeVat.subtract(totalExpenditureVat);
        var subTotalTotal = totalIncomeTotal.subtract(totalExpenditureTotal);

        var totalRetentionSubtotal = getLandlordItemsTotal(propertyFloats, LandlordReportItem::getSubTotal);
        var totalRetentionVat = getLandlordItemsTotal(propertyFloats, LandlordReportItem::getVat);
        var totalRetentionTotal = getLandlordItemsTotal(propertyFloats, LandlordReportItem::getTotal);

        var statementCount = this.findOrganisationStatements(organisation.getId()).size();

        return LandlordReport
                .builder()
                .subTotalSubTotal(subTotalSubTotal)
                .landlordStatementTemplateType(organisation.getLandlordStatementTemplateType())
                .subTotalVat(subTotalVat)
                .subTotalTotal(subTotalTotal)
                .totalIncomeSubtotal(totalIncomeSubtotal)
                .totalIncomeVat(totalIncomeVat)
                .totalIncomeTotal(totalIncomeTotal)
                .totalExpenditureSubtotal(totalExpenditureSubtotal)
                .totalExpenditureVat(totalExpenditureVat)
                .totalExpenditureTotal(totalExpenditureTotal)
                .totalRetentionSubtotal(totalRetentionSubtotal)
                .totalRetentionVat(totalRetentionVat)
                .totalRetentionTotal(totalRetentionTotal)
                .organisationVat(organisation.getCommissionBillVatNumber())
                .landlordBankName(landlord.getBankName())
                .reportNumber(join("-", "S", String.valueOf(statementCount + 1)))
                .landlordAccountName(landlord.getBeneficiaryName())
                .landlordAccountNumber(landlord.getBankAccountNumber())
                .landlordSortCode(landlord.getBankAccountSortCode())
                .date(dateFormatter.format(Date.from(Instant.now())))
                .landlordName(landlordName)
                .landlordAddress(landlordAddress)
                .propertyAddress(Optional.ofNullable(address).orElse(DEFAULT_VALUE))
                .allExpendatureInvoices(allExpendatureInvoices)
                .propertyAddress1(Optional.ofNullable(property.getAddressLine1()).orElse(DEFAULT_VALUE))
                .propertyAddress2(Optional.ofNullable(property.getAddressLine2()).orElse(DEFAULT_VALUE))
                .propertyAddress3(Optional.ofNullable(property.getAddressLine3()).orElse(DEFAULT_VALUE))
                .managementInvoices(managementInvoices)
                .postCode(Optional.ofNullable(property.getPostcode()).orElse(DEFAULT_VALUE))
                .organisationName(organisation.getName())
                .logoUrl(organisation.getLogo())
                .startDate(startDate)
                .endDate(endDate)
                .property(property)
                .propertyFloats(propertyFloats)
                .openingBalance(openingBalance)
                .closingBalance(closingBalance)
                .minimumBalance(minimumBalance)
                .clientPayable(clientPayable)
                .rentalIncomeInvoices(rentalIncomeInvoices)
                .tenancies(tenancyReport)
                .inArrears(inArrearsItems)
                .propertyExpenses(expensesReport)
                .periodTotal(periodTotal)
                .periodVat(periodVat)
                .periodSubtotal(periodSubTotal)
                .build();
    }

    @Override
    public void generateClientGeneralReport(ClientStatementCommand command) {
        var bucket = config.getRentancyDocumentUploads();
        var startMili = Instant.now().toEpochMilli();
        var startDate = command.getStartDate();
        var endDate = command.getEndDate();
        var organisationId = command.getOrganisationId();
        var userId = command.getUserId();
        var format = command.getFormat();
        var type = command.getType();
        var cognitoId = command.getCognitoId();
        var emailUserId = command.getSendEmailToUserId();
        var now = Instant.now();

        var organisation = organisationService.getOrganisation(organisationId);
        var properties = propertyService.findPropertiesWithOrganisation(organisationId);
        var landlordProperties = properties.stream()
                .filter(property -> property.getLandlords() != null)
                .filter(property -> property.getLandlords().contains(userId))
                .collect(toUnmodifiableList());
        var map = calculateClientStatement(organisationId, userId, startDate, endDate, now, organisation, landlordProperties);
        var reportData = new ArrayList<byte[]>();
        var data = jasperClient.generateClientStatement(map, format);
        reportData.add(data);

        reportData.addAll(landlordProperties.stream()
                .sorted(Comparator.comparing(Property::getReference))
                .map(property -> generateLandlordReportPdf(property.getId(), startDate, endDate, format, null))
                .collect(toUnmodifiableList())
        );

        log.info("General report generated - {}", reportData.size());

        var endMili = Instant.now().toEpochMilli();

        log.info("Time to generate report - {}", (endMili - startMili));

        var content = PDFUtils.merge(reportData);
        var folderName = s3Client.uploadPdf(bucket, data);
        var title = String.join(" ", "Client Statement", formatUser(userService.findUser(userId, false)));
        sendReportMessage(organisationId, folderName, startDate, endDate, title);

        if (type != null && StatementType.FINALISED == StatementType.valueOf(type)) {
            saveStatement(cognitoId, organisationId, startDate, endDate, "CLIENT", content, userId, null, null, false);
        }

        processReport(organisationId, emailUserId, content, now, map, "Client Statement Report", "CLIENT_STATEMENT", null);
    }

    @Override
    public byte[] generateDocumentReportPdf(String organisationId, String startDate, String endDate, String format, String sortBy) {
        var bucket = config.getRentancyDocumentUploads();
        var organisation = organisationService.getOrganisation(organisationId);
        var allDocuments = documentService.findOrganisationDocuments(organisationId, startDate, endDate);
        var propertyIds = allDocuments.stream().map(Document::getPropertyId).filter(Objects::nonNull).collect(toSet());
        var tenancyIds = allDocuments.stream().map(Document::getTenancyId).filter(Objects::nonNull).collect(toUnmodifiableSet());

        var tenancies = tenancyIds.stream().map(id -> propertyService.getTenancy(id, false)).collect(toUnmodifiableMap(Tenancy::getId, Function.identity()));

        tenancies.forEach((id, tenancy) -> {
            propertyIds.add(tenancy.getProperty());
        });

        var properties = propertyIds.stream().map(propertyService::getProperty).collect(toUnmodifiableMap(Property::getId, Function.identity()));

        var data = new HashMap<String, Object>();
        var documents = new HashMap<String, List<Map<String, String>>>();

        allDocuments
                .stream()
                .filter(document -> document.getPropertyId() != null || document.getTenancyId() != null)
                .forEach(document -> {
                    var tenancy = Optional.ofNullable(document.getTenancyId()).map(tenancies::get)
                            .orElse(null);
                    var property = Optional.ofNullable(document.getPropertyId()).map(properties::get)
                            .orElseGet(() -> properties.get(tenancy.getProperty()));

                    var key = generateDocumentKey(document, property, sortBy);

                    var documentBucket = documents.getOrDefault(key, new ArrayList<Map<String, String>>());
                    documentBucket.add(serializeDocument(document, property, tenancy));
                    documents.put(key, documentBucket);
                });

        var documentKeySet = documents.keySet().stream().sorted().collect(toUnmodifiableList());
        var documentsWrapper = new ArrayList<List<Map<String, String>>>();

        documentKeySet.forEach(key -> {
            documentsWrapper.add(documents.get(key));
        });

        data.put("documents", documentsWrapper);
        data.put("organisationName", organisation.getName());
        data.put("startDate", startDate);
        data.put("endDate", endDate);
        data.put("orderBy", sortBy);
        var dateFormatter = new SimpleDateFormat("dd MMM yyyy");
        data.put("reportDate", dateFormatter.format(Date.from(Instant.now())));


        var dataForUpload = jasperClient.generateDocumentReport(data, format);

        var folderName = s3Client.uploadPdf(bucket, dataForUpload);
        var title = "Document Report " + organisation.getName();
        sendReportMessage(organisationId, folderName, startDate, endDate, title);
        return dataForUpload;


    }

    @Override
    public byte[] generateTaskReportPdf(String organisationId, String startDate, String endDate, String format, String sortBy, @Nullable String userId) {
        var bucket = config.getRentancyDocumentUploads();
        var organisation = organisationService.getOrganisation(organisationId);
        var tasks = boardService.findOrganisationTasks(organisationId, startDate, endDate, userId);
        var propertyIds = tasks.stream().filter(task -> task.getParentType().equals("PROPERTY")).map(Task::getParentId).filter(Objects::nonNull).collect(toSet());
        var tenancyIds = tasks.stream().filter(task -> task.getParentType().equals("TENANCY")).map(Task::getParentId).filter(Objects::nonNull).collect(toUnmodifiableSet());
        var tenancies = tenancyIds.stream().map(id -> propertyService.getTenancy(id, false)).collect(toUnmodifiableMap(Tenancy::getId, Function.identity()));

        tenancies.forEach((id, tenancy) -> {
            propertyIds.add(tenancy.getProperty());
        });

        var properties = propertyIds.stream().map(propertyService::getProperty).collect(toUnmodifiableMap(Property::getId, Function.identity()));
        var propertyLandlord = properties.entrySet().stream()
                .flatMap(entry -> {
                    if (entry.getValue().getLandlords().isEmpty()) {
                        return Stream.of();
                    }
                    var landlord = Optional.ofNullable(entry.getValue().getPrimaryLandlordId())
                            .orElse(entry.getValue().getLandlords().get(0));
                    var userLandlord = userService.findUser(landlord, false);
                    return Stream.of(Map.entry(entry.getKey(), formatUser(userLandlord)));
                }).collect(toUnmodifiableMap(Map.Entry::getKey, Map.Entry::getValue));


        var columnIds = tasks.stream().map(Task::getColumnId).collect(toUnmodifiableSet());
        var columns = columnIds.stream().map(boardService::findColumn).collect(toUnmodifiableMap(BoardColumn::getId, Function.identity()));

        var tasksBucketMap = new HashMap<String, List<Map<String, String>>>();

        tasks.forEach(task -> {
            Tenancy tenancy = null;
            Property property = null;
            var column = columns.get(task.getColumnId());

            if (task.getParentType().equals("TENANCY")) {
                tenancy = tenancies.get(task.getParentId());
                property = properties.get(tenancy.getProperty());
            } else {
                property = properties.get(task.getParentId());
            }
            var landlordName = propertyLandlord.get(property.getId());

            var key = generateTaskKey(task, property, column, sortBy, landlordName);

            var taskBucket = tasksBucketMap.getOrDefault(key, new ArrayList<Map<String, String>>());
            taskBucket.add(serializeTask(task, column, property, tenancy, landlordName));
            tasksBucketMap.put(key, taskBucket);
        });

        var taskKeySet = tasksBucketMap.keySet().stream().sorted().collect(toUnmodifiableList());
        var tasksWrapper = new ArrayList<List<Map<String, String>>>();

        taskKeySet.forEach(key -> tasksWrapper.add(tasksBucketMap.get(key)));

        var data = new HashMap<String, Object>();
        data.put("documents", tasksWrapper);
        data.put("organisationName", organisation.getName());
        data.put("startDate", startDate);
        data.put("endDate", endDate);
        data.put("orderBy", sortBy);

        var dateFormatter = new SimpleDateFormat("dd MMM yyyy");
        data.put("reportDate", dateFormatter.format(Date.from(Instant.now())));

        if (userId != null) {
            var filterUser = userService.findUser(userId, false);
            var formattedUser = formatUser(filterUser) + filterUser.getEmails().stream().findFirst().map(email -> " " + email.getEmail()).orElse("");
            data.put("filterUser", formattedUser);
        }

        var dataForUpload = jasperClient.generateTaskReport(data, format);
        var folderName = s3Client.uploadPdf(bucket, dataForUpload);
        var title = "Taskt Report " + organisation.getName();
        sendReportMessage(organisationId, folderName, startDate, endDate, title);
        return dataForUpload;
    }

    public void sendReportMessage(Organisation organisation, String fileKey, String fileName) {
        var map = new HashMap<String, String>();
        map.put("fileName", fileName);
        map.put("fileKey", fileKey);
        var payload = wrappedToJsonString(map);
        appSyncServiceProvider.createMessage(organisation.getId(), organisation.getReportConversation(), organisation.getBotUser(), payload, "CONVERSATION_PDF");
    }

    public void sendReportMessage(String organisationId, String fileKey, String fileName) {
        var organisation = organisationService.getOrganisation(organisationId);
        sendReportMessage(organisation, fileKey, fileName);
    }

    public void sendReportMessage(String organisationId, String fileKey, String startDate, String endDate, String title) {
        var dateFormatter = new SimpleDateFormat("dd MMM yyyy");
        var fileName = title + " " + dateFormatter.format(Date.from(Instant.parse(startDate))) + " to " + dateFormatter.format(Date.from(Instant.parse(endDate)));
        var map = new HashMap<String, String>();
        map.put("fileName", fileName);
        map.put("fileKey", fileKey);
        var payload = wrappedToJsonString(map);
        var organisation = organisationService.getOrganisation(organisationId);
        appSyncServiceProvider.createMessage(organisationId, organisation.getReportConversation(), organisation.getBotUser(), payload, "CONVERSATION_PDF");
    }

    @Override
    public void generatePropertyBalanceReport(PropertyReportCommand command) {
        var startDate = "1990-01-01T00:00:00Z";
        var endDate = Instant.now().toString();
        var bucket = config.getRentancyDocumentUploads();
        var organisationId = command.getOrganisationId();
        var lineItems = invoiceService.findOrganisationLineItems(organisationId, startDate, endDate);
        var organisation = organisationService.getOrganisation(organisationId);
        var properties = propertyService.findPropertiesWithOrganisation(organisationId);
        var ledgerCodes = organisation.getLedgerCodes();
        var depositLedgerCodes = getDepositLedgerCodes(ledgerCodes);

        var now = Instant.now();
        var today = now.atOffset(UTC)
                .with(LocalTime.of(23, 59, 59, now.getNano()))
                .toInstant();

        var propertyLines = properties.stream()
                .map(property -> {
                    var data = new HashMap<String, Object>();

                    var relatedTenancies = propertyService.findPropertyTenancies(property.getId(), true);
                    var landlord = Optional.ofNullable(property.getPrimaryLandlordId())
                            .map(id -> userService.findUser(id, false))
                            .map(Utils::formatUser)
                            .orElseGet(() -> {
                                return Optional.ofNullable(property.getLandlords()).orElse(List.of())
                                        .stream()
                                        .findFirst()
                                        .map(id -> userService.findUser(id, false))
                                        .map(Utils::formatUser)
                                        .orElse("");
                            });
                    var tenancySummary = tenancyService.calculatePropertyTenanciesSummary(organisation, property, relatedTenancies, lineItems, today);
                    var previousTenancySummary = tenancyService.calculatePropertyTenanciesSummary(organisation, property, relatedTenancies, List.of(), today);
                    var expenses = tenancyService.calculateAllExpenses(lineItems, property.getReference(), depositLedgerCodes);
                    var previousExpenses = tenancyService.calculateAllExpenses(List.of(), property.getReference(), depositLedgerCodes);

                    var paidIncome = tenancySummary.getPaidIncome();
                    var previousPaidIncome = previousTenancySummary.getPaidIncome();
                    var openingBalance = previousPaidIncome.add(property.getOpeningBalance()).subtract(previousExpenses.getTotalExpenses());
                    var minimumBalance = property.getMinimumBalance();
                    var closingBalance = paidIncome.add(openingBalance).subtract(expenses.getTotalExpenses());
                    var clientPayable = closingBalance.subtract(minimumBalance);
                    var propertySummary = tenancyService.calculatePropertySummary(organisation, property, relatedTenancies, lineItems, List.of(), now);

                    data.put("reference", property.getReference());
                    data.put("landlord", landlord);
                    data.put("property", property.getAddressLine1());
                    data.put("balance", formatValue(closingBalance));
                    data.put("balanceN", closingBalance);
                    data.put("clientPayanble", formatValue(clientPayable));
                    data.put("clientPayanbleN", clientPayable);
                    data.put("inArrears", formatValue(propertySummary.getInArrears()));
                    data.put("inArrearsN", propertySummary.getInArrears());
                    data.put("unpaidBills", formatValue(propertySummary.getBillsOutstanding()));
                    data.put("unpaidBillsN", propertySummary.getBillsOutstanding());
                    data.put("float", formatValue(propertySummary.getMinimumBalance()));
                    data.put("floatN", propertySummary.getMinimumBalance());
                    data.put("deposit", formatValue(propertySummary.getPaidDeposit()));
                    data.put("depositN", propertySummary.getPaidDeposit());

                    return data;
                }).sorted(Comparator.comparing(t -> ((String) t.get("landlord")))).collect(toUnmodifiableList());
        var map = new HashMap<String, Object>();
        map.put("totalBalance", propertyLines.stream().map(m -> (BigDecimal) m.get("balanceN")).reduce(BigDecimal.ZERO, BigDecimal::add));
        map.put("totalDueToClient", propertyLines.stream().map(m -> (BigDecimal) m.get("clientPayanbleN")).reduce(BigDecimal.ZERO, BigDecimal::add));
        map.put("totalInArrearsa", propertyLines.stream().map(m -> (BigDecimal) m.get("inArrearsN")).reduce(BigDecimal.ZERO, BigDecimal::add));
        map.put("totalUnpaidBills", propertyLines.stream().map(m -> (BigDecimal) m.get("unpaidBillsN")).reduce(BigDecimal.ZERO, BigDecimal::add));
        map.put("totalFloat", propertyLines.stream().map(m -> (BigDecimal) m.get("floatN")).reduce(BigDecimal.ZERO, BigDecimal::add));
        map.put("totalDeposit", propertyLines.stream().map(m -> (BigDecimal) m.get("depositN")).reduce(BigDecimal.ZERO, BigDecimal::add));

        map.put("data", propertyLines);
        map.put("reportDate", formatDate(Instant.now().toString()));
        map.put("workspaceLogo", getLogo(organisation.getLogo()));
        map.put("workspaceName", organisation.getName());
        map.put("reportPeriod", "Period: " + formatDate(startDate) + " - " + formatDate(endDate));

        var data = jasperClient.generatePropertyBalanceReport(map, command.getFormat());

        var emailQueue = config.getEmailNotificationQueue();
        var queueRegion = config.getEmailNotificationRegion();

        var uploadBucket = config.getEmailAttachmentBucket();
        var key = s3Client.uploadPdf(uploadBucket, data);

        var date = new SimpleDateFormat("dd MMM yyyy").format(Date.from(now));
        var attachmentName = organisation.getName() + "-Property Balance Summary-" + date + ".pdf";

        var user = userService.findUser(command.getSenderId(), false);
        var email = user.getEmails().stream().findFirst().map(User.Email::getEmail).orElseThrow();
        var attachment = new EmailSenderPayload.EmailAttachment(uploadBucket, key, attachmentName, true);
        var subject = "Property Summary Balance Report";
        var emailPayload = EmailSenderPayload.builder()
                .organisationId(organisationId)
                .email(email)
                .type("PROPERTY_BALANCE_REPORT")
                .subject(subject)
                .attachments(List.of(attachment))
                .emails(List.of())
                .simple(false)
                .build();

        var folderName = s3Client.uploadPdf(bucket, data);
        var title = "Property Statement " + propertyLines.get(0).get("property");
        sendReportMessage(organisationId, folderName, startDate, endDate, title);
        sqsClient.enqueue(emailQueue, queueRegion, JSONUtils.wrappedToJsonString(emailPayload));
    }

    @Override
    public void generateClientBalanceReport(PropertyReportCommand command) {
        var bucket = config.getRentancyDocumentUploads();
        var startDate = "1990-01-01T00:00:00Z";
        var endDate = Instant.now().toString();
        var organisationId = command.getOrganisationId();
        var lineItems = invoiceService.findOrganisationLineItems(organisationId, startDate, endDate);
        var organisation = organisationService.getOrganisation(organisationId);
        // There is a potential bug here
        // We are retrieving only active contracts here, but maybe we should periodic ones as well? this WILL affect finance calculations
        // Is this a bug? A feature? Who knows ¯\_(ツ)_/¯
        // Take a look at NON_ACTIVE_TENANCY_STATUSES
        var tenancies = propertyService.findTenancies(organisationId, "ACTIVE", true);
        var properties = propertyService.findPropertiesWithOrganisation(organisationId);
        var ledgerCodes = organisation.getLedgerCodes();
        var depositLedgerCodes = getDepositLedgerCodes(ledgerCodes);

        var now = Instant.now();

        var landlordGroupedProperties = properties.stream()
                .filter(property -> property.getLandlords() != null && !property.getLandlords().isEmpty())
                .collect(groupingBy(t -> Optional.ofNullable(t.getPrimaryLandlordId())
                        .orElse(t.getLandlords().get(0))));

        var items = landlordGroupedProperties.entrySet()
                .stream()
                .map(entry -> {
                    var landlord = entry.getKey();
                    var landlordProperties = entry.getValue();

                    var propertySummary = PropertySummary.builder()
                            .balance(BigDecimal.ZERO)
                            .clientPayable(BigDecimal.ZERO)
                            .inArrears(BigDecimal.ZERO)
                            .billsOutstanding(BigDecimal.ZERO)
                            .minimumBalance(BigDecimal.ZERO)
                            .paidDeposit(BigDecimal.ZERO)
                            .build();

                    var summary = landlordProperties
                            .stream()
                            .map(property -> {
                                var currentTenancies = tenancies.stream().filter(tenancy -> tenancy.getProperty().equals(property.getId())).collect(toUnmodifiableList());
                                return tenancyService.calculatePropertySummary(organisation, property, currentTenancies, lineItems, List.of(), now);
                            }).reduce(propertySummary, this::sum);

                    var map = new HashMap<String, Object>();
                    var user = userService.findUser(landlord, false);
                    map.put("landlordName", formatUser(user));
                    map.put("propertyCount", landlordProperties.size());
                    map.put("balance", formatValue(summary.getBalance()));
                    map.put("balanceN", summary.getBalance());
                    map.put("dueToClient", formatValue(summary.getClientPayable()));
                    map.put("dueToClientN", summary.getClientPayable());
                    map.put("inArrears", formatValue(summary.getInArrears()));
                    map.put("inArrearsN", summary.getInArrears());
                    map.put("unpaidBills", formatValue(summary.getBillsOutstanding()));
                    map.put("unpaidBillsN", summary.getBillsOutstanding());
                    map.put("float", formatValue(summary.getMinimumBalance()));
                    map.put("floatN", summary.getMinimumBalance());
                    map.put("deposit", formatValue(summary.getPaidDeposit()));
                    map.put("depositN", summary.getPaidDeposit());
                    return map;
                }).sorted(Comparator.comparing(t -> ((String) t.get("landlordName")))).collect(toUnmodifiableList());

        var map = new HashMap<String, Object>();
        map.put("totalBalance", items.stream().map(m -> (BigDecimal) m.get("balanceN")).reduce(BigDecimal.ZERO, BigDecimal::add));
        map.put("dueToClient", items.stream().map(m -> (BigDecimal) m.get("dueToClientN")).reduce(BigDecimal.ZERO, BigDecimal::add));
        map.put("totalInArrears", items.stream().map(m -> (BigDecimal) m.get("inArrearsN")).reduce(BigDecimal.ZERO, BigDecimal::add));
        map.put("totalUnpaidBills", items.stream().map(m -> (BigDecimal) m.get("unpaidBillsN")).reduce(BigDecimal.ZERO, BigDecimal::add));
        map.put("totalFloat", items.stream().map(m -> (BigDecimal) m.get("floatN")).reduce(BigDecimal.ZERO, BigDecimal::add));
        map.put("totalDeposit", items.stream().map(m -> (BigDecimal) m.get("depositN")).reduce(BigDecimal.ZERO, BigDecimal::add));

        map.put("data", items);
        map.put("reportDate", formatDate(Instant.now().toString()));
        map.put("workspaceLogo", getLogo(organisation.getLogo()));
        map.put("workspaceName", organisation.getName());
        map.put("reportPeriod", "Period: " + formatDate(startDate) + " - " + formatDate(endDate));

        var data = jasperClient.generateClientBalanceReport(map, command.getFormat());
        var folderName = s3Client.uploadPdf(bucket, data);

        var title = "Client Balance " + organisation.getName();
        sendReportMessage(organisationId, folderName, startDate, endDate, title);
        processReport(organisationId, command.getSenderId(), data, now, map, "Client Balance Summary Report", "CLIENT_BALANCE_SUMMARY_REPORT", null);
    }

    @Override
    public void generateClientStatementReport(ClientStatementCommand command) {
        var bucket = config.getRentancyDocumentUploads();
        var startDate = command.getStartDate();
        var endDate = command.getEndDate();
        var organisationId = command.getOrganisationId();
        var userId = command.getUserId();
        var now = Instant.now();

        var organisation = organisationService.getOrganisation(organisationId);
        var properties = propertyService.findPropertiesWithOrganisation(organisationId);
        var landlordProperties = properties.stream()
                .filter(property -> property.getLandlords() != null)
                .filter(property -> property.getLandlords().contains(userId))
                .collect(toUnmodifiableList());
        var map = calculateClientStatement(organisationId, userId, startDate, endDate, now, organisation, landlordProperties);
        var data = jasperClient.generateClientStatement(map, command.getFormat());
        var folderName = s3Client.uploadPdf(bucket, data);
        var title = "CLient Satement " + formatUser(userService.findUser(userId, false));
        sendReportMessage(organisationId, folderName, startDate, endDate, title);
        processReport(organisationId, command.getSendEmailToUserId(), data, now, map, "Client Statement Report", "CLIENT_STATEMENT", null);
    }

    @Override
    public byte[] generateTenantStatementReport(String organisationId, String tenantId, String startDate, String endDate, String format) {
        var tenantUser = userService.findUser(tenantId, true);
        var organisation = organisationService.getOrganisation(organisationId);
        var organisationInvoices = invoiceService.findOrganisationInvoicesWithingDateRange(organisationId, startDate, endDate, true);
        var tenancies = propertyService.findTenancies(organisationId, null, false);

        var map = calculateTenantStatement(tenancies, organisationInvoices, tenantUser, organisation, startDate, endDate);

        log.info("Data - {}", wrappedToJsonString(map));

        return jasperClient.generateTenantStatement(map, format);
    }

    private void processReport(String organisationId, String recieverId, byte[] report, Instant now,
    Map<String, Object> parameters, String reportName,
    String reportType, String attachmentName, String emailContent) {

        var emailQueue = config.getEmailNotificationQueue();
        var queueRegion = config.getEmailNotificationRegion();
        var uploadBucket = config.getEmailAttachmentBucket();

        CompletableFuture<String> keyFuture = CompletableFuture.supplyAsync(() -> s3Client.uploadPdf(uploadBucket, report));
        CompletableFuture<User> userFuture = CompletableFuture.supplyAsync(() -> userService.findUser(recieverId, false));

        CompletableFuture.allOf(keyFuture, userFuture).join();
        var key = keyFuture.join();
        var user = userFuture.join();

        var email = Optional.ofNullable(user.getCognitoEmail())
        .or(() -> user.getEmails().stream().map(User.Email::getEmail).findFirst())
        .orElseThrow(() -> new IllegalStateException("Receiver email not found - " + recieverId));

        Supplier<EmailSenderPayload.EmailAttachment> attachmentSupplier =
        () -> new EmailSenderPayload.EmailAttachment(uploadBucket, key, attachmentName, true);

        var emailPayload = EmailSenderPayload.builder()
        .organisationId(organisationId)
        .email(email)
        .type(reportType)
        .subject(reportName)
        .body(emailContent)
        .attachments(List.of(attachmentSupplier.get()))
        .emails(List.of())
        .simple(false)
        .build();

        log.info("Report send via email {}", emailPayload);

        var jsonPayload = wrappedToJsonString(emailPayload);
        sqsClient.enqueue(emailQueue, queueRegion, jsonPayload);
    }


    public void processReportCaller(String organisationId, String recieverId, byte[] report, Instant now, Map<String, Object> parameters, String reportName, String reportType, String attachmentName, String emailContent) {
        processReport(organisationId, recieverId, report, now, parameters, reportName, reportType, attachmentName, emailContent);
    }

    private void processReport(String organisationId, String recieverId, byte[] report, Instant now, Map<String, Object> parameters, String reportName, String reportType, String emailContent) {
        var date = new SimpleDateFormat("dd MMM yyyy").format(Date.from(now));
        var attachmentName = parameters.getOrDefault("client", "") + "-" + date + ".pdf";

        processReport(organisationId, recieverId, report, now, parameters, reportName, reportType, attachmentName, emailContent);
    }
    private String getYearlyRentAmount(Tenancy tenancy) {
        BigDecimal amount;
        var rent = BigDecimal.valueOf(tenancy.getRent());
        switch (tenancy.getPeriod()) {
            case DAILY:
                amount = rent.multiply(BigDecimal.valueOf(tenancyService.getDaysAmountForCurrentYear()));
                break;
            case WEEKLY:
                amount = BigDecimal.valueOf(365).divide(BigDecimal.valueOf(7), RoundingMode.HALF_UP).multiply(rent);
                break;
            case TWO_WEEKLY:
                amount = BigDecimal.valueOf(365).divide(BigDecimal.valueOf(14), RoundingMode.HALF_UP).multiply(rent);
                break;
            case MONTHLY:
                amount = rent.multiply(BigDecimal.valueOf(12));
                break;
            case QUARTERLY:
                amount = rent.multiply(BigDecimal.valueOf(4));
                break;
            case UK_QUARTERLY:
                amount = rent.multiply(BigDecimal.valueOf(4));
                break;
            case SIX_MONTHLY:
                amount = rent.multiply(BigDecimal.valueOf(2));
                break;
            case ANNUALLY:
                amount = rent;
                break;
            case BI_ANNUALLY:
                amount = rent.divide(BigDecimal.valueOf(2), RoundingMode.HALF_UP);
                break;
            case FIVE_YEAR:
                amount = rent.divide(BigDecimal.valueOf(5), RoundingMode.HALF_UP);
                break;
            case TEN_YEAR:
                amount = rent.divide(BigDecimal.valueOf(10), RoundingMode.HALF_UP);
                break;
            case FIFTEEN_YEAR:
                amount = rent.divide(BigDecimal.valueOf(15), RoundingMode.HALF_UP);
                break;
            case TWENTY_YEAR:
                amount = rent.divide(BigDecimal.valueOf(20), RoundingMode.HALF_UP);
                break;
            case TWENTY_FIVE_YEAR:
                amount = rent.divide(BigDecimal.valueOf(25), RoundingMode.HALF_UP);
                break;
            default:
                throw new IllegalArgumentException("Unknown tenancy period " + tenancy.getPeriod());
        }

        return formatValue(amount.movePointLeft(2));
    }

    private String getPeriodReportFormat(Tenancy tenancy) {
        if (Objects.isNull(tenancy.getPeriod())) {
            return DEFAULT_VALUE;
        }

        if (tenancy.getPeriod() == Tenancy.TenancyPeriod.MONTHLY) {
            return "PCM";
        }
        return toCamelCase(tenancy.getPeriod().name());
    }

    private static String toCamelCase(String str) {
        var result = str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();

        return result.replaceAll("_", " ");
    }

    private String convertDate(String date) {
        var formatter = new SimpleDateFormat("dd MMM yyyy");

        var day = Instant.parse(date).plus(DAYS.getDuration().dividedBy(2));

        return formatter.format(Date.from(day));
    }

    private Map<String, String> serializeTenancyScheduleData(Tenancy tenancy, Organisation organisation) {
        var map = new HashMap<String, String>();
        var formatter = new SimpleDateFormat("dd MMM yyyy");
        var now = Instant.now().atZone(UTC);

        User tenant = null;

        if (tenancy.getTenants() != null && tenancy.getTenants().size() > 0) {
            tenant = userService.findUser(tenancy.getTenants().get(0), false);
        }
        var currency = Optional.ofNullable(tenancy.getCurrency()).orElse(organisation.getCurrency());
        var currencySymbol = Currency.getInstance(currency).getSymbol();
        map.put("currencySymbol", currencySymbol);
        var tenancyType = Optional.ofNullable(tenancy.getType()).map(Enum::name).orElse("");
        map.put("contractType", tenancyType);
        map.put("contractReference", tenancy.getReference());
        map.put("tenantName", formatUser(tenant));
        map.put("contractTitle", tenancy.getTitle());
        map.put("contractStatus", toCamelCase(tenancy.getStatus()));

        if (tenancy.getPeriod() != null && tenancy.getRent() != null) {
            map.put("rent", getYearlyRentAmount(tenancy));
        }
        map.put("VAT", Boolean.TRUE.equals(tenancy.getLandlordVat()) ? "Y" : "N");
        map.put("period", tenancy.getPeriod() == null ? "" : toCamelCase(tenancy.getPeriod().name()));

        if (tenancy.getRent() != null) {
            map.put("amountPsf", formatValue(BigDecimal.valueOf(tenancy.getRent())
                    .movePointLeft(2)));
        }

        map.put("dueDate", "" + tenancy.getPaymentDay());
        var nextInvoiceDate = nextInvoiceDateCalculator.calculate(now, tenancy);
        map.put("nextInvoiceDate", nextInvoiceDate != null ? formatter.format(nextInvoiceDate.getDue()) : "");

        if (tenancy.getDeposit() != null) {
            map.put("depositAmount", formatValue(BigDecimal.valueOf(tenancy.getDeposit())
                    .movePointLeft(2)));
        }
        map.put("area", formatValue(new BigDecimal(tenancy.getArea())));

        var contractStartDate = Optional.ofNullable(tenancy.getStartDate())
                .map(this::convertDate)
                .orElse(DEFAULT_VALUE);
        var contractEndDate = Optional.ofNullable(tenancy.getEndDate())
                .map(this::convertDate)
                .orElse(DEFAULT_VALUE);
        map.put("contractStartDate", contractStartDate);
        map.put("contractEndDate", contractEndDate);

        if (tenancy.getRentReview() != null) {
            map.put("contractReview", formatter.format(Date.from(Instant.parse(tenancy.getRentReview()))));
        }

        var breakItems = Optional.ofNullable(tenancy.getBreakClauseItems()).orElse(List.of());
        if (breakItems.size() > 0) {
            var who = breakItems.get(0).getWho().equals("LANDLORD") ? "L" : "T";
            map.put("breakItemDate1", who + " " + formatter.format(Date.from(Instant.parse(breakItems.get(0).getNoticeDate()))));
        }
        if (breakItems.size() > 1) {
            var who = breakItems.get(1).getWho().equals("LANDLORD") ? "L" : "T";

            map.put("breakItemDate2", who + " " + formatter.format(Date.from(Instant.parse(breakItems.get(1).getNoticeDate()))));
        }

        var tags = Optional.ofNullable(tenancy.getTags()).orElse(List.of()).stream().collect(joining(", "));
        map.put("tags", tags);

        return map;
    }

    /**
     * Initially I thought that this method wasn't fast enough that is why tenancy settings
     * are queried in batch. Later I realized that I had bug and it was infinity loop...
     */
    @Override
    public void sendTenancySchedule(TenancyScheduleCreationCommand command) {
        var bucket = config.getRentancyDocumentUploads();
        var organisationId = command.getOrganisationId();
        var allProperties = propertyService.findPropertiesWithOrganisation(organisationId);

        var groupedByLandlord = allProperties.stream()
                .filter(property -> Objects.nonNull(property.getLandlords()) && !property.getLandlords().isEmpty())
                .collect(groupingBy(property -> Optional.ofNullable(property.getPrimaryLandlordId()).orElse(property.getLandlords().get(0))));

        var organisation = organisationService.getOrganisation(organisationId);

        var allData = groupedByLandlord.keySet().stream()
                .map(landlord -> {
                    var properties = groupedByLandlord.get(landlord);

                    var propertyData = properties.stream()
                            .map(property -> {
                                var data = propertyService.findPropertyTenancies(property.getId(), true)
                                        .stream()
                                        .map(tenancy -> serializeTenancyScheduleData(tenancy, organisation))
                                        .collect(toUnmodifiableList());
                                var list = new ArrayList<List<Map<String, String>>>();
                                list.addAll(data.stream().collect(groupingBy(t -> t.get("contractType")))
                                        .values());

                                return new TenancySchedulePropertyWrapper.TenancySchedule(property.getAddressLine1(), property.getReference(), list);
                            })
                            .collect(toUnmodifiableList());

                    var formattedUser = formatUser(userService.findUser(landlord, false));
                    return new TenancySchedulePropertyWrapper(formattedUser, propertyData);
                }).collect(toUnmodifiableList());

        var dataWrapper = Map.of("data", allData, "organisationName", organisation.getName());

        var data = jasperClient.generateTenancySchedule(dataWrapper, "pdf");

        var map = Map.<String, Object>of("client", "Tenancy schedule report ");
        var folderName = s3Client.uploadPdf(bucket, data);
        var title = "TenancySchedule " + organisationService.getOrganisation(organisationId).getName();
        sendReportMessage(organisationId, folderName, title);
        processReport(organisationId, command.getSenderId(), data, Instant.now(), map, "Tenancy schedule Report", "CLIENT_STATEMENT", null);
    }

    private Optional<Property> getLineItemProperty(String organisationId, Invoice.LineItem lineItem) {
        return Optional.ofNullable(lineItem.getTrackingName())
                .flatMap(reference -> propertyService.findPropertyWithOrganisationAndReference(organisationId, reference));
    }

    private List<CashBalanceReportLineItem> generateCashBalancePropertySubReport(String organisationId, Organisation organisation,
                                                                                 List<Invoice.LineItem> lineItems) {
        var userProperties = propertyService.findPropertiesWithOrganisation(organisationId)
                .stream()
                .filter(property -> property.getLandlords() != null && property.getLandlords().size() > 0 && (property.getPrimaryLandlordId() != null || property.getLandlords().get(0) != null))
                .collect(groupingBy(property -> Optional.ofNullable(property.getPrimaryLandlordId()).orElse(property.getLandlords().get(0))));

        var previousLineItems = List.<Invoice.LineItem>of();

        return userProperties.entrySet().stream().flatMap(entry -> {
            var userId = entry.getKey();
            var user = userService.findUser(userId, false);
            var userValidLineItems = lineItems
                    .stream()
                    .filter(tenancyService::invoiceHasAmount)
                    .filter(lineItem -> Objects.nonNull(lineItem.getTrackingName()))
                    .collect(toUnmodifiableList());

            var userPropertyItems = entry.getValue().stream()
                    .filter(property -> Optional.ofNullable(user).map(User::getId).orElse(userId)
                            .equals(Optional.ofNullable(property.getPrimaryLandlordId()).orElse(property.getLandlords().get(0))))
                    .map(property -> {
                        var propertyLineItems = userValidLineItems.stream()
                                .filter(lineItem -> lineItem.getTrackingName().equals(property.getReference()))
                                .collect(toUnmodifiableList());

                        var propertyTenancies = propertyService.findPropertyTenancies(property.getId(), false);
                        var propertySummary = tenancyService.calculatePropertySummary(organisation, property, propertyTenancies, propertyLineItems, previousLineItems, Instant.now());

                        return CashBalanceReportLineItem.builder()
                                .clientName(formatUser(user))
                                .reference(property.getReference())
                                .property(property.getAddressLine1())
                                .balanceSubItem(formatValue(propertySummary.getClientPayable()))
                                .balanceSubItemDecimal(propertySummary.getClientPayable())
                                .balanceTotal(DEFAULT_VALUE)
                                .balanceTotalDecimal(BigDecimal.ZERO)
                                .build();
                    }).collect(toList());
            if (userPropertyItems.size() > 0) {
                var userPropertySummary = userPropertyItems.stream().map(CashBalanceReportLineItem::getBalanceSubItemDecimal).reduce(BigDecimal.ZERO, BigDecimal::add);

                userPropertyItems.add(CashBalanceReportLineItem.builder()
                        .clientName(DEFAULT_VALUE)
                        .reference(DEFAULT_VALUE)
                        .property(DEFAULT_VALUE)
                        .balanceSubItem(DEFAULT_VALUE)
                        .balanceSubItemDecimal(BigDecimal.ZERO)
                        .balanceTotal(formatValue(userPropertySummary))
                        .balanceTotalDecimal(userPropertySummary)
                        .build());
            }

            return userPropertyItems.stream();
        }).collect(toUnmodifiableList());
    }

    private List<CashBalanceReportLineItem> generateCashBalancePropertyUnpaidBills(Organisation organisation, List<Invoice.LineItem> lineItems) {
        var userProperties = propertyService.findPropertiesWithOrganisation(organisation.getId())
                .stream()
                .filter(property -> property.getLandlords() != null && property.getLandlords().size() > 0 && (property.getPrimaryLandlordId() != null || property.getLandlords().get(0) != null))
                .collect(groupingBy(property -> Optional.ofNullable(property.getPrimaryLandlordId()).orElse(property.getLandlords().get(0))));
        var ledgerCodes = getDepositLedgerCodes(organisation.getLedgerCodes());

        return userProperties.entrySet().stream().flatMap(entry -> {
            var userId = entry.getKey();
            var user = userService.findUser(userId, false);
            var userValidLineItems = lineItems
                    .stream()
                    .filter(tenancyService::invoiceHasAmount)
                    .filter(lineItem -> Objects.nonNull(lineItem.getTrackingName()))
                    .filter(lineItem -> Objects.nonNull(lineItem.getInvoiceId()))
                    .collect(toUnmodifiableList());

            var entries = entry.getValue().stream()
                    .filter(property -> Optional.ofNullable(user).map(User::getId).orElse(userId)
                            .equals(Optional.ofNullable(property.getPrimaryLandlordId()).orElse(property.getLandlords().get(0))))
                    .flatMap(property -> {
                        var propertyUnpaidBillLineItems = userValidLineItems.stream()
                                .filter(lineItem -> lineItem.getTrackingName().equals(property.getReference()))
                                .filter(lineItem -> !lineItem.isIncome() && !lineItem.getParentStatus().equals("PAID"))
                                .filter(lineItem -> Objects.isNull(lineItem.getAccountCode()) || !ledgerCodes.contains(lineItem.getAccountCode()))
                                .map(lineItem -> {
                                    var lineAmount = tenancyService.getLineItemAmount(lineItem);
                                    var paidAmount = tenancyService.getLineItemPaidAmount(lineItem);
                                    var remainingBillAmount = lineAmount.subtract(paidAmount);

                                    return CashBalanceReportLineItem.builder()
                                            .clientName(formatUser(user))
                                            .reference(property.getReference())
                                            .property(Optional.ofNullable(tenancyService.getParentReference(lineItem)).orElse(DEFAULT_VALUE))
                                            .balanceSubItem(formatValue(remainingBillAmount))
                                            .balanceSubItemDecimal(remainingBillAmount)
                                            .balanceTotal(DEFAULT_VALUE)
                                            .balanceTotalDecimal(BigDecimal.ZERO)
                                            .build();
                                })
                                .collect(toList());
                        return propertyUnpaidBillLineItems.stream();
                    }).collect(toList());

            if (entries.size() > 0) {
                var userPropertySummary = entries.stream()
                        .map(CashBalanceReportLineItem::getBalanceSubItemDecimal)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                entries.add(CashBalanceReportLineItem.builder()
                        .clientName(DEFAULT_VALUE)
                        .reference(DEFAULT_VALUE)
                        .property(DEFAULT_VALUE)
                        .balanceSubItem(DEFAULT_VALUE)
                        .balanceSubItemDecimal(BigDecimal.ZERO)
                        .balanceTotal(formatValue(userPropertySummary))
                        .balanceTotalDecimal(userPropertySummary)
                        .build());
            }
            return entries.stream();
        }).collect(toUnmodifiableList());
    }

    private List<CashBalanceReportLineItem> generateCashBalanceSubReportForBalances(String organisationId,
                                                                                    List<String> balanceLedgerCodes,
                                                                                    Map<String, List<Invoice.LineItem>> contactGroupedLineItems,
                                                                                    boolean showAsPositive) {
        return contactGroupedLineItems.entrySet()
                .stream()
                .flatMap(entrySetItem -> {
                    var user = userService.findUserWithXeroId(entrySetItem.getKey());

                    var userLineItems = entrySetItem.getValue()
                            .stream()
                            .filter(tenancyService::invoiceHasAmount)
                            .filter(lineItem -> Objects.nonNull(lineItem.getAccountCode()) && balanceLedgerCodes.contains(lineItem.getAccountCode()))
                            .map(lineItem -> {
                                var address = getLineItemProperty(organisationId, lineItem).map(Property::getAddressLine1).orElse(DEFAULT_VALUE);

                                var positive = showAsPositive || lineItem.isIncome() ? BigDecimal.ONE : BigDecimal.valueOf(-1);

                                return CashBalanceReportLineItem.builder()
                                        .clientName(formatUser(user))
                                        .reference(Optional.ofNullable(tenancyService.getParentReference(lineItem)).orElse(DEFAULT_VALUE))
                                        .property(address)
                                        .balanceSubItem(formatValue(tenancyService.getLineItemPaidAmount(lineItem).multiply(positive)))
                                        .balanceSubItemDecimal(tenancyService.getLineItemPaidAmount(lineItem).multiply(positive))
                                        .balanceTotal(DEFAULT_VALUE)
                                        .balanceTotalDecimal(BigDecimal.ZERO)
                                        .build();
                            }).collect(toList());

                    if (userLineItems.isEmpty()) {
                        return Stream.empty();
                    }

                    var total = userLineItems.stream()
                            .map(CashBalanceReportLineItem::getBalanceSubItemDecimal)
                            .reduce(BigDecimal::add)
                            .orElse(BigDecimal.ZERO);

                    userLineItems.add(CashBalanceReportLineItem.builder()
                            .clientName(DEFAULT_VALUE)
                            .reference(DEFAULT_VALUE)
                            .property(DEFAULT_VALUE)
                            .balanceSubItem(DEFAULT_VALUE)
                            .balanceSubItemDecimal(BigDecimal.ZERO)
                            .balanceTotal(formatValue(total))
                            .balanceTotalDecimal(total)
                            .build());

                    return userLineItems.stream();
                }).collect(toUnmodifiableList());
    }

    private List<CashBalanceReportLineItem> generateCashBalanceFloatSubReport(String organisationId,
                                                                              Map<String, List<Invoice.LineItem>> contactGroupedLineItems) {
        var userProperties = propertyService.findPropertiesWithOrganisation(organisationId)
                .stream()
                .filter(property -> property.getLandlords() != null && property.getLandlords().size() > 0 && (property.getPrimaryLandlordId() != null || property.getLandlords().get(0) != null))
                .collect(groupingBy(property -> Optional.ofNullable(property.getPrimaryLandlordId()).orElse(property.getLandlords().get(0))));

        return userProperties.entrySet().stream().flatMap(entry -> {
            var userId = entry.getKey();
            var user = userService.findUser(userId, false);
            var userPropertyItems = entry.getValue().stream()
                    .filter(property -> Optional.ofNullable(user).map(User::getId).orElse(userId)
                            .equals(Optional.ofNullable(property.getPrimaryLandlordId()).orElse(property.getLandlords().get(0))))
                    .map(property -> {
                        return CashBalanceReportLineItem.builder()
                                .clientName(formatUser(user))
                                .reference(property.getReference())
                                .property(property.getAddressLine1())
                                .balanceSubItem(formatValue(property.getMinimumBalance()))
                                .balanceSubItemDecimal(property.getMinimumBalance())
                                .balanceTotal(DEFAULT_VALUE)
                                .balanceTotalDecimal(BigDecimal.ZERO)
                                .build();
                    }).collect(toList());
            if (userPropertyItems.size() > 0) {
                var userPropertySummary = userPropertyItems.stream().map(CashBalanceReportLineItem::getBalanceSubItemDecimal).reduce(BigDecimal.ZERO, BigDecimal::add);

                userPropertyItems.add(CashBalanceReportLineItem.builder()
                        .clientName(DEFAULT_VALUE)
                        .reference(DEFAULT_VALUE)
                        .property(DEFAULT_VALUE)
                        .balanceSubItem(DEFAULT_VALUE)
                        .balanceSubItemDecimal(BigDecimal.ZERO)
                        .balanceTotal(formatValue(userPropertySummary))
                        .balanceTotalDecimal(userPropertySummary)
                        .build());
            }

            return userPropertyItems.stream();
        }).collect(toUnmodifiableList());
    }

    private BigDecimal getCashBalanceTotal(List<CashBalanceReportLineItem> lineItems) {
        return lineItems.stream()
                .map(CashBalanceReportLineItem::getBalanceTotalDecimal)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
    }

    private String getCashBalanceTotalFormatted(List<CashBalanceReportLineItem> lineItems) {
        return formatValue(getCashBalanceTotal(lineItems));
    }

    private byte[] generateCashBalancePropertySubReport(List<CashBalanceReportLineItem> userReportLineItems,
                                                        Map<String, String> overwriteParameters,
                                                        String pdfTitle, String sectionPageNumber) {
        var parameters = new HashMap<String, Object>();
        parameters.put("data", userReportLineItems);
        parameters.put("sectionPageNumber", sectionPageNumber);

        var total = userReportLineItems.stream()
                .map(CashBalanceReportLineItem::getBalanceTotalDecimal)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        parameters.put("total", formatValue(total));
        parameters.put("title", pdfTitle);
        parameters.put("firstColumnName", "Client");
        parameters.put("secondColumnName", "Reference");
        parameters.put("thirdColumnName", "Property");
        parameters.put("fourthColumnName", DEFAULT_VALUE);
        parameters.put("fivthColumnName", "Balance");
        parameters.putAll(overwriteParameters);

        return jasperClient.generateCashBalanceClientBalance(parameters, "pdf");
    }

    @Override
    public void generateCashBalanceReport(CashBalanceReportCommand payload) {
        var bucket = config.getRentancyDocumentUploads();
        var organisation = organisationService.getOrganisation(payload.getOrganisationId());
        var lineItems = invoiceService.findOrganisationLineItems(payload.getOrganisationId(), null, null)
                .stream()
                .filter(item -> Objects.nonNull(item.getParentContactId()))
                .collect(toUnmodifiableList());

        var contactGroupedLineItems = lineItems.stream().collect(groupingBy(Invoice.LineItem::getParentContactId));
        var subReports = new ArrayList<byte[]>();

        var suspenseBalancesLedgerCodes = getLedgerCodes(organisation.getLedgerCodes(),
                code -> code.getName().equals("Suspense Balances"));
        var suspenseBalancesLineItems = generateCashBalanceSubReportForBalances(organisation.getId(), suspenseBalancesLedgerCodes, contactGroupedLineItems, false);

        // suspenses are not included in income
        var paidIncomeLineItems = lineItems.stream().filter(lineItem -> Objects.isNull(lineItem.getAccountCode()) || !suspenseBalancesLedgerCodes.contains(lineItem.getAccountCode())).collect(toUnmodifiableList());
        var paidAmountLineItems = generateCashBalancePropertySubReport(payload.getOrganisationId(), organisation, paidIncomeLineItems);
        subReports.add(generateCashBalancePropertySubReport(paidAmountLineItems, Map.of(), "Client Balances", "2"));

        var floatLineItems = generateCashBalanceFloatSubReport(payload.getOrganisationId(), contactGroupedLineItems);
        subReports.add(generateCashBalancePropertySubReport(floatLineItems, Map.of(), "Float Balances", "3"));

        var tenantBalancesLedgerCodes = getLedgerCodes(organisation.getLedgerCodes(),
                code -> code.getName().equals("Tenant Balances"));

        var tenantBalancesLineItems = generateCashBalanceSubReportForBalances(organisation.getId(), tenantBalancesLedgerCodes, contactGroupedLineItems, false);
        subReports.add(generateCashBalancePropertySubReport(tenantBalancesLineItems, Map.of(), "Tenant Balances", "4"));

        var depositBalancesLedgerCodes = getLedgerCodes(organisation.getLedgerCodes(),
                code -> code.getName().equals("Deposits"));

        var depositBalancesLineItems = generateCashBalanceSubReportForBalances(organisation.getId(), depositBalancesLedgerCodes, contactGroupedLineItems, true);
        var depositBalancesColumnNames = Map.of("firstColumnName", "Contact");
        subReports.add(generateCashBalancePropertySubReport(depositBalancesLineItems, depositBalancesColumnNames, "Deposit Balances", "5"));

        var supplierBalancesLedgerCodes = getLedgerCodes(organisation.getLedgerCodes(),
                code -> code.getName().equals("Supplier Balances"));

        var supplierBalancesLineItems = generateCashBalanceSubReportForBalances(organisation.getId(), supplierBalancesLedgerCodes, contactGroupedLineItems, false);
        subReports.add(generateCashBalancePropertySubReport(supplierBalancesLineItems, Map.of(), "Supplier Balances", "6"));

        var userPropertyUnpaidBills = generateCashBalancePropertyUnpaidBills(organisation, lineItems);
        var unpaidBillsColumnNames = Map.of("firstColumnName", "Contact", "secondColumnName", "Reference", "thirdColumnName", "Invoice No", "fourthColumnName", "Amount Due", "fivthColumnName", "Balance");
        subReports.add(generateCashBalancePropertySubReport(userPropertyUnpaidBills, unpaidBillsColumnNames, "Bills Funded", "7"));

        var cashBalanceReportMainPageTotals = new HashMap<String, Object>();
        cashBalanceReportMainPageTotals.put("organisationName", organisation.getName());
        cashBalanceReportMainPageTotals.put("printedTime", DateTimeFormatter.ofPattern("dd MMM yyyy HH:mm:ss").withZone(UTC).format(Instant.now()));
        ;
        cashBalanceReportMainPageTotals.put("clientBalance", getCashBalanceTotalFormatted(paidAmountLineItems));
        cashBalanceReportMainPageTotals.put("floatBalance", getCashBalanceTotalFormatted(floatLineItems));
        cashBalanceReportMainPageTotals.put("tenantBalance", getCashBalanceTotalFormatted(tenantBalancesLineItems));
        cashBalanceReportMainPageTotals.put("depositBalance", getCashBalanceTotalFormatted(depositBalancesLineItems));
        cashBalanceReportMainPageTotals.put("supplierBalance", getCashBalanceTotalFormatted(supplierBalancesLineItems));
        cashBalanceReportMainPageTotals.put("suspenseBalance", getCashBalanceTotalFormatted(suspenseBalancesLineItems));
        cashBalanceReportMainPageTotals.put("unpaidBillsTotal", getCashBalanceTotalFormatted(userPropertyUnpaidBills));
        cashBalanceReportMainPageTotals.put("sectionPageNumber", "1");
        cashBalanceReportMainPageTotals.put("total", formatValue(getCashBalanceTotal(paidAmountLineItems)
                        .add(getCashBalanceTotal(floatLineItems))
                        .add(getCashBalanceTotal(tenantBalancesLineItems))
                        .subtract(getCashBalanceTotal(depositBalancesLineItems))
                        .add(getCashBalanceTotal(supplierBalancesLineItems))
                        .add(getCashBalanceTotal(userPropertyUnpaidBills))
                        .add(getCashBalanceTotal(suspenseBalancesLineItems))
                )
        );

        var mainPage = jasperClient.generateCashBalanceMainPage(cashBalanceReportMainPageTotals, "pdf");
        subReports.add(0, mainPage);
        var finalReport = PDFUtils.merge(subReports);
        var uploadedReportKey = s3Client.uploadPdf(bucket, finalReport);

        var date = new SimpleDateFormat("dd MMM yyyy").format(Date.from(Instant.now()));
        processReport(organisation.getId(), payload.getSenderId(), finalReport, Instant.now(), Map.of("client", organisation.getName()), "Cash Balance Report", "CLIENT_STATEMENT", DEFAULT_VALUE);
        var title = "Cash Balance Allocation " + organisation.getName();
        sendReportMessage(organisation, uploadedReportKey, title);
    }

    public void publishReportViaEmail(byte[] reportData, String organisationId, String title, String receiverId, String fileExt) {
        var organisation = organisationService.getOrganisation(organisationId);
        var now = Instant.now();
        var date = new SimpleDateFormat("dd MMM yyyy").format(Date.from(now));
        var fileNameParams = organisation.getName() + "-" + date + "-" + fileExt;

        processReport(organisationId, receiverId, reportData, now, Map.of("client", organisation.getName()), title, "CLIENT_STATEMENT", fileNameParams, DEFAULT_VALUE);
    }

    private byte[] toByteArray(Workbook workbook) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            workbook.write(outputStream);

            return outputStream.toByteArray();
        } catch (Exception e) {
            throw new IllegalStateException("Failed to write to output stream", e);
        }
    }

    private Map<String, Object> calculateClientStatement(String organisationId,
                                                         String userId,
                                                         String startDate,
                                                         String endDate,
                                                         Instant now,
                                                         Organisation organisation,
                                                         List<Property> landlordProperties) {
        var previousStartDate = "1990-01-01T00:00:00Z";

        var today = now.atOffset(UTC)
                .with(LocalTime.of(23, 59, 59, now.getNano()))
                .toInstant();

        var realStartDate = Instant.parse(startDate).minus(1, SECONDS).toString();
        var realPreviousLineItemsEndDate = Instant.parse(startDate).minus(1, DAYS).toString();
        var lineItems = invoiceService.findOrganisationLineItems(organisationId, realStartDate, endDate);
        var previousLineItems = invoiceService.findOrganisationLineItems(organisationId, previousStartDate, realPreviousLineItemsEndDate);

        var tenancies = propertyService.findTenancies(organisationId, null, true)
                .stream()
                .filter(tenancy -> !NON_ACTIVE_TENANCY_STATUSES.contains(tenancy.getStatus()))
                .filter(tenancy -> landlordProperties.stream().anyMatch(property -> tenancy.getProperty().equals(property.getId())))
                .collect(toUnmodifiableList());

        var depositLedgerCodes = getDepositLedgerCodes(organisation.getLedgerCodes());

        var propertySummary = landlordProperties.stream().map(property -> {
                    var relatedTenancies = tenancies.stream().filter(tenancy -> property.getId().equals(tenancy.getProperty())).collect(toUnmodifiableList());

                    var reference = property.getReference();
                    var tenancySummary = tenancyService.calculatePropertyTenanciesSummary(organisation, property, relatedTenancies, lineItems, today);
                    var previousTenancySummary = tenancyService.calculatePropertyTenanciesSummary(organisation, property, relatedTenancies, previousLineItems, today);

                    var previousExpenses = tenancyService.calculateAllExpenses(previousLineItems, reference, depositLedgerCodes);
                    var openingBalance = previousTenancySummary.getPaidIncome().add(property.getOpeningBalance()).subtract(previousExpenses.getTotalExpenses());
                    var minimumBalance = property.getMinimumBalance();
                    var expenses = tenancyService.calculateAllExpenses(lineItems, reference, depositLedgerCodes);
                    var closingBalance = tenancySummary.getPaidIncome().add(openingBalance).subtract(expenses.getTotalExpenses());
                    var clientPayable = closingBalance.subtract(minimumBalance);

                    var map = new HashMap<String, Object>();

                    map.put("reference", reference);
                    map.put("addressLine", property.getAddressLine1());
                    map.put("openingBalance", formatValue(openingBalance));
                    map.put("openingBalanceN", openingBalance);
                    map.put("income", formatValue(tenancySummary.getPaidIncome()));
                    map.put("incomeN", tenancySummary.getPaidIncome());
                    map.put("expenses", formatValue(expenses.getTotalExpenses()));
                    map.put("expensesN", expenses.getTotalExpenses());
                    map.put("closingBalance", formatValue(closingBalance));
                    map.put("closingBalanceN", closingBalance);
                    map.put("float", formatValue(property.getMinimumBalance()));
                    map.put("floatN", property.getMinimumBalance());
                    map.put("clientPayable", formatValue(clientPayable));
                    map.put("clientPayableN", clientPayable);

                    return map;
                }).sorted(Comparator.comparing(summary -> (String) summary.get("reference")))
                .collect(toUnmodifiableList());

        var dateFormatter = new SimpleDateFormat("dd MMM yyyy");

        var map = new HashMap<String, Object>();
        map.put("data", propertySummary);
        map.put("date", dateFormatter.format(Date.from(Instant.now())));
        var startDateFormatted = dateFormatter.format(Date.from(Instant.parse(startDate)));
        var endDateFormatted = dateFormatter.format(Date.from(Instant.parse(endDate)));
        map.put("period", "Period: " + startDateFormatted + " - " + endDateFormatted);
        var user = userService.findUser(userId, false);
        map.put("client", "Client: " + formatUser(user));
        map.put("clientAddress", user.getHomeAddress() == null ? "" : getUserAddressDescription(user.getHomeAddress()));
        map.put("openingBalanceSum", propertySummary.stream().map(t -> (BigDecimal) t.get("openingBalanceN")).reduce(BigDecimal.ZERO, BigDecimal::add));
        map.put("incomeSum", propertySummary.stream().map(t -> (BigDecimal) t.get("incomeN")).reduce(BigDecimal.ZERO, BigDecimal::add));
        map.put("expensesSum", propertySummary.stream().map(t -> (BigDecimal) t.get("expensesN")).reduce(BigDecimal.ZERO, BigDecimal::add));
        map.put("closingBalanceSum", propertySummary.stream().map(t -> (BigDecimal) t.get("closingBalanceN")).reduce(BigDecimal.ZERO, BigDecimal::add));
        map.put("floatSum", propertySummary.stream().map(t -> (BigDecimal) t.get("floatN")).reduce(BigDecimal.ZERO, BigDecimal::add));

        var dueToClient = propertySummary.stream().map(t -> (BigDecimal) t.get("clientPayableN")).reduce(BigDecimal.ZERO, BigDecimal::add);
        map.put("clientPayableSum", dueToClient);
        map.put("logo", getLogo(organisation.getLogo()));

        var landlordPaymentLedgerCode = getLandlordPaymentLedgerCode(organisation.getLedgerCodes());


        var deposits = BigDecimal.ZERO;/*lineItems.stream()
                .filter(lineItem -> lineItem.getAccountCode() != null)
                .filter(lineItem -> landlordPaymentLedgerCode.equals(lineItem.getAccountCode()))
                .map(tenancyService::getLineItemPaidAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .movePointLeft(2);*/

        map.put("landlordDeposits", deposits);
        map.put("Payable", deposits.add(dueToClient));

        return map;
    }

    private Map<String, Object> calculateTenantStatement(List<Tenancy> tenancies,
                                                         List<Invoice> invoices,
                                                         User tenant,
                                                         Organisation organisation,
                                                         String startDate,
                                                         String endDate) {
        var map = new HashMap<String, Object>();

        var tenantInvoices = invoices
                .stream()
                .filter(invoice -> Objects.nonNull(invoice.getContactId()))
                .filter(invoice -> {
                    return tenant.getXeroId().equals(invoice.getContactId());
                })
                .collect(toUnmodifiableList());

        var totalPaid = tenantInvoices
                .stream()
                .map(invoice -> new BigDecimal(invoice.getAmountPaid()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        var totalDue = tenantInvoices
                .stream()
                .map(invoice -> new BigDecimal(invoice.getAmountDue()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        var to = formatUser(tenant);
        var property = tenancies
                .stream()
                .filter(tenancy -> tenancy.getTenants().contains(tenant.getId()))
                .findAny()
                .map(tenancy -> propertyService.getProperty(tenancy.getProperty()))
                .orElse(null);

        log.info("Property - {}", property);

        map.put("tenantName", to);
        map.put("tenantAddressLine1", Optional.ofNullable(property).map(Property::getAddressLine1).orElse(null));
        map.put("tenantPostalCode", Optional.ofNullable(property).map(Property::getPostcode).orElse(null));
        map.put("reportPeriod", "Period: " + formatDate(startDate) + " - " + formatDate(endDate));
        map.put("totalPaid", formatValue(totalPaid));
        map.put("totalDue", formatValue(totalDue));
        map.put("workspaceName", organisation.getName());
        map.put("workspaceAddressLine1", organisation.getAddressLine1());
        map.put("workspaceAddressLine2", organisation.getAddressLine2());
        map.put("workspacePostalCode", organisation.getPostcode());
        map.put("workspaceCity", organisation.getCity());
        map.put("reportDate", formatDate(Instant.now().toString()));
        map.put("workspaceLogo", getLogo(organisation.getLogo()));
        map.put("invoices", toTenantStatementInvoices(tenantInvoices, to));

        return map;
    }

    private List<TenantReportPropertySummary> toTenantStatementProperties(Map<String, List<Invoice>> propertyInvoices, String to) {
        return propertyInvoices
                .keySet()
                .stream()
                .map(propertyId -> {
                    var property = propertyService.getProperty(propertyId);
                    var invoices = propertyInvoices.get(propertyId);

                    return new TenantReportPropertySummary(getPropertyDescription(property), toTenantStatementInvoices(invoices, to));
                }).collect(toUnmodifiableList());
    }

    private List<TenantReportSummary> toTenantStatementInvoices(List<Invoice> invoices, String to) {
        return invoices
                .stream()
                .map(invoice -> TenantReportSummary
                        .builder()
                        .amountDue(formatValue(new BigDecimal(invoice.getAmountDue())))
                        .amountPaid(formatValue(new BigDecimal(invoice.getAmountPaid())))
                        .status("PAID".equals(invoice.getStatus()) ? invoice.getStatus() : "UNPAID")
                        .date(formatDate(invoice.getDate().toString()))
                        .paymentDate(invoice.getPayments().stream().findAny().map(Invoice.XeroInvoicePayment::getDate).map(d -> formatDate(d.toString())).orElse(null))
                        .description(invoice
                                .getLineItems()
                                .stream()
                                .filter(lineItem -> Objects.nonNull(lineItem.getLineAmount()))
                                .findAny()
                                .map(Invoice.LineItem::getDescription)
                                .orElse(DEFAULT_VALUE))
                        .build()).collect(toUnmodifiableList());
    }

    @Override
    public CompletableFuture<AllClientSummary> generateAllClientSummary(String organisationId) {
        var now = Instant.now();
        var beginningOfCurrentMonth = YearMonth.from(Instant.now().atZone(UTC)).atDay(1).atStartOfDay().toInstant(UTC).minus(1, SECONDS);
        var endOfLastMonth = beginningOfCurrentMonth.minus(1, DAYS).toString();

        var organisationFuture = CompletableFuture.supplyAsync(() -> organisationService.getOrganisation(organisationId));
        var allPropertiesFuture = CompletableFuture.supplyAsync(() -> propertyService.findPropertiesWithOrganisation(organisationId));
        var allTenanciesFuture = CompletableFuture.supplyAsync(() -> propertyService.findTenancies(organisationId, null, false));
        var previousLineItemsFuture = CompletableFuture.supplyAsync(() -> invoiceService.findOrganisationLineItems(organisationId, null, endOfLastMonth));
        var lineItemsFuture = CompletableFuture.supplyAsync(() -> invoiceService.findOrganisationLineItems(organisationId, beginningOfCurrentMonth.toString(), null));

        return CompletableFuture.allOf(
                organisationFuture, allPropertiesFuture, allTenanciesFuture, previousLineItemsFuture, lineItemsFuture
        ).thenApply(v -> {
            try {
                var organisation = organisationFuture.get();
                var allProperties = allPropertiesFuture.get();
                var allTenancies = allTenanciesFuture.get();
                var previousLineItems = previousLineItemsFuture.get();
                var lineItems = lineItemsFuture.get();

                var landlordProperties = allProperties.stream()
                        .filter(property -> !property.getLandlords().isEmpty())
                        .collect(groupingBy(t -> Optional.ofNullable(t.getPrimaryLandlordId()).orElse(t.getLandlords().get(0))));
                var propertyTenancies = allTenancies.stream()
                        .filter(tenancy -> tenancy.getProperty() != null)
                        .collect(groupingBy(Tenancy::getProperty));
                var landlords = userService.findUsers(landlordProperties.keySet());
                var propertyZeroSummary = PropertySummary.zero();

                var clientSummaries = landlords.parallelStream().map(landlord -> {
                    var properties = landlordProperties.get(landlord.getId());
                    var propertySummary = properties.parallelStream().map(property ->
                            tenancyService.calculatePropertySummary(organisation, property,
                                    propertyTenancies.getOrDefault(property.getId(), emptyList()),
                                    lineItems, previousLineItems, now
                            )
                    ).reduce(propertyZeroSummary, tenancyService::reducePropertySummaries);

                    return ClientSummary.builder()
                            .landlordId(landlord.getId())
                            .propertyCount(properties.size())
                            .landlordName(formatUser(landlord))
                            .openingBalance(propertySummary.getOpeningBalance())
                            .income(propertySummary.getIncome())
                            .expenses(propertySummary.getExpenses())
                            .closingBalance(propertySummary.getBalance())
                            .minimumBalance(propertySummary.getMinimumBalance())
                            .dueToClient(propertySummary.getBalance().subtract(propertySummary.getMinimumBalance()))
                            .build();
                }).collect(toUnmodifiableList());

                return AllClientSummary.builder()
                        .clientSummaries(clientSummaries)
                        .totalOpeningBalance(clientSummaries.stream().map(ClientSummary::getOpeningBalance).reduce(BigDecimal.ZERO, BigDecimal::add))
                        .totalIncome(clientSummaries.stream().map(ClientSummary::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add))
                        .totalExpenses(clientSummaries.stream().map(ClientSummary::getExpenses).reduce(BigDecimal.ZERO, BigDecimal::add))
                        .totalClosing(clientSummaries.stream().map(ClientSummary::getClosingBalance).reduce(BigDecimal.ZERO, BigDecimal::add))
                        .totalMinimumBalance(clientSummaries.stream().map(ClientSummary::getMinimumBalance).reduce(BigDecimal.ZERO, BigDecimal::add))
                        .totalDueToClient(clientSummaries.stream().map(ClientSummary::getDueToClient).reduce(BigDecimal.ZERO, BigDecimal::add))
                        .build();

            } catch (InterruptedException | ExecutionException e) {
                throw new GenerateClientSummaryException("Unable to fetch data needed to construct report");
            }
        });
    }

    @Override
    public List<PropertySummaryResponse> generatePropertySummary(String organisationId, PropertySummaryRequest request) {
        var now = Instant.now();
        var beginningOfCurrentMonth = YearMonth.from(Instant.now().atZone(UTC)).atDay(1).atStartOfDay().toInstant(UTC);
        var endOfLastMonth = beginningOfCurrentMonth.minus(1, DAYS).toString();

        CompletableFuture<Organisation> organisationFuture = CompletableFuture.supplyAsync(() -> organisationService.getOrganisation(organisationId));
        CompletableFuture<List<Property>> allPropertiesFuture = CompletableFuture.supplyAsync(() -> propertyService.findPropertiesWithOrganisation(organisationId));
        CompletableFuture<List<Tenancy>> allTenanciesFuture = CompletableFuture.supplyAsync(() -> propertyService.findTenancies(organisationId, null, false));
        CompletableFuture<List<Invoice.LineItem>> previousLineItemsFuture = CompletableFuture.supplyAsync(() -> invoiceService.findOrganisationLineItems(organisationId, null, endOfLastMonth));
        CompletableFuture<List<Invoice.LineItem>> lineItemsFuture = CompletableFuture.supplyAsync(() -> invoiceService.findOrganisationLineItems(organisationId, beginningOfCurrentMonth.toString(), null));

        CompletableFuture.allOf(
            organisationFuture, allPropertiesFuture, allTenanciesFuture, previousLineItemsFuture, lineItemsFuture
        ).join();

        var organisation = organisationFuture.join();
        var allProperties = allPropertiesFuture.join();
        var allTenancies = allTenanciesFuture.join();
        var previousLineItems = previousLineItemsFuture.join();
        var lineItems = lineItemsFuture.join();

        return allProperties
                .stream()
                .filter(property -> request.getPropertyIds().stream().anyMatch(id -> property.getId().equals(id)))
                .map(property -> {
                    var tenancies = allTenancies.stream()
                            .filter(tenancy -> tenancy.getProperty().equals(property.getId()))
                            .collect(toUnmodifiableList());

                    var summary = tenancyService.calculatePropertySummary(organisation, property, tenancies, lineItems, previousLineItems, now);
                    return new PropertySummaryResponse(property.getId(), summary);
                }).collect(toUnmodifiableList());
    }

    public List<UserSummary> generateUserSummary(Organisation organisation, String ledgerCode) {
        var lineItems = invoiceService.findOrganisationLineItems(organisation.getId(), null, null)
                .stream()
                .filter(item -> ledgerCode.equals(item.getAccountCode()))
                .filter(item -> Objects.nonNull(item.getParentContactId()))
                .collect(toUnmodifiableList());

        var contactGroupedLineItems = lineItems.stream().collect(groupingBy(Invoice.LineItem::getParentContactId));

        return contactGroupedLineItems.entrySet().stream().map(entrySetItem -> {
            var user = userService.findUserWithXeroId(entrySetItem.getKey());
            var credit = entrySetItem.getValue()
                    .stream()
                    .filter(Invoice.LineItem::isIncome)
                    .map(tenancyService::getLineItemAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            var debit = entrySetItem.getValue()
                    .stream()
                    .filter(lineItem -> !lineItem.isIncome())
                    .map(tenancyService::getLineItemPaidAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            var vat = entrySetItem.getValue()
                    .stream()
                    .filter(lineItem -> !lineItem.isIncome())
                    .map(lineItem ->
                            tenancyService.getLineItemAmount(lineItem).subtract(tenancyService.getLineItemPaidAmount(lineItem))
                    )
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            return UserSummary.builder()
                    .userName(formatUser(user))
                    .credit(credit)
                    .debit(debit)
                    .vat(vat)
                    .build();
        }).collect(toUnmodifiableList());
    }

    @Override
    public List<UserSummary> generateUserSummary(String organisationId, String ledgerCode) {
        var organisation = organisationService.getOrganisation(organisationId);
        return generateUserSummary(organisation, ledgerCode);
    }

    @Override
    public String saveStatement(String cognitoId, String propertyId, String startDate, String endDate, String type, String landlordBillId, byte[] data, boolean approved) {
        var property = propertyService.getProperty(propertyId);
        var organisationId = property.getOrganisation();
        var landlord = Optional
                .ofNullable(property.getPrimaryLandlordId())
                .orElse(Optional
                        .ofNullable(property.getLandlords())
                        .flatMap(landlords -> landlords.stream().findFirst())
                        .orElse(null));

        return saveStatement(cognitoId, organisationId, startDate, endDate, type, data, landlord, propertyId, landlordBillId, approved);
    }

    @Override
    public void saveStatement(Statement statement) {
        reportRepository.saveStatement(statement);
    }

    private LandlordPayoutStatement.IncomeExpensePercentageSection getIncomeExpensePercentageSection(
            List<LandlordPayoutStatement.LandlordPayoutStatementItem> landlordPayoutStatementItems) {
        var total = landlordPayoutStatementItems.stream()
                .map(LandlordPayoutStatement.LandlordPayoutStatementItem::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        var items = landlordPayoutStatementItems
                .stream()
                .collect(groupingBy(LandlordPayoutStatement.LandlordPayoutStatementItem::getLedgerCodeName))
                .entrySet()
                .stream()
                .map(entryItem -> {
                    var itemAmount = entryItem.getValue().stream().map(LandlordPayoutStatement.LandlordPayoutStatementItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    var percentage = total.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : itemAmount.multiply(new BigDecimal(100)).divide(total, RoundingMode.HALF_UP);

                    return LandlordPayoutStatement.LandlordPayoutStatementPercentageItem.builder()
                            .itemName(entryItem.getKey())
                            .percentage("" + formatValue(percentage) + "%")
                            .currency(entryItem.getValue().stream().findFirst().map(LandlordPayoutStatement.LandlordPayoutStatementItem::getCurrency).orElseThrow())
                            .amount(itemAmount)
                            .build();
                }).collect(toUnmodifiableList());

        return LandlordPayoutStatement.IncomeExpensePercentageSection.builder()
                .items(items)
                .total(total)
                .build();
    }

    private BigDecimal getLandlordBalanceShare(User landlord, BigDecimal balance, @Nullable ParentProperty parentProperty, @Nullable Property property) {
        var isSplitOwnershipEnabled = Optional.ofNullable(parentProperty)
                .map(ParentProperty::isSplitOwnershipEnabled)
                .or(() -> Optional.ofNullable(property).map(Property::isSplitOwnershipEnabled))
                .orElse(false);

        var landlords = Optional.ofNullable(parentProperty)
                .map(ParentProperty::getLandlords)
                .or(() -> Optional.ofNullable(property).map(Property::getLandlords))
                .orElse(List.of());
        var primaryLandlord = getParentPropertyPrimaryLandlordId(parentProperty)
                .or(() -> getPrimaryLandlordId(property))
                .orElse(null);

        var targetId = Optional.ofNullable(parentProperty)
                .map(ParentProperty::getId)
                .or(() -> Optional.ofNullable(property).map(Property::getId))
                .orElse(DEFAULT_VALUE);

        if (!landlords.contains(landlord.getId())) {
            return BigDecimal.ZERO;
        }

        if (!isSplitOwnershipEnabled) {
            return Optional.ofNullable(primaryLandlord)
                    .filter(pl -> pl.equals(landlord.getId()))
                    .isPresent() ? balance : BigDecimal.ZERO;
        }

        var ownedPercentage = Optional.ofNullable(landlord.getOwnedProperties())
                .orElse(List.of())
                .stream()
                .filter(ownedProperty -> ownedProperty.getPropertyId().equals(targetId))
                .findFirst()
                .map(User.OwnedProperty::getPercentage)
                .orElse(0);

        return balance.divide(new BigDecimal(10000), RoundingMode.HALF_UP).multiply(new BigDecimal(ownedPercentage));
    }

    private List<Map<String, String>> formatExpenseAccountCodes(Organisation organisation,
                                                                List<Pair<String, List<String>>> ledgerCodeOrders,
                                                                Map<String, BigDecimal> accountCodeAmount) {
        var ledgetNameCodeMap = Optional.ofNullable(organisation.getLedgerCodes())
                .orElse(List.of())
                .stream()
                .filter(ledgerCode -> Objects.nonNull(ledgerCode.getCode()) && Objects.nonNull(ledgerCode.getDisplayName()))
                .collect(toUnmodifiableMap(Organisation.LedgerCode::getDisplayName, Organisation.LedgerCode::getCode, (a1, a2) -> a1));
        var currency = organisation.getCurrency();
        var currencySymbol = Currency.getInstance(currency).getSymbol();
        var preUsedLedgerCodes = ledgerCodeOrders.stream().flatMap(pair -> pair.getRight().stream()).collect(toUnmodifiableSet());

        var formattedExpenses = ledgerCodeOrders.stream().flatMap(pair -> {
            var amount = pair.getRight().stream().map(ledgerName -> {
                var code = ledgetNameCodeMap.get(ledgerName);

                if (Objects.isNull(code)) {
                    return BigDecimal.ZERO;
                }

                return accountCodeAmount.getOrDefault(code, BigDecimal.ZERO);
            }).reduce(BigDecimal.ZERO, BigDecimal::add);

            if (amount.compareTo(BigDecimal.ZERO) == 0) {
                return Stream.of();
            }

            return Stream.of(Map.of("description", pair.getKey(), "amount", formatValueWithCurrency(currencySymbol, amount)));
        }).collect(toList());

        accountCodeAmount.forEach((code, amount) -> {
            var ledgerCodeName = Optional.ofNullable(organisation.getLedgerCodes())
                    .orElse(List.of())
                    .stream()
                    .filter(ledgerCode -> Objects.nonNull(ledgerCode.getCode()) && ledgerCode.getCode().equals(code))
                    .findFirst()
                    .map(Organisation.LedgerCode::getDisplayName)
                    .orElse(code);

            if (preUsedLedgerCodes.contains(ledgerCodeName)) {
                return;
            }

            formattedExpenses.add(Map.of("description", ledgerCodeName, "amount", formatValueWithCurrency(currencySymbol, amount)));
        });

        return formattedExpenses;
    }

    private Set<String> getJournalUnwantedExpenseLedgerCodes(Organisation organisation) {
        var unwantedLedgerCodeNames = List.of("ZDS Income", "Resident Services Income", "EZDS");

        return Optional.ofNullable(organisation.getLedgerCodes())
                .orElse(List.of())
                .stream()
                .filter(ledgerCode -> unwantedLedgerCodeNames.contains(ledgerCode.getDisplayName()))
                .map(Organisation.LedgerCode::getCode)
                .collect(toUnmodifiableSet());
    }

    private BigDecimal reportGetSafePercentage(BigDecimal amount, BigDecimal total) {
        if (total.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return amount.divide(total, RoundingMode.HALF_UP).movePointRight(2);
    }

    private byte[] sendYppStatementReport(Organisation organisation, PayoutStatementGenerationCommand payload) {
        // TODO remove this
        try {
            log.info("Generating YPP statement report for organisation: {}", organisation.getId());
            log.info("Input: {}", wrappedToJsonString(payload));
        } catch(Exception e) {
            log.error(e);
        }

        var ledgerCodeUtilitiesGroup = List.of("Utilities", "Electric", "Gas", "Internet", "Water");
        var ledgerCodeGroupings = List.of(
                Pair.of("Lease income retained by YPP", List.of("Management Fees")),
                Pair.of("Building Operational Costs", List.of("Appliances", "Cleaning", "Furniture", "Waste Removals", "White Goods")),
                Pair.of("Utilities", ledgerCodeUtilitiesGroup),
                Pair.of("Insurance", List.of("Insurance")),
                Pair.of("Move-Outs", List.of("Move-Outs")),
                Pair.of("Professional Fees", List.of("Professional Fees")),
                Pair.of("SDLT", List.of("SDLT")),
                Pair.of("Miscellaneous", List.of("Miscellaneous")),
                Pair.of("ICL Projects", List.of("ICL Projects"))
        );

        var statements = reportRepository.findStatements(payload.getOrganisationId());
        var landlords = userService.findUsers(payload.getLandlordIds());
        var unwantedExpenseLedgerCodes = getJournalUnwantedExpenseLedgerCodes(organisation);
        var lineItems = invoiceService.getLineItems(payload.getLineItemIds())
                .stream()
                .filter(lineItem -> Objects.isNull(lineItem.getAccountCode()) || !unwantedExpenseLedgerCodes.contains(lineItem.getAccountCode()))
                .collect(toUnmodifiableList());
        var properties = propertyService.findPropertiesWithOrganisation(payload.getOrganisationId()).stream().filter(property -> Objects.nonNull(property.getReference())).collect(toUnmodifiableList());

        var currency = organisation.getCurrency();
        var currencySymbol = Currency.getInstance(currency).getSymbol();
        var dateFormatter = new SimpleDateFormat("dd/MM/yyyy");

        var relatedTrackingCodes = lineItems.stream().flatMap(lineItem -> Optional.ofNullable(lineItem.getTrackingName()).stream()).collect(toUnmodifiableSet());
        var relatedProperties = properties.stream().filter(property -> relatedTrackingCodes.contains(property.getReference())).collect(toUnmodifiableList());
        var relatedParentPropertyIds = relatedProperties.stream().flatMap(property -> Optional.ofNullable(property.getParentPropertyId()).stream()).collect(toUnmodifiableSet());
        var relatedParentProperties = propertyService.getParentProperties(relatedParentPropertyIds);

        var propertiesGroupByParentProperty = relatedProperties.stream().filter(property -> Objects.nonNull(property.getParentPropertyId())).collect(groupingBy(Property::getParentPropertyId));

        var map = new HashMap<String, Object>();
        map.put("currency", currencySymbol);

        var propertyIdIncome = relatedProperties.stream().map(property -> {
            var propertyLineItems = lineItems.stream().filter(Invoice.LineItem::isIncome).filter(lineItem -> property.getReference().equals(lineItem.getTrackingName())).collect(toUnmodifiableList());
            var propertyIncome = propertyLineItems.stream().map(tenancyService::getLineItemPaidAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            return Pair.of(property, propertyIncome);
        }).collect(toUnmodifiableList());

        var propertyExpenseLineItems = properties.stream().map(property -> {
            var propertyLineItems = lineItems.stream()
                    .filter(lineItem -> !lineItem.isIncome())
                    .filter(lineItem -> Objects.nonNull(lineItem.getAccountCode()))
                    .filter(lineItem -> property.getReference().equals(lineItem.getTrackingName()))
                    .collect(toUnmodifiableList());

            return Pair.of(property, propertyLineItems);
        }).collect(toUnmodifiableList());
        var accountCodeExpense = lineItems.stream().flatMap(lineItem -> {
            if (Objects.isNull(lineItem.getAccountCode()) || Objects.isNull(lineItem.getTrackingName()) || lineItem.isIncome() ||
                    properties.stream().noneMatch(property -> property.getReference().equals(lineItem.getTrackingName()))
            ) {
                return Stream.empty();
            }

            return Stream.of(Pair.of(lineItem.getAccountCode(), tenancyService.getLineItemAmount(lineItem)));
        }).collect(groupingBy(Pair::getLeft, reducing(BigDecimal.ZERO, Pair::getRight, BigDecimal::add)));

        var numberOfParentPropertiesWithIncome = propertiesGroupByParentProperty.entrySet().stream().filter(entry -> {
            var childProperties = entry.getValue();
            var parentPropertyIncome = childProperties.stream().map(property -> propertyIdIncome.stream()
                            .filter(pair -> pair.getLeft().getId().equals(property.getId()))
                            .map(Pair::getRight)
                            .findFirst()
                            .orElse(BigDecimal.ZERO))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            return parentPropertyIncome.compareTo(BigDecimal.ZERO) != 0;
        }).count();
        var totalIncome = propertyIdIncome.stream().map(Pair::getRight).reduce(BigDecimal.ZERO, BigDecimal::add);

        var parentPropertyNameBalance = propertiesGroupByParentProperty.entrySet().stream().flatMap(entry -> {
            var parentPropertyId = entry.getKey();
            var childProperties = entry.getValue();
            var parentProperty = relatedParentProperties.stream().filter(pp -> pp.getId().equals(parentPropertyId))
                    .findFirst()
                    .orElseThrow(() -> new RuntimeException("Parent property not found:" + parentPropertyId));

            var parentPropertyAddress = (Objects.nonNull(parentProperty.getAddressLine1()) ? parentProperty.getAddressLine1() : "") +
                    ", " +
                    (Objects.nonNull(parentProperty.getCity()) ? parentProperty.getCity() : "");
            var parentPropertyName = "Lease Income - " + parentPropertyAddress + " - Receivable by YPP";
            var parentPropertyIncome = childProperties.stream().map(property -> propertyIdIncome.stream()
                            .filter(pair -> pair.getLeft().getId().equals(property.getId()))
                            .map(Pair::getRight)
                            .findFirst()
                            .orElse(BigDecimal.ZERO))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            String percentage = "";

            if (numberOfParentPropertiesWithIncome > 1 && parentPropertyIncome.compareTo(BigDecimal.ZERO) != 0) {
                percentage = formatValue(reportGetSafePercentage(parentPropertyIncome, totalIncome)) + "%";
            }
            var item = Map.of("description", parentPropertyName, "percentage", percentage, "amount", formatValueWithCurrency(currencySymbol, parentPropertyIncome));

            return parentPropertyIncome.compareTo(BigDecimal.ZERO) == 0 ? Stream.empty() : Stream.of(item);
        }).collect(toUnmodifiableList());

        map.put("parentPropertyNameBalance", parentPropertyNameBalance);
        map.put("totalIncome", formatValueWithCurrency(currencySymbol, totalIncome));
        //map.put("StatementNumber", "" + (statements.size() + 1)); // TODO
        map.put("workspaceLogo", getLogo(organisation.getLogo()));
        map.put("date", dateFormatter.format(Date.from(Instant.parse(payload.getPeriodEndDate()).atZone(UTC).plusMonths(1).with(TemporalAdjusters.lastDayOfMonth()).toInstant())));
        map.put("period", dateFormatter.format(Date.from(Instant.parse(payload.getPeriodStartDate()))) + " - " + dateFormatter.format(Date.from(Instant.parse(payload.getPeriodEndDate()))));

        var primaryLandlordId = properties.stream().flatMap(property -> Optional.ofNullable(property.getPrimaryLandlordId()).stream())
                .filter(primaryLandlordI -> payload.getLandlordIds().contains(primaryLandlordI))
                .findFirst()
                .orElse(landlords.get(0).getId());
        var landlordBills = invoiceService.findOrganisationLandlordBills(organisation.getId())
                .stream()
                .filter(LandlordBill::isApproved)
                .filter(landlordBill -> Objects.nonNull(landlordBill.getLandlordId()))
                .filter(landlordBill -> payload.getLandlordIds().contains(landlordBill.getLandlordId()))
                .collect(toUnmodifiableList());
        map.put("previousLandlordBills", landlordBills.stream().map(bill -> {
            var landlord = landlords.stream().filter(ll -> ll.getId().equals(bill.getLandlordId())).findFirst().orElseThrow();
            var description = dateFormatter.format(Date.from(Instant.parse(bill.getDateRaised()))) + " - Added to " + formatUser(landlord) + " - Receivable by YPP";

            return Map.of("description", description, "amount", formatValueWithCurrency(currencySymbol, new BigDecimal(bill.getBillAmount()).movePointLeft(2)));
        }).collect(toUnmodifiableList()));

        var primaryLandlord = landlords.stream().filter(l -> l.getId().equals(primaryLandlordId)).findFirst().orElseThrow(() -> new RuntimeException("Landlord not found:" + primaryLandlordId));
        var landlordAddress = userService.findUserPostalAddress(primaryLandlordId);
        map.put("landlordCompanyName", Objects.nonNull(primaryLandlord.getCompanyName()) ? primaryLandlord.getCompanyName() : "");
        map.put("landlordStreet", Optional.ofNullable(landlordAddress).map(User.UserAddress::getAddressLine1).orElse(""));
        map.put("landlordCity", Optional.ofNullable(landlordAddress).map(User.UserAddress::getCity).orElse(""));
        map.put("landlordState", Optional.ofNullable(landlordAddress).map(User.UserAddress::getState).orElse(""));
        map.put("landlordPostcode", Optional.ofNullable(landlordAddress).map(User.UserAddress::getPostcode).orElse(""));

        var managementFeeLedgerCode = getLedgerCodes(organisation.getLedgerCodes(),
                ledgerCode -> ledgerCode.getName().equals("Management Fees")).stream().findAny()
                .orElse("326");
        var utilitiesLedgerCodes = getLedgerCodes(organisation.getLedgerCodes(), ledgerCode ->
                Objects.nonNull(ledgerCode.getName()) && ledgerCodeUtilitiesGroup.contains(ledgerCode.getName()));
        var managementFeeTotal = accountCodeExpense.getOrDefault(managementFeeLedgerCode, BigDecimal.ZERO);
        var utilitiesTotal = utilitiesLedgerCodes.stream().map(code -> accountCodeExpense.getOrDefault(code, BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        var expenseTotal = accountCodeExpense.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        var profitExclUtilities = totalIncome.subtract(expenseTotal).add(utilitiesTotal);
        var profit = totalIncome.subtract(expenseTotal);
        var expenseWithoutManagementUtilities = expenseTotal.subtract(managementFeeTotal).subtract(utilitiesTotal);

        map.put("managementFeeExpenseTotal", formatValueWithCurrency(currencySymbol, managementFeeTotal));
        map.put("managementFeePercentage", formatValue(reportGetSafePercentage(managementFeeTotal, totalIncome)) + "%");

        map.put("expensesExcludingUtilitiesManagementTotal", formatValueWithCurrency(currencySymbol, expenseWithoutManagementUtilities));
        map.put("expensesExcludingUtilitiesManagementPercentage", formatValue(reportGetSafePercentage(expenseWithoutManagementUtilities, totalIncome)) + "%");

        map.put("expensesExcludingUtilitiesTotal", formatValueWithCurrency(currencySymbol, expenseTotal.subtract(utilitiesTotal)));
        map.put("expensesExcludingUtilitiesPercentage", formatValue(reportGetSafePercentage(expenseTotal.subtract(utilitiesTotal), totalIncome)) + "%");

        map.put("profitExcludingUtilitiesTotal", formatValueWithCurrency(currencySymbol, profitExclUtilities));
        map.put("profitExcludingUtilitiesPercentage", formatValue(reportGetSafePercentage(profitExclUtilities, totalIncome)) + "%");

        map.put("UtilitiesTotal", formatValueWithCurrency(currencySymbol, utilitiesTotal));
        map.put("UtilitiesPercentage", formatValue(reportGetSafePercentage(utilitiesTotal, totalIncome)) + "%");

        map.put("profitTotal", formatValueWithCurrency(currencySymbol, profit));
        map.put("profitPercentage", formatValue(reportGetSafePercentage(profit, totalIncome)) + "%");

        map.put("incomePerProperty", propertyIdIncome.stream().flatMap(pair -> {
            var property = pair.getLeft();
            var hasInArrearsIncome = lineItems.stream()
                    .filter(lineItem -> property.getReference().equals(lineItem.getTrackingName()))
                    .filter(Invoice.LineItem::isIncome)
                    .anyMatch(lineItem -> !tenancyService.getLineItemPaidAmount(lineItem).equals(tenancyService.getLineItemAmount(lineItem)));
            var propertyDescription = (Objects.nonNull(property.getAddressLine1()) ? property.getAddressLine1() : "") + (hasInArrearsIncome ? " - Tenant in arrears" : "");

            var item = Map.of("description", propertyDescription, "amount", formatValueWithCurrency(currencySymbol, pair.getRight()));

            return (hasInArrearsIncome || pair.getRight().compareTo(BigDecimal.ZERO) > 0) ? Stream.of(item): Stream.of();
        }).collect(toUnmodifiableList()));

        map.put("accountCodeExpenses", formatExpenseAccountCodes(organisation, ledgerCodeGroupings, accountCodeExpense));
        map.put("expenseItemBreakdown", propertyExpenseLineItems.stream()
                .flatMap(entry -> entry.getValue().stream().map(lineItem -> Pair.of(entry.getKey(), lineItem)))
                .sorted((item1, item2) -> {
                    if (managementFeeLedgerCode.equals(item1.getRight().getAccountCode())) {
                        return -1;
                    }
                    return 1;
                })
                .map(entry -> {
                    var property = entry.getLeft();
                    var lineItem = entry.getRight();
                    var parentPropertyDescription = Optional.ofNullable(property.getParentPropertyId())
                            .flatMap(parentPropertyId -> relatedParentProperties.stream().filter(pp -> pp.getId().equals(parentPropertyId)).findFirst())
                            .flatMap(parentProperty -> Optional.ofNullable(parentProperty.getAddressLine1()));

                    var expenseDescription = Stream.of(parentPropertyDescription, Optional.ofNullable(property.getAddressLine1()), Optional.ofNullable(lineItem.getDescription()))
                                .flatMap(Optional::stream)
                                .collect(joining(" - "));

                    return Map.of("description", expenseDescription, "amount", formatValueWithCurrency(currencySymbol, tenancyService.getLineItemAmount(lineItem)));
                }).collect(toUnmodifiableList())
        );
        map.put("expenseTotal", formatValueWithCurrency(currencySymbol, expenseTotal));

        var data = PDFUtils.merge(List.of(jasperClient.generatePayoutStatementYPPReport(map), jasperClient.generatePayoutStatementYPPIncomeReport(map), jasperClient.generatePayoutStatementYPPExpenseReport(map)));

        var id = UUID.randomUUID().toString();
        var key = join("/", "public/generated_reports", join(".", id, "pdf"));
        var statement = Statement
                .builder()
                .id(id)
                .createdAt(Instant.now())
                .reference(join("-", "S", String.valueOf(statements.size() + 1)))
                .organisationId(payload.getOrganisationId())
                .relatedProperties(relatedProperties.stream().map(Property::getId).collect(toUnmodifiableSet()))
                .relatedParentProperties(relatedParentPropertyIds)
                .relatedLandlords(payload.getLandlordIds())
                .landlordBillId(payload.getLandlordBillId())
                .type("PROPERTY")
                .from(Instant.parse(payload.getPeriodStartDate()))
                .to(Instant.parse(payload.getPeriodEndDate()))
                .fileKey(key)
                .sent(false)
                .approved(true)
                .build();

        s3Client.uploadPdf(config.getRentancyDocumentUploads(), key, new ByteArrayInputStream(data), null);
        reportRepository.saveStatement(statement);

        return data;
    }

    @Override
    public byte[] sendStatementReport(PayoutStatementGenerationCommand payload) {
        // Potential TODOS
        // interact with report table??
        var organisation = organisationService.getOrganisation(payload.getOrganisationId());

        if (YPP_STATEMENT_VERSION.equals(organisation.getPayoutStatementVersion())) {
            return sendYppStatementReport(organisation, payload);
        }

        var statements = reportRepository.findStatements(payload.getOrganisationId());
        var landlords = userService.findUsers(payload.getLandlordIds());
        var lineItems = invoiceService.getLineItems(payload.getLineItemIds());
        var properties = propertyService.findPropertiesWithOrganisation(payload.getOrganisationId());

        var currency = organisation.getCurrency();
        var relatedTrackingCodes = lineItems.stream().flatMap(lineItem -> Optional.ofNullable(lineItem.getTrackingName()).stream()).collect(toUnmodifiableSet());
        var relatedProperties = properties.stream().filter(property -> property.getReference() != null && relatedTrackingCodes.contains(property.getReference())).collect(toUnmodifiableList());
        var relatedParentPropertyIds = relatedProperties.stream().flatMap(property -> Optional.ofNullable(property.getParentPropertyId()).stream()).collect(toUnmodifiableSet());
        var relatedParentProperties = propertyService.getParentProperties(relatedParentPropertyIds);

        var propertiesGroupByParentProperty = relatedProperties.stream().filter(property -> Objects.nonNull(property.getParentPropertyId())).collect(groupingBy(Property::getParentPropertyId));
        var propertiesWithoutParentProperty = relatedProperties.stream().filter(property -> Objects.isNull(property.getParentPropertyId())).map(Set::of).collect(toUnmodifiableList());

        var groupedProperties = Stream.concat(
                        propertiesGroupByParentProperty.values().stream().map(HashSet::new),
                        propertiesWithoutParentProperty.stream())
                .collect(toUnmodifiableList());

        var ledgerCodeNameMap = Optional.ofNullable(organisation.getLedgerCodes())
                .orElse(List.of())
                .stream()
                .filter(ledgerCode -> Objects.nonNull(ledgerCode.getCode()) && Objects.nonNull(ledgerCode.getDisplayName()))
                .collect(toUnmodifiableMap(Organisation.LedgerCode::getCode, Organisation.LedgerCode::getDisplayName, (a1, a2) -> a1));

        var landlordAmount = new HashMap<String, BigDecimal>();

        var groupedPropertySummarySection = groupedProperties
                .stream()
                .map(propertyGroup -> {
                    var firstProperty = propertyGroup.stream()
                            .findFirst()
                            .orElseThrow();
                    var parentProperty = Optional.ofNullable(firstProperty.getParentPropertyId())
                            .flatMap(parentPropertyId -> relatedParentProperties.stream().filter(pp -> pp.getId().equals(parentPropertyId)).findFirst());
                    var address = parentProperty.map(this::getParentPropertyDescription).orElse(getPropertyDescription(firstProperty));

                    var relatedLineItems = lineItems.stream()
                            .filter(lineItem -> lineItem.getTrackingName().equals(firstProperty.getReference()))
                            .map(lineItem -> LandlordPayoutStatement.LandlordPayoutStatementItem.builder()
                                    .description(Optional.ofNullable(lineItem.getDescription()).orElse(DEFAULT_VALUE))
                                    .ledgerCodeName(Optional.ofNullable(lineItem.getAccountCode()).map(ledgerCodeNameMap::get).orElse(DEFAULT_VALUE))
                                    .currency(currency)
                                    .amount(tenancyService.getLineItemAmount(lineItem))
                                    .income(lineItem.isIncome())
                                    .build())
                            .sorted((item1, item2) -> {
                                if (item1.isIncome() && item2.isIncome()) {
                                    return 0;
                                } else if (item1.isIncome()) {
                                    return -1;
                                } else {
                                    return 1;
                                }
                            })
                            .collect(toUnmodifiableList());

                    var total = relatedLineItems.stream().map(item -> item.isIncome() ? item.getAmount() : item.getAmount().multiply(new BigDecimal(-1))).reduce(BigDecimal.ZERO, BigDecimal::add);

                    landlords.forEach(landlord -> {
                        var oldBalance = landlordAmount.getOrDefault(landlord.getId(), BigDecimal.ZERO);
                        var additionalAmount = getLandlordBalanceShare(landlord, total, parentProperty.orElse(null), firstProperty);
                        landlordAmount.put(landlord.getId(), oldBalance.add(additionalAmount));
                    });

                    return LandlordPayoutStatement.PropertyParentPropertySection.builder()
                            .parentPropertySection(parentProperty.isPresent())
                            .address(address)
                            .items(relatedLineItems)
                            .total(total)
                            .minimumBalance(firstProperty.getMinimumBalance())
                            .currency(currency)
                            .build();
                })
                .sorted((section1, section2) -> {
                    var isPP1 = section1.isParentPropertySection();
                    var isPP2 = section2.isParentPropertySection();
                    if ((isPP1 && isPP2) || (!isPP1 && !isPP2)) {
                        return section1.getAddress().compareTo(section2.getAddress());
                    }

                    if (isPP1) {
                        return -1;
                    }

                    return 1;
                })
                .collect(toUnmodifiableList());

        var allSectionItems = groupedPropertySummarySection
                .stream()
                .map(LandlordPayoutStatement.PropertyParentPropertySection::getItems)
                .flatMap(Collection::stream)
                .collect(groupingBy(LandlordPayoutStatement.LandlordPayoutStatementItem::isIncome));

        var managementFeeLedgerCodeName = "Management Fees";
        var incomeSection = getIncomeExpensePercentageSection(allSectionItems.getOrDefault(Boolean.TRUE, List.of()));
        // Management fee is excluded from expenses
        var expenseSection = getIncomeExpensePercentageSection(allSectionItems.getOrDefault(Boolean.FALSE, List.of()));
        var managementFeeAmount = expenseSection.getItems().stream().filter(item -> item.getItemName().equals(managementFeeLedgerCodeName)).map(LandlordPayoutStatement.LandlordPayoutStatementPercentageItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        // For 'payoutSplitOwnershipReport' we need to exclude management fee
        var payoutExpenseSection = expenseSection.builder()
                .items(expenseSection.getItems().stream().filter(item -> !item.getItemName().equals(managementFeeLedgerCodeName)).collect(toUnmodifiableList()))
                .total(expenseSection.getTotal().subtract(managementFeeAmount))
                .build();

        var payoutAmount = incomeSection.getTotal().subtract(expenseSection.getTotal());

        var landlordPayoutPercentageSection = landlords.stream()
                .map(landlord -> {
                    var percentageItem = payload.getLandlordPercentages().stream().filter(item -> item.getLandlordId().equals(landlord.getId())).findAny();
                    var percentage = landlordAmount.getOrDefault(landlord.getId(), BigDecimal.ZERO).divide(payoutAmount.equals(BigDecimal.ZERO) ? BigDecimal.ONE : payoutAmount, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                    return LandlordPayoutStatement.LandlordPayoutStatementPercentageItem.builder()
                            .itemName(formatUser(landlord))
                            .percentage("" + formatValue(percentageItem.map(landlordPercentageItem -> landlordPercentageItem.getPercentage().setScale(0, RoundingMode.HALF_UP)).orElse(percentage)) + "%")
                            .currency(currency)
                            .amount(percentageItem.map(PayoutStatementGenerationCommand.LandlordPercentageItem::getAmount).orElse(landlordAmount.getOrDefault(landlord.getId(), BigDecimal.ZERO)))
                            .build();
                }).collect(toUnmodifiableList());

        var dateFormatter = new SimpleDateFormat("dd/MM/yyyy");

        var payoutStatement = LandlordPayoutStatement.builder()
                .organisationLogo(getLogo(organisation.getLogo()))
                .organisationName(organisation.getName())
                .date(dateFormatter.format(Date.from(Instant.now())))
                .period(dateFormatter.format(Date.from(Instant.parse(payload.getPeriodStartDate()))) + " - " + dateFormatter.format(Date.from(Instant.parse(payload.getPeriodEndDate()))))
                .landlordsDescription(landlords.stream().map(Utils::formatUser).collect(joining(DELIMITER)))
                .parentPropertyDescriptions(groupedPropertySummarySection.stream().filter(LandlordPayoutStatement.PropertyParentPropertySection::isParentPropertySection).map(LandlordPayoutStatement.PropertyParentPropertySection::getAddress).collect(joining(DELIMITER)))
                .propertyDescriptions(relatedProperties.stream().map(this::getPropertyDescription).collect(joining(DELIMITER)))
                .incomePercentageSection(incomeSection)
                .expensePercentageSection(expenseSection)
                .splitOwnershipExpenseSection(payoutExpenseSection)
                .payoutAmount(payoutAmount)
                .landlordPayoutPercentageSection(landlordPayoutPercentageSection)
                .groupedPropertySummarySection(groupedPropertySummarySection)
                .managementFeeAmount(managementFeeAmount)
                .build();

        var currencySymbol = Currency.getInstance(currency).getSymbol();

        var map = new HashMap<String, Object>();
        map.put("workspaceLogo", payoutStatement.getOrganisationLogo());
        map.put("workspaceName", payoutStatement.getOrganisationName());
        map.put("date", payoutStatement.getDate());
        map.put("period", payoutStatement.getPeriod());
        map.put("landlordsDescription", payoutStatement.getLandlordsDescription());
        map.put("parentPropertyDescriptions", payoutStatement.getParentPropertyDescriptions());
        map.put("propertyDescriptions", payoutStatement.getPropertyDescriptions());
        map.put("currency", currencySymbol);
        map.put("payoutAmount", formatValue(payoutStatement.getPayoutAmount()));
        map.put("expenseTotal", formatValue(payoutStatement.getExpensePercentageSection().getTotal()));

        // For 'payoutSplitOwnershipReport'
        map.put("incomeTotal", formatValue(payoutStatement.getIncomePercentageSection().getTotal()));
        map.put("managementFee", formatValue(payoutStatement.getManagementFeeAmount()));
        map.put("SplitExpenseTotal", formatValue(payoutStatement.getSplitOwnershipExpenseSection().getTotal()));
        var amount = payoutStatement.getIncomePercentageSection().getTotal().add(payoutStatement.getManagementFeeAmount());
        var splitAmount = payoutStatement.getSplitOwnershipExpenseSection().getTotal().add(payoutStatement.getPayoutAmount());
        log.info("Amount - {}", splitAmount.setScale(0, RoundingMode.HALF_DOWN).equals(BigDecimal.ZERO) ? BigDecimal.ONE : splitAmount);
        map.put("incomeTotalPercentage", formatValue(payoutStatement.getIncomePercentageSection().getTotal().multiply(new BigDecimal(100)).divide(amount.setScale(0, RoundingMode.HALF_DOWN).equals(BigDecimal.ZERO) ? BigDecimal.ONE : amount, RoundingMode.HALF_DOWN)));
        map.put("ManagementFeePercentage", formatValue(payoutStatement.getManagementFeeAmount().multiply(new BigDecimal(100)).divide(amount.setScale(0, RoundingMode.HALF_DOWN).equals(BigDecimal.ZERO) ? BigDecimal.ONE : amount, RoundingMode.HALF_DOWN)));
        map.put("expensesTotalPercentage", formatValue(payoutStatement.getSplitOwnershipExpenseSection().getTotal().multiply(new BigDecimal(100)).divide(splitAmount.setScale(0, RoundingMode.HALF_DOWN).equals(BigDecimal.ZERO) ? BigDecimal.ONE : splitAmount, RoundingMode.HALF_DOWN)));
        map.put("netTotalPercentage", formatValue(payoutStatement.getPayoutAmount().multiply(new BigDecimal(100)).divide(splitAmount.setScale(0, RoundingMode.HALF_DOWN).equals(BigDecimal.ZERO) ? BigDecimal.ONE : splitAmount, RoundingMode.HALF_DOWN)));
        map.put("splitExpensePercentageSection", payoutStatement.getSplitOwnershipExpenseSection().getItems().stream()
                .filter(item -> item.getAmount().compareTo(BigDecimal.ZERO) != 0)
                .map((sectionItem) -> {
                    var item = new HashMap<String, Object>();
                    item.put("itemName", sectionItem.getItemName());
                    item.put("percentage", sectionItem.getPercentage());
                    item.put("currency", Currency.getInstance(sectionItem.getCurrency()).getSymbol());
                    item.put("amount", formatValue(sectionItem.getAmount()));

                    return item;
                }).collect(toUnmodifiableList()));

        map.put("incomePercentageSection", payoutStatement.getIncomePercentageSection().getItems().stream().map(sectionItem -> {
            var item = new HashMap<String, Object>();
            item.put("itemName", sectionItem.getItemName());
            item.put("percentage", sectionItem.getPercentage());
            item.put("currency", Currency.getInstance(sectionItem.getCurrency()).getSymbol());
            item.put("amount", formatValue(sectionItem.getAmount()));

            return item;
        }).collect(toUnmodifiableList()));
        map.put("expensePercentageSection", payoutStatement.getExpensePercentageSection().getItems().stream().map(sectionItem -> {
            var item = new HashMap<String, Object>();
            item.put("itemName", sectionItem.getItemName());
            item.put("percentage", sectionItem.getPercentage());
            item.put("currency", Currency.getInstance(sectionItem.getCurrency()).getSymbol());
            item.put("amount", formatValue(sectionItem.getAmount()));

            return item;
        }).collect(toUnmodifiableList()));
        map.put("landlordPayoutPercentageSection", payoutStatement.getLandlordPayoutPercentageSection().stream().map(sectionItem -> {
            var item = new HashMap<String, Object>();
            item.put("itemName", sectionItem.getItemName());
            item.put("percentage", sectionItem.getPercentage());
            item.put("currency", Currency.getInstance(sectionItem.getCurrency()).getSymbol());
            item.put("amount", formatValue(sectionItem.getAmount()));

            return item;
        }).collect(toUnmodifiableList()));
        map.put("propertySummaryBalance", payoutStatement.getGroupedPropertySummarySection().stream().map(sectionItem -> {
            var item = new HashMap<String, Object>();
            item.put("itemName", sectionItem.getAddress());
            item.put("amount", formatValue(sectionItem.getTotal()));
            item.put("currency", Currency.getInstance(sectionItem.getCurrency()).getSymbol());
            return item;
        }).collect(toUnmodifiableList()));

        var defaultReportPage2Map = new HashMap<String, Object>();
        defaultReportPage2Map.put("groupedPropertySummarySection", payoutStatement.getGroupedPropertySummarySection().stream().map(sectionItem -> {
            var item = new HashMap<String, Object>();
            item.put("address", sectionItem.getAddress());
            item.put("total", formatValue(sectionItem.getTotal()));
            item.put("float", formatValue(sectionItem.getMinimumBalance()));
            item.put("currency", Currency.getInstance(sectionItem.getCurrency()).getSymbol());
            item.put("items", Map.of(
                    "headerData", Map.of(
                            "total", formatValue(sectionItem.getTotal()),
                            "float", formatValue(sectionItem.getMinimumBalance()),
                            "currency", Currency.getInstance(sectionItem.getCurrency()).getSymbol()
                    ),
                    "totalItems", sectionItem.getItems().stream().map(sectionSubItem -> {
                        var subItem = new HashMap<String, Object>();
                        subItem.put("description", sectionSubItem.getDescription());
                        subItem.put("ledgerCodeName", sectionSubItem.getLedgerCodeName());
                        subItem.put("currency", Currency.getInstance(sectionSubItem.getCurrency()).getSymbol());
                        subItem.put("amount", formatValue(sectionSubItem.isIncome() ? sectionSubItem.getAmount() : sectionSubItem.getAmount().multiply(new BigDecimal(-1))));

                        return subItem;
                    }).collect(toUnmodifiableList())
            ));

            return item;
        }).collect(toUnmodifiableList()));

        var splitOwnershipPage2Map = new HashMap<String, Object>();
        splitOwnershipPage2Map.put("groupedPropertySummarySection", payoutStatement.getGroupedPropertySummarySection().stream()
                .filter(i -> i.getItems().stream().anyMatch(LandlordPayoutStatement.LandlordPayoutStatementItem::isIncome))
                .map(sectionItem -> {
                    var item = new HashMap<String, Object>();
                    item.put("address", sectionItem.getAddress());
                    item.put("total", formatValue(sectionItem.getTotal()));
                    item.put("float", formatValue(sectionItem.getMinimumBalance()));
                    item.put("currency", Currency.getInstance(sectionItem.getCurrency()).getSymbol());
                    item.put("items", sectionItem.getItems().stream()
                            .filter(LandlordPayoutStatement.LandlordPayoutStatementItem::isIncome)
                            .map(sectionSubItem -> {
                                var subItem = new HashMap<String, Object>();
                                subItem.put("description", sectionSubItem.getDescription());
                                subItem.put("ledgerCodeName", sectionSubItem.getLedgerCodeName());
                                subItem.put("currency", Currency.getInstance(sectionSubItem.getCurrency()).getSymbol());
                                subItem.put("amount", formatValue(sectionSubItem.getAmount()));

                                return subItem;
                            }).collect(toUnmodifiableList()));

                    return item;
                }).collect(toUnmodifiableList()));

        log.info("Report data - {}", wrappedToJsonString(map));

        byte[] data;

        switch (organisation.getPayoutStatementVersion()) {
            case "SPLIT_OWNERSHIP":
                data = PDFUtils.merge(List.of(jasperClient.generateSplitPayoutStatementReport(map), jasperClient.generatePage2PayoutStatementReport(splitOwnershipPage2Map)));
                break;
            case "DEFAULT":
                data = PDFUtils.merge(List.of(jasperClient.generatePayoutStatementReport(map), jasperClient.generatePage2PayoutStatementReport(defaultReportPage2Map)));
                break;
            default:
                throw new RuntimeException("Unknown payout statement version:" + organisation.getPayoutStatementVersion() + ",id:" + organisation.getId());
        }

        var id = UUID.randomUUID().toString();
        var key = join("/", "public/generated_reports", join(".", id, "pdf"));
        Statement statement = Statement
                .builder()
                .id(id)
                .createdAt(Instant.now())
                .reference(join("-", "S", String.valueOf(statements.size() + 1)))
                .organisationId(payload.getOrganisationId())
                .relatedProperties(relatedProperties.stream().map(Property::getId).collect(toUnmodifiableSet()))
                .relatedParentProperties(relatedParentPropertyIds)
                .relatedLandlords(payload.getLandlordIds())
                .landlordBillId(payload.getLandlordBillId())
                .type("PROPERTY")
                .from(Instant.parse(payload.getPeriodStartDate()))
                .to(Instant.parse(payload.getPeriodEndDate()))
                .fileKey(key)
                .sent(false)
                .approved(true)
                .build();

        s3Client.uploadPdf(config.getRentancyDocumentUploads(), key, new ByteArrayInputStream(data), null);
        reportRepository.saveStatement(statement);

        return data;
    }

    @Override
    public String generatePropertyExtractReport(String propertyId) {
        var property = propertyService.getProperty(propertyId);
        var organisation = organisationService.getOrganisation(property.getOrganisation());
        var contact = Optional.ofNullable(property.getLandlords()).map(l -> l.stream().findAny().orElse(null)).map(l -> userService.findUser(l, false)).orElse(null);
        var utilities = property.getUtilities().stream().filter(Objects::nonNull).map(Property.Utility::getName).collect(toList());
        var amenities = property.getAmenities().stream().filter(Objects::nonNull).filter(u -> !u.isBlank())
                .map(u -> findUAmenityLabel(u)).collect(toUnmodifiableList());
        var images = property.getImages().stream().map(image -> {
            try {
                return toApiResponse(downloadLogo(image).readAllBytes());
            } catch (Exception e) {
                log.error("Failed to download image", e);
                return null;
            }
        }).filter(Objects::nonNull).collect(toUnmodifiableList());
        var floorPlans = property.getFloorPlans().stream().map(floorPlan -> {
            try {
                return toApiResponse(downloadLogo(floorPlan).readAllBytes());
            } catch (Exception e) {
                log.error("Failed to download floorPlan", e);
                return null;
            }
        }).filter(Objects::nonNull).collect(toUnmodifiableList());
        var secondReportImages = List.of(
                "propertyFirstImage",
                "propertySecondImage"
        );
        var thirdReportImages = List.of(
                "propertyThirdImage",
                "propertyFourthImage",
                "propertyFifthImage",
                "propertySixthImage"
        );
        var thirdReportFloorPlans = List.of(
                "floorPlanFirstImage",
                "floorPlanSecondImage"
        );
        var utilityKeys = List.of(
                "utilityItem1",
                "utilityItem2",
                "utilityItem3",
                "utilityItem4",
                "utilityItem5",
                "utilityItem6"
        );
        var amenityKeys = List.of(
                "amenityItem1",
                "amenityItem2",
                "amenityItem3",
                "amenityItem4",
                "amenityItem5",
                "amenityItem6"
        );

        var reportStartPageParameters = new HashMap<String, Object>();
        var reportSecondPageParameters = new HashMap<String, Object>();
        var reportThirdPageParameters = new HashMap<String, Object>();

        reportStartPageParameters.put("workspaceLogo", getLogo(organisation.getLogo()));
        reportStartPageParameters.put("addressLine1", property.getAddressLine1());
        reportStartPageParameters.put("addressLine2", String.join(DELIMITER, property.getAddressLine2(), property.getCity(), property.getPostcode()));
        reportStartPageParameters.put("rentFrequency", formatEnumValue(property.getRentPeriod()));
        reportStartPageParameters.put("leaseTerms", formatEnumValue(property.getLeaseTerm()));
        reportStartPageParameters.put("furnished", formatEnumValue(property.getFurnished()));
        reportStartPageParameters.put("bedrooms", String.valueOf(property.getBedRooms()));
        reportStartPageParameters.put("bathrooms", String.valueOf(property.getBathRooms()));
        reportStartPageParameters.put("squaremetres", String.valueOf(property.getSqmt()));
        reportStartPageParameters.put("councilTaxBand", property.getCouncilTax());
        reportStartPageParameters.put("epcRating", property.getTaxEPCRating());
        reportStartPageParameters.put("certificateNumber", property.getCertificateNumber());
        reportStartPageParameters.put("propertyFirstImage", Optional.ofNullable(property.getCoverImage()).map(cover -> {
            try {
                return toApiResponse(downloadLogo(cover).readAllBytes());
            } catch (Exception e) {
                log.error("Failed to download image", e);
                return null;
            }
        }).orElse(null));
        reportStartPageParameters.put("expiryDate", Optional.ofNullable(property.getCertificateExpirationDate()).map(Utils::formatDate).orElse(null));
        reportStartPageParameters.put("monthlyRent", formatValue(BigDecimal.valueOf(property.getMonthlyRent()).movePointLeft(2)));
        reportStartPageParameters.put("securityDeposit", formatValue(BigDecimal.valueOf(property.getSecurityDeposit()).movePointLeft(2)));
        IntStream.range(0, utilities.size()).filter(i -> i < utilityKeys.size()).forEach(i -> {
            reportStartPageParameters.put(utilityKeys.get(i), utilities.get(i));
        });
        IntStream.range(0, amenities.size()).filter(i -> i < amenityKeys.size()).forEach(i -> {
            reportStartPageParameters.put(amenityKeys.get(i), amenities.get(i));
        });

        reportSecondPageParameters.put("summary", property.getSummary());
        reportSecondPageParameters.put("detailedDescription", property.getDescription());
        reportSecondPageParameters.put("companyAddress", property.getAddressLine1());
        if (contact != null && contact.getEmails() != null && !contact.getEmails().isEmpty()) {
            contact.getEmails().stream().findAny().ifPresent(email -> {
                reportSecondPageParameters.put("contactEmail", email.getEmail());
            });
        }
        if (contact != null && contact.getPhones() != null && !contact.getPhones().isEmpty()) {
            contact.getPhones().stream().findAny().ifPresent(phone -> {
                reportSecondPageParameters.put("contactPhoneNumber", phone.getPhone());
            });
        }
        IntStream.range(0, images.size()).filter(i -> i < secondReportImages.size()).forEach(i -> {
            reportSecondPageParameters.put(secondReportImages.get(i), images.get(i));
        });

        IntStream.range(2, images.size()).filter(i -> i < thirdReportImages.size()).forEach(i -> {
            reportThirdPageParameters.put(thirdReportImages.get(i), images.get(i));
        });
        IntStream.range(0, floorPlans.size()).filter(i -> i < thirdReportFloorPlans.size()).forEach(i -> {
            reportThirdPageParameters.put(thirdReportFloorPlans.get(i), floorPlans.get(i));
        });

        log.info("First page data - {}", reportStartPageParameters);
        log.info("Second page data - {}", reportSecondPageParameters);
        log.info("Third page data - {}", reportThirdPageParameters);

        var report = PDFUtils.merge(List.of(
                jasperClient.generatePropertyExtractReportPage1(reportStartPageParameters),
                jasperClient.generatePropertyExtractReportPage2(reportSecondPageParameters),
                jasperClient.generatePropertyExtractReportPage3(reportThirdPageParameters)
        ));

        var uploadBucket = config.getRentancyDocumentUploads();
        var generatedKey = s3Client.uploadPdf(config.getRentancyDocumentUploads(), report);

        return s3Client.getS3Url(uploadBucket, generatedKey);
    }

    @Override
    public byte[] generateStripeChargeInvoicePdf(String organisationId, String chargeId) {
        var organisation = organisationService.getOrganisation(organisationId);
        var charge = organisationService.getOrganisationStripeCharge(chargeId)
                .orElseThrow(() -> new RuntimeException("Couldn't find " + chargeId));

        if (!charge.getOrganisationId().equals(organisationId)) {
            throw new IllegalStateException(organisationId + " doesn't has access to:" + chargeId);
        }

        var currencySymbol = Currency.getInstance(config.getStripeChargeCurrency().toUpperCase()).getSymbol();
        var dateFormatter = new SimpleDateFormat("dd/MM/yyyy");

        var map = new HashMap<String, Object>();

        var vat = new BigDecimal(config.getStripeVatChargeRate()).movePointRight(2);
        var vatPercentage = vat.toString() + "%";
        var chargeDay = dateFormatter.format(Date.from(Instant.parse(charge.getCreatedAt())));

        map.put("invoiceNumber", charge.getNumber());
        map.put("chargeDay", chargeDay);
        map.put("organisationName", organisation.getName());
        map.put("organisationAddressLine1", Optional.ofNullable(organisation.getAddressLine1()).orElse(DEFAULT_VALUE));
        map.put("organisationAddressLine2", Optional.ofNullable(organisation.getAddressLine2()).orElse(DEFAULT_VALUE));
        map.put("organisationCity", Optional.ofNullable(organisation.getCity()).orElse(DEFAULT_VALUE));
        map.put("organisationCountry", Optional.ofNullable(organisation.getCountry()).orElse(DEFAULT_VALUE));
        map.put("organisationPostalCode", Optional.ofNullable(organisation.getPostcode()).orElse(DEFAULT_VALUE));
        map.put("organisationVatNumber", Optional.ofNullable(organisation.getCommissionBillVatNumber()).orElse(DEFAULT_VALUE));
        map.put("vatPercentage", vatPercentage);

        BigDecimal subtotal;
        if (charge.getChargeType().equals("MINIMUM_CHARGE")) {
            subtotal = new BigDecimal(config.getStripeMinimumCharge()).movePointLeft(2);
            map.put("lineItemDescription", "Minimum Monthly Subscription");
            map.put("unitQuantity", "1");
            map.put("lineItemUnitPrice", currencySymbol + new BigDecimal(config.getStripeMinimumCharge()).movePointLeft(2).toString());
        } else if (charge.getChargeType().equals("PER_PROPERTY_CHARGE")) {
            subtotal = new BigDecimal(config.getStripePropertyChargeRate()).multiply(new BigDecimal(charge.getPropertyCount())).movePointLeft(2);
            map.put("lineItemDescription", "Monthly Subscription");
            map.put("unitQuantity", "" + charge.getPropertyCount());
            map.put("lineItemUnitPrice", currencySymbol + new BigDecimal(config.getStripePropertyChargeRate()).movePointLeft(2).toString());
        } else {
            // Manual charge
            var paymentObject = wrappedDeserializePayload(charge.getData(), Map.class);
            subtotal = new BigDecimal(charge.getAmount()).movePointLeft(2).multiply(BigDecimal.ONE.subtract(vat));
            map.put("lineItemDescription", Optional.ofNullable(paymentObject.get("description")).orElse(DEFAULT_VALUE));
            map.put("unitQuantity", "1");
            map.put("lineItemUnitPrice", currencySymbol + subtotal.toString());
        }

        map.put("taxRate", vatPercentage);
        map.put("lineItemAmount", currencySymbol + subtotal.toString());
        map.put("subtotal", currencySymbol + subtotal.toString());
        map.put("vatAmount", currencySymbol + subtotal.multiply(new BigDecimal(config.getStripeVatChargeRate())));
        map.put("total", currencySymbol + new BigDecimal(charge.getAmount()).movePointLeft(2).toString());
        map.put("amountDue", map.get("total"));

        return jasperClient.generateStripeChargeInvoice(map);
    }

    private String findUtilityLabel(String utility) {
        var labelMap = Map.of(
                "waterBill", "Water Bill",
                "gasBill ", "Gas Bill ",
                "electricityBill", "Electricity Bill",
                "oilBill", "Oil Bill",
                "satelliteCable Bill ", "Satellite Cable Bill ",
                "internetBill", "Internet Bill",
                "allBills", "All Bills",
                "councilTax ", "Council Tax ",
                "TVLicense ", "TV License "
        );

        return labelMap.getOrDefault(utility, utility);
    }

    private String findUAmenityLabel(String amenity) {
        Map<String, String> labelMap = new HashMap<>();
        labelMap.put("fireplace", "Fireplace");
        labelMap.put("twinSizebed", "Twin-size bed");
        labelMap.put("gym", "Gym");
        labelMap.put("securitySystem", "Security system");
        labelMap.put("wheelChairAccessible", "Wheelchair accessible");
        labelMap.put("privateBalcony", "Private balcony");
        labelMap.put("dishWasher", "Dishwasher");
        labelMap.put("stove", "Stove");
        labelMap.put("kingSizeBed", "King-size bed");
        labelMap.put("toaster", "Toaster");
        labelMap.put("hairDryer", "Hair dryer");
        labelMap.put("smokeDetector", "Smoke detector");
        labelMap.put("firstAidKit", "First-aid kit");
        labelMap.put("storageSpace", "Storage space");
        labelMap.put("dryer", "Dryer");
        labelMap.put("parking", "Parking");
        labelMap.put("pool", "Pool");
        labelMap.put("petFriendly", "Pet-friendly");
        labelMap.put("elevator", "Elevator");
        labelMap.put("centralAirConditioning", "Central air conditioning");
        labelMap.put("microwave", "Microwave");
        labelMap.put("oven", "Oven");
        labelMap.put("sofaBed", "Sofa bed");
        labelMap.put("iron", "Iron");
        labelMap.put("vacuumCleaner", "Vacuum cleaner");
        labelMap.put("carbonMonoxideDetector", "Carbon monoxide detector");
        labelMap.put("luggageRack", "Luggage rack");
        labelMap.put("view", "View");
        labelMap.put("queenSizeBed", "Queen-size bed");
        labelMap.put("laundryFacilities", "Laundry facilities");
        labelMap.put("playground", "Playground");
        labelMap.put("smokeFree", "Smoke-free");
        labelMap.put("rooftopDeck", "Rooftop deck");
        labelMap.put("centralHeating", "Central heating");
        labelMap.put("refrigerator", "Refrigerator");
        labelMap.put("washer", "Washer");
        labelMap.put("coffeeMaker", "Coffee maker");
        labelMap.put("ironingBoard", "Ironing board");
        labelMap.put("airPurifier", "Air purifier");
        labelMap.put("fireExtinguisher", "Fire extinguisher");
        labelMap.put("closetSpace", "Closet space");
        labelMap.put("quietNeighborhood", "Quiet neighborhood");

        return labelMap.getOrDefault(amenity, amenity);
    }

    @Override
    public void sendStatementReport(String cognitoId, String statementId, boolean skip) {
        try {
            var statement = reportRepository.get(statementId);
            var currentUser = userService.findUserWithCognitoId(cognitoId);

            if (!skip) {
                log.info("Statement - {}, User - {}", statement, currentUser);

                var content = s3Client
                        .downloadFromLondon(config.getRentancyDocumentUploads(), statement.getFileKey())
                        .readAllBytes();
                var user = userService.findUser(statement.getClientId(), false);
                var property = propertyService.getProperty(statement.getPropertyId());
                var organisation = organisationService.getOrganisation(statement.getOrganisationId());
                var body = String.join(
                        " ",
                        "Please find attached your latest statement for property",
                        property.getAddressLine1(),
                        "for the period",
                        formatDate(statement.getFrom().toString()),
                        "to",
                        formatDate(statement.getTo().toString()),
                        ".\n",
                        organisation.getName());

                processReport(organisation.getId(), statement.getClientId(), content, Instant.now(), Map.of("client", formatUser(user)), "Property statement report", "PROPERTY_STATEMENT", body);
            }

            reportRepository.saveStatement(statement
                    .toBuilder()
                    .sent(true)
                    .sentBy(getUserInitials(formatUser(currentUser)))
                    .sentDate(Instant.now())
                    .build());

            if (statement.isApproved() && statement.isPayedOut() && statement.isBillsUpdated()) {
                var landlordBill = invoiceService.getLandlordBill(statement.getLandlordBillId());

                landlordBill.setStatus(LandlordBill.LandlordBillStatus.COMPLETED);

                invoiceService.updateLandlordBill(landlordBill);
            }
        } catch (Exception e) {
            throw new IllegalStateException("Failed to send statement - " + statementId, e);
        }
    }

    @Override
    public void sendStatementReport(String landlordBillItemId) {
        try {
            var statement = reportRepository.findLandlordBillStatements(landlordBillItemId)
                    .stream()
                    .sorted((o1, o2) -> o2.getCreatedAt().compareTo(o1.getCreatedAt()))
                    .findAny()
                    .orElseThrow(() -> new IllegalStateException("Statement not found - " + landlordBillItemId));

            log.info("Statement - {}", statement);

            var content = s3Client
                    .downloadFromLondon(config.getRentancyDocumentUploads(), statement.getFileKey())
                    .readAllBytes();
            var landlords = userService.findUsers(statement.getRelatedLandlords());
            var properties = propertyService.getProperties(statement.getRelatedProperties());
            var parentProperties = propertyService.getParentProperties(statement.getRelatedParentProperties());
            var organisation = organisationService.getOrganisation(statement.getOrganisationId());
            var body = String.join(
                    " ",
                    "Please find attached your latest statement for",
                    String.join("",
                            parentProperties.stream().map(ParentProperty::getAddressLine1).collect(joining(", ")),
                            properties.map(Property::getAddressLine1).collect(joining(", "))
                    ),
                    "for the period",
                    formatDate(statement.getFrom().toString()),
                    "to",
                    formatDate(statement.getTo().toString()),
                    ".\n",
                    organisation.getName());

            landlords.forEach(user -> {
                processReport(organisation.getId(), user.getId(), content, Instant.now(), Map.of("client", formatUser(user)), "Landlord statement report", "PROPERTY_STATEMENT", body);
            });

            reportRepository.saveStatement(statement
                    .toBuilder()
                    .sent(true)
                    .sentDate(Instant.now())
                    .build());

        } catch (Exception e) {
            throw new IllegalStateException("Failed to send statement report {}" + e.getMessage(), e);
        }
    }

    @Override
    public void generateOverseasResidentReport(String organisationId, String senderId) {
        var organisation = organisationService.getOrganisation(organisationId);
        var organisationUsers = userService.findOrganisationUsersWithAddresses(organisationId);
        var organisationProperties = propertyService.findPropertiesWithOrganisation(organisationId);
        var organisationLineItems = invoiceService.findOrganisationLineItems(organisationId, null, null);

        var nonResidentLedgerCode = Optional.ofNullable(organisation)
                .map(Organisation::getLedgerCodes)
                .map(ledgerCodes -> getLedgerCodes(ledgerCodes,
                        ledgerCode -> ledgerCode.getName().equals("Non-Resident")).stream().findAny().orElse("330"))
                .orElse("330");
        var dateFormatter = new SimpleDateFormat("dd/MM/yyyy");

        var allLandlordIds = organisationProperties.stream().map(property ->
                Optional.ofNullable(property.getLandlords()).orElse(List.of())
        ).flatMap(Collection::stream).collect(toUnmodifiableSet());

        var nonResidentUsers = organisationUsers.stream()
                .filter(user -> allLandlordIds.contains(user.getId()))
                .map(user -> {
                    var debitCreditTotal = organisationProperties.stream()
                            .filter(property -> Objects.nonNull(property.getLandlords()) && property.getLandlords().size() > 0 && property.getLandlords().get(0).equals(user.getId()))
                            .map(property ->
                                    organisationLineItems.stream()
                                            .filter(lineItem -> Objects.nonNull(lineItem.getAccountCode()) && nonResidentLedgerCode.equals(lineItem.getAccountCode()))
                                            .filter(lineItem -> Objects.nonNull(lineItem.getTrackingName()) && lineItem.getTrackingName().equals(property.getReference()))
                                            .map(tenancyService::getLineItemAmount)
                                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            ).reduce(BigDecimal.ZERO, BigDecimal::add);

                    var userAddress = userService.findUserPostalAddress(user.getId());

                    var landlordAddress = Optional
                            .ofNullable(userAddress)
                            .map(this::getUserAddressDescription)
                            .orElse(DEFAULT_VALUE);

                    var map = new HashMap<String, String>();

                    map.put("landlordName", formatUser(user));
                    map.put("landlordAddress", landlordAddress);
                    map.put("overseasLandlord", Boolean.TRUE.equals(user.isOverseasResident()) ? "Yes" : "No");
                    map.put("exemptionCertificate", Optional.ofNullable(user.getOverseasResidentExemptionCertificate()).orElse("-"));
                    map.put("exemptionDate", Optional.ofNullable(user.getOverseasResidentExemptionDate()).map(dateFormatter::format).orElse("-"));
                    map.put("overseasResidentTax", Optional.ofNullable(user.getOverseasResidentTax()).orElse("-"));
                    map.put("overseasResidentTaxBalance", BigDecimal.ZERO.equals(debitCreditTotal) ? "-" : formatValue(debitCreditTotal));

                    return map;
                }).collect(toUnmodifiableList());

        var data = jasperClient.generateOverseasResidentReport(Map.of("data", nonResidentUsers));

        processReport(organisationId, senderId, data, Instant.now(), Map.of(), "Overseas Resident Report", "CLIENT_STATEMENT", null);
    }

    @Override
    public void generateSupplierLandlordStatementReport(SupplierLandlordStatementReportCommand payload) {
        var supplierUser = userService.findUser(payload.getSupplierUserId(), true);
        var organisation = organisationService.getOrganisation(payload.getOrganisationId());
        var organisationProperties = propertyService.findPropertiesWithOrganisation(payload.getOrganisationId());
        var lineItems = invoiceService.findOrganisationLineItems(payload.getOrganisationId(), payload.getStartDate(), payload.getEndDate());

        var map = new HashMap<String, Object>();

        map.put("supplierName", formatUser(supplierUser));
        map.put("supplierAddressLine1", Optional.ofNullable(supplierUser.getPostalAddress()).map(User.UserAddress::getAddressLine1).orElse(DEFAULT_VALUE));
        map.put("supplierAddressLine2", Optional.ofNullable(supplierUser.getPostalAddress()).map(User.UserAddress::getAddressLine2).orElse(DEFAULT_VALUE));
        map.put("supplierAddressLine3", Optional.ofNullable(supplierUser.getPostalAddress()).map(User.UserAddress::getAddressLine3).orElse(DEFAULT_VALUE));
        map.put("supplierPostalCode", Optional.ofNullable(supplierUser.getPostalAddress()).map(User.UserAddress::getPostcode).orElse(DEFAULT_VALUE));

        map.put("bankName", Optional.ofNullable(supplierUser.getBankName()).orElse(DEFAULT_VALUE));
        map.put("bankAccountName", Optional.ofNullable(supplierUser.getBankAccountNumber()).orElse(DEFAULT_VALUE));
        map.put("bankAccountNumber", Optional.ofNullable(supplierUser.getBankAccountNumber()).orElse(DEFAULT_VALUE));
        map.put("bankSortCode", Optional.ofNullable(supplierUser.getBankAccountSortCode()).orElse(DEFAULT_VALUE));
        map.put("organisationVATNumber", Optional.ofNullable(organisation.getCommissionBillVatNumber()).orElse(DEFAULT_VALUE));

        var ledgerCodeNameMap = new HashMap<String, String>();

        // there could be multiple ledger codes against specific name
        organisation.getLedgerCodes().forEach(ledgerCode -> {
            if (Objects.nonNull(ledgerCode.getCode()) && !ledgerCodeNameMap.containsKey(ledgerCode.getCode())) {
                ledgerCodeNameMap.put(ledgerCode.getCode(), ledgerCode.getName());
            }
        });

        var dateFormatter = new SimpleDateFormat("dd/MM/yyyy");

        var data = organisationProperties.stream()
                .flatMap(property -> {
                    var propertyLineItems = lineItems.stream()
                            .filter(lineItem -> Objects.nonNull(lineItem.getTrackingName()))
                            .filter(lineItem -> lineItem.getTrackingName().equals(property.getReference()))
                            .filter(lineItem -> Objects.nonNull(lineItem.getAccountCode()))
                            .filter(lineItem -> !lineItem.isIncome() || payload.isShowIncomeLineItems())
                            .filter(lineItem -> lineItem.getParentAgainstUserXeroId() != null && lineItem.getParentAgainstUserXeroId().equals(supplierUser.getXeroId()))
                            .collect(toUnmodifiableList());
                    var vat = propertyLineItems.stream()
                            .map(lineItem ->
                                    lineItem.isIncome() ? new BigDecimal(lineItem.getTaxAmount()).multiply(new BigDecimal("-1")) : new BigDecimal(lineItem.getTaxAmount())
                            )
                            .reduce(BigDecimal::add)
                            .orElse(BigDecimal.ZERO);

                    var total = propertyLineItems.stream()
                            .map(lineItem -> {
                                var lineAmount = tenancyService.getLineItemAmount(lineItem);

                                if (lineItem.isIncome()) {
                                    lineAmount = lineAmount.multiply(new BigDecimal("-1"));
                                }

                                return lineAmount;
                            })
                            .reduce(BigDecimal::add)
                            .orElse(BigDecimal.ZERO);
                    var formattedPropertyLineItems = propertyLineItems.stream()
                            .collect(groupingBy(Invoice.LineItem::getAccountCode))
                            .entrySet()
                            .stream()
                            .map(entry -> {
                                var propertyData = new HashMap<String, Object>();
                                propertyData.put("ledgerCode", entry.getKey());
                                propertyData.put("ledgerCodeName", ledgerCodeNameMap.getOrDefault(entry.getKey(), DEFAULT_VALUE));
                                propertyData.put("lineItems", entry.getValue().stream().map(lineItem -> {
                                    var lineItemMap = new HashMap<String, String>();
                                    lineItemMap.put("date", formatDate(lineItem.getParentDueDate()));
                                    lineItemMap.put("reference", Optional.ofNullable(tenancyService.getParentReference(lineItem)).orElse(DEFAULT_VALUE));
                                    lineItemMap.put("description", Optional.ofNullable(lineItem.getDescription()).orElse(DEFAULT_VALUE));

                                    var lineItemTotal = tenancyService.getLineItemAmount(lineItem);
                                    var lineItemVat = getValueIfPresent(lineItem.getTaxAmount());
                                    var lineItemSubTotal = lineItemTotal.subtract(lineItemVat);

                                    if (lineItem.isIncome()) {
                                        lineItemSubTotal = lineItemSubTotal.multiply(new BigDecimal("-1"));
                                        lineItemVat = lineItemVat.multiply(new BigDecimal("-1"));
                                        lineItemTotal = lineItemTotal.multiply(new BigDecimal("-1"));
                                    }

                                    lineItemMap.put("netAmount", formatValue(lineItemSubTotal));
                                    lineItemMap.put("vatAmount", formatValue(lineItemVat));
                                    lineItemMap.put("totalAmount", formatValue(lineItemTotal));

                                    return lineItemMap;
                                }).collect(toUnmodifiableList()));

                                return propertyData;
                            }).collect(toUnmodifiableList());
                    var propertyLocation = Stream.of(property.getAddressLine1(), property.getCity(), property.getPostcode())
                            .filter(StringUtils::hasText)
                            .collect(joining(", "));
                    var propertyDescription = "Property: " + propertyLocation;

                    if (formattedPropertyLineItems.isEmpty()) {
                        return Stream.of();
                    }

                    return Stream.of(Map.of("vat", vat, "total", total, "propertyDescription", propertyDescription, "formattedPropertyLineItems", formattedPropertyLineItems));
                }).collect(toUnmodifiableList());

        var vat = data.stream().map(t -> (BigDecimal) t.get("vat")).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        var total = data.stream().map(t -> (BigDecimal) t.get("total")).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        map.put("vat", formatValue(vat.abs()));
        map.put("total", formatValue(total.abs()));
        map.put("startDate", dateFormatter.format(Date.from(Instant.parse(payload.getStartDate()))));
        map.put("endDate", dateFormatter.format(Date.from(Instant.parse(payload.getEndDate()))));
        map.put("workspaceLogo", getLogo(organisation.getLogo()));
        map.put("data", data);

        var bytes = jasperClient.generateSupplierLandlordReport(map);

        processReport(payload.getOrganisationId(), payload.getSenderUserId(), bytes, Instant.now(), Map.of(), "Supplier/Landlord Statement Report", "CLIENT_STATEMENT", null);
    }

    @Override
    public void sendRevenueReport() {
        var excludeTenancyStatuses = Set.of("ARCHIVED", "VACATED", "VACATING", "NOTICE_GIVEN", "CANCELLED", "DRAFT");
        var dateFormatter = new SimpleDateFormat("dd/MM/yyyy");
        var map = new HashMap<String, Object>();

        var allOrganisations = organisationService.findAllOrganisations();
        var allProperties = propertyService.findAllProperties();
        var allTenancies = propertyService.findAllTenancies()
                .stream()
                .filter(tenancy -> !excludeTenancyStatuses.contains(tenancy.getStatus()))
                .filter(tenancy -> Objects.isNull(tenancy.getArchived()) || tenancy.getArchived().equals(false))
                .collect(toUnmodifiableList());

        var allAdminUserIds = allOrganisations.stream().map(Organisation::getAdminUser).filter(Objects::nonNull).collect(toSet());

        var adminUsers = userService.findUsers(allAdminUserIds).stream().collect(groupingBy(User::getId));

        var groupedProperties = allProperties.stream()
                .filter(property -> Objects.nonNull(property.getOrganisation()))
                .filter(property -> !property.isArchived())
                .collect(groupingBy((Property::getOrganisation)));
        var groupedTenancies = allTenancies.stream()
                .filter(tenancy -> Objects.nonNull(tenancy.getOrganisation()))
                .collect(groupingBy((Tenancy::getOrganisation)));

        var currentDateFormatted = dateFormatter.format(Date.from(Instant.now()));
        map.put("currentDate", currentDateFormatted);

        map.put("data", allOrganisations.stream()
                .sorted((o1, o2) -> {
                    var date1 = Optional.ofNullable(o1.getCreatedAt()).orElse(DEFAULT_VALUE);
                    var date2 = Optional.ofNullable(o2.getCreatedAt()).orElse(DEFAULT_VALUE);

                    return -date1.compareTo(date2);
                })
                .map(organisation -> {
                    var organisationMap = new HashMap<String, String>();

                    var adminUser = Optional.ofNullable(organisation.getAdminUser()).map(adminUsers::get).map(users -> users.get(0));
                    var organisationCreationDate = Optional.ofNullable(organisation.getCreatedAt())
                            .map(Instant::parse)
                            .map(Date::from).map(dateFormatter::format).orElse(DEFAULT_VALUE);
                    var organisationType = Optional.ofNullable(organisation.getType())
                            .map(Enum::name)
                            .map(name -> "LANDLORD".equals(name) ? "Landlord" : "Agent")
                            .orElse("Agent");

                    organisationMap.put("workspaceName", organisation.getName());
                    organisationMap.put("workspaceType", organisationType);
                    organisationMap.put("workspaceCountry", Optional.ofNullable(organisation.getCountry()).orElse(DEFAULT_VALUE));
                    organisationMap.put("propertyCount", "" + groupedProperties.getOrDefault(organisation.getId(), List.of()).size());
                    organisationMap.put("tenancyCount", "" + groupedTenancies.getOrDefault(organisation.getId(), List.of()).size());
                    organisationMap.put("creationDate", organisationCreationDate);
                    organisationMap.put("createUserName", adminUser.map(Utils::formatUser).orElse(DEFAULT_VALUE));
                    organisationMap.put("createUserEmail", adminUser.flatMap(user -> user.getEmails().stream().map(User.Email::getEmail).findFirst()).orElse(DEFAULT_VALUE));

                    return organisationMap;
                })
                .collect(toUnmodifiableList()));

        var bytes = jasperClient.generateRevenueReport(map);

        var senderUserEmails = Stream.of(config.getRevenueReportSenderEmails().split(","));

        senderUserEmails.forEach(email -> {
            log.info("Sending revenue to report to {}", email);

            var emailQueue = config.getEmailNotificationQueue();
            var queueRegion = config.getEmailNotificationRegion();
            var uploadBucket = config.getEmailAttachmentBucket();

            var key = s3Client.uploadPdf(uploadBucket, bytes);

            var attachment = new EmailSenderPayload.EmailAttachment(uploadBucket, key, "Rentancy Revenue Report " + currentDateFormatted + ".pdf", true);
            var emailPayload = EmailSenderPayload.builder()
                    .organisationId(config.getPrimaryOrganisationId())
                    .email(email)
                    .type("CLIENT_STATEMENT")
                    .subject("Rentancy Revenue Report " + currentDateFormatted)
                    .attachments(List.of(attachment))
                    .emails(List.of())
                    .simple(false)
                    .build();

            sqsClient.enqueue(emailQueue, queueRegion, wrappedToJsonString(emailPayload));
        });
    }

    @Override
    public void sendOrganisationReportAsExcel() {
        var currentDateFormatted = new SimpleDateFormat("dd/MM/yyyy").format(Date.from(Instant.now()));
        DataExporterResponse revenueReportResponse = dataExporterService.generateOrganisationReportAsExcel();

        var senderUserEmails = Stream.of(config.getOrganisationReportSenderEmails().split(","));

        senderUserEmails.forEach(email -> {
            log.info("Sending Organisation report to {}", email);
            var emailQueue = config.getEmailNotificationQueue();
            var queueRegion = config.getEmailNotificationRegion();
            var uploadBucket = config.getEmailAttachmentBucket();
            var key = s3Client.uploadExcel(uploadBucket, revenueReportResponse.getContent());
            var attachment = new EmailSenderPayload.EmailAttachment(uploadBucket, key, "Rentancy Organisation Report " + currentDateFormatted + ".xlsx", true);
            var emailPayload = EmailSenderPayload.builder()
                    .organisationId(config.getPrimaryOrganisationId())
                    .email(email)
                    .type("CLIENT_STATEMENT")
                    .subject("Rentancy Organization Report " + currentDateFormatted)
                    .attachments(List.of(attachment))
                    .emails(List.of())
                    .simple(false)
                    .build();

            sqsClient.enqueue(emailQueue, queueRegion, wrappedToJsonString(emailPayload));
        });
    }

    @Override
    public void sendOrganisationStripeChargesReport() {
        var dateFormatter = new SimpleDateFormat("dd/MM/yyyy");
        var now = Instant.now();
        var previousDay = now.minus(1, DAYS);
        var from = previousDay.truncatedTo(DAYS);
        var to = now.truncatedTo(DAYS).minus(1, SECONDS);
        var emails = config.getOrganisationStripeChargeReportEmails();
        var organisationStripeCharges = organisationService.findOrganisationStripeCharges(from.toString(), to.toString())
                .stream().sorted((o1, o2) -> {
                    var o1Number = Integer.parseInt(o1.getNumber().split("-")[2]);
                    var o2Number = Integer.parseInt(o2.getNumber().split("-")[2]);

                    return o1Number - o2Number;
                }).collect(toUnmodifiableList());
        var chargeReport = dataExporterService.generateOrganisationChargesReport(organisationStripeCharges);
        var subscriptionReport = dataExporterService.generateOrganisationSubscriptionReport(organisationStripeCharges);

        var emailQueue = config.getEmailNotificationQueue();
        var queueRegion = config.getEmailNotificationRegion();
        var uploadBucket = config.getEmailAttachmentBucket();

        var chargeKey = s3Client.uploadExcel(uploadBucket, chargeReport.getContent());
        var subscriptionKey = s3Client.uploadExcel(uploadBucket, subscriptionReport.getContent());

        var emailPayload = EmailSenderPayload
                .builder()
                .emails(emails)
                .attachments(List.of(
                        new EmailSenderPayload.EmailAttachment(uploadBucket, chargeKey, String.join(DEFAULT_VALUE, chargeReport.getFileName(), ".xlsx"), true)
                ))
                .subject(String.join(DEFAULT_VALUE, "DailyXeroInvoiceImport", dateFormatter.format(Date.from(previousDay))))
                .simple(true)
                .build();
        var subscriptionPayload = EmailSenderPayload
                .builder()
                .emails(emails)
                .attachments(List.of(
                        new EmailSenderPayload.EmailAttachment(uploadBucket, subscriptionKey, String.join(DEFAULT_VALUE, subscriptionReport.getFileName(), ".xlsx"), true)
                ))
                .subject(String.join(DEFAULT_VALUE, "DailySubscriptionCharges", dateFormatter.format(Date.from(previousDay))))
                .simple(true)
                .build();
        sqsClient.enqueue(emailQueue, queueRegion, wrappedToJsonString(emailPayload));
        sqsClient.enqueue(emailQueue, queueRegion, wrappedToJsonString(subscriptionPayload));
    }

    @Override
    public void sendOrganisationDailySignupReport() {
        var dateFormatter = new SimpleDateFormat("dd/MM/yyyy");
        var now = Instant.now();
        var previousDay = now.minus(1, DAYS);
        var from = previousDay.truncatedTo(DAYS);
        var to = now.truncatedTo(DAYS).minus(1, SECONDS);
        var emails = config.getOrganisationDailySignupReportEmails();
        var organisations = organisationService.findOrganisationsCreatedBetween(from.toString(), to.toString());
        var organisationSignupReport = dataExporterService.generateOrganisationSignupReport(organisations);

        var emailQueue = config.getEmailNotificationQueue();
        var queueRegion = config.getEmailNotificationRegion();
        var uploadBucket = config.getEmailAttachmentBucket();

        var organisationSignupReportKey = s3Client.uploadExcel(uploadBucket, organisationSignupReport.getContent());

        var emailPayload = EmailSenderPayload
                .builder()
                .emails(emails)
                .attachments(List.of(
                        new EmailSenderPayload.EmailAttachment(uploadBucket, organisationSignupReportKey, String.join(DEFAULT_VALUE, organisationSignupReport.getFileName(), ".xlsx"), true)
                ))
                .subject(String.join(DEFAULT_VALUE, "DailySignUps", dateFormatter.format(Date.from(previousDay))))
                .simple(true)
                .build();
        sqsClient.enqueue(emailQueue, queueRegion, wrappedToJsonString(emailPayload));
    }

    @Override
    public List<Statement> findOrganisationStatements(String organisationId) {
        return reportRepository.findStatements(organisationId);
    }

    @Override
    public Optional<Statement> findLastFinalizedStatement(String propertyId) {
        var statements = reportRepository.findPropertyStatements(propertyId);

        log.info("Statements - {}", statements);

        return statements
                .stream()
                .filter(Statement::isApproved)
                .min((o1, o2) -> o2.getTo().compareTo(o1.getTo()));
    }

    @Override
    public Statement getStatement(String statementId) {
        return reportRepository.get(statementId);
    }

    @Override
    public void deleteByLandlordBillId(String id) {
        reportRepository.deleteByLandlordBillId(id);
    }

    @Override
    public byte[] generateTenantLedgerReport(Organisation organisation, PropertyLedgersSummary summary) {
        var dateFormatter = new SimpleDateFormat("dd/MM/yyyy");

        var map = new HashMap<String, Object>();

        map.put("startDate", dateFormatter.format(Date.from(Instant.parse(summary.getStartDate()))));
        map.put("endDate", dateFormatter.format(Date.from(Instant.parse(summary.getEndDate()))));
        map.put("workspaceLogo", getLogo(organisation.getLogo()));
        map.put("workspaceName", organisation.getName());
        map.put("data", summary.getItems()
                .stream()
                .map(item -> {
                    var itemMap = new HashMap<String, String>();
                    var type = item.getLineItemType().name().toLowerCase();

                    itemMap.put("date", item.getDate());
                    itemMap.put("type", type.substring(0, 1).toUpperCase() + type.substring(1));
                    itemMap.put("description", item.getDescription());
                    itemMap.put("propertyAddress", item.getPropertyAddress());
                    itemMap.put("tenancyReference", Optional.ofNullable(item.getTenancyReference()).orElse(DEFAULT_VALUE));
                    itemMap.put("due", Optional.ofNullable(item.getDueAmount()).map(Utils::formatValue).orElse("-"));
                    itemMap.put("debit", Optional.ofNullable(item.getDebitAmount()).map(Utils::formatValue).orElse("0"));
                    itemMap.put("credit", Optional.ofNullable(item.getCreditAmount()).map(Utils::formatValue).orElse("0"));
                    itemMap.put("held", Optional.ofNullable(item.getHeldAmount()).map(Utils::formatValue).orElse("0"));
                    itemMap.put("invoiceReference", Optional.ofNullable(item.getXeroNumber()).orElse(DEFAULT_VALUE));

                    return itemMap;
                })
                .collect(toUnmodifiableList()));

        return jasperClient.generateTenantLedgerReport(map);
    }

    @Override
    public void exportOrganisationProperties(OrganisationPropertyReportCommand command) {
        var data = dataExporterService.exportOrganisationProperties(command.getOrganisationId());

        processReportCaller(command.getOrganisationId(), command.getSenderId(), data.getContent(), Instant.now(), null, data.getFileName(), "XLS_REPORT", "organisationReport.xlsx", "");
    }

    @Override
    public byte[] generatePropertyLedgersSummary(Organisation organisation,
                                                 String propertyId,
                                                 PropertyLedgersSummary summary,
                                                 BackendResponseFormat format) {
        switch (format) {
            case JSON:
                try {
                    return toJsonString(summary).getBytes();
                } catch (JsonProcessingException e) {
                    log.error("Unknown error, failed to serialize property ledgers {} {}", organisation.getId(), propertyId);
                    throw new RuntimeException("Failed to serialize");
                }
            case PDF:
                return generateTenantLedgerReport(organisation, summary);
            case EXCEL:
                return dataExporterService.generateTenantLedgerReport(summary, ExportFormat.XLSX).getContent();
            default:
                throw new RuntimeException("Unknown format " + format.name());
        }
    }

    @Override
    public void generateMonthlyJournalReport(Organisation organisation, List<JournalResult> journalResults) {
        var reportBytes = dataExporterService.generateMonthlyJournalReport(journalResults, ExportFormat.XLSX).getContent();
        var recipientUserEmails = userService.findOrganisationUsersWithRole(organisation.getId(), "FINANCE")
                .stream()
                .map(user -> Optional.ofNullable(user.getCognitoEmail())
                        .orElse(Optional.ofNullable(user.getEmails())
                                .orElse(List.of())
                                .stream()
                                .findAny()
                                .map(User.Email::getEmail)
                                .orElse(null)))
                .filter(Objects::nonNull);

        var senderUserEmails = Stream.concat(recipientUserEmails, Stream.of(config.getMonthlyJournalReportSenderEmails().split(",")))
                        .collect(toUnmodifiableSet());

        log.info("Sending monthly journal to {}", senderUserEmails);

        var dateFormatter = new SimpleDateFormat("dd/MM/yyyy");
        var currentDateFormatted = dateFormatter.format(Date.from(Instant.now()));

        var emailSubject = "Rentancy monthly journal '" + organisation.getName() + "' " + currentDateFormatted;
        var filename = emailSubject + ".xlsx";

        senderUserEmails.forEach(email -> {
            log.info("Sending monthly journal to report to {}", email);

            var emailQueue = config.getEmailNotificationQueue();
            var queueRegion = config.getEmailNotificationRegion();
            var uploadBucket = config.getEmailAttachmentBucket();

            var key = s3Client.uploadExcel(uploadBucket, reportBytes);

            var attachment = new EmailSenderPayload.EmailAttachment(uploadBucket, key, filename, true);
            var emailPayload = EmailSenderPayload.builder()
                    .organisationId(organisation.getId())
                    .email(email)
                    .type("CLIENT_STATEMENT")
                    .subject(emailSubject)
                    .attachments(List.of(attachment))
                    .emails(List.of())
                    .simple(false)
                    .build();

            sqsClient.enqueue(emailQueue, queueRegion, wrappedToJsonString(emailPayload));
        });
    }

    @Override
    public byte[] generateForecastReport(String organisationId, String startDate, int periodLengthInMonths) {
        var organisation = organisationService.getOrganisation(organisationId);
        var endDate = Instant.parse(startDate).atZone(UTC).plusMonths(periodLengthInMonths).toInstant().toString();

        log.info("Fetching required data to build report...");
        var tenanciesFuture = CompletableFuture.supplyAsync(() -> propertyService.findTenancies(organisationId, null, true)
                .stream()
                .filter(tenancy -> ACTIVE_TENANCY_STATUSES.contains(tenancy.getStatus()))
                .collect(toUnmodifiableList()));
        var propertyIdMapFuture = CompletableFuture.supplyAsync(() -> propertyService.findPropertiesWithOrganisation(organisationId).stream()
                .collect(toUnmodifiableMap(Property::getId, Function.identity())));
        var userIdMapFuture = CompletableFuture.supplyAsync(() -> userService.findUsersWithoutAddressesByOrganisation(organisationId).stream()
                .collect(toUnmodifiableMap(User::getId, Function.identity())));
        var lineItemsFuture = CompletableFuture.supplyAsync(() -> invoiceService.findOrganisationLineItems(organisation.getId(), null, endDate));
        CompletableFuture.allOf(tenanciesFuture, propertyIdMapFuture, userIdMapFuture, lineItemsFuture).join();

        var tenancies = tenanciesFuture.join();
        var propertyIdMap = propertyIdMapFuture.join();
        var userIdMap = userIdMapFuture.join();
        var lineItems = lineItemsFuture.join();
        log.info("Fetching required data to build report... Done");

        log.info("Constructing lookup map for tenancy -> line items...");
        var lineItemsLookup = tenancies.parallelStream()
                .filter(it -> propertyIdMap.get(it.getProperty()) != null)
                .map(it -> {
                    var property = propertyIdMap.get(it.getProperty());
                    var relatedLineItems =  lineItems.stream()
                            .filter(lineItem -> tenancyService.isInvoiceLineItemAgainstTenancy(lineItem, property, it))
                            .collect(Collectors.toList());
                    return Pair.of(it.getId(), relatedLineItems);
                }).collect(Collectors.toMap(Pair::getKey, Pair::getValue));
        log.info("Constructing lookup map for tenancy -> line items... Done");

        var dateFormatter = new SimpleDateFormat("dd MMM yyyy");
        var monthFormatter = new SimpleDateFormat("MMM");
        var now = Instant.now();

        var userIdTenantBalanceBalance = getPrimaryTenantIds(tenancies)
                .stream()
                .map(tenantUserId -> {
                    var user = userIdMap.get(tenantUserId);

                    if (Objects.isNull(user) || Objects.isNull(user.getXeroId())) {
                        return Pair.of(tenantUserId, BigDecimal.ZERO);
                    }

                    var balance = tenancyService.calculateTenantBalancePerformant(organisation, user, tenancies, lineItemsLookup);

                    log.debug("Tenant balance: {} {} {}", user.getId(), user.getXeroId(), balance);

                    return Pair.of(user.getId(), balance);
                })
                .collect(toUnmodifiableMap(Pair::getKey, Pair::getValue));

        var forecastReportItems = tenancies.stream()
                .filter(tenancy -> propertyIdMap.get(tenancy.getProperty()) != null)
                .filter(tenancy -> getPrimaryTenantIds(List.of(tenancy)).stream().map(userIdMap::get).anyMatch(Objects::nonNull))
                .filter(tenancy -> Objects.nonNull(tenancy.getPeriod()) && Objects.nonNull(tenancy.getRent())) // We have to be able calculate monthly rent amount
                .map(tenancy -> {
                    var property = propertyIdMap.get(tenancy.getProperty());
                    var landlordName = getPrimaryLandlordIds(List.of(property))
                            .stream()
                            .map(userIdMap::get)
                            .findFirst()
                            .map(Utils::formatUser)
                            .orElse(DEFAULT_VALUE);
                    var tenant = getPrimaryTenantIds(List.of(tenancy))
                            .stream()
                            .map(userIdMap::get)
                            .filter(Objects::nonNull)
                            .findFirst()
                            .orElseThrow(() -> new IllegalStateException("No primary tenants for tenancy: " + tenancy.getId()));
                    var tenantName = formatUser(tenant);

                    var tenancyStartDate = Optional.ofNullable(tenancy.getStartDate())
                            .map(Instant::parse)
                            .map(Date::from)
                            .map(dateFormatter::format)
                            .orElse(DEFAULT_VALUE);
                    var tenancyEndDate = Optional.ofNullable(tenancy.getEndDate())
                            .map(Instant::parse)
                            .map(Date::from)
                            .map(dateFormatter::format)
                            .orElse(DEFAULT_VALUE);

                    var monthlyRent = tenancyService.getTenancyMonthlyRent(tenancy);
                    var tenantId = getPrimaryTenantIds(List.of(tenancy)).stream().findFirst().orElseThrow();

                    var periodMonths = new ArrayList<JournalForecastReport.JournalForecastReportTenancyItemMonth>(periodLengthInMonths);
                    var currentBalance = userIdTenantBalanceBalance.get(tenantId);
                    for (var i = 0 ; i < periodLengthInMonths; i++) {
                        var periodDate = now.atZone(UTC).plusMonths(i);
                        var monthName = monthFormatter.format(Date.from(Instant.parse(periodDate.toString())));

                        var isAfterStartDate = Objects.isNull(tenancy.getStartDate()) || Instant.parse(tenancy.getStartDate()).atZone(UTC).isBefore(periodDate);
                        var isBeforeEndDate = Objects.isNull(tenancy.getEndDate()) || Instant.parse(tenancy.getEndDate()).atZone(UTC).isAfter(periodDate);

                        var item = JournalForecastReport.JournalForecastReportTenancyItemMonth.builder()
                                .monthName(monthName)
                                .collected(BigDecimal.ZERO)
                                .monthlyRent(BigDecimal.ZERO)
                                .forecast(BigDecimal.ZERO)
                                .build();

                        // if in contract start/end range
                        if (isAfterStartDate && isBeforeEndDate) {
                            var relatedLineItems = lineItemsLookup.get(tenancy.getId());
                            var alreadyCollectedAmount = tenancyService.getTenancyMonthTransferAmount(relatedLineItems, periodDate.getYear(), periodDate.getMonthValue());
                            var expectedToCollectAmount = tenancyService.getTenancyJournalAmount(tenancy, currentBalance, periodDate);

                            var amountToChargeBalance = expectedToCollectAmount.subtract(alreadyCollectedAmount).max(BigDecimal.ZERO);
                            currentBalance = currentBalance.subtract(amountToChargeBalance);

                            item.setCollected(alreadyCollectedAmount);
                            item.setMonthlyRent(monthlyRent);
                            item.setForecast(alreadyCollectedAmount.add(amountToChargeBalance));
                        }

                        periodMonths.add(item);
                    }

                    return JournalForecastReport.JournalForecastReportTenancyItem
                            .builder()
                            .landlordName(landlordName)
                            .propertyReference(property.getReference())
                            .tenancyReference(tenancy.getReference())
                            .tenantName(tenantName)
                            .tenancyStatus(toCamelCase(tenancy.getStatus()))
                            .tenancyPeriod(getPeriodReportFormat(tenancy))
                            .tenancyStartDate(tenancyStartDate)
                            .tenancyEndDate(tenancyEndDate)
                            .tenantBalanceAmountHeld(userIdTenantBalanceBalance.getOrDefault(tenant.getId(), BigDecimal.ZERO))
                            .monthlyRentAmount(monthlyRent)
                            .periodMonths(periodMonths)
                            .build();
                }).collect(toUnmodifiableList());

        var forecastReport = JournalForecastReport.builder()
                .date(dateFormatter.format(Date.from(Instant.now())))
                .startDate(dateFormatter.format(Date.from(Instant.parse(startDate))))
                .periodLengthInMonths(periodLengthInMonths)
                .items(forecastReportItems)
                .build();

        return dataExporterService.generateJournalForecastReport(organisation, forecastReport, ExportFormat.XLSX)
                .getContent();
    }

    @Override
    public void generateIncomeArrearsSummary(String organisationId, String receiverId, IncomeArrearsSummary arrearsSummary) {

        log.info("Start Generate report, received data arrearsSummary: {} ms", System.currentTimeMillis() - getCurrentTime());

        CompletableFuture<Organisation> organisationFuture =
                CompletableFuture.supplyAsync(() -> organisationService.getOrganisation(organisationId));
        CompletableFuture<DataExporterResponse> dataFuture =
                CompletableFuture.supplyAsync(() -> dataExporterService.generateIncomeArrearsReport(arrearsSummary, ExportFormat.XLSX));

        organisationFuture.thenCombine(dataFuture, (organisation, data) -> {
            log.info("Then combine generate reports: {} ms", System.currentTimeMillis() - getCurrentTime());
            var uploadBucket = config.getRentancyDocumentUploads();
            var fileName = arrearsSummary.getOrganisationName() + " Arrears Report.xlsx";
            var fileKey = s3Client.uploadExcel(uploadBucket, data.getContent());
            var map = Map.of(
                    "fileName", "Arrears Report.xlsx",
                    "fileKey", fileKey
            );
            log.info("upload to s3 done", System.currentTimeMillis() - getCurrentTime());
            appSyncServiceProvider.createMessage(organisationId, organisation.getReportConversation(),
                    organisation.getBotUser(), wrappedToJsonString(map),
                    "CONVERSATION_ATTACHMENTS");

            CompletableFuture.runAsync(() ->
                    processReport(organisationId, receiverId, data.getContent(), Instant.now(),
                            Map.of(), "Arrears Report", "XLS_REPORT", fileName, null)
            );
            return null;
        }).join();

        log.info("Time taken to generate income arrears summary: {} ms", System.currentTimeMillis() - getCurrentTime());
    }

    @Override
    public byte[] getIncomeArrearsSummaryReport(IncomeArrearsSummary arrearsSummary) {
        var report = dataExporterService.generateIncomeArrearsReport(arrearsSummary, ExportFormat.XLSX);
        return report.getContent();
    }

    private String saveStatement(String cognitoId, String organisationId, String startDate, String endDate, String type, byte[] data, String landlord, String propertyId, String landlordBillId, boolean approved) {
        var user = userService.findUserWithCognitoId(cognitoId);
        var statements = reportRepository.findStatements(organisationId);
        var statementCount = statements.size();
        var landlordBillStatement = statements
                .stream()
                .filter(s -> propertyId.equals(s.getPropertyId()) && Objects.nonNull(landlordBillId) && landlordBillId.equals(s.getLandlordBillId()))
                .findAny()
                .orElse(null);
        var id = UUID.randomUUID().toString();
        var key = join("/", "public/generated_reports", join(".", id, "pdf"));
        Statement statement;

        if (landlordBillStatement == null) {
            statement = Statement
                    .builder()
                    .id(id)
                    .reference(join("-", "S", String.valueOf(statementCount + 1)))
                    .organisationId(organisationId)
                    .propertyId(propertyId)
                    .landlordBillId(landlordBillId)
                    .clientId(landlord)
                    .finalisedBy(getUserInitials(formatUser(user)))
                    .type(type)
                    .from(Instant.parse(startDate))
                    .to(Instant.parse(endDate))
                    .fileKey(key)
                    .sent(false)
                    .approved(approved)
                    .build();
        } else {
            statement = landlordBillStatement
                    .toBuilder()
                    .finalisedBy(getUserInitials(formatUser(user)))
                    .type(type)
                    .to(Instant.now())
                    .fileKey(key)
                    .sent(false)
                    .approved(approved)
                    .build();
        }

        s3Client.uploadPdf(config.getRentancyDocumentUploads(), key, new ByteArrayInputStream(data), null);
        reportRepository.saveStatement(statement);

        return id;
    }

    private Map<String, Object> toLandlordPdfData(LandlordReport report, @Nullable StatementType statementType) {
        // standard version variables
        var tenancies = report.getTenancies()
                .stream()
                .map(landlordReportTenancy -> {
                    var item = new HashMap<String, Object>();
                    item.put("reference", landlordReportTenancy.getReference());
                    item.put("title", landlordReportTenancy.getTitle());
                    item.put("endDate", Optional.ofNullable(landlordReportTenancy.getEndDate()).map(Utils::formatDate).orElse(""));
                    item.put("invoices", landlordReportTenancy
                            .getInvoices()
                            .stream()
                            .map(this::getLandlordSubReportItemMap)
                            .collect(toUnmodifiableList()));

                    return item;
                }).collect(toUnmodifiableList());

        var expenses = report.getPropertyExpenses()
                .stream()
                .map(landlordReportExpenses -> {
                    var item = new HashMap<String, Object>();
                    item.put("account", landlordReportExpenses.getAccount());
                    item.put("bills", landlordReportExpenses
                            .getBills()
                            .stream()
                            .map(this::getLandlordSubReportItemMap)
                            .collect(toUnmodifiableList()));

                    return item;
                }).collect(toUnmodifiableList());

        var inArrears = report.getInArrears()
                .stream()
                .map(this::getLandlordSubReportItemMap)
                .collect(toUnmodifiableList());

        // version 1 variables
        var rentalIncomeInvoices = report.getRentalIncomeInvoices()
                .stream()
                .map(this::getLandlordSubReportItemMapWithUserDescription)
                .collect(toUnmodifiableList());

        var managementInvoices = report.getManagementInvoices()
                .stream()
                .map(this::getLandlordSubReportItemMapWithUserDescription)
                .collect(toUnmodifiableList());

        var allExpendatureInvoices = report.getAllExpendatureInvoices()
                .stream()
                .map(this::getLandlordSubReportItemMapWithUserDescription)
                .collect(toUnmodifiableList());

        var propertyFloats = report.getPropertyFloats()
                .stream()
                .map(this::getLandlordSubReportItemMapWithUserDescription)
                .collect(toUnmodifiableList());

        var arrearInvoices = report.getInArrears()
                .stream()
                .map(this::getLandlordSubReportItemMapWithUserDescription)
                .collect(toUnmodifiableList());

        var values = new HashMap<String, Object>();

        values.put("headerText", (statementType != null && statementType == StatementType.AD_HOC) ? "DRAFT STATEMENT" : "STATEMENT");
        values.put("propertyReference", report.getProperty().getReference());
        values.put("reportDate", formatDate(Instant.now().toString()));
        values.put("workspaceLogo", getLogo(report.getLogoUrl()));
        values.put("workspaceName", report.getOrganisationName());
        values.put("propertyAddress", report.getPropertyAddress());
        values.put("landlordName", report.getLandlordName());
        values.put("landlordAddress", report.getLandlordAddress());
        values.put("periodTotal", formatValue(report.getPeriodTotal()));
        values.put("periodVat", formatValue(report.getPeriodVat()));
        values.put("periodSubTotal", formatValue(report.getPeriodSubtotal()));
        values.put("openingBalance", formatValue(report.getOpeningBalance()));
        values.put("closingBalance", formatValue(report.getClosingBalance()));
        values.put("minimumBalance", formatValue(report.getMinimumBalance()));
        values.put("landlordPayment", formatValue(report.getClientPayable()));
        values.put("tenancies", tenancies);
        values.put("expenses", expenses);
        values.put("inArrears", inArrears);
        values.put("date", report.getDate());
        values.put("propertyAddress1", report.getPropertyAddress1());
        values.put("propertyAddress2", report.getPropertyAddress2());
        values.put("propertyAddress3", report.getPropertyAddress3());
        values.put("postCode", report.getPostCode());
        values.put("organisationVat", Optional.ofNullable(report.getOrganisationVat()).orElse(DEFAULT_VALUE));
        values.put("landlordBankName", Optional.ofNullable(report.getLandlordBankName()).orElse(DEFAULT_VALUE));
        values.put("landlordAccountName", Optional.ofNullable(report.getLandlordAccountName()).orElse(DEFAULT_VALUE));
        values.put("landlordAccountNumber", Optional.ofNullable(report.getLandlordAccountNumber()).orElse(DEFAULT_VALUE));
        values.put("landlordSortCode", Optional.ofNullable(report.getLandlordSortCode()).orElse(DEFAULT_VALUE));
        values.put("reportNumber", report.getReportNumber());

        values.put("arrearInvoices", arrearInvoices);
        values.put("propertyFloats", propertyFloats);
        values.put("allExpendatureInvoices", allExpendatureInvoices);
        values.put("managementInvoices", managementInvoices);
        values.put("rentalIncomeInvoices", rentalIncomeInvoices);
        values.put("totalIncomeSubtotal", formatValue(report.getTotalIncomeSubtotal()));
        values.put("totalIncomeVat", formatValue(report.getTotalIncomeVat()));
        values.put("totalIncomeTotal", formatValue(report.getTotalIncomeTotal()));
        values.put("totalExpenditureSubtotal", formatValue(report.getTotalExpenditureSubtotal()));
        values.put("totalExpenditureVat", formatValue(report.getTotalExpenditureVat()));
        values.put("totalExpenditureTotal", formatValue(report.getTotalExpenditureTotal()));

        values.put("subTotalSubTotal", formatValue(report.getSubTotalSubTotal()));
        values.put("subTotalVat", formatValue(report.getSubTotalVat()));
        values.put("subTotalTotal", formatValue(report.getSubTotalTotal()));

        values.put("totalRetentionSubtotal", formatValue(report.getTotalRetentionSubtotal()));
        values.put("totalRetentionVat", formatValue(report.getTotalRetentionVat()));
        values.put("totalRetentionTotal", formatValue(report.getTotalRetentionTotal()));
        values.put("reportPeriod", "Period: " + formatDate(report.getStartDate()) + " - " + formatDate(report.getEndDate()));
        values.put("reportPeriodValues", formatDate(report.getStartDate()) + " - " + formatDate(report.getEndDate()));

        return values;
    }

    private Map<String, String> getLandlordSubReportItemMap(LandlordReportItem landlordReportItem) {
        var subItem = new HashMap<String, String>();
        var status = landlordReportItem.getStatus();
        subItem.put("reference", Optional.ofNullable(landlordReportItem.getReference()).filter(from -> !from.contains("null")).orElse(DEFAULT_VALUE));
        subItem.put("from", Optional.ofNullable(landlordReportItem.getFrom()).filter(from -> !from.contains("null")).orElse(DEFAULT_VALUE));
        subItem.put("description", landlordReportItem.getDescription());
        subItem.put("creationDate", landlordReportItem.getCreationDate().equals(DEFAULT_VALUE) ? DEFAULT_VALUE : formatDate(landlordReportItem.getCreationDate()));
        subItem.put("status", status.equals("AUTHORISED") ? "AUTH" : status);
        subItem.put("subTotal", formatValue(landlordReportItem.getSubTotal()));
        subItem.put("vat", formatValue(landlordReportItem.getVat()));
        subItem.put("total", formatValue(landlordReportItem.getTotal()));

        return subItem;
    }

    private Map<String, String> getLandlordSubReportItemMapWithUserDescription(LandlordReportItem item) {
        var itemMap = getLandlordSubReportItemMap(item);

        itemMap.put("description", item.getDescription() + (StringUtils.hasText(item.getFrom()) ? ", " + item.getFrom() : ""));

        return itemMap;
    }

    private LandlordReportItem toLandlordReportItem(com.rentancy.integrations.pojos.Invoice.LineItem lineItem, BigDecimal subTotal, BigDecimal vat, BigDecimal total) {
        var user = userService.findUserWithXeroId(lineItem.getParentContactId());

        if (user == null) {
            log.error("Couldn't find contact user for invoice {}", lineItem.getId());
        }
        var formattedUser = formatUser(user);

        return LandlordReportItem.builder()
                .reference(tenancyService.getParentReference(lineItem))
                .from(formattedUser)
                .status(lineItem.getParentStatus())
                .creationDate(lineItem.getParentDate())
                .description(lineItem.getDescription())
                .accountCode(lineItem.getAccountCode())
                .vat(vat)
                .subTotal(subTotal)
                .total(total)
                .build();
    }

    private LandlordReportItem toLandlordReportItem(com.rentancy.integrations.pojos.Invoice.LineItem lineItem) {
        var total = tenancyService.getLineItemAmount(lineItem);
        var vat = new BigDecimal(lineItem.getTaxAmount());
        var subTotal = total.subtract(vat);

        return toLandlordReportItem(lineItem, subTotal, vat, total);
    }

    private LandlordReportItem getPaidAmountLandlordReportItem(com.rentancy.integrations.pojos.Invoice.LineItem lineItem) {
        var paidAmount = tenancyService.getLineItemPaidAmount(lineItem);
        var vatPaidAmount = tenancyService.getLineItemTaxVatPaidAmount(lineItem);
        var subTotal = paidAmount.subtract(vatPaidAmount);

        return toLandlordReportItem(lineItem, subTotal, vatPaidAmount, paidAmount);
    }

    private LandlordReportItem getNotPaidAmountLandlordReportItem(com.rentancy.integrations.pojos.Invoice.LineItem lineItem) {
        var paidAmount = tenancyService.getLineItemPaidAmount(lineItem);
        var vatPaidAmount = tenancyService.getLineItemTaxVatPaidAmount(lineItem);

        var vatNotPaidAmount = new BigDecimal(lineItem.getTaxAmount()).subtract(vatPaidAmount);
        var lineItemNotPaidAmount = tenancyService.getLineItemAmount(lineItem).subtract(paidAmount);
        var unpaidSubTotal = lineItemNotPaidAmount.subtract(vatNotPaidAmount);

        return toLandlordReportItem(lineItem, unpaidSubTotal, vatNotPaidAmount, lineItemNotPaidAmount);
    }

    private String getLedgerName(List<Organisation.LedgerCode> ledgerCodes, String code) {
        return ledgerCodes
                .stream()
                .filter(ledgerCode -> Objects.nonNull(ledgerCode.getCode()))
                .filter(ledgerCode -> ledgerCode.getCode().equals(code))
                .findAny()
                .map(Organisation.LedgerCode::getDisplayName)
                .orElse(code);
    }

    public List<String> getDepositLedgerCodes(List<Organisation.LedgerCode> ledgerCodes) {
        return getLedgerCodes(ledgerCodes, ledgerCode -> DEPOSIT_LEDGER_CODE_NAMES.contains(ledgerCode.getName()));
    }

    private List<String> getLedgerCodes(List<Organisation.LedgerCode> ledgerCodes,
                                        Predicate<Organisation.LedgerCode> predicate) {
        return ledgerCodes
                .stream()
                .filter(predicate)
                .filter(ledgerCode -> Objects.nonNull(ledgerCode.getCode()))
                .map(Organisation.LedgerCode::getCode)
                .collect(toUnmodifiableList());
    }

    private String getUserAddressDescription(User.UserAddress address) {
        return getAddressDescription(address.getAddressLine1(), address.getAddressLine2(), address.getAddressLine3(), address.getPostcode(), address.getCity(), DELIMITER);
    }


    private String getAddressDescription(String addressLine1, String addressLine2, String addressLine3, String postcode, String town, String delimiter) {
        return List.of(
                        getDetail(addressLine1),
                        getDetail(addressLine2),
                        getDetail(addressLine3),
                        getDetail(town),
                        getDetail(postcode))
                .stream()
                .filter(StringUtils::hasText)
                .collect(joining(delimiter));
    }

    private String getDetail(String detail) {
        return getOrDefault(detail, DEFAULT_VALUE);
    }

    private String getPropertyDescription(Property property) {
        return getAddressDescription(property.getAddressLine1(), property.getAddressLine2(), property.getAddressLine3(), property.getPostcode(), property.getCity(), DELIMITER);
    }

    private String getParentPropertyDescription(ParentProperty parentProperty) {
        return List.of(
                        getDetail(parentProperty.getAddressLine1()),
                        getDetail(parentProperty.getCity()),
                        getDetail(parentProperty.getPostcode())
                )
                .stream()
                .filter(StringUtils::hasText)
                .collect(joining(DELIMITER));
    }

    private String generateDocumentKey(Document document, Property property, String sortBy) {
        var dateFormatter = new SimpleDateFormat("yyyy MM dd");
        var date = dateFormatter.format(Date.from(Instant.parse(document.getExpiry())));

        switch (sortBy) {
            case "date":
                return date + " " + document.getType() + " " + property.getAddressLine1();
            case "type":
                return document.getType() + " " + date + " " + property.getAddressLine1();
            case "property":
                return property.getAddressLine1() + " " + document.getType() + " " + date;
            default:
                throw new IllegalStateException("Invalid sorting option");
        }
    }

    private Map<String, String> serializeDocument(Document document, Property property, @Nullable Tenancy tenancy) {
        var dateFormatter = new SimpleDateFormat("dd MMM yyyy");
        var data = new HashMap<String, String>();

        data.put("property", property.getAddressLine1());

        var type = document.getType().toLowerCase().replaceAll("_", " ");
        data.put("type", type.substring(0, 1).toUpperCase() + type.substring(1));
        data.put("validUntil", dateFormatter.format(Date.from(Instant.parse(document.getExpiry()))));
        data.put("documentName", document.getName());
        data.put("propertyReference", property.getReference());
        data.put("dateAdded", dateFormatter.format(Date.from(Instant.parse(document.getCreatedAt()))));

        if (tenancy != null) {
            data.put("contractReference", tenancy.getReference());
            data.put("contractTitle", tenancy.getTitle());
        }

        return data;
    }

    private String generateTaskKey(Task task, Property property, BoardColumn column, String sortBy, @Nullable String landlordName) {
        var dateFormatter = new SimpleDateFormat("yyyy MM dd");
        var date = Optional.ofNullable(task.getDeadline())
                .map(deadline -> dateFormatter.format(Date.from(Instant.parse(deadline))))
                .orElse("");

        switch (sortBy) {
            case "date":
                return date + " " + column.getName() + " " + property.getAddressLine1();
            case "landlord":
                if (landlordName == null) {
                    return " " + property.getAddressLine1() + " " + date;
                }
                return landlordName + " " + column.getName() + " " + property.getAddressLine1();
            case "property":
                return property.getAddressLine1() + " " + date + " " + column.getName();
            default:
                throw new IllegalStateException("Invalid sorting option");
        }
    }

    private Map<String, String> serializeTask(Task task, BoardColumn column, Property property, @Nullable Tenancy tenancy, @Nullable String landlord) {
        var dateFormatter = new SimpleDateFormat("dd MMM yyyy");
        var data = new HashMap<String, String>();
        var dueDate = Optional.ofNullable(task.getDeadline())
                .map(deadline -> dateFormatter.format(Date.from(Instant.parse(deadline))))
                .orElse("");

        data.put("dueDate", dueDate);
        data.put("columnNameStatus", column.getName());
        data.put("propertyAddress", property.getAddressLine1());
        data.put("taskTitle", task.getName());
        data.put("taskDescription", task.getDescription());

        if (task.getAssigneeUserId() != null) {
            data.put("assignee", formatUser(userService.findUser(task.getAssigneeUserId(), false)));
        }
        data.put("propertyReference", property.getReference());
        data.put("landlord", landlord);

        if (tenancy != null) {
            var leadTenant = Optional.ofNullable(tenancy.getPrimaryTenant()).map(id -> userService.findUser(id, false)).map(Utils::formatUser).orElse(null);
            data.put("contractReference", tenancy.getReference());
            data.put("contractTitle", tenancy.getTitle());
            data.put("leadTenant", leadTenant);
        }

        return data;
    }

    private String getLogo(String url) {
        return Optional
                .ofNullable(url)
                .map(logo -> Optional.ofNullable(Utils.downloadResource(logo))
                        .orElse(downloadLogo(logo))
                ).map(inputStream -> {
                    try {
                        return toApiResponse(inputStream.readAllBytes());
                    } catch (IOException e) {
                        log.error("Failed to read bytes", e);
                        return null;
                    }
                }).orElse(null);
    }

    private InputStream downloadLogo(String key) {
        try {
            var object = s3Client.downloadFromLondon(config.getRentancyDocumentUploads(), join("/", "public", key));

            var content = object.readAllBytes();
            var image = ImageIO.read(new ByteArrayInputStream(content));

            log.info("Image - {}", image);
            if (image == null) {
                return null;
            }

            return new ByteArrayInputStream(content);
        } catch (Exception e) {
            log.error("Failed to download logo from Rentancy bucket", e);
            return null;
        }
    }

    private PropertySummary sum(PropertySummary sum1, PropertySummary sum2) {
        return PropertySummary.builder()
                .balance(sum1.getBalance().add(sum2.getBalance()))
                .clientPayable(sum1.getClientPayable().add(sum2.getClientPayable()))
                .inArrears(sum1.getInArrears().add(sum2.getInArrears()))
                .billsOutstanding(sum1.getBillsOutstanding().add(sum2.getBillsOutstanding()))
                .minimumBalance(sum1.getMinimumBalance().add(sum2.getMinimumBalance()))
                .paidDeposit(sum1.getPaidDeposit().add(sum2.getPaidDeposit()))
                .build();
    }

    private String getLandlordPaymentLedgerCode(List<Organisation.LedgerCode> ledgerCodes) {
        return getLedgerCodes(ledgerCodes,
                ledgerCode -> ledgerCode.getName().equals("Landlord Payments")).stream().findAny()
                .orElse("328");

    }

}
