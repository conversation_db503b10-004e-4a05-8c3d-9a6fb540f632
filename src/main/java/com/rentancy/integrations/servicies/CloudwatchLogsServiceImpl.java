package com.rentancy.integrations.servicies;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.CloudwatchLogRecord;
import com.rentancy.integrations.servicies.persistence.ConversationService;
import com.rentancy.integrations.servicies.persistence.OrganisationService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.stream.Collectors;

@RequiredArgsConstructor
class CloudwatchLogsServiceImpl implements CloudwatchLogsService {

    private static final Logger log = LogManager.getLogger(CloudwatchLogsServiceImpl.class);

    private final Config config;
    private final OrganisationService organisationService;
    private final ConversationService conversationService;
    private final AppSyncServiceProvider appSyncServiceProvider;

    @Override
    public void handleRecord(CloudwatchLogRecord record) {
        var conversationId = config.getErrorChannelConversationId();
        var message  = String.join(
                "\n",
                "Log group: " + record.getLogGroup(),
                "Log stream: " + record.getLogStream(),
                record.getLogEvents().stream().map(CloudwatchLogRecord.LogEvent::getMessage).collect(Collectors.joining("\n"))
        );
        var conversation = conversationService.findConversation(conversationId);
        var organisationId = conversation.getOrganisationId();
        var organisation = organisationService.getOrganisation(organisationId);

        log.info("Sending message - {}, {}, {}", message, organisationId, conversationId);

        appSyncServiceProvider.createMessage(organisationId, conversationId, organisation.getBotUser(), message, "TEXT");
    }
}
