package com.rentancy.integrations.servicies;

import com.rentancy.integrations.pojos.*;
import com.rentancy.integrations.pojos.IncomeArrearsSummary.ArrearsSortBy;
import com.rentancy.integrations.pojos.IncomeArrearsSummary.ArrearsSortOrder;
import com.rentancy.integrations.pojos.PropertyLedgersSummary.PropertyLedgersTableType;

import javax.annotation.Nullable;
import java.time.Clock;
import java.util.List;

public interface PortfolioService {
    void collectAutoInvoiceTenancies(AutoInvoiceEvent.SourceType sourceType, Clock clock);

    void raiseTenancyInvoice(TenancyInvoiceSenderPayload payload);

    void raiseCommissionBill(TenancyInvoiceSenderPayload payload);

    String createManualInvoice(String cognitoId, String tenancyId);

    void createTenancyInvoice(String organisationId, TenancyInvoiceInput invoiceInput);

    void matchInvoice(PortfolioInvoiceMatcherPayload payload);

    void addPropertyTrackingCode(PropertyTrackingCodePayload payload);

    void saveLandlordBill(Tenancy tenancy, String amountPaid, String invoiceId);

    void updateLandlordBill(String propertyId);

    void sendLandlordBill(String cognitoId, String id, String statementId);

    void sendLandlordBills(String cognitoId, String statementId, LandlordBillsRequest request);

    List<InvoiceAttachment> getInvoiceAttachments(String invoiceId, String organisationId);

    void raiseLandlordCommission(String organisationId, String tenant, String token, List<String> tenancyIds);

    boolean invoiceRecordProcessed(String messageId);

    void saveInvoiceRecord(String messageId);

    void createPropertyBill(String organisationId, PropertyBillCreationCommand body);

    void postInvoiceHistory();

    void raiseOverseasResidentBill(TenancyInvoiceSenderPayload payload);

    void bulkPayoutLandlordBills(LandlordBillBulkPayoutRequest request);

    IncomeArrearsSummary getIncomeArrearsSummary(String organisationId, ArrearsSortBy sortBy, ArrearsSortOrder sortOrder, @Nullable String filter, int page, int limit);

    byte[] getPropertyLedgersSummary(String organisationId, String propertyId, PropertyLedgersTableType tableType, String startDate, String endDate, @Nullable String filterTenancyReference, BackendResponseFormat format);

    void sendIncomeArrearsSummary(String organisationId, String receiverId, @Nullable String filter);

    byte[] getIncomeArrearsSummaryReport(String organisationId, @Nullable String filter);

    void triggerAutoJournalNow(String organisationId);

    void collectAutoJournalTenancies();

    void sendMonthlyJournalBills(String organisationId);

    void sendLandlordBillV2(String organisationId, LandlordBillsRequestV2 request);

    PropertyExpensesDetails calculateParentPropertyExpenses(String parentPropertyId, int limit, int page);

    void sendLandlordBillV3(String organisationId, LandlordBillsRequestV3 request);

    void createDepositBill(DepositBillCreationCommand body);

    void returnDeposit(DepositReturnCommand command);

    void payBillWithFloat(PayWithFloatPayload payload);
}
