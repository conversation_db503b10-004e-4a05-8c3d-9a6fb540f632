package com.rentancy.integrations.servicies;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.*;
import com.rentancy.integrations.pojos.IncomeArrearsSummary.ArrearsSortBy;
import com.rentancy.integrations.pojos.IncomeArrearsSummary.ArrearsSortOrder;
import com.rentancy.integrations.pojos.IncomeArrearsSummary.IncomeArrearsSummaryItem;
import com.rentancy.integrations.pojos.Organisation;
import com.rentancy.integrations.pojos.Invoice.XeroInvoicePayment;
import com.rentancy.integrations.pojos.Organisation.LedgerCode;
import com.rentancy.integrations.pojos.PropertyLedgersSummary.PropertyLedgersLineItem;
import com.rentancy.integrations.pojos.PropertyLedgersSummary.PropertyLedgersLineItemType;
import com.rentancy.integrations.pojos.User;
import com.rentancy.integrations.pojos.PropertyLedgersSummary.PropertyLedgersTableType;
import com.rentancy.integrations.pojos.User.Email;
import com.rentancy.integrations.servicies.autojournal.TrackingCategoryService;
import com.rentancy.integrations.servicies.payout.PayoutProcessor;
import com.rentancy.integrations.servicies.payout.ProcessLandlordPayoutCmd;
import com.rentancy.integrations.servicies.persistence.InvoiceService;
import com.rentancy.integrations.servicies.persistence.OrganisationService;
import com.rentancy.integrations.servicies.persistence.PropertyService;
import com.rentancy.integrations.servicies.persistence.UserService;
import com.rentancy.integrations.servicies.rent.NextInvoiceDateCalculator;
import com.rentancy.integrations.servicies.rent.NextInvoiceDateCalculator.RentPeriod;
import com.rentancy.integrations.servicies.xero_integration.callback.AfterCreationOfRaiseCommissionBillData;
import com.rentancy.integrations.servicies.xero_integration.queue.InvoiceOutboundSender;
import com.rentancy.integrations.servicies.xero_integration.queue.dto.CallbackOutbound;
import com.rentancy.integrations.servicies.xero_integration.queue.dto.SqsInvoiceMessagePattern;
import com.rentancy.integrations.servicies.xero_integration.queue.dto.SqsInvoiceType;
import com.rentancy.integrations.util.SentryErrors;
import com.rentancy.integrations.util.Utils;
import com.xero.models.accounting.Invoice;
import com.xero.models.accounting.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.math3.util.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.Temporal;
import java.util.Currency;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.rentancy.integrations.pojos.Integration.IntegrationService.XERO;
import static com.rentancy.integrations.pojos.Integration.IntegrationStatus.FAILED;
import static com.rentancy.integrations.pojos.Invoice.StatusEnum.DRAFT;
import static com.rentancy.integrations.pojos.Invoice.StatusEnum.SUBMITTED;
import static com.rentancy.integrations.pojos.Invoice.TypeEnum.ACCPAY;
import static com.rentancy.integrations.pojos.Organisation.Type.LANDLORD;
import static com.rentancy.integrations.pojos.RentInvoiceHistory.RentHistoryType.*;
import static com.rentancy.integrations.util.JSONUtils.wrappedToJsonString;
import static com.rentancy.integrations.util.Utils.*;
import static java.time.ZoneOffset.UTC;
import static java.time.temporal.ChronoUnit.DAYS;
import static java.util.stream.Collectors.*;

public class PortfolioXeroProcessor extends PortfolioBaseProcessor {
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("dd-MM-yyyy");

    private static final Set<String> LEDGER_CODES_RELEVANT_FOR_TENANT_LEDGER = Set.of(
            "Amendment Fee", "Cleaning Fee", "Maintenance - Damages", "Keys & Fobs", "Littering Fee",
            "Lockout Fee", "Parking Admin Fee", "Retained Discount Fee", "Retained Holding Fee",
            "Takeover Fee", "Maintenance - Charges", "Refund Fee", "Tenant Balances"
    );

    private static final Set<String> LEDGER_CODES_RELEVANT_FOR_LANDLORD_LEDGER = Set.of(
            "Rent Income", "Other", "Council Tax", "Maintenance", "Appliances", "Furniture", "Landlord Balances",
            "White Goods", "Electric", "Gas", "Internet", "Water", "Move-Outs", "Professional Fees", "SDLT",
            "Miscellaneous", "Management Fees", "Cleaning", "Insurance", "Waste Removal"
    );

    private static final Set<String> LEDGER_CODES_RELEVANT_FOR_DEPOSITS_LEDGER = Set.of("Deposits");

    private static final Logger log = LogManager.getLogger(PortfolioXeroProcessor.class);

    private static final String DELIMITER = ", ";

    private final XeroClient xeroClient;

    private final XeroService xeroService;

    private final PayoutProcessor payoutProcessor;

    private final TrackingCategoryService trackingCategoryService;

    private final InvoiceOutboundSender invoiceOutboundSender;

    public PortfolioXeroProcessor(Config config, IntegrationService integrationService, UserService userService,
                                  InvoiceService invoiceService, OrganisationService orgService, PropertyService propertyService,
                                  SQSClient sqsClient, TenancyService tenancyService, ReportService reportService,
                                  AppSyncServiceProvider appSyncServiceProvider, XeroClient xeroClient, XeroService xeroService, NextInvoiceDateCalculator nextInvoiceDateCalculator, Clock clock,
                                  TrackingCategoryService trackingCategoryService, InvoiceOutboundSender invoiceOutboundSender) {
        super(config, integrationService, userService, invoiceService, orgService, propertyService, sqsClient, tenancyService, reportService, nextInvoiceDateCalculator, appSyncServiceProvider, clock);
        this.xeroClient = xeroClient;
        this.xeroService = xeroService;
        this.invoiceOutboundSender = invoiceOutboundSender;
        this.payoutProcessor = new PayoutProcessor(xeroClient, userService, invoiceService, propertyService);
        this.trackingCategoryService = trackingCategoryService;
    }

    static Temporal startExecutionTime = Instant.now();

    static Temporal setExecutionTime(Temporal time) {
        startExecutionTime = time;
        return startExecutionTime;
    }

    private String getPropertyBudgetReference(Property property, String tenancyReference) {
        return propertyService.findPropertyBudgets(property.getId())
                .stream()
                .filter(budget -> Arrays.asList(budget.getReferences().split(",")).contains(tenancyReference))
                .findFirst()
                .map(PropertyBudget::getReferenceNumber)
                .map(number -> property.getReference() + "/" + number)
                .orElse(property.getReference());
    }

    private Invoice constructInvoice(ConstructRentInvoiceCommand cmd) {
        var tenancy = cmd.tenancy();
        var primaryTenant = Optional
                .ofNullable(tenancy.getPrimaryTenant())
                .orElseThrow(() -> new IllegalStateException("Primary tenant not found"));
        var rentValue = tenancy.getRent();
        var rent = BigDecimal.valueOf(rentValue).movePointLeft(2).doubleValue();
        var reference = tenancy.getReference();
        var organisationId = tenancy.getOrganisation();
        var landlordVat = Optional.ofNullable(tenancy.getLandlordVat()).orElse(false);
        var propertyId = tenancy.getProperty();
        var tenancyType = tenancy.getType();
        var tenancyTitle = tenancy.getTitle();
        var dateFormatter = new SimpleDateFormat("dd MMM yyyy");

        var organisation = organisationService.getOrganisation(organisationId);
        log.info("Organisation - {}", wrappedToJsonString(organisation));
        var property = propertyService.getProperty(propertyId);
        var landlord = Optional.ofNullable(property.getPrimaryLandlordId())
                .map(id -> userService.findUser(id, false))
                .orElseGet(() -> {
                    return getOrEmpty(property.getLandlords())
                            .stream()
                            .findFirst()
                            .map(id -> userService.findUser(id, false))
                            .orElse(null);
                });
        var currency = Optional.ofNullable(organisation).map(Organisation::getCurrency).orElse(tenancy.getSettings().getCurrency());
        var user = Optional.ofNullable(userService.findUser(primaryTenant, false))
                .orElseThrow(() -> new IllegalStateException("Primary tenant user not found"));
        var contact = getXeroContact(cmd.contacts(), user);
        var trackingOption = getPropertyBudgetReference(property, tenancy.getReference());
        var propertyTrackingName = findMappedTrackingCategory(organisation, "Property");
        var contactsTrackingName = findMappedTrackingCategory(organisation, "Contacts");
        var tracking = new ArrayList<>(getTrackingItem(cmd.trackingCategories(), cmd.tenant(), cmd.token(), propertyTrackingName, trackingOption));
        Optional.ofNullable(landlord)
                .map(Utils::formatUser)
                .ifPresent(name -> tracking.addAll(getTrackingItem(cmd.trackingCategories(), cmd.tenant(), cmd.token(), contactsTrackingName, name)));

        var invoiceStartDate = dateFormatter.format(Date.from(cmd.rentPeriod().startTime().toInstant()));
        var invoiceEndDate = dateFormatter.format(Date.from(cmd.rentPeriod().endTime().toInstant()));
        var invoiceTemplate = findInvoiceTemplate(organisation, tenancyType.name());
        var rentDescription = getRentDescription(invoiceTemplate, tenancyTitle, tenancyType.name(), invoiceStartDate, invoiceEndDate);
        var brandingThemeId = findBrandingTheme(cmd.brandingThemes(), invoiceTemplate);
        var rentIncomeLedgerCode = Optional
                .ofNullable(tenancy.getSettings().getTenancyLedgerCode())
                .filter(ledgerCode -> Objects.nonNull(ledgerCode.getCode()))
                .map(LedgerCode::getCode)
                .orElse(Optional.ofNullable(organisation)
                        .map(Organisation::getLedgerCodes)
                        .map(ledgerCodes -> mapToLedgerCode(tenancyType, ledgerCodes).stream().findAny().orElse("200"))
                        .orElse("200"));
        var invoice = new Invoice()
                .type(Invoice.TypeEnum.ACCREC)
                .status(cmd.status())
                .date(toXeroDate(cmd.issueDate().toEpochMilli()))
                .dueDate(toXeroDate(cmd.dueDate().toEpochMilli()))
                .currencyCode(CurrencyCode.fromValue(currency))
                .reference(reference)
                .contact(contact.isCustomer(true).isSupplier(false))
                .lineAmountTypes(landlordVat ? LineAmountTypes.EXCLUSIVE : LineAmountTypes.NOTAX)
                .lineItems(new ArrayList<>(List.of(new LineItem()
                                .quantity(BigDecimal.ONE.doubleValue())
                                .unitAmount(rent)
                                .description(rentDescription)
                                .accountCode(rentIncomeLedgerCode)
                                .taxType(landlordVat ? "OUTPUT2" : "NONE")
                                .tracking(tracking),
                        new LineItem().description("Property: " + getPropertyDescription(property)))));

        if (LANDLORD != organisation.getType()) {
            invoice.getLineItems()
                    .add(new LineItem().description("Landlord: " + getOrganisationDescription(formatUser(landlord), organisation)));
            Optional.ofNullable(landlord)
                    .map(User::getVat)
                    .ifPresent(vat -> invoice.getLineItems().add(new LineItem().description("VAT Number " + vat)));
        }

        Optional.ofNullable(brandingThemeId).ifPresent(invoice::setBrandingThemeID);

        return invoice;
    }

    private List<String> mapToLedgerCode(Tenancy.TenancyType tenancyType, List<LedgerCode> ledgerCodes) {
        switch (tenancyType) {
            case SERVICE_CHARGE:
                return getLenderCodes(ledgerCodes, ledgerCode -> ledgerCode.getName().equals("Service Charge Income"));
            case LEASE:
                return getLenderCodes(ledgerCodes, ledgerCode -> ledgerCode.getName().equals("Lease Income"));
            case GROUND_RENT:
                return getLenderCodes(ledgerCodes, ledgerCode -> ledgerCode.getName().equals("Ground Rent Income"));
            default:
                return getLenderCodes(ledgerCodes, ledgerCode -> ledgerCode.getName().equals("Rent Income"));
        }
    }

    private UUID findBrandingTheme(BrandingThemes brandingThemes, Organisation.InvoiceTemplate invoiceTemplate) {
        try {
            return Optional.ofNullable(invoiceTemplate)
                    .map(Organisation.InvoiceTemplate::getTemplateName)
                    .map(template -> {
                        return brandingThemes
                                .getBrandingThemes()
                                .stream()
                                .filter(brandingTheme -> brandingTheme.getName().equals(template))
                                .findAny()
                                .orElse(null);
                    })
                    .map(BrandingTheme::getBrandingThemeID)
                    .orElse(null);
        } catch (Exception e) {
            log.error("Failed to get branding theme", e);
        }

        return null;
    }

    @Override
    public void raiseTenancyInvoicesSafe(TenancyInvoiceSenderPayload payload, Organisation organisation) {
        try {
            raiseTenancyInvoice(payload);
        } catch (HttpClientErrorException e) {
            SentryErrors.catchException(e);
            var statusCode = e.getStatusCode();
            log.info("Status code - {}", statusCode);

            if (statusCode == HttpStatus.UNAUTHORIZED || statusCode == HttpStatus.FORBIDDEN) {
                onInvoiceRaiseForbiddenFailure(payload, organisation);
                integrationService.updateIntegrationStatus(payload.getOrganisationId(), FAILED, Integration.IntegrationService.XERO);
            }
            log.error("Failed to create batch of invoices", e);
        }
    }

    private void onInvoiceRaiseForbiddenFailure(TenancyInvoiceSenderPayload payload, Organisation organisation) {
        var emailQueue = config.getEmailTemplateNotificationQueue();
        var queueRegion = config.getEmailNotificationRegion();
        var supportEmail = config.getRentancySupportEmail();
        var dateFormatter = new SimpleDateFormat("dd MMM yyyy");

        var adminUser = userService.findUser(organisation.getAdminUser(), false);
        var email = Optional
                .ofNullable(adminUser.getCognitoEmail())
                .orElseGet(() -> adminUser.getEmails().stream().findFirst().map(Email::getEmail)
                        .orElseThrow(() -> new IllegalStateException("Receiver email not found - " + organisation.getAdminUser())));

        var bodyObj = payload.getTenancies().stream().map(id -> propertyService.getTenancy(id, true))
                .map(tenancy -> {
                    var rentPeriod = nextInvoiceDateCalculator.calculateRentPeriodForSendDate(payload.getSendDate().atZone(UTC), tenancy);
                    var invoiceStartDate = dateFormatter.format(Date.from(rentPeriod.startTime().toInstant()));
                    var invoiceEndDate = dateFormatter.format(Date.from(rentPeriod.endTime().toInstant()));
                    var tenancyType = tenancy.getType();
                    var invoiceTemplate = findInvoiceTemplate(organisation, tenancyType.name());
                    var tenancyTitle = tenancy.getTitle();
                    var rentDescription = getRentDescription(invoiceTemplate, tenancyTitle, tenancyType.name(), invoiceStartDate, invoiceEndDate);

                    var map = new HashMap<String, String>();
                    map.put("reference", tenancy.getReference());
                    map.put("description", rentDescription);
                    return map;
                }).collect(toUnmodifiableList());
        var body = wrappedToJsonString(Map.of("invoices", bodyObj));

        var emailCustomer = EmailSenderPayload.builder()
                .organisationId(organisation.getId())
                .email(email)
                .subject("LoftyWorks Update: Auto Invoicing Error")
                .body(body)
                .type("XERO_ON_INVOICE_RAISE_FAILURE_CUSTOMER")
                .build();
        sqsClient.enqueue(emailQueue, queueRegion, wrappedToJsonString(emailCustomer));

        var emailSupport = EmailSenderPayload.builder()
                .organisationId(organisation.getId())
                .email(supportEmail)
                .subject("Auto Invoice Failure: " + organisation.getName())
                .body(body)
                .type("XERO_ON_INVOICE_RAISE_FAILURE_SUPPORT")
                .build();
        sqsClient.enqueue(emailQueue, queueRegion, wrappedToJsonString(emailSupport));
    }

    private void raiseTenancyInvoice(TenancyInvoiceSenderPayload payload) {
        var token = payload.getToken();
        var tenant = payload.getTenant();

        var tenancyIds = payload.getTenancies();
        var organisationId = payload.getOrganisationId();
        var sendDate = payload.getSendDate().atZone(UTC);
        var issueDate = payload.getIssueDate().atZone(UTC);

        log.info("Tenancies - {}", tenancyIds.size());

        var tenancies = tenancyIds.stream().map(id -> propertyService.getTenancy(id, true)).collect(toUnmodifiableList());

        var contacts
                = xeroClient.listContactsWithRetry(tenant, token, Instant.parse(XeroClient.DEFAULT_XERO_START_TIMESTAMP));
        // Because of Xero api limit
        sleep(1000);
        var trackingCategories = xeroClient.getTrackingCategoriesWithRetry(tenant, token);
        var brandingThemes = xeroClient.getBrandingThemesWithRetry(tenant, token);
        // Because of Xero api limit
        sleep(1000);

        log.info("Tracking categories - {}", wrappedToJsonString(trackingCategories.getTrackingCategories()));
        log.info("Branding themes - {}", wrappedToJsonString(brandingThemes));

        var invoices = tenancies
                .stream()
                .filter(tenancy -> {
                    var dueDate = sendDate.plusDays(tenancy.getSettings().getInvoiceRentInAdvanceDays());
                    var invoiceExists = invoiceService.autoInvoiceExists(tenancy.getId(), dueDate, TENANCY_INVOICE);
                    log.info("Due date, Invoice exists - {}, {}", dueDate, invoiceExists);

                    return !invoiceExists;
                })
                .map(tenancy -> {
                    Invoice invoice;
                    try {
                        var rentPeriod = nextInvoiceDateCalculator.calculateRentPeriodForSendDate(sendDate, tenancy);
                        var dueDate = sendDate.plusDays(tenancy.getSettings().getInvoiceRentInAdvanceDays());
                        var constructInvoiceCmd = ConstructRentInvoiceCommand.builder()
                                .tenancy(tenancy)
                                .tenant(tenant)
                                .token(token)
                                .rentPeriod(rentPeriod)
                                .status(Invoice.StatusEnum.AUTHORISED)
                                .contacts(contacts)
                                .trackingCategories(trackingCategories)
                                .brandingThemes(brandingThemes)
                                .dueDate(dueDate.toInstant())
                                .issueDate(payload.getIssueDate())
                                .build();
                        invoice = constructInvoice(constructInvoiceCmd);
                    } catch (Exception e) {
                        log.error("Failed to construct rent invoice for - " + wrappedToJsonString(tenancy), e);
                        invoice = new Invoice().hasErrors(true);
                    }

                    return invoice;
                })
                .filter(invoice -> !invoice.getHasErrors())
                .collect(toUnmodifiableList());

        log.info("Invoices - {}", invoices.size());

        if (invoices.isEmpty()) {
            log.info("There are no invoices");
            return;
        }

        var batches = ListUtils.partition(invoices, 150);

        List<Tenancy.Settings> updatedPaymentData = new ArrayList<>();
        batches.forEach(batch -> {
            try {
                var response = xeroClient.createInvoicesWithRetry(batch, tenant, token);

                log.info("Response - {}", wrappedToJsonString(response));

                var rentInvoiceHistoryItems = response.stream().map(invoiceResponse -> {
                    var status = invoiceResponse.getStatusAttributeString();
                    var warnings = invoiceResponse.getWarnings();
                    var errors = invoiceResponse.getValidationErrors();
                    var reference = invoiceResponse.getReference();
                    var contact = invoiceResponse.getContact();
                    var tenancy = tenancies
                            .stream()
                            .filter(t -> t.getReference().equals(reference))
                            .findAny()
                            .orElseThrow();
                    var rentPeriod = nextInvoiceDateCalculator.calculateRentPeriodForSendDate(sendDate, tenancy);
                    var invoiceHistoryEntry = RentInvoiceHistory.builder()
                            .periodFromDate(rentPeriod.startTime().toInstant().toString())
                            .periodEndDate(rentPeriod.endTime().toInstant().toString())
                            .tenancyId(tenancy.getId())
                            .propertyId(tenancy.getProperty())
                            .againstUser(tenancy.getPrimaryTenant())
                            .type(TENANCY_INVOICE)
                            .organisationId(organisationId)
                            .tenantId(tenant)
                            .build();

                    if ("OK".equals(status) || "WARNING".equals(status)) {
                        var invoiceId = invoiceResponse.getInvoiceID();
                        invoiceHistoryEntry = invoiceHistoryEntry
                                .toBuilder()
                                .xeroId(invoiceId.toString())
                                .successful(true)
                                .message(status + DELIMITER + warnings.stream().map(ValidationError::getMessage).collect(joining(DELIMITER)))
                                .build();

                        var settings = tenancy.settingsWithRentPaymentRecorded(issueDate, rentPeriod);
                        tenancy.setSettings(settings);
                        updatedPaymentData.add(settings);

                        sendInvoiceEmailToTenant(organisationId, invoiceId.toString(), tenancy, rentPeriod, contact, invoiceResponse.getAmountDue(), invoiceResponse.getDueDate(), invoiceResponse.getCurrencyCode().getValue());
                    } else if ("ERROR".equals(status)) {
                        invoiceHistoryEntry = invoiceHistoryEntry
                                .toBuilder()
                                .successful(false)
                                .message(status + DELIMITER + errors.stream().map(ValidationError::getMessage).collect(joining(DELIMITER)))
                                .build();
                        SentryErrors.catchException(new Exception(invoiceHistoryEntry.getMessage()));
                    }

                    return invoiceHistoryEntry;
                }).collect(toUnmodifiableList());

                log.info("History items - {}", rentInvoiceHistoryItems);
                invoiceService.saveInvoiceHistory(rentInvoiceHistoryItems);
                log.info("Updating settings rent payment information for {} tenancies", updatedPaymentData.size());
                propertyService.updateTenancySettings(updatedPaymentData);
            } catch (HttpClientErrorException e) {
                var statusCode = e.getStatusCode();

                if (statusCode == HttpStatus.UNAUTHORIZED || statusCode == HttpStatus.FORBIDDEN) {
                    throw e;
                } else {
                    SentryErrors.catchException(e);
                    log.info("Status code - {}", statusCode);
                    log.error("Failed to create batch of invoices", e);
                }
            }
        });
    }

    private void sendInvoiceEmailToTenant(String organisationId, String invoiceId, Tenancy tenancy, RentPeriod rentPeriod, Contact contact, Double amountDue, String dueDate, String currencyCode) {
        try {
            if (tenancy.getSettings().isSendInvoiceToTenant()) {
                var invoiceQueue = config.getEmailInvoiceQueue();
                var queueRegion = config.getEmailNotificationRegion();
                var dateFormatter = new SimpleDateFormat("yyyy-MM-dd");
                var invoiceStartDate = dateFormatter.format(Date.from(rentPeriod.startTime().toInstant()));
                var invoiceEndDate = dateFormatter.format(Date.from(rentPeriod.endTime().toInstant()));
                Optional.ofNullable(contact.getEmailAddress())
                        .ifPresent(email -> {
                            var invoicePayload = EmailInvoicePayload
                                    .builder()
                                    .organisation(organisationId)
                                    .to(email)
                                    .subject(getRentDescription(null, tenancy.getTitle(), tenancy.getType().name(), invoiceStartDate, invoiceEndDate))
                                    .contactName(contact.getName())
                                    .propertyAddress(tenancy.getAddress())
                                    .amount(String.join(DEFAULT_VALUE, Currency.getInstance(currencyCode).getSymbol(), new BigDecimal(amountDue).toString()))
                                    .date(dateFormatter.format(Date.from(toInstant(dueDate))))
                                    .link(UriComponentsBuilder
                                            .fromUriString(String.join("/", "https:/", config.getRentancyDomain()))
                                            .path(String.join("/", "/xero", "invoice"))
                                            .queryParam("organisationId", organisationId)
                                            .queryParam("invoiceId", invoiceId)
                                            .build()
                                            .toUriString())
                                    .organisation(organisationId)
                                    .build();

                            sqsClient.enqueue(invoiceQueue, queueRegion, wrappedToJsonString(invoicePayload));
                        });
            }
        } catch (Exception e) {
            log.error("Failed to send email to tenant", e);
        }
    }


    private List<String> getLenderCodes(List<LedgerCode> ledgerCodes,
                                        Predicate<LedgerCode> predicate) {
        return ledgerCodes
                .stream()
                .filter(predicate)
                .filter(ledgerCode -> Objects.nonNull(ledgerCode.getCode()))
                .map(LedgerCode::getCode)
                .collect(toUnmodifiableList());
    }

    private String getContractDescription(Tenancy tenancy) {
        return String.join(" ", tenancy.getReference(),
                Optional.ofNullable(tenancy.getTitle()).orElse(DEFAULT_VALUE));
    }

    // TODO: probably can be safely removed, there are only 2 successful calls of this method during last 2 years on prod
    @Override
    public void raiseLandlordCommission(String tenant, String token, List<String> tenancyIds, Organisation organisation) {
        log.info("Tenancies size - {}", tenancyIds.size());
        var invoiceList = new ArrayList<Invoice>();
        var contacts
                = xeroClient.listContactsWithRetry(tenant, token, Instant.parse(XeroClient.DEFAULT_XERO_START_TIMESTAMP));
        var trackingCategories = xeroClient.getTrackingCategoriesWithRetry(tenant, token);

        log.info("Tracking categories - {}", wrappedToJsonString(trackingCategories.getTrackingCategories()));

        var tenancies = tenancyIds.stream().map(id -> propertyService.getTenancy(id, true)).collect(toUnmodifiableList());

        for (var tenancy : tenancies) {
            var tenancySettings = tenancy.getSettings();
            if (tenancySettings.getFeeType().equals("PERCENTAGE_OF_AMOUNT_RECEIVED")) {
                continue;
            }
            var totalCommission = tenancyService.getManagementFee(tenancy, null);
            var property = propertyService.getProperty(tenancy.getProperty());
            var landlords = Optional.ofNullable(tenancy.getLandlords()).orElse(List.of());

            if (landlords.isEmpty()) {
                log.error("No landlords found to raise a commission invoice in tenancy " + tenancy.getId());
                continue;
            }


            var invoicedLandlord = Optional.ofNullable(property.getPrimaryLandlordId())
                    .map(id -> userService.findUser(id, false))
                    .orElseGet(() -> {
                        return landlords.stream().findFirst().map(id -> userService.findUser(id, false)).orElseThrow();
                    });
            var contact = getXeroContact(contacts, invoicedLandlord);

            var currency = Optional.ofNullable(organisation).map(Organisation::getCurrency).orElse(tenancy.getSettings().getCurrency());

            var now = Instant.now();
            var dueDate = now.plus(tenancySettings.getInvoiceRentInAdvanceDays(), DAYS);
            var description = getCommissionBillDescription(dueDate.atZone(UTC), tenancy);

            // In the future we might change logic to split the bill equally between landlords. Currently we bill only the first one
            var billedLandlords = landlords.stream().findFirst().stream().collect(toUnmodifiableList());
            var commission = totalCommission.movePointRight(2)
                    .divide(new BigDecimal(billedLandlords.size()), RoundingMode.HALF_UP)
                    .movePointLeft(2);
            var shouldAddVAT = organisation.isAddCommissionBillVat();

            for (String landlordId : billedLandlords) {
                var landlord = userService.findUser(landlordId, false);
                if (Objects.isNull(landlord)) {
                    log.error("Landlord user not found - {}", landlordId);
                    continue;
                }
                var propertyTrackingName = findMappedTrackingCategory(organisation, "Property");
                var contactsTrackingName = findMappedTrackingCategory(organisation, "Contacts");

                var landlordPaymentLedgerCode = Optional.ofNullable(organisation)
                        .map(Organisation::getLedgerCodes)
                        .map(ledgerCodes -> getLenderCodes(ledgerCodes,
                                ledgerCode -> ledgerCode.getName().equals("Management Fees")).stream().findAny().orElse("326"))
                        .orElse("326");
                var tracking = new ArrayList<>(getTrackingItem(trackingCategories, tenant, token, propertyTrackingName, property.getReference()));
                Optional.ofNullable(formatUser(landlord))
                        .ifPresent(name -> tracking.addAll(getTrackingItem(trackingCategories, tenant, token, contactsTrackingName, name)));

                var invoice = new Invoice()
                        .type(Invoice.TypeEnum.ACCPAY)
                        .status(Invoice.StatusEnum.AUTHORISED)
                        .date(toXeroDate(now.toEpochMilli()))
                        .dueDate(toXeroDate(dueDate.toEpochMilli()))
                        .currencyCode(CurrencyCode.fromValue(currency))
                        .invoiceNumber(property.getReference())
                        .contact(contact.isSupplier(true).isCustomer(false))
                        .lineAmountTypes(shouldAddVAT ? LineAmountTypes.EXCLUSIVE : LineAmountTypes.NOTAX)
                        .lineItems(new ArrayList<>(List.of(new LineItem()
                                        .quantity(BigDecimal.ONE.doubleValue())
                                        .unitAmount(commission.doubleValue())
                                        .taxType(shouldAddVAT ? "INPUT2" : "NONE")
                                        .description(description)
                                        .tracking(tracking)
                                        .accountCode(landlordPaymentLedgerCode),
                                new LineItem().description("Property: " + getPropertyDescription(property)),
                                new LineItem().description(String.join(" ", "Landlord:", formatUser(landlord)))))
                        );

                log.info("Date - {}", now);
                log.info("Commission Bill - {}", wrappedToJsonString(invoice));

                invoiceList.add(invoice);
            }
        }

        log.info("Bills - {}", invoiceList.size());

        try {
            var response = xeroClient.createInvoicesWithRetry(invoiceList, tenant, token);

            log.info("Response - {}", wrappedToJsonString(response));

            var rentInvoiceHistoryItems = response.stream().map(invoiceResponse -> {
                var status = invoiceResponse.getStatusAttributeString();
                var warnings = invoiceResponse.getWarnings();
                var errors = invoiceResponse.getValidationErrors();
                var reference = invoiceResponse.getInvoiceNumber();
                var invoiceId = invoiceResponse.getInvoiceID();

                var property = propertyService.findPropertyWithOrganisationAndReference(organisation.getId(), reference).orElseThrow();

                var invoiceHistoryEntry = RentInvoiceHistory.builder()
                        .propertyId(property.getId())
                        .againstUser(Optional.ofNullable(property.getPrimaryLandlordId()).orElse(property.getLandlords().get(0)))
                        .type(LANDLORD_COMMISSION)
                        .organisationId(organisation.getId())
                        .tenantId(tenant)
                        .build();

                if ("OK".equals(status) || "WARNING".equals(status)) {
                    invoiceHistoryEntry = invoiceHistoryEntry
                            .toBuilder()
                            .xeroId(invoiceId.toString())
                            .successful(true)
                            .message(status + DELIMITER + warnings.stream().map(ValidationError::getMessage).collect(joining(DELIMITER)))
                            .build();
                } else if ("ERROR".equals(status)) {
                    invoiceHistoryEntry = invoiceHistoryEntry
                            .toBuilder()
                            .successful(false)
                            .message(status + DELIMITER + errors.stream().map(ValidationError::getMessage).collect(joining(DELIMITER)))
                            .build();
                    SentryErrors.catchException(new Exception(invoiceHistoryEntry.getMessage()));
                }

                return invoiceHistoryEntry;
            }).collect(toUnmodifiableList());

            log.info("History items - {}", rentInvoiceHistoryItems);

            invoiceService.saveInvoiceHistory(rentInvoiceHistoryItems);
        } catch (HttpClientErrorException e) {
            SentryErrors.catchException(e);
            var statusCode = e.getStatusCode();
            log.info("Status code - {}", statusCode);

            if (statusCode == HttpStatus.UNAUTHORIZED || statusCode == HttpStatus.FORBIDDEN) {
                integrationService.updateIntegrationStatus(organisation.getId(), FAILED, Integration.IntegrationService.XERO);
            }
            throw e;
        }
    }

    @Override
    protected boolean sendLandlordBill(Organisation organisation, LandlordBill bill, @Nullable String customLandlordId, @Nullable Integer billAmount) {
        var tenancy = propertyService.findTenancyWithOrganisationAndReference(organisation.getId(), bill.getTenancyReference())
                .orElseThrow();
        var property = propertyService.getProperty(tenancy.getProperty());
        var parentProperty = Optional.ofNullable(property.getParentPropertyId())
                .map(propertyService::getParentProperty)
                .orElse(null);
        var landlordId = Optional.ofNullable(customLandlordId).orElse(Optional.ofNullable(property.getPrimaryLandlordId()).orElse(property.getLandlords().get(0)));
        var integration = integrationService.findIntegrationByOrganisationId(organisation.getId(), Integration.IntegrationService.XERO);
        var tenant = integration.getTenantId();
        var token = integration.getAccessToken();
        var contacts
                = xeroClient.listContactsWithRetry(tenant, token, Instant.parse(XeroClient.DEFAULT_XERO_START_TIMESTAMP));
        var trackingCategories = xeroClient.getTrackingCategoriesWithRetry(tenant, token);

        log.info("Tracking categories - {}", wrappedToJsonString(trackingCategories.getTrackingCategories()));

        var currency = Optional.ofNullable(organisation).map(Organisation::getCurrency).orElse(tenancy.getSettings().getCurrency());
        var landlord = Optional.ofNullable(userService.findUser(landlordId, false)).orElseThrow();
        var contact = getXeroContact(contacts, landlord);

        var now = Instant.now();
        var dueDate = toXeroDate(now.toEpochMilli());

        var landlordPaymentLedgerCode = Optional.ofNullable(organisation)
                .map(Organisation::getLedgerCodes)
                .map(this::getLandlordPaymentLedgerCode)
                .orElse("328");

        var trackingOption = getPropertyBudgetReference(property, tenancy.getReference());
        var propertyTrackingName = findMappedTrackingCategory(organisation, "Property");
        var contactsTrackingName = findMappedTrackingCategory(organisation, "Contacts");
        var tracking = new ArrayList<>(getTrackingItem(trackingCategories, integration.getTenantId(), integration.getAccessToken(), propertyTrackingName, trackingOption));
        Optional.ofNullable(landlord)
                .map(Utils::formatUser)
                .ifPresent(name -> tracking.addAll(getTrackingItem(trackingCategories, integration.getTenantId(), integration.getAccessToken(), contactsTrackingName, name)));

        var invoice = new Invoice()
                .type(Invoice.TypeEnum.ACCPAY)
                .status(Invoice.StatusEnum.AUTHORISED)
                .date(dueDate)
                .dueDate(dueDate)
                .currencyCode(CurrencyCode.fromValue(currency))
                .invoiceNumber(String.join("/", Optional.ofNullable(parentProperty).map(ParentProperty::getReference).orElse(property.getReference()), generateRandomString()))
                .contact(contact.isSupplier(true).isCustomer(false))
                .lineAmountTypes(LineAmountTypes.NOTAX)
                .lineItems(List.of(new LineItem()
                                .quantity(BigDecimal.ONE.doubleValue())
                                .unitAmount(Optional.ofNullable(billAmount).orElse(bill.getBillAmount()) * 1.0 / 100)
                                .accountCode(landlordPaymentLedgerCode)
                                .taxType("NONE")
                                .tracking(tracking)
                                .description("Client Payment " + new SimpleDateFormat("dd MMM yyyy").format(Date.from(Instant.now()))),
                        new LineItem().description("Property: " + getPropertyDescription(property)),
                        new LineItem().description("Contract: " + tenancy.getReference())));

        var invoiceHistoryBuilder = RentInvoiceHistory.builder()
                .tenancyId(tenancy.getId())
                .propertyId(tenancy.getProperty())
                .againstUser(landlord.getId())
                .type(LANDLORD_BILL)
                .organisationId(organisation.getId());

        try {
            var invoiceResult = xeroClient.createInvoiceWithRetry(invoice, integration.getTenantId(), integration.getAccessToken());
            var invoiceId = invoiceResult.getInvoiceID().toString();

            var invoiceHistory = invoiceHistoryBuilder
                    .xeroId(invoiceId)
                    .successful(true)
                    .message("200 - ok")
                    .build();
            invoiceService.saveInvoiceHistory(List.of(invoiceHistory));

            bill.setInvoiceId(invoiceId);
            bill.setDateRaised(Instant.now().toString());

            invoiceService.updateLandlordBill(bill);

            return !invoiceResult.getHasErrors();
        } catch (Exception e) {
            log.error("Failed to raise landlord bill ", e);
            var invoiceHistory = invoiceHistoryBuilder.successful(false)
                    .message(e.getMessage())
                    .build();

            invoiceService.saveInvoiceHistory(List.of(invoiceHistory));

            return false;
        }
    }

    private String getLandlordPaymentLedgerCode(List<LedgerCode> ledgerCodes) {
        return getLenderCodes(ledgerCodes,
                ledgerCode -> ledgerCode.getName().equals("Landlord Payments")).stream().findAny()
                .orElse("328");

    }

    @Override
    public List<InvoiceAttachment> getInvoiceAttachments(String invoiceId, Organisation organisation) {
        var integration = integrationService.findIntegrationByOrganisationId(organisation.getId(), Integration.IntegrationService.XERO);
        var invoice = invoiceService.getInvoice(invoiceId);

        return xeroClient.getInvoiceAttachmentsWithRetry(invoice.getInvoiceId(), integration.getTenantId(), integration.getAccessToken());
    }

    @Override
    public void sendLandlordBillV2(Organisation organisation, LandlordBillsRequestV2 request) {
        var landlordBill = invoiceService.getLandlordBill(request.getId());
        landlordBill.setBillAmount(request.getAmountToRaise().movePointRight(2).intValue());
        sendLandlordBill(landlordBill, null, null);
        updateLandlordInvoiceStatuses(organisation, request.getLineItemIds());
    }

    @Override
    public void sendLandlordBillV3(Organisation organisation, LandlordBillsRequestV3 request) {
        var integration = integrationService.findByOrganisationWithTokenRefresh(organisation.getId(), XERO);
        var tenant = integration.getTenantId();
        var token = integration.getAccessToken();
        var xeroCredentials = new XeroCredentials(integration.getTenantId(), integration.getAccessToken());
        // TODO: it's possible to query for multiple contacts by id in Xero API
        var contacts = xeroClient.listContactsNoPaginationWithRetry(tenant, token, Instant.parse(XeroClient.DEFAULT_XERO_START_TIMESTAMP));
        // TODO: it might be more efficient to query for required tracking categories than to query for all and filter
        var trackingCategories = xeroClient.getTrackingCategoriesWithRetry(tenant, token);
        var landlordBill = invoiceService.getLandlordBill(request.getId());

        request.getItems().forEach(landlordItem -> payoutProcessor.processPayout(
                ProcessLandlordPayoutCmd.builder()
                        .landlordBill(landlordBill)
                        .landlordItem(landlordItem)
                        .contacts(contacts)
                        .xeroCredentials(xeroCredentials)
                        .trackingCategories(trackingCategories)
                        .organisation(organisation)
                        .build()
        ));
        updateLandlordInvoiceStatuses(organisation, request.getLineItemIds());
    }

    private void updateLandlordInvoiceStatuses(Organisation organisation, List<String> lineItemIds) {
        var lineItems = invoiceService.getLineItems(new HashSet<>(lineItemIds));
        var integration = integrationService.findConnectedIntegration(organisation.getId(), organisation.getAdminUser(), Integration.IntegrationService.XERO);
        invoiceService.updateLineItemsFinalizedStatus(lineItemIds, true);

        var invoiceIds = lineItems.stream().map(com.rentancy.integrations.pojos.Invoice.LineItem::getInvoiceId).collect(toUnmodifiableSet());
        var invoices = invoiceService.getInvoices(invoiceIds);

        log.info("Invoices - {}", invoices);

        var xeroInvoicesToUpdate = invoices
                .stream()
                .filter(invoice -> ACCPAY.name().equals(invoice.getType()))
                .filter(invoice -> DRAFT.name().equals(invoice.getStatus()) || SUBMITTED.name().equals(invoice.getStatus()))
                .map(invoice -> new Invoice().invoiceID(UUID.fromString(invoice.getInvoiceId())).status(Invoice.StatusEnum.AUTHORISED))
                .collect(toUnmodifiableList());

        log.info("Request - {}", xeroInvoicesToUpdate);

        if (xeroInvoicesToUpdate.isEmpty()) {
            return;
        }

        var result = xeroClient.createInvoicesWithRetry(xeroInvoicesToUpdate, integration.getTenantId(), integration.getAccessToken());

        result.forEach(invoiceResponse -> {
            var status = invoiceResponse.getStatusAttributeString();
            var errors = invoiceResponse.getValidationErrors();
            var reference = invoiceResponse.getInvoiceNumber();

            if ("ERROR".equals(status)) {
                log.error("Failed to update invoice - {}, {}", reference, errors.stream().map(ValidationError::getMessage).collect(joining(", ")));
            }
        });
    }

    @Override
    public void createPropertyBill(PropertyBillCreationCommand body, Organisation organisation) {
        var propertyTrackingName = findMappedTrackingCategory(organisation, "Property");
        var contactsTrackingName = findMappedTrackingCategory(organisation, "Contacts");
        var adminUserId = organisation.getAdminUser();
        var integration = integrationService.findConnectedIntegration(organisation.getId(), adminUserId, Integration.IntegrationService.XERO);

        var tenantId = integration.getTenantId();
        var token = integration.getAccessToken();
        var fromUser = userService.findUser(body.getFromUserId(), false);
        var contact = Optional.ofNullable(fromUser.getXeroId()).map(id -> xeroClient.getContactWithRetry(id, tenantId, token))
                .orElseThrow(() -> new IllegalStateException("Failed to find contact in Xero - " + fromUser.getId()));
        var lineAmountTypes = LineAmountTypes.valueOf(body.getTaxType());

        var tenant = integration.getTenantId();
        var trackingCategories = xeroClient.getTrackingCategoriesWithRetry(tenant, token);

        Property property = null;
        ParentProperty parentProperty = null;
        List<LineItemTracking> tracking;
        if (body.isParentProperty()) {
            parentProperty = propertyService.getParentProperty(body.getPropertyId());
            tracking = new ArrayList<>(getTrackingItem(trackingCategories, tenant, token, propertyTrackingName, parentProperty.getReference()));
        } else {
            property = propertyService.getProperty(body.getPropertyId());
            tracking = new ArrayList<>(getTrackingItem(trackingCategories, tenant, token, propertyTrackingName, property.getReference()));
        }

        (body.isParentProperty() ?
                getPrimaryLandlordIdsForParentProperty(List.of(parentProperty)) :
                getPrimaryLandlordIds(List.of(property)))
                .stream()
                .findFirst()
                .map(landlordId -> userService.findUser(landlordId, false))
                .map(Utils::formatUser)
                .ifPresent(name -> tracking.addAll(getTrackingItem(trackingCategories, tenant, token, contactsTrackingName, name)));

        var invoice = new Invoice()
                .type(Invoice.TypeEnum.ACCPAY)
                .status(Optional.ofNullable(body.getStatus()).map(Invoice.StatusEnum::valueOf).orElse(Invoice.StatusEnum.SUBMITTED))
                .date(toXeroDate(Instant.parse(body.getDate()).toEpochMilli()))
                .dueDate(toXeroDate(Instant.parse(body.getDueDate()).toEpochMilli()))
                .currencyCode(CurrencyCode.fromValue(organisation.getCurrency()))
                .invoiceNumber(String.join("/", body.isParentProperty() ? parentProperty.getReference() : property.getReference(), generateRandomString()))
                .contact(contact.isSupplier(true).isCustomer(false))
                .lineAmountTypes(lineAmountTypes);

        var lineItems = body.getLineItems()
                .stream()
                .map(lineItem -> new LineItem()
                        .taxType(lineItem.getTaxType())
                        .description(lineItem.getDescription())
                        .accountCode(lineItem.getLedgerCode())
                        .quantity(lineItem.getQuantity())
                        .unitAmount(lineItem.getUnitPrice())
                        .tracking(tracking))
                .collect(Collectors.toList());

        invoice.lineItems(lineItems);

        var result = xeroClient.createInvoiceWithRetry(invoice, tenantId, token);
        log.info("Response - {}", wrappedToJsonString(result));

        var validationErrors = result.getValidationErrors();
        var status = result.getStatusAttributeString();

        if ("ERROR".equals(status)) {
            throw new IllegalStateException("Failed to generate invoice - " +
                    validationErrors.stream().map(ValidationError::getMessage).collect(joining("\n")));
        }
    }

    @Override
    public void raiseCommissionBill(TenancyInvoiceSenderPayload payload, Organisation organisation) {
        var tenancyIds = payload.getTenancies();
        var token = payload.getToken();
        var tenant = payload.getTenant();
        var bills = new ArrayList<Invoice>();
        var organisationId = payload.getOrganisationId();
        var paidAmount = payload.getPaidAmount();
        var xeroInvoiceId = payload.getXeroInvoiceId();
        var journalingTenants = payload.getJournalingTenantAmount();
        var sendDate = payload.getSendDate();

        log.info("Tenancies size - {}", tenancyIds.size());

        var tenancies = propertyService.findTenancies(tenancyIds, true);

        var trackingCategories = xeroClient.getTrackingCategoriesWithRetry(tenant, token);
        var propertyTrackingName = findMappedTrackingCategory(organisation, "Property");
        var contactsTrackingName = findMappedTrackingCategory(organisation, "Contacts");

        // Because of Xero api limit
        sleep(1000);

        log.debug("Tracking categories - {}", wrappedToJsonString(trackingCategories.getTrackingCategories()));

        if (!StringUtils.hasLength(organisation.getXeroId())) {
            log.warn("Workspace contact isn't synced with Xero, we can't proceed - {}", wrappedToJsonString(organisation));
            return;
        }

        var contact = xeroClient.getContactWithRetry(organisation.getXeroId(), tenant, token);
        Map<String, Instant> tenancyDueDates = new HashMap<>();

        for (var tenancy : tenancies) {
            var tenancySettings = tenancy.getSettings();
            var tenancyId = tenancy.getId();
            BigDecimal totalCommission;
            Instant dueDate;

            if (tenancySettings.getFeeType().equals("PERCENTAGE_OF_AMOUNT_RECEIVED")) {
                log.info("Xero invoice id - {}", xeroInvoiceId);
                if (Objects.isNull(xeroInvoiceId)) {
                    dueDate = Instant.parse(payload.getDate());
                    totalCommission = tenancyService.getManagementFee(tenancy, journalingTenants.get(tenancy.getId()));
                } else {
                    var items = invoiceService.findHistoryItemsForInvoice(xeroInvoiceId);
                    log.info("History items - {}", items);
                    dueDate = items.stream().findAny().map(RentInvoiceHistory::getPeriodFromDate).map(Instant::parse)
                            .orElseThrow(() -> new IllegalStateException("Failed to find invoice date"));
                    totalCommission = tenancyService.getManagementFee(tenancy, paidAmount);
                }
            } else if (tenancySettings.getFeeType().equals("PERCENTAGE_OF_JOURNAL_AMOUNT_RECEIVED")) {
                dueDate = Instant.parse(payload.getDate());
                totalCommission = tenancyService.getManagementFee(tenancy, journalingTenants.get(tenancy.getId()));
            } else {
                totalCommission = tenancyService.getManagementFee(tenancy, null);
                var inAdvanceDays = tenancy.getSettings().getInvoiceRentInAdvanceDays();
                dueDate = sendDate.plus(inAdvanceDays, DAYS);
            }

            tenancyDueDates.put(tenancyId, dueDate);

            var property = propertyService.getProperty(tenancy.getProperty());
            var landlords = property.getLandlordList();

            if (landlords.isEmpty()) {
                log.error("No landlords found to raise a commission invoice in tenancy " + tenancy.getId());
                continue;
            }

            var currency = Optional.ofNullable(organisation).map(Organisation::getCurrency).orElse(tenancy.getSettings().getCurrency());

            var description = getCommissionBillDescription(dueDate.atZone(UTC), tenancy);

            // In the future we might change logic to split the bill equally between landlords. Currently we bill only the first one
            var billedLandlords = Optional.ofNullable(property.getPrimaryLandlordId())
                    .map(List::of)
                    .orElse(landlords.stream().findFirst().stream().collect(toUnmodifiableList()));
            var commission = totalCommission.divide(new BigDecimal(billedLandlords.size()), RoundingMode.HALF_UP);
            var shouldAddVAT = organisation.isAddCommissionBillVat();

            for (String landlordId : billedLandlords) {
                var landlord = userService.findUser(landlordId, false);
                if (Objects.isNull(landlord)) {
                    log.error("Landlord user not found - {}", landlordId);
                    continue;
                }
                var landlordPaymentLedgerCode = Optional
                        .ofNullable(tenancySettings.getFeeLedgerCode())
                        .filter(ledgerCode -> Objects.nonNull(ledgerCode.getCode()))
                        .map(LedgerCode::getCode)
                        .filter(code -> !code.isEmpty() && !code.isBlank())
                        .orElse(Optional.ofNullable(organisation)
                                .map(Organisation::getLedgerCodes)
                                .map(ledgerCodes -> getLenderCodes(ledgerCodes,
                                        ledgerCode -> ledgerCode.getName().equals("Management Fees")).stream().findAny().orElse("326"))
                                .orElse("326"));
                var tracking = new ArrayList<>(getTrackingItem(trackingCategories, tenant, token, propertyTrackingName, property.getReference()));
                Optional.ofNullable(formatUser(landlord))
                        .ifPresent(name -> tracking.addAll(getTrackingItem(trackingCategories, tenant, token, contactsTrackingName, name)));

                var invoice = new Invoice()
                        .type(Invoice.TypeEnum.ACCPAY)
                        .status(Invoice.StatusEnum.SUBMITTED)
                        .date(toXeroDate(payload.getIssueDate().toEpochMilli()))
                        .dueDate(toXeroDate(dueDate.toEpochMilli()))
                        .currencyCode(CurrencyCode.fromValue(currency))
                        .invoiceNumber(String.join("/", property.getReference(), tenancy.getReference(), generateRandomString()))
                        .contact(contact.isSupplier(true).isCustomer(false))
                        .lineAmountTypes(shouldAddVAT ? (organisation.isAddCommissionBillVatInclusive() ? LineAmountTypes.INCLUSIVE : LineAmountTypes.EXCLUSIVE) : LineAmountTypes.NOTAX)
                        .lineItems(new ArrayList<>(List.of(new LineItem()
                                        .quantity(BigDecimal.ONE.doubleValue())
                                        .unitAmount(commission.doubleValue())
                                        .taxType(shouldAddVAT ? "INPUT2" : "NONE")
                                        .description(description)
                                        .tracking(tracking)
                                        .accountCode(landlordPaymentLedgerCode),
                                new LineItem().description("Property: " + getPropertyDescription(property)),
                                new LineItem().description("Contract: " + getContractDescription(tenancy)),
                                new LineItem().description(String.join(" ", "Landlord:", formatUser(landlord)))))
                        );

                if (shouldAddVAT) {
                    var vatNumberDescription = Optional.ofNullable(organisation.getCommissionBillVatNumber()).orElse("");
                    invoice.getLineItems().add(new LineItem().description(organisation.getAddressLine1() + " VAT number " + vatNumberDescription));
                }

                if (commission.compareTo(BigDecimal.ZERO) == 0) {
                    log.info("Commission is zero, skipping the bill for landlord - {}", landlordId);
                    continue;
                }

                log.info("Due date - {}", dueDate);
                log.info("Commission Bill - {}", wrappedToJsonString(invoice));

                bills.add(invoice);
            }
        }

        log.info("Bills - {}", bills.size());

        var batches = ListUtils.partition(bills, 150);

        batches.forEach(batch -> {
            try {
                var response = xeroClient.createInvoicesWithRetry(batch, tenant, token);

                log.info("Response - {}", wrappedToJsonString(response));

                var rentInvoiceHistoryItems = response.stream().map(invoiceResponse -> {
                    var status = invoiceResponse.getStatusAttributeString();
                    var warnings = invoiceResponse.getWarnings();
                    var errors = invoiceResponse.getValidationErrors();
                    var invoiceId = invoiceResponse.getInvoiceID();
                    var lineItemDescriptions = invoiceResponse
                            .getLineItems()
                            .stream().map(LineItem::getDescription)
                            .collect(toUnmodifiableList());
                    var tenancy = tenancies
                            .stream()
                            .filter(t -> lineItemDescriptions.stream().anyMatch(desc -> desc.contains(t.getReference())))
                            .findAny()
                            .orElse(null);

                    var invoiceHistoryEntry = RentInvoiceHistory.builder()
                            .type(COMMISSION)
                            .organisationId(organisationId)
                            .tenantId(tenant)
                            .build();

                    var dueDate = Optional.ofNullable(tenancy)
                            .map(it -> tenancyDueDates.get(it.getId()))
                            .orElse(null);
                    if (tenancy != null && dueDate != null) {
                        var rentPeriod = nextInvoiceDateCalculator.calculateRentPeriodForDueDate(dueDate.atZone(UTC), tenancy);
                        invoiceHistoryEntry = invoiceHistoryEntry.toBuilder()
                                .periodFromDate(rentPeriod.startTime().toInstant().toString())
                                .periodEndDate(rentPeriod.endTime().toInstant().toString())
                                .tenancyId(tenancy.getId())
                                .propertyId(tenancy.getProperty())
                                .againstUser(tenancy.getPrimaryTenant())
                                .build();
                    }

                    if ("OK".equals(status) || "WARNING".equals(status)) {
                        invoiceHistoryEntry = invoiceHistoryEntry
                                .toBuilder()
                                .xeroId(invoiceId.toString())
                                .successful(true)
                                .message(status + DELIMITER + warnings.stream().map(ValidationError::getMessage).collect(joining(DELIMITER)))
                                .build();
                    } else if ("ERROR".equals(status)) {
                        invoiceHistoryEntry = invoiceHistoryEntry
                                .toBuilder()
                                .successful(false)
                                .message(status + DELIMITER + errors.stream().map(ValidationError::getMessage).collect(joining(DELIMITER)))
                                .build();
                        SentryErrors.catchException(new Exception(invoiceHistoryEntry.getMessage()));
                    }

                    return invoiceHistoryEntry;
                }).collect(toUnmodifiableList());

                log.info("History items - {}", rentInvoiceHistoryItems);

                invoiceService.saveInvoiceHistory(rentInvoiceHistoryItems);
            } catch (HttpClientErrorException e) {
                SentryErrors.catchException(e);
                var statusCode = e.getStatusCode();
                log.info("Status code - {}", statusCode);

                if (statusCode == HttpStatus.UNAUTHORIZED || statusCode == HttpStatus.FORBIDDEN) {
                    integrationService.updateIntegrationStatus(organisationId, FAILED, Integration.IntegrationService.XERO);
                }
                log.error("Failed to create batch of bills", e);
            }
        });
    }

    @Override
    public Map<String, String> raiseCommissionBillWithSqs(TenancyInvoiceSenderPayload payload, Organisation organisation) {
        var tenancyInvoiceNumberMapOutput = new HashMap<String, String>();
        var tenancyIds = payload.getTenancies();
        var token = payload.getToken();
        var tenant = payload.getTenant();
        var bills = new ArrayList<Pair<Invoice, CallbackOutbound>>();
        var organisationId = payload.getOrganisationId();
        var paidAmount = payload.getPaidAmount();
        var xeroInvoiceId = payload.getXeroInvoiceId();
        var journalingTenants = payload.getJournalingTenantAmount();
        var sendDate = payload.getSendDate();

        log.info("Tenancies size - {}", tenancyIds.size());

        var tenancies = propertyService.findTenancies(tenancyIds, true);

        var trackingCategories = xeroClient.getTrackingCategoriesWithRetry(tenant, token);
        var propertyTrackingName = findMappedTrackingCategory(organisation, "Property");
//        var contactsTrackingName = findMappedTrackingCategory(organisation, "Contacts");

        // Because of Xero api limit
        sleep(1000);

        log.debug("Tracking categories - {}", wrappedToJsonString(trackingCategories.getTrackingCategories()));

        if (!StringUtils.hasLength(organisation.getXeroId())) {
            log.warn("Workspace contact isn't synced with Xero, we can't proceed - {}", wrappedToJsonString(organisation));
            return tenancyInvoiceNumberMapOutput;
        }

        var contact = xeroClient.getContactWithRetry(organisation.getXeroId(), tenant, token);
        for (var tenancy : tenancies) {
            var tenancySettings = tenancy.getSettings();
            var tenancyId = tenancy.getId();
            BigDecimal totalCommission;
            Instant dueDate;

            if (tenancySettings.getFeeType().equals("PERCENTAGE_OF_AMOUNT_RECEIVED")) {
                log.info("Xero invoice id - {}", xeroInvoiceId);
                if (Objects.isNull(xeroInvoiceId)) {
                    dueDate = Instant.parse(payload.getDate());
                    totalCommission = tenancyService.getManagementFee(tenancy, journalingTenants.get(tenancy.getId()));
                } else {
                    var items = invoiceService.findHistoryItemsForInvoice(xeroInvoiceId);
                    log.info("History items - {}", items);
                    dueDate = items.stream().findAny().map(RentInvoiceHistory::getPeriodFromDate).map(Instant::parse)
                            .orElseThrow(() -> new IllegalStateException("Failed to find invoice date"));
                    totalCommission = tenancyService.getManagementFee(tenancy, paidAmount);
                }
            } else if (tenancySettings.getFeeType().equals("PERCENTAGE_OF_JOURNAL_AMOUNT_RECEIVED")) {
                dueDate = Instant.parse(payload.getDate());
                totalCommission = tenancyService.getManagementFee(tenancy, journalingTenants.get(tenancy.getId()));
            } else {
                totalCommission = tenancyService.getManagementFee(tenancy, null);
                var inAdvanceDays = tenancy.getSettings().getInvoiceRentInAdvanceDays();
                dueDate = sendDate.plus(inAdvanceDays, DAYS);
            }

            var property = propertyService.getProperty(tenancy.getProperty());
            var landlords = property.getLandlordList();

            if (landlords.isEmpty()) {
                log.error("No landlords found to raise a commission invoice in tenancy " + tenancy.getId());
                continue;
            }

            var currency = Optional.ofNullable(organisation).map(Organisation::getCurrency).orElse(tenancy.getSettings().getCurrency());

            var description = "Management Fee for period " + payload.getJournalPeriod().startTime().format(DATE_TIME_FORMATTER) + " to " + payload.getJournalPeriod().endTime().format(DATE_TIME_FORMATTER);

            // In the future we might change logic to split the bill equally between landlords. Currently we bill only the first one
            var billedLandlords = Optional.ofNullable(property.getPrimaryLandlordId())
                    .map(List::of)
                    .orElse(landlords.stream().findFirst().stream().collect(toUnmodifiableList()));
            var commission = totalCommission.divide(new BigDecimal(billedLandlords.size()), RoundingMode.HALF_UP);
            var shouldAddVAT = organisation.isAddCommissionBillVat();

            for (String landlordId : billedLandlords) {
                var landlord = userService.findUser(landlordId, false);
                if (Objects.isNull(landlord)) {
                    log.error("Landlord user not found - {}", landlordId);
                    continue;
                }
                var landlordPaymentLedgerCode = Optional
                        .ofNullable(tenancySettings.getFeeLedgerCode())
                        .filter(ledgerCode -> Objects.nonNull(ledgerCode.getCode()))
                        .map(LedgerCode::getCode)
                        .filter(code -> !code.isEmpty() && !code.isBlank())
                        .orElse(Optional.ofNullable(organisation)
                                .map(Organisation::getLedgerCodes)
                                .map(ledgerCodes -> getLenderCodes(ledgerCodes,
                                        ledgerCode -> ledgerCode.getName().equals("Management Fees")).stream().findAny().orElse("326"))
                                .orElse("326"));
                var tracking = new ArrayList<>(getTrackingItem(trackingCategories, tenant, token, propertyTrackingName, property.getReference()));
//                Optional.ofNullable(formatUser(landlord))
//                        .ifPresent(name -> tracking.addAll(getTrackingItem(trackingCategories, tenant, token, contactsTrackingName, name)));

                var invoice = new Invoice()
                        .type(Invoice.TypeEnum.ACCPAY)
                        .status(Invoice.StatusEnum.SUBMITTED)
                        .date(toXeroDate(payload.getIssueDate().toEpochMilli()))
                        .dueDate(toXeroDate(dueDate.toEpochMilli()))
                        .currencyCode(CurrencyCode.fromValue(currency))
                        .invoiceNumber(String.join("/", property.getReference(), tenancy.getReference(), generateRandomString()))
                        .contact(contact.isSupplier(true).isCustomer(false))
                        .lineAmountTypes(shouldAddVAT ? (organisation.isAddCommissionBillVatInclusive() ? LineAmountTypes.INCLUSIVE : LineAmountTypes.EXCLUSIVE) : LineAmountTypes.NOTAX)
                        .lineItems(new ArrayList<>(List.of(new LineItem()
                                        .quantity(BigDecimal.ONE.doubleValue())
                                        .unitAmount(commission.doubleValue())
                                        .taxType(shouldAddVAT ? "INPUT2" : "NONE")
                                        .description(description)
                                        .tracking(tracking)
                                        .accountCode(landlordPaymentLedgerCode),
                                new LineItem().description("Property: " + getPropertyDescription(property)),
                                new LineItem().description("Contract: " + getContractDescription(tenancy)),
                                new LineItem().description(String.join(" ", "Landlord:", formatUser(landlord)))))
                        );

                if (shouldAddVAT) {
                    var vatNumberDescription = Optional.ofNullable(organisation.getCommissionBillVatNumber()).orElse("");
                    invoice.getLineItems().add(new LineItem().description(organisation.getAddressLine1() + " VAT number " + vatNumberDescription));
                }

                if (commission.compareTo(BigDecimal.ZERO) == 0) {
                    log.info("Commission is zero, skipping the bill for landlord - {}", landlordId);
                    continue;
                }

                log.info("Due date - {}", dueDate);
                log.info("Commission Bill - {}", wrappedToJsonString(invoice));

                CallbackOutbound callback = CallbackOutbound.afterCreationOfRaiseCommissionBill(
                        config.getXeroInvoicesCallbackQueue(),
                        tenancyId,
                        tenant,
                        dueDate
                );

                bills.add(new Pair<>(invoice, callback));
                tenancyInvoiceNumberMapOutput.put(tenancy.getReference(), invoice.getInvoiceNumber());
            }
        }

        log.info("Bills - {}", bills.size());
        for (Pair<Invoice, CallbackOutbound> billWithCallback : bills) {
            Invoice bill = billWithCallback.getFirst();
            SqsInvoiceMessagePattern<Invoice> outboundMessage = SqsInvoiceMessagePattern.<Invoice>builder()
                    .organisationId(organisationId)
                    .tenantId(tenant)
                    .entityId(bill.getInvoiceNumber())
                    .type(SqsInvoiceType.CREATE_INVOICE)
                    .body(bill)
                    .callback(billWithCallback.getSecond())
                    .build();
            invoiceOutboundSender.sendMessageToTopic(outboundMessage);
            log.info("Sending commission bill to sqs - {}", outboundMessage);
        }
        return tenancyInvoiceNumberMapOutput;
    }

    public void handleAfterRaiseCommissionBillCallback(String organisationId,
                                                       String tenantId,
                                                       List<AfterCreationOfRaiseCommissionBillData> commissionBills) {
        List<String> tenancyIds = commissionBills.stream().map(AfterCreationOfRaiseCommissionBillData::getTenancyId).distinct().collect(toList());
        Map<String, Tenancy> tenanciesById = propertyService.findTenancies(tenancyIds, true)
                .stream()
                .collect(toMap(Tenancy::getId, Function.identity()));
        ArrayList<RentInvoiceHistory> rentInvoiceHistoryItems = new ArrayList<>();
        for (AfterCreationOfRaiseCommissionBillData commisionBill : commissionBills) {
            Invoice invoice = commisionBill.getInvoice();
            var status = invoice.getStatusAttributeString();
            var warnings = invoice.getWarnings();
            var errors = invoice.getValidationErrors();
            var invoiceId = invoice.getInvoiceID();
            var tenancy = tenanciesById.get(commisionBill.getTenancyId());

            var invoiceHistoryEntry = RentInvoiceHistory.builder()
                    .type(COMMISSION)
                    .organisationId(organisationId)
                    .tenantId(tenantId)
                    .build();

            var dueDate = commisionBill.getDueDate();
            if (tenancy != null && dueDate != null) {
                var rentPeriod = nextInvoiceDateCalculator.calculateRentPeriodForDueDate(dueDate.atZone(UTC), tenancy);
                invoiceHistoryEntry = invoiceHistoryEntry.toBuilder()
                        .periodFromDate(rentPeriod.startTime().toInstant().toString())
                        .periodEndDate(rentPeriod.endTime().toInstant().toString())
                        .tenancyId(tenancy.getId())
                        .propertyId(tenancy.getProperty())
                        .againstUser(tenancy.getPrimaryTenant())
                        .build();
            }

            if ("OK".equals(status) || "WARNING".equals(status)) {
                invoiceHistoryEntry = invoiceHistoryEntry
                        .toBuilder()
                        .xeroId(invoiceId.toString())
                        .successful(true)
                        .message(status + DELIMITER + warnings.stream().map(ValidationError::getMessage).collect(joining(DELIMITER)))
                        .build();
            } else if ("ERROR".equals(status)) {
                invoiceHistoryEntry = invoiceHistoryEntry
                        .toBuilder()
                        .successful(false)
                        .message(status + DELIMITER + errors.stream().map(ValidationError::getMessage).collect(joining(DELIMITER)))
                        .build();
                SentryErrors.catchException(new Exception(invoiceHistoryEntry.getMessage()));
            }

            rentInvoiceHistoryItems.add(invoiceHistoryEntry);
        }

        log.info("History items - {}", rentInvoiceHistoryItems);

        invoiceService.saveInvoiceHistory(rentInvoiceHistoryItems);
    }

    private String getCommissionBillDescription(ZonedDateTime dueDate, Tenancy tenancy) {
        var rentPeriod = nextInvoiceDateCalculator.calculateRentPeriodForDueDate(dueDate, tenancy);
        var startTime = rentPeriod.startTime();
        var managementFeePeriodStart = startTime.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        var managementFeePeriodEnd = managementFeePeriodStart.plusMonths(1).minusDays(1);

        return "Management Fee for period " + managementFeePeriodStart.format(DATE_TIME_FORMATTER) + " to " + managementFeePeriodEnd.format(DATE_TIME_FORMATTER);
    }

    /**
     * I assume that Rentancy is always in sync with Xero, because we have daily job to sync contacts
     */
    private Contact getXeroContact(Contacts allContacts, User user) {
        var contact = allContacts
                .getContacts()
                .stream().filter(xeroContact -> xeroContact.getContactID().toString().equals(user.getXeroId()))
                .findAny()
                .orElseThrow(() -> new IllegalStateException("Xero contact not found for " + user.getCompanyName()));

        return contact;
    }

    // TODO: probably can be safely removed, there's no place in any of the projects including FE where the endpoint is called
    @Override
    public String createManualInvoice(String cognitoId, String tenancyId, Organisation organisation) {
        var adminUserId = organisation.getAdminUser();
        var integration = integrationService.findConnectedIntegration(organisation.getId(), adminUserId, Integration.IntegrationService.XERO);

        var tenancy = propertyService.getTenancy(tenancyId, true);
        var tenantId = integration.getTenantId();
        var token = integration.getAccessToken();
        var contacts
                = xeroClient.listContactsWithRetry(tenantId, token, Instant.parse(XeroClient.DEFAULT_XERO_START_TIMESTAMP));
        var trackingCategories = xeroClient.getTrackingCategoriesWithRetry(tenantId, token);
        var brandingThemes = xeroClient.getBrandingThemesWithRetry(tenantId, token);

        log.info("Tracking categories - {}", wrappedToJsonString(trackingCategories.getTrackingCategories()));
        log.info("Branding themes - {}", wrappedToJsonString(brandingThemes));

        var dueDate = Instant.now();
        var rentPeriod = nextInvoiceDateCalculator.calculateRentPeriodForDueDate(dueDate.atZone(UTC), tenancy);
        var constructInvoiceCmd = ConstructRentInvoiceCommand.builder()
                .tenancy(tenancy)
                .tenant(tenantId)
                .token(token)
                .rentPeriod(rentPeriod)
                .status(Invoice.StatusEnum.DRAFT)
                .contacts(contacts)
                .trackingCategories(trackingCategories)
                .brandingThemes(brandingThemes)
                .dueDate(dueDate)
                .issueDate(dueDate)
                .build();
        var invoice = constructInvoice(constructInvoiceCmd);

        log.info("Invoice - {}", wrappedToJsonString(invoice));

        var result = xeroClient.createInvoiceWithRetry(invoice, tenantId, token);
        var validationErrors = result.getValidationErrors();
        var status = result.getStatusAttributeString();
        var invoiceHistoryEntry = RentInvoiceHistory.builder()
                .periodFromDate(rentPeriod.startTime().toInstant().toString())
                .periodEndDate(rentPeriod.endTime().toInstant().toString())
                .tenancyId(tenancy.getId())
                .propertyId(tenancy.getProperty())
                .againstUser(tenancy.getPrimaryTenant())
                .type(TENANCY_INVOICE)
                .organisationId(organisation.getId())
                .tenantId(tenantId)
                .build();

        if ("ERROR".equals(status)) {
            invoiceHistoryEntry = invoiceHistoryEntry
                    .toBuilder()
                    .successful(false)
                    .message(status + DELIMITER + result.getValidationErrors().stream().map(ValidationError::getMessage).collect(joining(DELIMITER)))
                    .build();

            invoiceService.saveInvoiceHistory(List.of(invoiceHistoryEntry));
            throw new IllegalStateException("Failed to generate invoice - " +
                    validationErrors.stream().map(ValidationError::getMessage).collect(joining("\n")));
        }

        invoiceHistoryEntry = invoiceHistoryEntry
                .toBuilder()
                .xeroId(result.getInvoiceID().toString())
                .successful(true)
                .message(status)
                .build();

        invoiceService.saveInvoiceHistory(List.of(invoiceHistoryEntry));

        return UriComponentsBuilder
                .fromUriString(config.getXeroUIRootPath())
                .path("/AccountsReceivable/Edit.aspx")
                .queryParam("InvoiceID", result.getInvoiceID().toString())
                .build()
                .toUriString();
    }

    @Override
    public void createTenancyInvoice(TenancyInvoiceInput invoiceInput, Organisation organisation) {
        var adminUserId = organisation.getAdminUser();
        var integration = integrationService.findConnectedIntegration(organisation.getId(), adminUserId, Integration.IntegrationService.XERO);

        var tenantId = integration.getTenantId();
        var token = integration.getAccessToken();
        var trackingCategories = xeroClient.getTrackingCategoriesWithRetry(tenantId, token);
        var brandingThemes = xeroClient.getBrandingThemesWithRetry(tenantId, token);
        var tenancy = propertyService.getTenancy(invoiceInput.getTenancyId(), true);
        var propertyId = tenancy.getProperty();
        log.info("Organisation - {}", wrappedToJsonString(organisation));
        var property = propertyService.getProperty(propertyId);
        var landlord = Optional.ofNullable(property.getPrimaryLandlordId())
                .map(id -> userService.findUser(id, false))
                .orElseGet(() -> {
                    return getOrEmpty(property.getLandlords())
                            .stream()
                            .findFirst()
                            .map(id -> userService.findUser(id, false))
                            .orElse(null);
                });
        var currency = Optional.ofNullable(organisation).map(Organisation::getCurrency).orElse(tenancy.getSettings().getCurrency());
        var user = Optional.ofNullable(userService.findUser(invoiceInput.getFromUserId(), false))
                .orElseThrow(() -> new IllegalStateException("Primary tenant user not found"));
        var contact = Optional.ofNullable(user.getXeroId()).map(id -> xeroClient.getContactWithRetry(id, tenantId, token))
                .orElseThrow(() -> new IllegalStateException("Failed to find contact in Xero - " + user.getId()));
        var trackingOption = getPropertyBudgetReference(property, tenancy.getReference());
        var propertyTrackingName = findMappedTrackingCategory(organisation, "Property");
        var contactsTrackingName = findMappedTrackingCategory(organisation, "Contacts");
        var tracking = new ArrayList<>(getTrackingItem(trackingCategories, tenantId, token, propertyTrackingName, trackingOption));
        Optional.ofNullable(landlord)
                .map(Utils::formatUser)
                .ifPresent(name -> tracking.addAll(getTrackingItem(trackingCategories, tenantId, token, contactsTrackingName, name)));

        var invoiceTemplate = findInvoiceTemplate(organisation, tenancy.getType().name());
        var brandingThemeId = Optional.ofNullable(invoiceInput.getBrandingName())
                .flatMap(brandingName -> brandingThemes
                        .getBrandingThemes()
                        .stream()
                        .filter(brandingTheme -> brandingTheme.getName().equals(brandingName))
                        .findAny())
                .map(BrandingTheme::getBrandingThemeID)
                .orElseGet(() -> findBrandingTheme(brandingThemes, invoiceTemplate));
        var lineItems = new ArrayList<>(invoiceInput
                .getLineItems()
                .stream()
                .map(lineItem -> new LineItem()
                        .quantity(lineItem.getQuantity())
                        .unitAmount(lineItem.getUnitPrice())
                        .description(lineItem.getDescription())
                        .accountCode(lineItem.getLedgerCode())
                        .taxType(lineItem.getTaxType())
                        .tracking(tracking)).collect(toUnmodifiableList()));

        lineItems.add(new LineItem().description("Property: " + getPropertyDescription(property)));

        var invoice = new Invoice()
                .type(Invoice.TypeEnum.ACCREC)
                .status(Invoice.StatusEnum.AUTHORISED)
                .date(toXeroDate(Instant.parse(invoiceInput.getDate()).toEpochMilli()))
                .dueDate(toXeroDate(Instant.parse(invoiceInput.getDueDate()).toEpochMilli()))
                .currencyCode(CurrencyCode.fromValue(currency))
                .reference(tenancy.getReference())
                .contact(contact.isCustomer(true).isSupplier(false))
                .lineAmountTypes(LineAmountTypes.valueOf(invoiceInput.getTaxType()))
                .lineItems(lineItems);

        Optional.ofNullable(brandingThemeId).ifPresent(invoice::setBrandingThemeID);

        var result = xeroClient.createInvoicesWithRetry(List.of(invoice), tenantId, token);

        log.info("Tenancy invoice result - {}", wrappedToJsonString(result));

        var invoiceResult = result.stream().findAny().orElseThrow();
        var invoiceHistoryEntry = RentInvoiceHistory.builder()
                .tenancyId(tenancy.getId())
                .propertyId(tenancy.getProperty())
                .againstUser(tenancy.getPrimaryTenant())
                .type(TENANCY_INVOICE)
                .organisationId(organisation.getId())
                .tenantId(tenantId)
                .manual(true)
                .build();

        if (Objects.nonNull(tenancy.getPeriod())) {
            var dueDate = Instant.parse(invoiceInput.getDueDate());
            var rentPeriod = nextInvoiceDateCalculator.calculateRentPeriodForDueDate(dueDate.atZone(UTC), tenancy);
            invoiceHistoryEntry.setPeriodFromDate(rentPeriod.startTime().toInstant().toString());
            invoiceHistoryEntry.setPeriodEndDate(rentPeriod.endTime().toInstant().toString());
        } else {
            log.info("Tenancy:{} has no period", tenancy.getId());
        }

        if (invoiceResult.getHasErrors()) {
            invoiceHistoryEntry = invoiceHistoryEntry
                    .toBuilder()
                    .successful(false)
                    .message(invoiceResult.getStatusAttributeString() + DELIMITER + invoiceResult.getValidationErrors().stream().map(ValidationError::getMessage).collect(joining(DELIMITER)))
                    .build();

            invoiceService.saveInvoiceHistory(List.of(invoiceHistoryEntry));
            throw new IllegalStateException(invoiceResult
                    .getValidationErrors()
                    .stream()
                    .map(ValidationError::getMessage)
                    .collect(joining(", ")));
        }

        invoiceHistoryEntry = invoiceHistoryEntry
                .toBuilder()
                .xeroId(invoiceResult.getInvoiceID().toString())
                .successful(true)
                .message(invoiceResult.getStatusAttributeString())
                .build();

        invoiceService.saveInvoiceHistory(List.of(invoiceHistoryEntry));
    }

    @Override
    public void addPropertyTrackingCode(PropertyTrackingCodePayload payload, Organisation organisation) {
        var reference = payload.getReference();
        var organisationId = payload.getOrganisationId();


        var propertyTrackingName = findMappedTrackingCategory(organisation, "Property");
        var adminUser = organisation.getAdminUser();

        var integration = integrationService.findConnectedIntegration(organisationId, adminUser, Integration.IntegrationService.XERO);

        var tenantId = integration.getTenantId();
        var token = integration.getAccessToken();

        var trackingCategories = xeroClient.getTrackingCategoriesWithRetry(tenantId, token);

        log.info("Tracking categories - {}", wrappedToJsonString(trackingCategories.getTrackingCategories()));

        createTrackingCategory(trackingCategories, tenantId, token, reference, propertyTrackingName);
    }

    @Override
    public void raiseOverseasResidentBill(TenancyInvoiceSenderPayload payload, Organisation organisation) {
        var invoiceList = new ArrayList<Invoice>();
        var tenant = payload.getTenant();
        var token = payload.getToken();
        var organisationId = payload.getOrganisationId();
        var sendDate = payload.getSendDate();
        var issueDate = payload.getIssueDate();
        var contacts
                = xeroClient.listContactsWithRetry(tenant, token, Instant.parse(XeroClient.DEFAULT_XERO_START_TIMESTAMP));

        var tenancies = payload.getTenancies()
                .stream()
                .map(id -> propertyService.getTenancy(id, true))
                .filter(tenancy -> {
                    var landlords = Optional.ofNullable(tenancy.getLandlords()).orElse(List.of());
                    if (landlords.isEmpty()) return false;

                    return landlords.stream()
                            .findFirst()
                            .map(landlord -> userService.findUser(landlord, false).isOverseasResident())
                            .orElse(false);
                })
                .collect(toUnmodifiableList());
        var againstUserId = organisation.getDefaultCountryTaxBotId();
        var againstUser = userService.findUser(againstUserId, false);
        var countryContact = getXeroContact(contacts, againstUser);

        for (var tenancy : tenancies) {
            var property = propertyService.getProperty(tenancy.getProperty());
            var currency = Optional.ofNullable(organisation).map(Organisation::getCurrency).orElse(tenancy.getSettings().getCurrency());
            var inAdvanceDays = tenancy.getSettings().getInvoiceRentInAdvanceDays();
            var dueDate = sendDate.plus(inAdvanceDays, DAYS);

            var paymentLedgerCode = Optional.ofNullable(organisation)
                    .map(Organisation::getLedgerCodes)
                    .map(ledgerCodes -> getLenderCodes(ledgerCodes,
                            ledgerCode -> ledgerCode.getName().equals("Non-Resident")).stream().findAny().orElse("330"))
                    .orElse("330");

            var rentValue = tenancy.getRent();
            var rent = BigDecimal.valueOf(rentValue).movePointLeft(2);

            if (Optional.ofNullable(tenancy.getLandlordVat()).orElse(false)) {
                rent = rent.multiply(new BigDecimal("1.2"));
            }

            var invoice = new Invoice()
                    .type(Invoice.TypeEnum.ACCPAY)
                    .status(Invoice.StatusEnum.AUTHORISED)
                    .date(toXeroDate(issueDate.toEpochMilli()))
                    .dueDate(toXeroDate(dueDate.toEpochMilli()))
                    .currencyCode(CurrencyCode.fromValue(currency))
                    .invoiceNumber(property.getReference())
                    .contact(countryContact)
                    .lineAmountTypes(organisation.isOverseasResidentBillVAT() ? LineAmountTypes.EXCLUSIVE : LineAmountTypes.NOTAX)
                    .lineItems(new ArrayList<>(List.of(new LineItem()
                                    .quantity(BigDecimal.ONE.doubleValue())
                                    .unitAmount(rent.multiply(new BigDecimal("0.2")).doubleValue())
                                    .taxType(organisation.isOverseasResidentBillVAT() ? "INPUT2" : "NONE")
                                    .description("")
                                    .accountCode(paymentLedgerCode)
                            ))
                    );

            log.info("Due date - {}", dueDate);
            log.info("Overseas resident Bill - {}", wrappedToJsonString(invoice));

            invoiceList.add(invoice);
        }

        log.info("overseas residents bills - {}", invoiceList.size());
        if (invoiceList.isEmpty()) {
            return;
        }

        try {
            var response = xeroClient.createInvoicesWithRetry(invoiceList, tenant, token);

            log.info("Response - {}", wrappedToJsonString(response));
        } catch (HttpClientErrorException e) {
            SentryErrors.catchException(e);
            var statusCode = e.getStatusCode();
            log.info("Status code - {}", statusCode);

            if (statusCode == HttpStatus.UNAUTHORIZED || statusCode == HttpStatus.FORBIDDEN) {
                integrationService.updateIntegrationStatus(organisationId, FAILED, Integration.IntegrationService.XERO);
            }
            throw e;
        }
    }

    private Optional<String> getPropertyLandlord(Property property) {
        if (Objects.nonNull(property.getPrimaryLandlordId())) {
            return Optional.of(property.getPrimaryLandlordId());
        } else if (Objects.nonNull(property.getLandlords())) {
            return property.getLandlords().stream().findFirst();
        }
        return Optional.empty();
    }

    private List<IncomeArrearsSummaryItem> formatArrearsLineItems(List<com.rentancy.integrations.pojos.Invoice.LineItem> lineItems, Property property, Tenancy tenancy,
                                                                  @Nullable User landLord, @Nullable User tenant) {

        var tenantImageId = Optional.ofNullable(tenant).flatMap(tenantUser -> Optional.ofNullable(tenantUser.getUserImageId())).orElse(null);
        var tenantNumbers = Optional.ofNullable(tenant).map(User::getPhones).orElse(List.of());
        var landLordImageId = Optional.ofNullable(landLord).map(landlord -> landLord.getUserImageId()).orElse(null);

        return lineItems.stream().filter(lineItem -> Objects.nonNull(lineItem.getInvoiceId()))
                .collect(groupingBy(com.rentancy.integrations.pojos.Invoice.LineItem::getInvoiceId))
                .entrySet()
                .stream()
                .map(entrySet -> {
                    var lineItem = entrySet.getValue()
                            .stream()
                            .findFirst()
                            .orElseThrow(() -> new RuntimeException(entrySet.getKey() + " has no line items"));

                    var concatenatedDescription = entrySet.getValue().stream()
                            .map(com.rentancy.integrations.pojos.Invoice.LineItem::getDescription)
                            .filter(Objects::nonNull)
                            .filter(Predicate.not(String::isBlank))
                            .collect(joining(", "));

                    var totalInvoiceAmount = entrySet.getValue()
                            .stream()
                            .map(tenancyService::getLineItemAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    var paidAmount = entrySet.getValue()
                            .stream()
                            .map(tenancyService::getLineItemPaidAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    return IncomeArrearsSummaryItem.builder()
                            .invoiceReference(lineItem.getParentNumber())
                            .tenancyReference(tenancyService.getParentReference(lineItem))
                            .tenantName(formatUser(tenant))
                            .tenantEmail(findUserEmail(tenant))
                            .landlordName(Optional.ofNullable(landLord).map(Utils::formatUser).orElse(DEFAULT_VALUE))
                            .tenantImageId(tenantImageId)
                            .tenantPhoneNumbers(tenantNumbers)
                            .landlordImageId(landLordImageId)
                            .propertyImageId(property.getCoverImage())
                            .propertyReference(property.getReference())
                            .propertyAddress(getPropertyDescription(property))
                            .invoiceDueDate(lineItem.getParentDueDate())
                            .daysInArrears(DAYS.between(Instant.parse(lineItem.getParentDueDate()), Instant.now()))
                            .totalInvoiceAmount(totalInvoiceAmount)
                            .arrearsAmount(totalInvoiceAmount.subtract(paidAmount))
                            .arrearsInvoiceCount(0)
                            .invoiceDescription(concatenatedDescription)
                            .fullyManagedTenancy(!tenancy.isDontCollectRent())
                            .tenancyStatus(tenancy.getStatus())
                            .tenancyType(tenancy.getType())
                            .tenancyStartDate(tenancy.getStartDate())
                            .tenancyEndDate(tenancy.getEndDate())
                            .build();
                })
                .sorted(Comparator.comparingLong(IncomeArrearsSummaryItem::getDaysInArrears))
                .collect(toUnmodifiableList());
    }


    @Override
    public IncomeArrearsSummary getIncomeArrearsSummary(ArrearsSortBy sortBy, ArrearsSortOrder sortOrder, @Nullable String filter, int page, int limit, Organisation organisation) {
        setExecutionTime(Instant.now());
        log.info("Getting income arrears summary for organisation: {}", organisation.getId());
        var propertiesFuture = CompletableFuture.supplyAsync(() -> propertyService.findPropertiesWithOrganisation(organisation.getId()));
        var tenanciesFuture = CompletableFuture.supplyAsync(() -> propertyService.findTenancies(organisation.getId(), null, false));
        var lineItemsFuture = CompletableFuture.supplyAsync(() -> invoiceService.findIncomeLineItems(organisation.getId()));

        var tenantTenanciesFuture = tenanciesFuture.thenApply(tenancies -> {
            log.info("getIncomeSummary thenApply tenancies future:{} ms", Duration.between(startExecutionTime, Instant.now()).toMillis());
            return tenancies.stream()
                    .filter(tenancy -> Objects.nonNull(tenancy.getPrimaryTenant()) || (Objects.nonNull(tenancy.getTenants()) && !tenancy.getTenants().isEmpty()))
                    .collect(groupingBy(tenancy -> Objects.nonNull(tenancy.getPrimaryTenant()) ? tenancy.getPrimaryTenant() : tenancy.getTenants().get(0)));
        });

        var ledgerCodesFuture = CompletableFuture.supplyAsync(() -> {
            log.info("getIncomeSummary thenApply ledgerCodes future:{} ms", Duration.between(startExecutionTime, Instant.now()).toMillis());
            return getLenderCodes(organisation.getLedgerCodes(), ledgerCode -> organisation.getIncomeLedgerCodeNames().contains(ledgerCode.getName()))
                    .stream()
                    .collect(toUnmodifiableSet());
        });

        var groupedLineItemsFuture = lineItemsFuture.thenCombine(ledgerCodesFuture, (lineItems, ledgerCodes) -> {
            log.info("getIncomeSummary thenCombine groupedLineItems future:{} ms", Duration.between(startExecutionTime, Instant.now()).toMillis());
            final int BATCH_SIZE = 100;

            List<List<com.rentancy.integrations.pojos.Invoice.LineItem>> batches = ListUtils.partition(lineItems, BATCH_SIZE);
            Map<String, List<com.rentancy.integrations.pojos.Invoice.LineItem>> groupedLineItems = new HashMap<>();

            for (List<com.rentancy.integrations.pojos.Invoice.LineItem> batch : batches) {
                batch.stream()
                        .filter(lineItem -> ledgerCodes.contains(lineItem.getAccountCode()) && tenancyService.getLineItemAmount(lineItem).compareTo(BigDecimal.ZERO) != 0)
                        .forEach(lineItem -> {
                            groupedLineItems
                                    .computeIfAbsent(lineItem.getTrackingName(), k -> new ArrayList<>())
                                    .add(lineItem);
                        });
            }

            return groupedLineItems;
        });

        var allUserIdsFuture = CompletableFuture.allOf(propertiesFuture, tenantTenanciesFuture, groupedLineItemsFuture)
                .thenApply(v -> {
                    log.info("getIncomeSummary thenApply allUserIds future:{} ms", Duration.between(startExecutionTime, Instant.now()).toMillis());
                    var properties = propertiesFuture.join();
                    var tenantTenancies = tenantTenanciesFuture.join();
                    var groupedLineItems = groupedLineItemsFuture.join();

                    return tenantTenancies.values().stream()
                            .flatMap(Collection::stream)
                            .flatMap(tenancy -> {
                                var property = properties.stream()
                                        .filter(p -> p.getId().equals(tenancy.getProperty()))
                                        .findAny()
                                        .orElse(null);

                                if (property == null || groupedLineItems.getOrDefault(property.getReference(), List.of()).isEmpty()) {
                                    return Stream.empty();
                                }

                                var list = new ArrayList<String>();
                                getPropertyLandlord(property).ifPresent(list::add);

                                if (Objects.nonNull(tenancy.getPrimaryTenant())) {
                                    list.add(tenancy.getPrimaryTenant());
                                } else if (Objects.nonNull(tenancy.getTenants())) {
                                    tenancy.getTenants().stream().findFirst().ifPresent(list::add);
                                }

                                list.addAll(Optional.ofNullable(tenancy.getGuarantors()).orElse(List.of()));
                                return list.stream();
                            })
                            .filter(Objects::nonNull)
                            .collect(Collectors.toUnmodifiableSet());
                });

        log.info("After collecting all user ids: {} ms", Duration.between(startExecutionTime, Instant.now()).toMillis());

        var usersFuture = allUserIdsFuture.thenApply(userService::findUsers);
        var todayFuture = CompletableFuture.supplyAsync(tenancyService::getToday);

        var propertyMapFuture = propertiesFuture.thenApply(properties -> {
            log.info("getIncomeSummary thenCombine propertiesFuture future:{} ms", Duration.between(startExecutionTime, Instant.now()).toMillis());
            return properties.stream().collect(Collectors.toMap(Property::getId, property -> property));
        });

        var userMapFuture = usersFuture.thenApply(users -> {
            log.info("getIncomeSummary thenCombine userMapFuture future:{} ms", Duration.between(startExecutionTime, Instant.now()).toMillis());
            return users.stream().collect(Collectors.toMap(User::getId, user -> user));
        });

        CompletableFuture.allOf(usersFuture, todayFuture, propertyMapFuture, userMapFuture).join();

        var propertyMap = propertyMapFuture.join();
        var userMap = userMapFuture.join();
        var today = todayFuture.join();

        log.info("Before calculating summary: {} ms", Duration.between(startExecutionTime, Instant.now()).toMillis());

        var BATCH_SIZE = 100;
        var summaryLines = tenantTenanciesFuture.join().entrySet().stream()
                .collect(Collectors.toList()) // Collect to list to enable batch processing
                .stream()
                .collect(Collectors.groupingBy(entrySet -> entrySet.getKey().hashCode() % BATCH_SIZE)) // Group by hash code modulo BATCH_SIZE
                .values().stream()
                .flatMap(batch -> batch.parallelStream() // Process each batch in parallel
                        .flatMap(entrySet -> processTenantTenancies(entrySet, propertyMap, groupedLineItemsFuture.join(), organisation, today, userMap, tenanciesFuture.join()))
                        .filter(item -> Objects.isNull(filter)
                                || item.getTenantName().contains(filter)
                                || item.getLandlordName().contains(filter)
                                || item.getPropertyAddress().contains(filter)
                                || item.getInvoiceReference().contains(filter)
                                || item.getTenancyReference().contains(filter)
                        )
                        .sorted(getIncomeArrearsSummaryItemComparator())
                )
                .collect(toUnmodifiableList());

        log.info("After calculating summary: {} ms", Duration.between(startExecutionTime, Instant.now()).toMillis());

        return buildSummary(organisation, filter, page, limit, summaryLines, sortBy);
    }


    private Stream<IncomeArrearsSummaryItem> processTenantTenancies(Map.Entry<String, List<Tenancy>> entrySet, Map<String, Property> propertyMap, Map<String, List<com.rentancy.integrations.pojos.Invoice.LineItem>> groupedLineItems, Organisation organisation, Instant today, Map<String, User> userMap, List<Tenancy> tenancies) {
        var tenantId = entrySet.getKey();
        var tenanciesList = entrySet.getValue();

        var tenanciesByProperty = tenanciesList.stream()
                .collect(Collectors.groupingBy(Tenancy::getProperty));

        var tenancySummaries = tenanciesByProperty.entrySet().stream()
                .map(propertyEntry -> {
                    var propertyId = propertyEntry.getKey();
                    var propertyTenancies = propertyEntry.getValue();

                    var property = propertyMap.get(propertyId);
                    if (property == null) {
                        log.error("Couldn't find property for tenancies: {}", wrappedToJsonString(propertyTenancies));
                        return null;
                    }

                    var relevantLineItems = groupedLineItems.getOrDefault(property.getReference(), List.of());
                    return tenancyService.calculateFinanceBalanceSummary(
                            organisation,
                            relevantLineItems,
                            organisation.getIncomeLedgerCodeNames(),
                            property.getReference(),
                            true,
                            today,
                            propertyTenancies
                    );
                })
                .filter(Objects::nonNull)
                .filter(summary -> !summary.getInArrearsLineItems().isEmpty())
                .collect(toUnmodifiableList());

        if (tenancySummaries.isEmpty()) {
            return Stream.empty();
        }

        var tenant = userMap.get(tenantId);

        return tenancySummaries.stream().flatMap(summary -> summary.getInArrearsLineItems().stream().findFirst().stream().map(lineItem -> mapToIncomeArrearsSummaryItem(lineItem, summary, propertyMap, tenancies, userMap, tenant)));
    }

    private IncomeArrearsSummaryItem mapToIncomeArrearsSummaryItem(com.rentancy.integrations.pojos.Invoice.LineItem lineItem, FinanceSummary summary, Map<String, Property> propertyMap, List<Tenancy> tenancies, Map<String, User> userMap, User tenant) {
        var property = propertyMap.values().stream()
                .filter(p -> lineItem.getTrackingName().equals(p.getReference()))
                .findFirst()
                .orElse(null);

        if (property == null) {
            log.error("Couldn't find property with id: {}", lineItem.getTrackingName());
            return null;
        }

        var tenancy = tenancies.stream()
                .filter(t -> t.getReference().equals(tenancyService.getParentReference(lineItem)))
                .findFirst()
                .orElse(null);

        if (tenancy == null) {
            log.error("Couldn't find tenancy with reference: {}", tenancyService.getParentReference(lineItem));
            return null;
        }

        var landLord = getPropertyLandlord(property)
                .flatMap(landlordId -> Optional.ofNullable(userMap.get(landlordId)))
                .orElse(null);

        var landLordImageId = Optional.ofNullable(landLord).map(User::getUserImageId).orElse(null);
        var landlordName = Optional.ofNullable(landLord).map(Utils::formatUser).orElse(DEFAULT_VALUE);
        var tenantImageId = Optional.ofNullable(tenant).map(User::getUserImageId).orElse(null);
        var tenantNumbers = Optional.ofNullable(tenant).map(User::getPhones).orElse(List.of());

        var tenantArrearsItems = summary.getInArrearsLineItems();
        var totalArrearsInvoiceAmounts = tenantArrearsItems.stream()
                .map(tenancyService::getLineItemAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        var hasGuarantors = tenancy.getGuarantors() != null && !tenancy.getGuarantors().isEmpty();
        var guarantors = Optional.ofNullable(tenancy.getGuarantors()).orElse(List.of()).stream()
                .map(userMap::get)
                .filter(Objects::nonNull)
                .collect(toUnmodifiableList());

        var daysInArrears = tenantArrearsItems.stream()
                .mapToLong(item -> DAYS.between(Instant.parse(item.getParentDueDate()), Instant.now()))
                .max()
                .orElseThrow();

        var arrearsInvoices = formatArrearsLineItems(tenantArrearsItems, property, tenancy, landLord, tenant);

        return mapToIncomeArrearsSummaryItem(lineItem, summary, property, tenancy, landLord, tenant, landlordName, tenantImageId, tenantNumbers, landLordImageId, totalArrearsInvoiceAmounts, hasGuarantors, guarantors, daysInArrears, arrearsInvoices, tenantArrearsItems);
    }

    private IncomeArrearsSummaryItem mapToIncomeArrearsSummaryItem(com.rentancy.integrations.pojos.Invoice.LineItem lineItem, FinanceSummary summary, Property property, Tenancy tenancy, User landLord, User tenant, String landlordName, String tenantImageId, List<User.Phone> tenantNumbers, String landLordImageId, BigDecimal totalArrearsInvoiceAmounts, boolean hasGuarantors, List<User> guarantors, long daysInArrears, List<IncomeArrearsSummaryItem> arrearsInvoices, List<com.rentancy.integrations.pojos.Invoice.LineItem> tenantArrearsItems) {
        return IncomeArrearsSummaryItem.builder()
                .invoiceReference(lineItem.getParentNumber())
                .tenancyReference(tenancyService.getParentReference(lineItem))
                .tenantName(formatUser(tenant))
                .tenantEmail(findUserEmail(tenant))
                .propertyId(property.getId())
                .tenancyId(tenancy.getId())
                .landlordName(landlordName)
                .tenantImageId(tenantImageId)
                .tenantPhoneNumbers(tenantNumbers)
                .landlordImageId(landLordImageId)
                .propertyImageId(property.getCoverImage())
                .propertyReference(property.getReference())
                .propertyAddress(getPropertyDescription(property))
                .invoiceDueDate(lineItem.getParentDueDate())
                .invoiceDescription(lineItem.getDescription())
                .daysInArrears(daysInArrears)
                .totalInvoiceAmount(totalArrearsInvoiceAmounts)
                .arrearsAmount(arrearsInvoices.stream().map(IncomeArrearsSummaryItem::getArrearsAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
                .hasGuarantors(hasGuarantors)
                .guarantors(guarantors)
                .fullyManagedTenancy(!tenancy.isDontCollectRent())
                .tenancyStatus(tenancy.getStatus())
                .tenancyType(tenancy.getType())
                .tenancyStartDate(tenancy.getStartDate())
                .tenancyEndDate(tenancy.getEndDate())
                .arrearsInvoiceCount((int) tenantArrearsItems.stream().map(com.rentancy.integrations.pojos.Invoice.LineItem::getInvoiceId).filter(Objects::nonNull).distinct().count())
                .arrearsInvoices(arrearsInvoices)
                .build();
    }

    private Comparator<IncomeArrearsSummaryItem> getIncomeArrearsSummaryItemComparator() {
        return Comparator.comparing(IncomeArrearsSummaryItem::getInvoiceReference)
                .thenComparing(IncomeArrearsSummaryItem::getTenancyReference)
                .thenComparing(IncomeArrearsSummaryItem::getTenantName)
                .thenComparing(IncomeArrearsSummaryItem::getLandlordName)
                .thenComparing(IncomeArrearsSummaryItem::getPropertyReference)
                .thenComparing(IncomeArrearsSummaryItem::getPropertyAddress)
                .thenComparing(IncomeArrearsSummaryItem::getInvoiceDueDate)
                .thenComparing(IncomeArrearsSummaryItem::getDaysInArrears)
                .thenComparing(IncomeArrearsSummaryItem::getTotalInvoiceAmount)
                .thenComparing(IncomeArrearsSummaryItem::getArrearsAmount);
    }


    private IncomeArrearsSummary buildSummary(Organisation organisation, String filter, int page, int limit,
                                              List<IncomeArrearsSummaryItem> summaryLines, ArrearsSortBy sortBy) {
        var items = (limit == 0 && page == 0) ? summaryLines : sliceIncomeArrearsSummaryItem(summaryLines, limit, page);

        return IncomeArrearsSummary.builder()
                .organisationName(organisation.getName())
                .incomeArrearsSummaryItemCount(summaryLines.size())
                .items(items)
                .invoiceTotalAmount(summaryLines.stream().map(IncomeArrearsSummaryItem::getTotalInvoiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
                .arrearsTotalAmount(summaryLines.stream().map(IncomeArrearsSummaryItem::getArrearsAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
                .totalArrearsInvoiceCount(summaryLines.stream().mapToLong(IncomeArrearsSummaryItem::getArrearsInvoiceCount).sum())
                .appliedFilter(filter)
                .page(page)
                .limit(limit)
                .sortedBy(sortBy)
                .build();
    }


    public List<IncomeArrearsSummaryItem> sliceIncomeArrearsSummaryItem(List<IncomeArrearsSummaryItem> list, int limit, int page) {
        var batches = ListUtils.partition(list, limit);
        return batches.size() < page ? List.of() : batches.get(page - 1);
    }

    @Override
    protected void updatePropertyBills(String cognitoId, String statementId, String organisationId, List<com.rentancy.integrations.pojos.Invoice> invoices) {
        var invoicesUpdateInput = new XeroInvoicesUpdateInput();
        invoicesUpdateInput.setOrganisationId(organisationId);
        invoicesUpdateInput.setStatus(Invoice.StatusEnum.AUTHORISED.name());
        invoicesUpdateInput.setInvoiceIds(invoices
                .stream()
                .map(com.rentancy.integrations.pojos.Invoice::getInvoiceId)
                .collect(toUnmodifiableList()));

        xeroService.updateInvoicesStatus(cognitoId, statementId, invoicesUpdateInput);
    }

    private List<LineItemTracking> getTrackingItem(TrackingCategories trackingCategories,
                                                   String tenant,
                                                   String token,
                                                   String categoryName,
                                                   String optionName) {
        try {
            var categoryOption
                    = this.trackingCategoryService.createTrackingCategory(trackingCategories, tenant, token, optionName, categoryName, xeroClient);

            return List.of(new LineItemTracking()
                    .name(categoryName)
                    .option(optionName)
                    .trackingCategoryID(categoryOption.getCategoryId())
                    .trackingOptionID(categoryOption.getOptionId()));
        } catch (Exception e) {
            log.error("Failed to get Tracking item - " + categoryName, e);
        }

        return List.of();
    }

    private TrackingCategoryOption createTrackingCategory(TrackingCategories trackingCategories,
                                                          String tenantId,
                                                          String token,
                                                          String optionName,
                                                          String categoryName) {
        var categoryId = Optional
                .ofNullable(findTrackingCategory(trackingCategories, categoryName))
                .map(TrackingCategory::getTrackingCategoryID)
                .orElseGet(() -> {
                    var result = xeroClient.createTrackingCategoryWithRetry(tenantId, token, categoryName);
                    var propertyTrackingCategory = findTrackingCategory(result, categoryName);
                    return propertyTrackingCategory.getTrackingCategoryID();
                });
        var optionId = xeroClient.createTrackingCategoryOptionWithRetry(tenantId, token, categoryId.toString(), optionName);

        return new TrackingCategoryOption(categoryId, optionId);
    }

    private TrackingCategory findTrackingCategory(TrackingCategories categories, String name) {
        return categories
                .getTrackingCategories()
                .stream()
                .filter(trackingCategory -> trackingCategory.getName().equals(name))
                .findAny()
                .orElse(null);
    }

    private String getOrganisationDescription(String landlord, Organisation organisation) {
        return List.of(
                        landlord,
                        "C/O " + organisation.getName(),
                        getDetail(organisation.getAddressLine1()),
                        getDetail(organisation.getPostcode()))
                .stream()
                .filter(StringUtils::hasText)
                .collect(joining(DELIMITER));
    }

    private String getAddressDescription(String addressLine1, String addressLine2, String addressLine3, String postcode, String town, String delimiter) {
        return List.of(
                        getDetail(addressLine1),
                        getDetail(addressLine2),
                        getDetail(addressLine3),
                        getDetail(postcode),
                        getDetail(town))
                .stream()
                .filter(StringUtils::hasText)
                .collect(joining(delimiter));
    }

    private String getPropertyDescription(Property property) {
        return getAddressDescription(property.getAddressLine1(), property.getAddressLine2(), property.getAddressLine3(), property.getPostcode(), property.getCity(), DELIMITER);
    }

    private String findUserEmail(@Nullable User user) {
        return Optional
                .ofNullable(user)
                .map(User::getCognitoEmail)
                .or(() -> Optional.ofNullable(user)
                        .map(User::getEmails)
                        .stream()
                        .flatMap(Collection::stream)
                        .findAny()
                        .map(Email::getEmail)
                ).orElse(null);
    }

    private PropertyLedgersLineItem formatToLedgersLineItem(String propertyAddress, SimpleDateFormat dateFormatter,
                                                            com.rentancy.integrations.pojos.Invoice.LineItem lineItem) {
        PropertyLedgersLineItemType itemType;
        BigDecimal dueAmount = tenancyService.getLineItemAmount(lineItem);
        BigDecimal debitAmount = BigDecimal.ZERO;
        BigDecimal creditAmount = BigDecimal.ZERO;

        if (lineItem.isBalanceTransfer()) {
            itemType = PropertyLedgersLineItemType.TRANSFER;
        } else if (Objects.nonNull(lineItem.getTransactionId())) {
            itemType = PropertyLedgersLineItemType.PAYMENT;
        } else if (lineItem.isParentIncome()) {
            itemType = PropertyLedgersLineItemType.INVOICE;
        } else {
            itemType = PropertyLedgersLineItemType.EXPENSE;
        }

        if (lineItem.isBalanceTransfer() || Objects.nonNull(lineItem.getTransactionId())) {
            debitAmount = lineItem.isIncome() ? BigDecimal.ZERO : dueAmount;
            creditAmount = lineItem.isIncome() ? dueAmount : BigDecimal.ZERO;
            dueAmount = BigDecimal.ZERO;
        }

        return PropertyLedgersLineItem.builder()
                .date(dateFormatter.format(Date.from(Instant.parse(lineItem.getParentDueDate()))))
                .timeInstant(Instant.parse(lineItem.getParentDueDate()))
                .lineItemType(itemType)
                .description(lineItem.getDescription())
                .propertyAddress(propertyAddress)
                .tenancyReference(lineItem.isIncome() ? tenancyService.getParentReference(lineItem) : DEFAULT_VALUE)
                .dueAmount(dueAmount)
                .debitAmount(debitAmount)
                .creditAmount(creditAmount)
                .heldAmount(BigDecimal.ZERO)
                .xeroNumber(tenancyService.getParentNumber(lineItem))
                .build();
    }

    private BigDecimal getOverPayment(String organisationId, Set<com.rentancy.integrations.pojos.Invoice> invoices) {
        var batchPaymentIds = invoices.stream()
                .flatMap(invoice -> Objects.isNull(invoice.getPayments()) ? Stream.of() : invoice.getPayments().stream())
                .map(XeroInvoicePayment::getBatchPaymentId)
                .filter(Objects::nonNull)
                .collect(toUnmodifiableSet());

        if (batchPaymentIds.isEmpty()) {
            return BigDecimal.ZERO;
        }

        return invoiceService.getOverPaymentsByBatchIds(organisationId, batchPaymentIds)
                .map(overPayment -> {
                    var creditedAmount = Optional.ofNullable(overPayment.getRemainingCredit())
                            .map(BigDecimal::new)
                            .orElse(BigDecimal.ZERO);

                    return "RECEIVE-OVERPAYMENT".equals(overPayment.getType()) ? creditedAmount : creditedAmount.multiply(new BigDecimal(-1));
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public PropertyLedgersSummary getPropertyLedgersSummary(String propertyId, PropertyLedgersTableType tableType, String userStartDate, String userEndDate, @Nullable String filterTenancyReference, Organisation organisation) {
        var property = propertyService.getProperty(propertyId);

        var startDate = getBeginningOfDay(userStartDate);
        var endDate = getBeginningOfDay(userEndDate);

        Set<String> tenancyReferences = Set.of();
        Set<String> ledgerCodeNames;
        switch (tableType) {
            case TENANT:
                tenancyReferences = propertyService.findPropertyTenancies(propertyId, false)
                        .stream()
                        .map(Tenancy::getReference)
                        .collect(toUnmodifiableSet());
                ledgerCodeNames = LEDGER_CODES_RELEVANT_FOR_TENANT_LEDGER;
                break;
            case LANDLORD:
                ledgerCodeNames = LEDGER_CODES_RELEVANT_FOR_LANDLORD_LEDGER;
                break;
            case DEPOSIT:
                ledgerCodeNames = LEDGER_CODES_RELEVANT_FOR_DEPOSITS_LEDGER;
                break;
            default:
                throw new RuntimeException("Unknown table type:" + tableType.name());
        }

        Set<String> relevantLedgerCodes = organisation.getLedgerCodes().stream()
                .filter(lc -> ledgerCodeNames.contains(lc.getName()))
                .map(LedgerCode::getCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toUnmodifiableSet());

        var finalTenancyReferences = tenancyReferences;
        var allLineItems = invoiceService.findLineItemsByTrackingName(organisation.getId(), property.getReference())
                .stream()
                .filter(lineItem -> Objects.nonNull(lineItem.getAccountCode()) && relevantLedgerCodes.contains(lineItem.getAccountCode()))
                .filter(lineItem -> finalTenancyReferences.isEmpty() || !lineItem.isIncome() ||
                        Optional.ofNullable(tenancyService.getParentReference(lineItem))
                                .filter(finalTenancyReferences::contains)
                                .isPresent())
                .filter(lineItem -> Objects.isNull(filterTenancyReference) || !lineItem.isIncome() ||
                        filterTenancyReference.equals(tenancyService.getParentReference(lineItem)) ||
                        tenancyService.getInvoiceLineItemTenancyReference(lineItem)
                                .filter(filterTenancyReference::equals)
                                .isPresent())
                .filter(tenancyService::invoiceHasAmount)
                .collect(toUnmodifiableList());

        var dateFormatter = new SimpleDateFormat("dd/MM/yyyy");
        var propertyAddress = getPropertyDescription(property);

        var lineItems = allLineItems.stream()
                .filter(lineItem -> Instant.parse(endDate).isAfter(Instant.parse(lineItem.getParentDueDate())))
                .map(lineItem -> formatToLedgersLineItem(propertyAddress, dateFormatter, lineItem));

        var relatedLineItems = allLineItems.stream()
                .filter(lineItem -> Instant.parse(endDate).isAfter(Instant.parse(lineItem.getParentDueDate())))
                .filter(lineItem -> Instant.parse(startDate).isBefore(Instant.parse(lineItem.getParentDueDate())))
                .filter(lineItem -> !lineItem.isBalanceTransfer())
                .collect(toUnmodifiableList());

        var paidInvoiceIds = relatedLineItems.stream()
                .filter(lineItem -> tenancyService.getLineItemPaidAmount(lineItem).compareTo(BigDecimal.ZERO) > 0)
                .map(com.rentancy.integrations.pojos.Invoice.LineItem::getInvoiceId)
                .filter(Objects::nonNull)
                .collect(toUnmodifiableSet());

        var paidInvoices = invoiceService.getInvoices(paidInvoiceIds);

        HashMap<String, List<com.rentancy.integrations.pojos.Invoice.LineItem>> invoiceLineItems = new HashMap<>();
        allLineItems.forEach(li -> invoiceLineItems.computeIfAbsent(li.getInvoiceId(), l -> new ArrayList<>()).add(li));
        paidInvoices.forEach(invoice -> invoice.setLineItems(invoiceLineItems.getOrDefault(invoice.getId(), List.of())));

        var invoicePayments = paidInvoices
                .stream()
                .flatMap(invoice ->
                        invoice.getPayments().stream()
                                .map(payment -> {
                                    var isIncome = Invoice.TypeEnum.ACCREC.getValue().equals(invoice.getType());
                                    // extract the sum of LineItem amount to only show the payment amount associated to this property
                                    // since we pay off the whole Invoice and not individual LineItems, we need to divide the LineItem amount
                                    // by the whole payment amount to get the "estimated" proportional payment for this LineItem
                                    var lineItemsAmountSum = invoice.getLineItems()
                                            .stream()
                                            .map(li -> li.getLineAmount())
                                            .map(BigDecimal::new)
                                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                                            .setScale(10);
                                    var paymentAmount = new BigDecimal(payment.getAmount()).setScale(10);
                                    var lineItemsProportion = lineItemsAmountSum.divide(new BigDecimal(invoice.getTotal()).setScale(10), RoundingMode.HALF_UP);
                                    var paymentAmountForThisProperty = lineItemsProportion.multiply(paymentAmount).setScale(2, RoundingMode.HALF_UP);
                                    log.info("sum: {}, payment: {}, proportion: {}, amount: {}", lineItemsAmountSum, paymentAmount, lineItemsProportion, paymentAmountForThisProperty);

                                    return PropertyLedgersLineItem.builder()
                                            .date(dateFormatter.format(Date.from(payment.getDate())))
                                            .timeInstant(payment.getDate())
                                            .lineItemType(PropertyLedgersLineItemType.PAYMENT)
                                            .description(DEFAULT_VALUE)
                                            .propertyAddress(propertyAddress)
                                            .tenancyReference(isIncome ? invoice.getReference() : DEFAULT_VALUE)
                                            .dueAmount(BigDecimal.ZERO)
                                            .debitAmount(isIncome ? BigDecimal.ZERO : paymentAmountForThisProperty)
                                            .creditAmount(isIncome ? paymentAmountForThisProperty : BigDecimal.ZERO)
                                            .heldAmount(BigDecimal.ZERO)
                                            .xeroNumber(isIncome ? invoice.getNumber() : invoice.getReference())
                                            .build();
                                })
                );

        var sortedItems = Stream.concat(lineItems, invoicePayments)
                .sorted(Comparator.comparing(PropertyLedgersLineItem::getTimeInstant))
                .collect(Collectors.toList());

        BigDecimal held = BigDecimal.ZERO;

        for (var item : sortedItems) {
            held = held.add(item.getCreditAmount()).subtract(item.getDebitAmount());
            item.setHeldAmount(held);
        }

        var startThreshold = Instant.parse(startDate);

        var periodItems = sortedItems.stream()
                .filter(item -> item.getTimeInstant().compareTo(startThreshold) >= 0)
                .collect(toUnmodifiableList());

        var openingBalanceAmount = sortedItems.stream()
                .filter(item -> item.getTimeInstant().isBefore(startThreshold))
                .reduce((first, second) -> second)
                .map(PropertyLedgersLineItem::getHeldAmount)
                .orElse(BigDecimal.ZERO);

        var closingBalanceAmount = Optional.of(sortedItems)
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(list.size() - 1).getHeldAmount())
                .orElse(BigDecimal.ZERO);

        var arrearsTotalAmount = relatedLineItems.stream()
                .map(lineItem -> tenancyService.getLineItemAmount(lineItem)
                        .subtract(tenancyService.getLineItemPaidAmount(lineItem)))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return PropertyLedgersSummary.builder()
                .openingBalanceAmount(openingBalanceAmount)
                .closingBalanceAmount(closingBalanceAmount)
                .debitTotalAmount(periodItems.stream().map(PropertyLedgersLineItem::getDebitAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
                .creditTotalAmount(periodItems.stream().map(PropertyLedgersLineItem::getCreditAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
                .arrearsTotalAmount(arrearsTotalAmount)
                .overPayment(getOverPayment(organisation.getId(), paidInvoices))
                .items(periodItems)
                .tableType(tableType)
                .startDate(startDate)
                .endDate(endDate)
                .propertyAddress(getPropertyDescription(property))
                .build();
    }

    public void createDepositBill(DepositBillCreationCommand body, Organisation organisation) {
        var propertyTrackingName = findMappedTrackingCategory(organisation, "Property");
        var adminUserId = organisation.getAdminUser();
        var integration = integrationService.findConnectedIntegration(organisation.getId(), adminUserId, Integration.IntegrationService.XERO);

        var tenantId = integration.getTenantId();
        var token = integration.getAccessToken();
        var contacts
                = xeroClient.listContactsWithRetry(tenantId, token, Instant.parse(XeroClient.DEFAULT_XERO_START_TIMESTAMP));
        var fromUser = userService.findUser(organisation.getDepositSchemeLedgerContactUserId(), false);
        var lineItem = invoiceService.getLineItems(Set.of(body.getLineItemId())).stream().findAny().orElseThrow();
        var accounts = invoiceService.findAccountsWithOrganisationId(organisation.getId(), tenantId);
        var contact = getXeroContact(contacts, fromUser);

        var trackingCategories = xeroClient.getTrackingCategoriesWithRetry(tenantId, token);

        var tenancy = propertyService.findTenancyWithOrganisationAndReference(organisation.getId(), lineItem.getParentReference()).orElseThrow();
        var property = propertyService.getProperty(tenancy.getProperty());
        List<LineItemTracking> tracking = new ArrayList<>(getTrackingItem(trackingCategories, tenantId, token, propertyTrackingName, property.getReference()));

        var invoice = new Invoice()
                .type(Invoice.TypeEnum.ACCPAY)
                .status(Invoice.StatusEnum.AUTHORISED)
                .date(toXeroDate(Instant.now().toEpochMilli()))
                .dueDate(toXeroDate(Instant.now().toEpochMilli()))
                .currencyCode(CurrencyCode.fromValue(organisation.getCurrency()))
                .invoiceNumber(String.join("/", tenancy.getReference(), generateRandomString()))
                .contact(contact.isSupplier(true).isCustomer(false))
                .lineAmountTypes(LineAmountTypes.NOTAX);

        var invoiceLineItem = new LineItem()
                .taxType("NONE")
                .description("Deposit transfer")
                .accountCode(organisation.getLedgerCodes().stream().filter(ledgerCode -> "Deposit Scheme".equals(ledgerCode.getName())).map(LedgerCode::getCode).findAny().orElseThrow())
                .quantity(1.0)
                .unitAmount(body.getAmount().doubleValue())
                .tracking(tracking);

        invoice.lineItems(List.of(invoiceLineItem));

        var result = xeroClient.createInvoiceWithRetry(invoice, tenantId, token);
        log.info("Response - {}", wrappedToJsonString(result));

        var validationErrors = result.getValidationErrors();
        var status = result.getStatusAttributeString();

        if ("ERROR".equals(status)) {
            throw new IllegalStateException("Failed to generate invoice - " +
                    validationErrors.stream().map(ValidationError::getMessage).collect(joining("\n")));
        }

        propertyService.updateTenancyDepositTransferredStatus(tenancy.getId(), true);
    }

    public void returnDeposit(DepositReturnCommand command, Organisation organisation) {
        var now = Instant.now();
        var propertyTrackingName = findMappedTrackingCategory(organisation, "Property");
        var adminUserId = organisation.getAdminUser();
        var integration = integrationService.findConnectedIntegration(organisation.getId(), adminUserId, Integration.IntegrationService.XERO);

        var tenantId = integration.getTenantId();
        var token = integration.getAccessToken();
        var contacts
                = xeroClient.listContactsWithRetry(tenantId, token, Instant.parse(XeroClient.DEFAULT_XERO_START_TIMESTAMP));
        var fromUser = userService.findUser(organisation.getDepositSchemeLedgerContactUserId(), false);
        var lineItem = invoiceService.getLineItems(Set.of(command.getLineItemId())).stream().findAny().orElseThrow();
        var accounts = invoiceService.findAccountsWithOrganisationId(organisation.getId(), tenantId);
        var contact = getXeroContact(contacts, fromUser);

        var trackingCategories = xeroClient.getTrackingCategoriesWithRetry(tenantId, token);

        var tenancy = propertyService.findTenancyWithOrganisationAndReference(organisation.getId(), lineItem.getParentReference()).orElseThrow();
        var property = propertyService.getProperty(tenancy.getProperty());
        List<LineItemTracking> tracking = new ArrayList<>(getTrackingItem(trackingCategories, tenantId, token, propertyTrackingName, property.getReference()));

        var invoice = new Invoice()
                .type(Invoice.TypeEnum.ACCREC)
                .status(Invoice.StatusEnum.AUTHORISED)
                .date(toXeroDate(now.toEpochMilli()))
                .dueDate(toXeroDate(now.toEpochMilli()))
                .currencyCode(CurrencyCode.fromValue(organisation.getCurrency()))
                .reference(tenancy.getReference())
                .contact(contact.isSupplier(false).isCustomer(true))
                .lineAmountTypes(LineAmountTypes.NOTAX);

        var depositSchemeCode = organisation.getLedgerCodes().stream().filter(ledgerCode -> "Deposit Scheme".equals(ledgerCode.getName())).map(LedgerCode::getCode).findAny().orElseThrow();
        var tenantDeposit = organisation.getLedgerCodes().stream().filter(ledgerCode -> "Deposit Scheme".equals(ledgerCode.getName())).map(LedgerCode::getCode).findAny().orElseThrow();
        var tenantDepositAccount = accounts.stream().filter(account -> tenantDeposit.equals(account.getCode())).findAny().orElseThrow();

        var invoiceLineItems = new ArrayList<>(List.of(new LineItem()
                .taxType("NONE")
                .description("Deposit Refund to Tenant")
                .accountCode(depositSchemeCode)
                .quantity(1.0)
                .unitAmount(command.getNetTotal().doubleValue())
                .tracking(tracking)));

        if (Objects.nonNull(command.getDeductions()) && !command.getDeductions().isEmpty()) {
            var deductionTotal = command.getDepositTotal().subtract(command.getNetTotal());
            var deductionLineItem = new LineItem()
                    .taxType("NONE")
                    .description("Deposit Reimursement")
                    .accountCode(depositSchemeCode)
                    .quantity(1.0)
                    .unitAmount(deductionTotal.doubleValue())
                    .tracking(tracking);

            invoiceLineItems.add(deductionLineItem);

            // TODO: save this code fragment temporarily ???
//            deductionBills.addAll(command
//                    .getDeductions()
//                    .stream()
//                    .map(depositDeduction -> {
//                        var supplier = userService.findUser(depositDeduction.getSupplierId(), false);
//                        var billContact = getXeroContact(contacts, supplier);
//                        var bill = new Invoice()
//                                .type(Invoice.TypeEnum.ACCPAY)
//                                .status(Invoice.StatusEnum.SUBMITTED)
//                                .date(toXeroDate(now.toEpochMilli()))
//                                .dueDate(toXeroDate(now.toEpochMilli()))
//                                .currencyCode(CurrencyCode.fromValue(organisation.getCurrency()))
//                                .invoiceNumber(String.join("/", tenancy.getReference(), generateRandomString()))
//                                .contact(billContact.isSupplier(true).isCustomer(false))
//                                .lineAmountTypes(LineAmountTypes.NOTAX)
//                                .lineItems(new ArrayList<>(List.of(new LineItem()
//                                        .taxType("NONE")
//                                        .description(depositDeduction.getDescription())
//                                        .accountCode(tenantDeposit.getCode())
//                                        .quantity(1.0)
//                                        .unitAmount(depositDeduction.getAmount().doubleValue())
//                                        .tracking(tracking))));
//
//                        return bill;
//                    }).collect(toUnmodifiableList()));
        }

        invoice.lineItems(invoiceLineItems);

        var result = xeroClient.createInvoiceWithRetry(invoice, tenantId, token);
        log.info("Response - {}", wrappedToJsonString(result));

        var validationErrors = result.getValidationErrors();
        var status = result.getStatusAttributeString();

        if ("ERROR".equals(status)) {
            throw new IllegalStateException("Failed to generate invoice - " +
                    validationErrors.stream().map(ValidationError::getMessage).collect(joining("\n")));
        }

        var payment = new com.xero.models.accounting.Payment()
                .invoice(result)
                .amount(command.getNetTotal().doubleValue())
                .date(toXeroDate(now.toEpochMilli()))
                .account(new com.xero.models.accounting.Account()
                        .accountID(UUID.fromString(tenantDepositAccount.getAccountId()))
                );

        xeroClient.createPaymentWithRetry(payment, tenantId, token);
        propertyService.updateTenancyDepositReturnedStatus(tenancy.getId(), true);
    }

    public void payBillWithFloat(PayWithFloatPayload payload, Organisation organisation) {
        var now = Instant.now();
        var adminUserId = organisation.getAdminUser();
        var integration = integrationService.findConnectedIntegration(organisation.getId(), adminUserId, Integration.IntegrationService.XERO);

        var tenantId = integration.getTenantId();
        var token = integration.getAccessToken();
        var contacts
                = xeroClient.listContactsWithRetry(tenantId, token, Instant.parse(XeroClient.DEFAULT_XERO_START_TIMESTAMP));
        var fromUser = userService.findUser(organisation.getBalanceTransferContactUserId(), false);
        var bill = invoiceService.getInvoice(payload.getInvoiceId());
        var propertyReference = bill.getNumber().split("/")[0];
        var property = propertyService.findPropertyWithOrganisationAndReference(organisation.getId(), propertyReference).orElseThrow();
        var contact = getXeroContact(contacts, fromUser);

        var trackingCategories = xeroClient.getTrackingCategoriesWithRetry(tenantId, token);
        var propertyTrackingName = findMappedTrackingCategory(organisation, "Property");
        var contactsTrackingName = findMappedTrackingCategory(organisation, "Contacts");

        var tracking = new ArrayList<>(getTrackingItem(trackingCategories, tenantId, token, propertyTrackingName, propertyReference));
        Optional.of(formatUser(userService.findUser(property.getPrimaryLandlordId(), false)))
                .ifPresent(name -> tracking.addAll(getTrackingItem(trackingCategories, tenantId, token, contactsTrackingName, name)));
        var floatBalance = organisation.getLedgerCodes().stream().filter(ledgerCode -> "Float Balance".equals(ledgerCode.getName())).map(LedgerCode::getCode).findAny().orElseThrow();
        var propertyExpenses = organisation.getLedgerCodes().stream().filter(ledgerCode -> "Property Expenses".equals(ledgerCode.getName())).map(LedgerCode::getCode).findAny().orElseThrow();

        var invoice = new Invoice()
                .type(Invoice.TypeEnum.ACCPAY)
                .status(Invoice.StatusEnum.AUTHORISED)
                .date(toXeroDate(now.toEpochMilli()))
                .dueDate(toXeroDate(now.toEpochMilli()))
                .currencyCode(CurrencyCode.fromValue(organisation.getCurrency()))
                .invoiceNumber(generateRandomString())
                .contact(contact.isSupplier(true).isCustomer(false))
                .lineAmountTypes(LineAmountTypes.NOTAX)
                .lineItems(new ArrayList<>(List.of(
                        new LineItem()
                                .taxType("NONE")
                                .description("Debit Float")
                                .quantity(1.0)
                                .unitAmount(payload.getFloatAmount().multiply(BigDecimal.valueOf(-1)).doubleValue())
                                .accountCode(floatBalance)
                                .tracking(tracking),
                        new LineItem()
                                .taxType("NONE")
                                .description("Credit Expense")
                                .quantity(1.0)
                                .unitAmount(payload.getFloatAmount().doubleValue())
                                .accountCode(propertyExpenses)
                                .tracking(tracking)
                )));

        var result = xeroClient.createInvoiceWithRetry(invoice, tenantId, token);

        if ("ERROR".equals(result.getStatusAttributeString())) {
            throw new IllegalStateException("Failed to create bill - " + result.getValidationErrors());
        }

        xeroClient.createInvoiceWithRetry(new Invoice().invoiceID(UUID.fromString(bill.getInvoiceId())).status(Invoice.StatusEnum.AUTHORISED), tenantId, token);
    }

    @Data
    @AllArgsConstructor
    private static class TrackingCategoryOption {
        private UUID categoryId;
        private UUID optionId;
    }

    @Data
    @AllArgsConstructor
    private static class InvoiceLineItemPair {
        private Invoice invoice;
        private LineItem lineItem;
    }

}
