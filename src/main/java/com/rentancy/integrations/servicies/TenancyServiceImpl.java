package com.rentancy.integrations.servicies;

import com.rentancy.integrations.pojos.*;
import com.rentancy.integrations.pojos.Invoice.LineItem;
import com.rentancy.integrations.pojos.Organisation.LedgerCode;
import com.rentancy.integrations.servicies.persistence.InvoiceService;
import com.rentancy.integrations.servicies.persistence.OrganisationService;
import com.rentancy.integrations.servicies.persistence.PropertyService;
import com.rentancy.integrations.util.Utils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.StringUtils;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.temporal.ChronoField;
import java.time.temporal.ChronoUnit;
import java.time.temporal.Temporal;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.rentancy.integrations.util.Utils.getPrimaryTenantIds;
import static java.time.ZoneOffset.UTC;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toUnmodifiableList;

@RequiredArgsConstructor
public class TenancyServiceImpl implements TenancyService {

    private static final Logger log = LogManager.getLogger(TenancyServiceImpl.class);

    /**
     * Sometimes we are diving value and precision gets lost. Now we first multiply by this value and then divide by it at some point
     */
    private static final BigDecimal PRECISION_VALUE = BigDecimal.valueOf(1000000000);

    private final PropertyService propertyService;
    private final OrganisationService organisationService;
    private final InvoiceService invoiceService;

    @Override
    public Instant getToday() {
        var now = Instant.now();
        return now.atOffset(UTC)
                .with(LocalTime.of(23, 59, 59, now.getNano()))
                .toInstant();
    }

    private List<String> getLedgerCodes(List<Organisation.LedgerCode> ledgerCodes,
                                        Predicate<Organisation.LedgerCode> predicate) {
        return ledgerCodes
                .stream()
                .filter(predicate)
                .map(LedgerCode::getCode)
                .filter(Objects::nonNull)
                .collect(toUnmodifiableList());
    }

    @Override
    public BigDecimal getLineItemAmount(Invoice.LineItem lineItem) {
        // We don't expect amount credits in balance transfer
        if (lineItem.isBalanceTransfer()) {
            return Optional.ofNullable(lineItem.getLineAmount())
                    .map(BigDecimal::new)
                    .map(BigDecimal::abs)
                    .orElse(BigDecimal.ZERO);
        }

        if (Objects.isNull(lineItem.getLineAmount()) || new BigDecimal(lineItem.getParentTotal()).compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        var lineItemCreditPercentage = new BigDecimal(lineItem.getLineAmount())
                .multiply(PRECISION_VALUE)
                .divide(new BigDecimal(lineItem.getParentTotal()), RoundingMode.HALF_UP);

        var lineItemCreditAmount = new BigDecimal(lineItem.getParentAmountCredited())
                .multiply(lineItemCreditPercentage)
                .divide(PRECISION_VALUE, RoundingMode.HALF_UP);

        if (lineItem.isTaxExclusive()) {
            return new BigDecimal(lineItem.getLineAmount()).add(new BigDecimal(lineItem.getTaxAmount())).subtract(lineItemCreditAmount);
        }

        return new BigDecimal(lineItem.getLineAmount()).subtract(lineItemCreditAmount);
    }

    @Override
    public BigDecimal getLineItemPaidAmount(Invoice.LineItem lineItem) {
        if (lineItem.isBalanceTransfer()) {
            return getLineItemAmount(lineItem).abs();
        }

        var paid = new BigDecimal(lineItem.getParentPaidAmount());
        var total = new BigDecimal(lineItem.getParentTotal()).subtract(new BigDecimal(lineItem.getParentAmountCredited()));

        if (total.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        var lineItemAmount = getLineItemAmount(lineItem);

        return lineItemAmount.multiply(paid).divide(total, RoundingMode.HALF_UP);
    }

    @Override
    public boolean invoiceHasAmount(Invoice.LineItem lineItem) {
        if (lineItem.isBalanceTransfer() && getLineItemAmount(lineItem).compareTo(BigDecimal.ZERO) != 0) {
            return true;
        }

        if ("0.00".equals(lineItem.getParentTotal()) || Objects.isNull(lineItem.getLineAmount())) {
            return false;
        }

        var total = new BigDecimal(lineItem.getParentTotal()).subtract(new BigDecimal(lineItem.getParentAmountCredited()));

        return total.compareTo(BigDecimal.ZERO) > 0;
    }

    @Override
    public BigDecimal getLineItemTaxVatPaidAmount(Invoice.LineItem lineItem) {
        if (lineItem.isBalanceTransfer()) {
            return BigDecimal.ZERO;
        }

        var total = getLineItemAmount(lineItem);
        var paidAmount = getLineItemPaidAmount(lineItem);

        return paidAmount.multiply(PRECISION_VALUE)
                .multiply(new BigDecimal(lineItem.getTaxAmount()))
                .divide(total, RoundingMode.HALF_UP)
                .divide(PRECISION_VALUE, RoundingMode.HALF_UP);
    }

    @Override
    public PropertySummary reducePropertySummaries(PropertySummary s1, PropertySummary s2) {
        return PropertySummary.builder()
                .income(s1.getIncome().add(s2.getIncome()))
                .expenses(s1.getExpenses().add(s2.getExpenses()))
                .balance(s1.getBalance().add(s2.getBalance()))
                .minimumBalance(s1.getMinimumBalance().add(s2.getMinimumBalance()))
                .openingBalance(s1.getOpeningBalance().add(s2.getOpeningBalance()))
                .build();
    }

    @Override
    public TenancySummary calculateTenancySummary(String tenancyId) {
        var tenancy = propertyService.getTenancy(tenancyId, false);
        var property = propertyService.getProperty(tenancy.getProperty());
        var organisation = organisationService.getOrganisation(tenancy.getOrganisation());
        var organisationLineItems = invoiceService.findOrganisationLineItems(organisation.getId(), null, null)
                .stream()
                .filter(lineItem -> tenancy.getReference().equals(getParentReference(lineItem)))
                .collect(toUnmodifiableList());

        var now = Instant.now();
        var today = getToday();

        var balanceSummary = calculateFinanceBalanceSummary(organisation, property, organisationLineItems, today, List.of(tenancy));

        var openingBalance = Optional.ofNullable(tenancy.getOpeningBalance())
                .map(BigDecimal::valueOf)
                .orElse(BigDecimal.ZERO);

        return TenancySummary.builder()
                .paidIncome(balanceSummary.getIncome().getPaidAmount())
                .totalIncome(openingBalance.add(balanceSummary.getIncome().getTotalAmount()))
                .inArrears(balanceSummary.getIncome().getInArrearsAmount())
                .paidDeposit(balanceSummary.getDepositBalance())
                .raisedNotPaid(balanceSummary.getIncome().getRaisedNotPaidAmount())
                .build();
    }

    @Override
    public String getParentReference(LineItem lineItem) {
        // For invoice bills, reference is set in parentNumber, otherwise in reference
        return !lineItem.isParentIncome() && Objects.nonNull(lineItem.getInvoiceId()) ? lineItem.getParentNumber() : lineItem.getParentReference();
    }

    @Override
    public String getParentNumber(LineItem lineItem) {
        // For invoice bills, reference is set in parentNumber, otherwise in reference
        return lineItem.isParentIncome() && !Objects.isNull(lineItem.getInvoiceId()) ? lineItem.getParentNumber() : lineItem.getParentReference();
    }

    public Optional<String> getInvoiceLineItemTenancyReference(LineItem lineItem) {
        if (Objects.isNull(lineItem.getInvoiceId())) {
            return Optional.empty();
        }

        var reference = getParentReference(lineItem);

        if (lineItem.isParentIncome()) {
            return Optional.ofNullable(reference);
        } else {
            return Optional.ofNullable(reference)
                    .map(ref -> ref.split("/"))
                    .filter(ref -> ref.length >= 2)
                    .map(ref -> ref[1])
                    .filter(StringUtils::hasText);
        }
    }

    private BigDecimal getTransactionUserBalance(User user, List<LineItem> lineItems) {
        return lineItems
                .stream()
                .filter(lineItem -> Objects.nonNull(lineItem.getTransactionId()))
                .filter(lineItem -> Objects.nonNull(user.getXeroId()) && user.getXeroId().equals(lineItem.getParentAgainstUserXeroId()))
                .filter(this::invoiceHasAmount)
                .map(lineItem -> new BigDecimal(lineItem.isIncome() ? 1 : -1).multiply(getLineItemPaidAmount(lineItem)))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public boolean isInvoiceLineItemAgainstTenancy(LineItem lineItem, Property property, Tenancy tenancy) {
        var tenancyReference = tenancy.getReference();
        return property.getReference().equals(lineItem.getTrackingName()) &&
                getInvoiceLineItemTenancyReference(lineItem).filter(tenancyReference::equals).isPresent();
    }

    @Override
    public BigDecimal calculateTenantBalancePerformant(Organisation organisation, User tenant, List<Tenancy> tenancies, Map<String, List<LineItem>> lineItemsLookup) {
        var tenantBalanceLedgerCodeName = "Tenant Balances";
        var tenantBalanceLedgerCodes = getLedgerCodes(organisation.getLedgerCodes(), ledgerCode -> tenantBalanceLedgerCodeName.equals(ledgerCode.getName()));

        return tenancies.stream()
                .filter(tenancy -> lineItemsLookup.get(tenancy.getId()) != null && getPrimaryTenantIds(List.of(tenancy)).contains(tenant.getId()))
                .map(tenancy -> {
                    var relatedLineItems = lineItemsLookup.get(tenancy.getId()).stream()
                            .filter(lineItem -> Objects.nonNull(lineItem.getAccountCode()) && tenantBalanceLedgerCodes.contains(lineItem.getAccountCode()))
                            .collect(Collectors.toSet());

                    // Journals are always bills
                    return relatedLineItems
                            .stream()
                            .filter(this::invoiceHasAmount)
                            .collect(groupingBy(LineItem::getInvoiceId))
                            .values()
                            .stream()
                            .map(items -> items
                                    .stream()
                                    .map(lineItem ->
                                            new BigDecimal(lineItem.isIncome() ? 1 : -1).multiply(getLineItemPaidAmount(lineItem))
                                    )
                                    .findFirst()
                                    .orElse(BigDecimal.ZERO)
                            ).reduce(BigDecimal.ZERO, BigDecimal::add);
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public BigDecimal calculateTenantBalance(Organisation organisation, User tenant, Map<String, Property> propertyIdMap, List<Tenancy> tenancies, List<LineItem> lineItems) {
        var tenantBalanceLedgerCodeName = "Tenant Balances";
        var tenantBalanceLedgerCodes = getLedgerCodes(organisation.getLedgerCodes(), ledgerCode -> tenantBalanceLedgerCodeName.equals(ledgerCode.getName()));
        var tenantBalanceLineItems = lineItems.stream()
                .filter(lineItem -> Objects.nonNull(lineItem.getAccountCode()) && tenantBalanceLedgerCodes.contains(lineItem.getAccountCode()))
                .collect(toUnmodifiableList());

        return tenancies.stream()
                .filter(tenancy -> getPrimaryTenantIds(List.of(tenancy)).contains(tenant.getId()))
                .map(tenancy -> {
                    var property = propertyIdMap.get(tenancy.getProperty());
                    var relatedLineItems = tenantBalanceLineItems
                            .stream()
                            .filter(lineItem -> isInvoiceLineItemAgainstTenancy(lineItem, property, tenancy))
                            .collect(toUnmodifiableList());

                    // Journals are always bills
                    return relatedLineItems
                            .stream()
                            .filter(this::invoiceHasAmount)
                            .collect(groupingBy(LineItem::getInvoiceId))
                            .values()
                            .stream()
                            .map(items -> items
                                    .stream()
                                    .map(lineItem ->
                                            new BigDecimal(lineItem.isIncome() ? 1 : -1).multiply(getLineItemPaidAmount(lineItem))
                                    )
                                    .findFirst()
                                    .orElse(BigDecimal.ZERO)
                            ).reduce(BigDecimal.ZERO, BigDecimal::add);
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public FinanceSummary calculateFinanceBalanceSummary(Organisation organisation, List<Invoice.LineItem> lineItems,
                                                         List<String> ledgerCodeNames, String trackingName, boolean income,
                                                         Instant today, @Nullable List<Tenancy> tenancies) {
        var ledgerCodes = getLedgerCodes(organisation.getLedgerCodes(), ledgerCode -> ledgerCodeNames.contains(ledgerCode.getName()));

        var paidAmount = BigDecimal.ZERO;
        var totalAmount = BigDecimal.ZERO;
        var inArrearsAmount = BigDecimal.ZERO;
        var paidLineItems = new ArrayList<LineItem>();
        var raisedNotPaidLineItems = new ArrayList<LineItem>();
        var inArrearsLineItems = new ArrayList<LineItem>();
        var tenancyPaidLineItems = new HashMap<String, List<LineItem>>();
        var tenancyInArrearsLineItems = new HashMap<String, List<LineItem>>();
        var relevantLineItems = new ArrayList<LineItem>();

        for (var lineItem : lineItems) {
            if (!isLineItemRelevantForFinanceBalanceSummary(lineItem, ledgerCodes, trackingName, income, tenancies)) {
                continue;
            }
            relevantLineItems.add(lineItem);

            var lineItemAmount = getLineItemAmount(lineItem);
            var lineItemPaidAmount = getLineItemPaidAmount(lineItem);
            var parentReference = getParentReference(lineItem);

            var isPaidLineItem = lineItemPaidAmount.compareTo(BigDecimal.ZERO) != 0;
            var isRaisedNotPaidLineItem = lineItemAmount.subtract(lineItemPaidAmount).compareTo(BigDecimal.ZERO) != 0;
            var isInArrearsLineItem = isRaisedNotPaidLineItem && today.isAfter(Instant.parse(lineItem.getParentDueDate()));
            var isTenancyPaidLineItem = isPaidLineItem && Objects.nonNull(parentReference);
            var isTenancyInArrearsLineItem = isInArrearsLineItem && Objects.nonNull(parentReference);

            totalAmount = totalAmount.add(lineItemAmount);
            paidAmount = paidAmount.add(lineItemPaidAmount);

            if (isPaidLineItem) {
                paidLineItems.add(lineItem);
            }

            if (isRaisedNotPaidLineItem) {
                raisedNotPaidLineItems.add(lineItem);
            }

            if (isInArrearsLineItem) {
                inArrearsLineItems.add(lineItem);
                inArrearsAmount = inArrearsAmount.add(lineItemPaidAmount.subtract(lineItemPaidAmount));
            }

            if (isTenancyPaidLineItem) {
                tenancyPaidLineItems.computeIfAbsent(parentReference, k -> new ArrayList<>()).add(lineItem);
            }

            if (isTenancyInArrearsLineItem) {
                tenancyInArrearsLineItems.computeIfAbsent(parentReference, k -> new ArrayList<>()).add(lineItem);
            }
        }

        return FinanceSummary.builder()
                .paidAmount(paidAmount)
                .raisedNotPaidAmount(totalAmount.subtract(paidAmount))
                .inArrearsAmount(inArrearsAmount)
                .totalAmount(totalAmount)
                .paidLineItems(paidLineItems)
                .raisedNotPaidLineItems(raisedNotPaidLineItems)
                .inArrearsLineItems(inArrearsLineItems)
                .allLineItems(relevantLineItems)
                .tenancyPaidLineItems(tenancyPaidLineItems)
                .tenancyInArrearsLineItems(tenancyInArrearsLineItems)
                .build();
    }

    private boolean isLineItemRelevantForFinanceBalanceSummary(LineItem lineItem, List<String> ledgerCodes,
                                                               String trackingName, boolean income,
                                                               @Nullable List<Tenancy> tenancies) {
        var c1 = !income || Objects.isNull(tenancies) || tenancies.stream().anyMatch(tenancy -> tenancy.getReference().equals(getParentReference(lineItem)));
        var c2 = getLineItemAmount(lineItem).compareTo(BigDecimal.ZERO) != 0;
        var c3 = Objects.nonNull(lineItem.getAccountCode()) && ledgerCodes.contains(lineItem.getAccountCode());
        var c4 = Objects.nonNull(trackingName) && trackingName.equals(lineItem.getTrackingName());
        var c5 = lineItem.isIncome() == income;
        // Includes both income and expenses
        return (c1) && (c2) && (c3) && (c4) && (c5);
    }

    @Override
    public List<String> getBillLedgerCodeNames(Organisation organisation) {
        return organisation.getLedgerCodes()
                .stream()
                .map(LedgerCode::getName)
                .filter(name -> !DEPOSIT_LEDGER_CODE_NAMES.contains(name) && !"Tenant Balances".equals(name))
                .collect(toUnmodifiableList());
    }

    private FinanceBalanceSummary calculateFinanceBalanceSummary(Organisation organisation,
                                                                 Property property,
                                                                 List<Invoice.LineItem> lineItems,
                                                                 Instant today,
                                                                 @Nullable List<Tenancy> tenancies) {
        var incomeSummary = calculateFinanceBalanceSummary(organisation, lineItems, organisation.getIncomeLedgerCodeNames(), property.getReference(), true, today, tenancies);
        var expenseSummary = calculateFinanceBalanceSummary(organisation, lineItems, getBillLedgerCodeNames(organisation), property.getReference(), false, today, tenancies);
        var organisationLedgerCodes = organisation.getLedgerCodes();
        var floatBalanceLedgerCode = organisationLedgerCodes.stream()
                .filter(ledgerCode -> "Float Balance".equals(ledgerCode.getName()))
                .findFirst()
                .map(Organisation.LedgerCode::getCode)
                .orElse(null);
        var depositIncomeSummary = calculateFinanceBalanceSummary(organisation, lineItems, DEPOSIT_LEDGER_CODE_NAMES, property.getReference(), true, today, tenancies);
        var depositExpenseSummary = calculateFinanceBalanceSummary(organisation, lineItems, DEPOSIT_LEDGER_CODE_NAMES, property.getReference(), false, today, tenancies);
        var minimumBalance = lineItems.stream()
                .filter(lineItem -> lineItem.getTrackingName() != null && lineItem.getTrackingName().equals(property.getReference()))
                .filter(lineItem -> lineItem.getAccountCode() != null && floatBalanceLedgerCode != null && floatBalanceLedgerCode.equals(lineItem.getAccountCode()) && lineItem.getLineAmount() != null)
                .map(lineItem -> getLineItemAmount(lineItem))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return FinanceBalanceSummary.builder()
                .income(incomeSummary)
                .expenses(expenseSummary)
                .depositIncome(depositIncomeSummary)
                .depositExpense(depositExpenseSummary)
                .balance(incomeSummary.getPaidAmount().subtract(expenseSummary.getPaidAmount()))
                .depositBalance(depositIncomeSummary.getPaidAmount().subtract(depositExpenseSummary.getPaidAmount()))
                .minimumBalance(minimumBalance)
                .build();
    }


    @Override
    public TenancySummary calculatePropertyTenanciesSummary(Organisation organisation,
                                                            Property property,
                                                            List<Tenancy> tenancies,
                                                            List<Invoice.LineItem> lineItems,
                                                            Instant today) {
        var incomeSummary = calculateFinanceBalanceSummary(organisation, property, lineItems, today, tenancies);

        return TenancySummary.builder()
                .paidIncome(incomeSummary.getIncome().getPaidAmount())
                .totalIncome(incomeSummary.getIncome().getTotalAmount())
                .inArrears(incomeSummary.getIncome().getInArrearsAmount())
                .paidDeposit(incomeSummary.getDepositBalance())
                .raisedNotPaid(incomeSummary.getIncome().getRaisedNotPaidAmount())
                .tenancyPaidLineItems(incomeSummary.getIncome().getTenancyPaidLineItems())
                .inArrearsLineItems(incomeSummary.getIncome().getTenancyInArrearsLineItems())
                .build();
    }

    @Override
    public PropertyExpenses calculateAllExpenses(List<Invoice.LineItem> allLineItems, String trackingName, List<String> excludingLedgerCodes) {
        var lineItems = new ArrayList<LineItem>();
        var expense = allLineItems.stream()
                .filter(lineItem -> !lineItem.isIncome())
                .filter(lineItem -> lineItem.getLineAmount() != null && lineItem.getAccountCode() != null && !excludingLedgerCodes.contains(lineItem.getAccountCode()))
                .map(lineItem -> {
                            var expensesRaised = BigDecimal.ZERO;
                            var outstandingAmount = BigDecimal.ZERO;

                            var lineAmount = getLineItemAmount(lineItem);
                            var lineItemTracking = lineItem.getTrackingName();
                            boolean isLineItemRelevant = Objects.nonNull(trackingName) && trackingName.equals(lineItemTracking);
                            if (isLineItemRelevant) {
                                expensesRaised = expensesRaised.add(lineAmount);
                                outstandingAmount = lineAmount.subtract(getLineItemPaidAmount(lineItem));

                                lineItems.add(lineItem);
                            }
                            return Pair.of(expensesRaised, outstandingAmount);
                        }
                ).reduce(Pair.of(BigDecimal.ZERO, BigDecimal.ZERO), (a1, a2) -> Pair.of(a1.getLeft().add(a2.getLeft()), a1.getRight().add(a2.getRight())));

        return PropertyExpenses
                .builder()
                .totalExpenses(expense.getLeft())
                .outstandingExpense(expense.getRight())
                .lineItems(lineItems)
                .build();
    }

    @Override
    public PropertySummary calculatePropertySummary(Organisation organisation, Property property, List<Tenancy> tenancies,
                                                    List<Invoice.LineItem> allLineItems, List<Invoice.LineItem> oldInvoices,
                                                    Instant now) {
        var today = now.atOffset(UTC)
                .with(LocalTime.of(23, 59, 59, now.getNano()))
                .toInstant();

        var activeTenancies = tenancies.stream().filter(tenancy -> {
            if (tenancy.getStartDate() == null && tenancy.getEndDate() == null) {
                return false;
            }
            if (tenancy.getStartDate() == null) {
                return now.isBefore(Utils.convertDateToInstant(tenancy.getEndDate()));
            }
            if (tenancy.getEndDate() == null) {
                return now.isAfter(Utils.convertDateToInstant(tenancy.getStartDate()));
            }

            var startDate = Utils.convertDateToInstant(tenancy.getStartDate());
            var endDate = Utils.convertDateToInstant(tenancy.getEndDate());

            return now.isAfter(startDate) && now.isBefore(endDate);
        }).count();

        var monthsRemaining = tenancies.stream()
                .filter(tenancy -> tenancy.getEndDate() != null)
                .map(Tenancy::getEndDate)
                .reduce((date1, date2) -> Utils.convertDateToInstant(date1).isAfter(Utils.convertDateToInstant(date2)) ? date1 : date2)
                .map(date -> {
                    if (Instant.now().isAfter(Utils.convertDateToInstant(date))) {
                        return 0l;
                    }
                    var nowLocal = LocalDate.ofInstant(Instant.now(), UTC);
                    var endDate = LocalDate.ofInstant(Utils.convertDateToInstant(date), UTC);
                    return ChronoUnit.MONTHS.between(nowLocal, endDate);
                })
                .orElse(0l);

        var balanceSummary = calculateFinanceBalanceSummary(organisation, property, allLineItems, today, tenancies);
        var previousBalanceSummary = calculateFinanceBalanceSummary(organisation, property, oldInvoices, today, tenancies);

        var paidIncome = balanceSummary.getIncome().getPaidAmount();
        var previousPaidIncome = previousBalanceSummary.getIncome().getPaidAmount();
        var openingBalance = previousPaidIncome.add(property.getOpeningBalance()).subtract(previousBalanceSummary.getExpenses().getTotalAmount());
        var closingBalance = paidIncome.add(openingBalance).subtract(previousBalanceSummary.getExpenses().getTotalAmount()).subtract(balanceSummary.getExpenses().getTotalAmount());
        var clientPayable = closingBalance.subtract(balanceSummary.getMinimumBalance());

        return PropertySummary.builder()
                .inArrears(balanceSummary.getIncome().getInArrearsAmount())
                .income(balanceSummary.getIncome().getPaidAmount())
                .paidDeposit(balanceSummary.getDepositBalance())
                .expenses(balanceSummary.getExpenses().getTotalAmount())
                .minimumBalance(balanceSummary.getMinimumBalance())
                .balance(closingBalance)
                .incomeRaisedNotPaid(balanceSummary.getIncome().getRaisedNotPaidAmount())
                .billsOutstanding(balanceSummary.getExpenses().getRaisedNotPaidAmount())
                .openingBalance(openingBalance)
                .activeContracts(activeTenancies)
                .totalArea(tenancies.stream().mapToInt(Tenancy::getArea).sum())
                .monthsRemaining(monthsRemaining)
                .clientPayable(clientPayable)
                .build();
    }

    @Override
    public List<PropertyFinanceSummary> calculatePropertySummary(Property property, Organisation organisation, List<Invoice.LineItem> lineItems) {
        var propertyTenancies = propertyService.findPropertyTenancies(property.getId(), false);

        var now = Instant.now();

        var propertySummary = calculatePropertySummary(organisation, property, propertyTenancies, lineItems, List.of(), now);

        return List.of(PropertyFinanceSummary.builder()
                .name(null)
                .budgetId(null)
                .reference(property.getReference())
                .inArrears(propertySummary.getInArrears())
                .income(propertySummary.getIncome())
                .paidDeposit(propertySummary.getPaidDeposit())
                .expenses(propertySummary.getExpenses())
                .minimumBalance(propertySummary.getMinimumBalance())
                .balance(propertySummary.getBalance())
                .dueToClient(propertySummary.getClientPayable())
                .incomeRaisedNotPaid(propertySummary.getIncomeRaisedNotPaid())
                .billsOutstanding(propertySummary.getBillsOutstanding())
                .activeContracts(propertySummary.getActiveContracts())
                .totalArea(propertySummary.getTotalArea())
                .monthsRemaining(propertySummary.getMonthsRemaining())
                .openingBalance(propertySummary.getOpeningBalance())
                .build());
    }

    @Override
    public List<PropertyFinanceSummary> calculatePropertySummary(Property property, Organisation organisation) {
        var allLineItems = invoiceService.findOrganisationLineItems(organisation.getId(), null, null);
        return calculatePropertySummary(property, organisation, allLineItems);
    }

    public PropertyExpenses calculateParentPropertyExpenses(Organisation organisation, List<Invoice.LineItem> allLineItems, String trackingName) {
        var depositLedgerCodes = getLedgerCodes(organisation.getLedgerCodes(), ledgerCode -> DEPOSIT_LEDGER_CODE_NAMES.contains(ledgerCode.getName()));

        return this.calculateAllExpenses(allLineItems, trackingName, depositLedgerCodes);
    }

    PropertyFinanceSummary calculateParentPropertySummary(ParentProperty parentProperty, Organisation organisation) {
        var allLineItems = invoiceService.findOrganisationLineItems(organisation.getId(), null, null);
        var properties = propertyService.getParentPropertyProperties(parentProperty.getId())
                .stream()
                .filter(property -> !property.isArchived())
                .collect(toUnmodifiableList());

        var propertyTenancies = propertyService.findTenancies(organisation.getId(), null, false)
                .stream()
                .collect(groupingBy(Tenancy::getProperty));

        var now = Instant.now();

        var parentPropertyExpense = calculateParentPropertyExpenses(organisation, allLineItems, parentProperty.getReference());
        var parentPropertyInitialBalance = PropertyFinanceSummary.builder()
                .inArrears(BigDecimal.ZERO)
                .income(BigDecimal.ZERO)
                .paidDeposit(BigDecimal.ZERO)
                .expenses(parentPropertyExpense.getTotalExpenses())
                .minimumBalance(BigDecimal.ZERO)
                .balance(BigDecimal.ZERO)
                .dueToClient(parentPropertyExpense.getTotalExpenses().multiply(BigDecimal.valueOf(-1)))
                .incomeRaisedNotPaid(BigDecimal.ZERO)
                .billsOutstanding(parentPropertyExpense.getOutstandingExpense())
                .activeContracts(0)
                .totalArea(0)
                .monthsRemaining(0)
                .openingBalance(BigDecimal.ZERO)
                .build();

        return properties.stream()
                .map(property -> {
                    var tenancies = propertyTenancies.getOrDefault(property.getId(), List.of());

                    var propertySummary = calculatePropertySummary(organisation, property, tenancies, allLineItems, List.of(), now);

                    return PropertyFinanceSummary.builder()
                            .reference(property.getReference())
                            .inArrears(propertySummary.getInArrears())
                            .income(propertySummary.getIncome())
                            .paidDeposit(propertySummary.getPaidDeposit())
                            .expenses(propertySummary.getExpenses())
                            .minimumBalance(propertySummary.getMinimumBalance())
                            .balance(propertySummary.getBalance())
                            .dueToClient(propertySummary.getClientPayable())
                            .incomeRaisedNotPaid(propertySummary.getIncomeRaisedNotPaid())
                            .billsOutstanding(propertySummary.getBillsOutstanding())
                            .activeContracts(propertySummary.getActiveContracts())
                            .totalArea(propertySummary.getTotalArea())
                            .monthsRemaining(propertySummary.getMonthsRemaining())
                            .openingBalance(propertySummary.getOpeningBalance())
                            .build();
                })
                .reduce(parentPropertyInitialBalance,
                        (pp1, pp2) -> PropertyFinanceSummary.builder()
                                .reference(parentProperty.getReference())
                                .inArrears(pp1.getInArrears().add(pp2.getInArrears()))
                                .income(pp1.getIncome().add(pp2.getIncome()))
                                .paidDeposit(pp1.getPaidDeposit().add(pp2.getPaidDeposit()))
                                .expenses(pp1.getExpenses().add(pp2.getExpenses()))
                                .minimumBalance(pp1.getMinimumBalance().add(pp2.getMinimumBalance()))
                                .balance(pp1.getBalance().add(pp2.getBalance()))
                                .dueToClient(pp1.getDueToClient().add(pp2.getDueToClient()))
                                .incomeRaisedNotPaid(pp1.getIncomeRaisedNotPaid().add(pp2.getIncomeRaisedNotPaid()))
                                .billsOutstanding(pp1.getBillsOutstanding().add(pp2.getBillsOutstanding()))
                                .activeContracts(pp1.getActiveContracts() + pp2.getActiveContracts())
                                .totalArea(pp1.getTotalArea() + pp2.getTotalArea())
                                .monthsRemaining(pp1.getMonthsRemaining() + pp2.getMonthsRemaining())
                                .openingBalance(pp1.getOpeningBalance().add(pp2.getOpeningBalance()))
                                .build());
    }

    @Override
    public List<PropertyFinanceSummary> calculatePropertySummary(String propertyId) {
        var property = propertyService.getProperty(propertyId);
        var organisation = organisationService.getOrganisation(property.getOrganisation());

        return this.calculatePropertySummary(property, organisation);
    }

    @Override
    public PropertyFinanceSummary calculateParentPropertyFinanceSummary(String parentPropertyId) {
        var parentProperty = propertyService.getParentProperty(parentPropertyId);
        var organisation = organisationService.getOrganisation(parentProperty.getOrganisationId());

        return this.calculateParentPropertySummary(parentProperty, organisation);
    }

    public int getDaysAmountForCurrentYear() {
        return Year.now().length();
    }

    @Override
    public BigDecimal getTenancyMonthlyRent(Tenancy tenancy) {
        BigDecimal rentJournalAmount;

        switch (tenancy.getPeriod()) {
            case DAILY:
                rentJournalAmount = new BigDecimal(tenancy.getRent())
                        .multiply(new BigDecimal(getDaysAmountForCurrentYear()))
                        .divide(new BigDecimal(12 * 30), RoundingMode.HALF_UP);
                break;
            case WEEKLY:
                rentJournalAmount = new BigDecimal(tenancy.getRent())
                        .multiply(new BigDecimal(365))
                        .divide(new BigDecimal(12), RoundingMode.HALF_UP)
                        .divide(new BigDecimal(7), RoundingMode.HALF_UP);
                break;
            case TWO_WEEKLY:
                rentJournalAmount = new BigDecimal(tenancy.getRent())
                        .multiply(new BigDecimal(365))
                        .divide(new BigDecimal(12), RoundingMode.HALF_UP)
                        .divide(new BigDecimal(7), RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(2));
                break;
            case MONTHLY:
                rentJournalAmount = new BigDecimal(tenancy.getRent());
                break;
            case UK_QUARTERLY:
            case QUARTERLY:
                rentJournalAmount = new BigDecimal(tenancy.getRent()).divide(new BigDecimal(3), RoundingMode.HALF_UP);
                break;
            case SIX_MONTHLY:
                rentJournalAmount = new BigDecimal(tenancy.getRent()).divide(new BigDecimal(6), RoundingMode.HALF_UP);
                break;
            case ANNUALLY:
                rentJournalAmount = new BigDecimal(tenancy.getRent()).divide(new BigDecimal(12), RoundingMode.HALF_UP);
                break;
            case BI_ANNUALLY:
                rentJournalAmount = new BigDecimal(tenancy.getRent()).divide(new BigDecimal(24), RoundingMode.HALF_UP);
                break;
            case FIVE_YEAR:
                rentJournalAmount = new BigDecimal(tenancy.getRent()).divide(new BigDecimal(60), RoundingMode.HALF_UP);
                break;
            case TEN_YEAR:
                rentJournalAmount = new BigDecimal(tenancy.getRent()).divide(new BigDecimal(120), RoundingMode.HALF_UP);
                break;
            case FIFTEEN_YEAR:
                rentJournalAmount = new BigDecimal(tenancy.getRent()).divide(new BigDecimal(180), RoundingMode.HALF_UP);
                break;
            case TWENTY_YEAR:
                rentJournalAmount = new BigDecimal(tenancy.getRent()).divide(new BigDecimal(240), RoundingMode.HALF_UP);
                break;
            case TWENTY_FIVE_YEAR:
                rentJournalAmount = new BigDecimal(tenancy.getRent()).divide(new BigDecimal(300), RoundingMode.HALF_UP);
                break;
            default:
                throw new IllegalArgumentException("Unknown period " + tenancy.getPeriod() + " in tenancy " + tenancy.getId());
        }

        return rentJournalAmount
                .divide(BigDecimal.TEN, RoundingMode.HALF_UP)
                .divide(BigDecimal.TEN, RoundingMode.HALF_UP);
    }

    @Override
    public BigDecimal getTenancyJournalAmount(Tenancy tenancy, BigDecimal tenantBalance, Temporal now) {
        var rent = getTenancyMonthlyRent(tenancy);

        if (Objects.isNull(tenancy.getStartDate())) {
            return rent
                    .min(tenantBalance)
                    .max(BigDecimal.ZERO);
        }

        var startDate = Utils.convertDateToInstant(tenancy.getStartDate()).atZone(UTC);
        var startDateMonth = Month.from(startDate).getValue();
        var currentMonth = Month.from(now).getValue();
        if (tenancy.isEnableProRataJournal() && currentMonth - startDateMonth < 2) {
            var daysTillEndOfMonth = YearMonth.from(startDate).lengthOfMonth() - startDate.get(ChronoField.DAY_OF_MONTH) + 1;
            return BigDecimal.valueOf(daysTillEndOfMonth)
                    .multiply(rent)
                    .multiply(BigDecimal.valueOf(12))
                    .movePointRight(2)
                    .divide(BigDecimal.valueOf(365), RoundingMode.HALF_UP)
                    .movePointLeft(2)
                    .min(tenantBalance)
                    .max(BigDecimal.ZERO);
        }
        return rent
                .min(tenantBalance)
                .max(BigDecimal.ZERO);
    }

    private BigDecimal movePoint(Optional<BigDecimal> itemOpt) {
        return itemOpt
                .map(amount -> amount.movePointLeft(2))
                .orElseThrow();
    }

    @Override
    public BigDecimal getManagementFee(Tenancy tenancy, BigDecimal receivedRentAmount) {
        var tenancySettings = tenancy.getSettings();

        if (tenancySettings.getFeeType().equals("PERCENTAGE_OF_FULL_AMOUNT")) {
            var rentAmount = movePoint(Optional.ofNullable(tenancy.getRent()).map(BigDecimal::new));
            var percentageAmount = movePoint(Optional.ofNullable(tenancySettings.getRentCommission()));
            return rentAmount.multiply(percentageAmount);
        } else if (tenancySettings.getFeeType().equals("PERCENTAGE_OF_AMOUNT_RECEIVED")
                || tenancySettings.getFeeType().equals("PERCENTAGE_OF_JOURNAL_AMOUNT_RECEIVED")) {
            var percentageAmount = movePoint(Optional.ofNullable(tenancySettings.getRentCommission()));
            return receivedRentAmount.multiply(percentageAmount);
        } else {
            return movePoint(Optional.ofNullable(tenancySettings.getFixedFee()).map(BigDecimal::new));
        }
    }

    @Override
    public BigDecimal getTenancyMonthTransferAmount(List<LineItem> lineItems, int year, int month) {
        return lineItems.stream()
                .filter(LineItem::isBalanceTransfer)
                .filter(this::invoiceHasAmount)
                .filter(lineItem -> {
                    var creationDate = Instant.parse(lineItem.getParentDate()).atZone(UTC).toLocalDate();
                    return creationDate.getYear() == year && creationDate.getMonthValue() == month;
                })
                .collect(groupingBy(LineItem::getInvoiceId))
                .values()
                .stream()
                .map(items -> items
                        .stream()
                        .map(this::getLineItemAmount)
                        .findFirst()
                        .orElse(BigDecimal.ZERO)
                ).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public Optional<Tenancy.DepositStatus> getDepositStatusForTenancy(Tenancy tenancy) {
        if (Objects.isNull(tenancy) || Objects.isNull(tenancy.getReference())) {
            return Optional.empty();
        }

        if (tenancy.getDepositStatus() == Tenancy.DepositStatus.RECEIVING) {
            return Optional.of(Tenancy.DepositStatus.RECEIVING);
        }
        if (tenancy.getDepositStatus() == Tenancy.DepositStatus.REGISTERING && tenancy.isDepositRegistered() && tenancy.isDepositTransferred()) {
            if (tenancy.isDepositReleased()) {
                return Optional.of(Tenancy.DepositStatus.REFUNDED);
            }

            return Optional.of(Tenancy.DepositStatus.REFUNDING);
        }
        if (tenancy.getDepositStatus() == Tenancy.DepositStatus.REGISTERING) {
            return Optional.of(Tenancy.DepositStatus.REGISTERING);
        }
        return Optional.of(Tenancy.DepositStatus.RECEIVING);
    }

    @Override
    public Optional<DepositManagementPayload.DepositStatus> getDashboardDepositStatusForTenancy(Tenancy tenancy) {
        if (Objects.isNull(tenancy) || Objects.isNull(tenancy.getReference())) {
            return Optional.empty();
        }
        if (Objects.isNull(tenancy.getDepositStatus())) {
            return Optional.empty();
        }
        if (tenancy.getDepositStatus() == Tenancy.DepositStatus.RECEIVING) {
            return Optional.of(DepositManagementPayload.DepositStatus.RECEIVING);
        }
        if (tenancy.getDepositStatus() == Tenancy.DepositStatus.REGISTERING && tenancy.isDepositRegistered() && tenancy.isDepositTransferred()) {
            if (tenancy.isDepositReleased()) {
                return Optional.of(DepositManagementPayload.DepositStatus.REFUNDED);
            }

            return Optional.of(DepositManagementPayload.DepositStatus.REFUNDING);
        }
        if (tenancy.getDepositStatus() == Tenancy.DepositStatus.REGISTERING) {
            return Optional.of(DepositManagementPayload.DepositStatus.REGISTERING);
        }
        return Optional.of(DepositManagementPayload.DepositStatus.RECEIVING);
    }
}
