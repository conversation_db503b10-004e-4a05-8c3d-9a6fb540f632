package com.rentancy.integrations.servicies.readmodel;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.Invoice;
import com.rentancy.integrations.pojos.Property;
import com.rentancy.integrations.pojos.Tenancy;
import com.rentancy.integrations.pojos.sqs.ReadModelParentPropertySummaryFillCommand;
import com.rentancy.integrations.servicies.SQSClient;
import com.rentancy.integrations.servicies.persistence.DDBClient;
import com.rentancy.integrations.servicies.persistence.InvoiceRepository;
import com.rentancy.integrations.servicies.persistence.PropertyRepository;
import com.rentancy.integrations.servicies.persistence.TenancyRepository;
import com.rentancy.integrations.servicies.persistence.config.TenancyRepositoryConfig;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

import static com.rentancy.integrations.util.JSONUtils.wrappedToJsonString;

@AllArgsConstructor
public class FindParentPropertyFromLineItemUseCase {
    private static final Logger logger = LoggerFactory.getLogger(FindParentPropertyFromLineItemUseCase.class);

    private final static String READ_MODEL_PARENT_PROPERTY_SUMMARY_QUEUE = "READ_MODEL_PARENT_PROPERTY_SUMMARY_QUEUE";

    private final TenancyRepository tenancyRepository;
    private final PropertyRepository propertyRepository;
    private final SQSClient sqsClient;
    private final InvoiceRepository invoiceRepository;
    private final Config config;

    public static FindParentPropertyFromLineItemUseCase create(Config config) {
        DDBClient ddbClient = new DDBClient(config);
        TenancyRepository tenancyRepository = new TenancyRepository(new TenancyRepositoryConfig(), ddbClient);
        PropertyRepository propertyRepository = new PropertyRepository(config, ddbClient);
        InvoiceRepository invoiceRepository = new InvoiceRepository(config, ddbClient);
        SQSClient sqsClient = new SQSClient();
        return new FindParentPropertyFromLineItemUseCase(tenancyRepository, propertyRepository, sqsClient, invoiceRepository, config);
    }

    public void findParentPropertyFromLineItemAndSendOutSqsMessages(List<Invoice.LineItem> lineItems) {
        logger.info("Line items: {}", wrappedToJsonString(lineItems));

        var invoiceIds = extractInvoiceIdsFromLineItems(lineItems);

        var tenancies = findTenanciesForInvoiceIdsFromRepo(invoiceIds);

        // for property bills, the invoice is attached to property directly
        var invoiceProperties = findInvoicesAndAttachedPropertiesFromRepo(invoiceIds);

        var propertyIds = extractPropertyIdsFromTenanciesAndInvoices(tenancies, invoiceProperties);

        var properties = findPropertiesFromRepo(propertyIds);

        // send command for each parent property
        var messages = gatherMessages(lineItems, tenancies, properties, invoiceProperties);
        sendMessagesToSqs(messages);

        var straightParentPropertiesMessages = getParentPropertiesFromTrackingNamesInLineItems(lineItems);
        sendMessagesToSqs(straightParentPropertiesMessages);
    }

    private List<ReadModelParentPropertySummaryFillCommand> getParentPropertiesFromTrackingNamesInLineItems(List<Invoice.LineItem> lineItems) {
        var trackingNames = lineItems.stream().map(li -> Pair.of(li, li.getTrackingName())).collect(Collectors.toList());
        logger.info("Getting parent properties from line item tracking names: {}", wrappedToJsonString(trackingNames.stream().map(Pair::getRight).collect(Collectors.toList())));
        var parentPropertiesByTracking = findParentPropertiesByTrackingName(trackingNames.stream().map(Pair::getRight).collect(Collectors.toList()));
        logger.info("Found Parent Properties: {}", wrappedToJsonString(parentPropertiesByTracking));
        return trackingNames.stream()
                .map((pair) -> {
                    var opp = parentPropertiesByTracking.stream()
                            .filter(p -> pair.getRight().equals(p.getReference()))
                            .findFirst();
                    if (opp.isEmpty()) return null;
                    var pp = opp.get();
                    var lineItem = pair.getLeft();
                    return new ReadModelParentPropertySummaryFillCommand(
                            lineItem.getParentReference(),
                            pp.getId(),
                            lineItem.getUpdatedAt().toString()
                    );
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<Property> findParentPropertiesByTrackingName(List<String> trackingNames) {
        return trackingNames.stream()
                .flatMap(tn -> propertyRepository.findParentPropertyByReference(tn).stream())
                .collect(Collectors.toList());
    }

    private void sendMessagesToSqs(List<ReadModelParentPropertySummaryFillCommand> messages) {
        logger.info("Messages : {}", wrappedToJsonString(messages));
        var queueName = config.getVariable(READ_MODEL_PARENT_PROPERTY_SUMMARY_QUEUE);
        messages.forEach(pp -> sqsClient.enqueue(queueName, wrappedToJsonString(pp)));
    }

    private List<Property> findPropertiesFromRepo(Set<String> propertyIds) {
        var properties = propertyRepository.getProperties(propertyIds).collect(Collectors.toList());
        logger.info("Properties : {}", wrappedToJsonString(properties));
        return properties;
    }

    private static Set<String> extractPropertyIdsFromTenanciesAndInvoices(List<Pair<String, List<Tenancy>>> tenancies, List<Pair<String, List<String>>> invoiceProperties) {
        Set<String> propertyIds = tenancies.stream().flatMap(pair -> pair.getRight().stream().map(Tenancy::getProperty)).filter(Objects::nonNull).collect(Collectors.toSet());
        propertyIds.addAll(invoiceProperties.stream().flatMap(p -> p.getRight().stream()).collect(Collectors.toSet()));
        logger.info("Property Ids : {}", wrappedToJsonString(propertyIds));
        return propertyIds;
    }

    private List<Pair<String, List<String>>> findInvoicesAndAttachedPropertiesFromRepo(List<String> invoiceIds) {
        var invoiceProperties = invoiceRepository
                .getInvoices(new HashSet<>(invoiceIds)).stream()
                .map(invoice -> Pair.of(invoice.getId(), invoice.getProperties()))
                .collect(Collectors.toList());
        logger.info("Invoice properties: {}", wrappedToJsonString(invoiceProperties));
        return invoiceProperties;
    }

    private List<Pair<String, List<Tenancy>>> findTenanciesForInvoiceIdsFromRepo(List<String> invoiceIds) {
        var tenancies =  invoiceIds.stream().map(i -> Pair.of(i, tenancyRepository.findTenanciesByInvoiceId(i))).collect(Collectors.toList());
        logger.info("Tenancies : {}", wrappedToJsonString(tenancies));
        return tenancies;
    }

    private static List<String> extractInvoiceIdsFromLineItems(List<Invoice.LineItem> lineItems) {
        var invoiceIds =  lineItems.stream().map(Invoice.LineItem::getInvoiceId).collect(Collectors.toList());
        logger.info("Invoice Ids : {}", wrappedToJsonString(invoiceIds));
        return invoiceIds;
    }


    private List<ReadModelParentPropertySummaryFillCommand> gatherMessages(
            List<Invoice.LineItem> lineItems,
            List<Pair<String, List<Tenancy>>> tenancies,
            List<Property> properties,
            List<Pair<String, List<String>>> invoiceProperties) {

        // Create a map for quick lookup of parentPropertyId by propertyId
        Map<String, String> propertyToParentMap = new HashMap<>();
        for (Property property : properties) {
            propertyToParentMap.put(property.getId(), property.getParentPropertyId());
        }

        // Create a list to hold the commands
        List<ReadModelParentPropertySummaryFillCommand> commands = new ArrayList<>();

        // Iterate over each LineItem
        for (Invoice.LineItem lineItem : lineItems) {
            String organisationId = lineItem.getParentReference();
            String invoiceId = lineItem.getInvoiceId();

            // Find corresponding tenancies for the invoiceId
            for (Pair<String, List<Tenancy>> pair : tenancies) {
                if (pair.getLeft().equals(invoiceId)) {
                    List<Tenancy> tenancyList = pair.getRight();

                    // For each tenancy, get the propertyId and find its parentPropertyId
                    for (Tenancy tenancy : tenancyList) {
                        String propertyId = tenancy.getProperty();
                        String parentPropertyId = propertyToParentMap.get(propertyId);

                        if (parentPropertyId != null) {
                            ReadModelParentPropertySummaryFillCommand command = new ReadModelParentPropertySummaryFillCommand(
                                    organisationId,
                                    parentPropertyId,
                                    lineItem.getUpdatedAt().toString()
                            );
                            commands.add(command);
                        }
                    }
                }
            }

            // Separate loop to find the parentProperties in the invoices
            var propertiesForInvoice = invoiceProperties.stream().filter(p -> p.getLeft().equals(invoiceId)).flatMap(p -> p.getRight().stream()).collect(Collectors.toList());
            for (String propertyId : propertiesForInvoice) {
                String parentPropertyId = propertyToParentMap.get(propertyId);

                if (parentPropertyId != null) {
                    ReadModelParentPropertySummaryFillCommand command = new ReadModelParentPropertySummaryFillCommand(
                            organisationId,
                            parentPropertyId,
                            lineItem.getUpdatedAt().toString()
                    );
                    commands.add(command);
                }
            }
        }

        return commands;
    }
}
