package com.rentancy.integrations.servicies;

import com.amazonaws.HttpMethod;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.*;
import com.rentancy.integrations.config.Config;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.lang.Nullable;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.time.Instant;
import java.util.Optional;
import java.util.UUID;

class S3Client {

    private static final Logger log = LogManager.getLogger(S3Client.class);
    private static final int  PRESIGNED_URL_EXPIRATION_TIME = 1000 * 60 * 5;
    private final AmazonS3 s3client;
    private final AmazonS3 s3LondonClient;

    S3Client(Config config) {
        this.s3client = AmazonS3ClientBuilder
                .standard()
                .withRegion(config.getAWSRegion())
                .build();

        this.s3LondonClient = AmazonS3ClientBuilder
                .standard()
                .withRegion(config.getUploadsRegion())
                .build();
    }
    public String getObjectUrl(String bucketName,String key){
        return s3LondonClient.getUrl(bucketName, key).toString();
    }

    public String getPresignedUrl(String bucketName, String fileKey) {
        var generatedUrl = "";

        try {
            // Set the presigned URL to expire after one hour.
            java.util.Date expiration = new java.util.Date();
            var expTimeMillis = Instant.now().toEpochMilli();
            expTimeMillis +=  PRESIGNED_URL_EXPIRATION_TIME;
            expiration.setTime(expTimeMillis);

            // Generate the presigned URL.
           log.info("Generating pre-signed URL.");

            var generatePresignedUrlRequest =
                    new GeneratePresignedUrlRequest(bucketName, fileKey)
                            .withMethod(HttpMethod.GET)
                            .withExpiration(expiration);
            URL url = s3LondonClient.generatePresignedUrl(generatePresignedUrlRequest);
            generatedUrl = url.toString();
            log.info("Pre-Signed URL: " + url);
        } catch (Exception e) {
            // The call was transmitted successfully, but Amazon S3 couldn't process
            // it, so it returned an error response.
            throw new IllegalStateException(e.getMessage());
        }
        return generatedUrl;
    }

    private String upload(String bucket, byte[] contentInput, String fileExtension) {
        String contentType=null;
        var content = new ByteArrayInputStream(contentInput);
        var reportKey = UUID.randomUUID().toString();
        var key = "public/generated_reports/" + reportKey + "." + fileExtension;
        try (var input = content) {
            var metadata = new ObjectMetadata();
            metadata.setContentLength(input.available());

            Optional.ofNullable(contentType).ifPresent(metadata::setContentType);

            var request = new PutObjectRequest(bucket, key, input, metadata);

            log.info("Uploading with metadata - " + metadata);

            s3LondonClient.putObject(request);

            return key;
        } catch (IOException e) {
            throw new IllegalStateException("Failed to upload file to S3: " + key, e);
        }
    }

    public String uploadPdf(String bucket, byte[] contentInput) {
        return upload(bucket, contentInput, "pdf");
    }

    public String uploadExcel(String bucket, byte[] contentInput) {
        return upload(bucket, contentInput, "xlsx");
    }

    public String uploadPdf(String bucket, String key, InputStream content, @Nullable String contentType) {
        try (var input = content) {
            var metadata = new ObjectMetadata();
            metadata.setContentLength(input.available());

            Optional.ofNullable(contentType).ifPresent(metadata::setContentType);

            var request = new PutObjectRequest(bucket, key, input, metadata);

            log.info("Uploading with metadata - " + metadata);

            s3LondonClient.putObject(request);

            return key;
        } catch (IOException e) {
            throw new IllegalStateException("Failed to upload file to S3: " + key, e);
        }
    }

    public String getS3Url(String bucket, String key) {
        return "s3://" + bucket + "/" + key;
    }

    public InputStream download(String bucket, String key) {
        return getObject(bucket, key).getObjectContent();
    }

    public InputStream downloadFromLondon(String bucket, String key) {
        return getObjectLondon(bucket, key).getObjectContent();
    }

    private S3Object getObject(String bucket, String key) {
        return s3client.getObject(bucket, key);
    }

    private S3Object getObjectLondon(String bucket, String key) {
        return s3LondonClient.getObject(bucket, key);
    }
}
