package com.rentancy.integrations.servicies.enhanced.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;


@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@DynamoDbBean
public class InvoiceProperty {

    private String id;
    private String invoicePropertyInvoiceId;
    private String invoicePropertyOrganisationId;
    private String invoicePropertyPropertyId;

    @DynamoDbPartitionKey
    public String getId() {
        return id;
    }

}
