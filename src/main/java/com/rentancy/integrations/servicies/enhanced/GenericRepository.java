package com.rentancy.integrations.servicies.enhanced;

import com.rentancy.integrations.config.Config;
import software.amazon.awssdk.enhanced.dynamodb.*;
import software.amazon.awssdk.enhanced.dynamodb.model.*;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

public abstract class GenericRepository<T, ID> implements DynamoRepository<T, ID> {

    private static final int MAX_BULK_REMOVE_SIZE = 25;
    protected final DynamoDbEnhancedClient dynamoDbEnhancedClient;
    protected final String tableName;
    private final Class<T> clazz;

    protected GenericRepository(DynamoDbEnhancedClient dynamoDbEnhancedClient, Config config, String table, Class<T> clazz) {
        String tableName = config.getVariable(table);
        this.dynamoDbEnhancedClient = dynamoDbEnhancedClient;
        this.tableName = tableName;
        this.clazz = clazz;
    }

    public Optional<T> getById(ID id) {
        DynamoDbTable<T> dynamoDbTable = dynamoDbEnhancedClient.table(tableName, TableSchema.fromBean(clazz));
        return Optional.ofNullable(dynamoDbTable.getItem(Key.builder().partitionValue(id.toString()).build()));
    }

    public List<T> findAll() {
        DynamoDbTable<T> documentTable = dynamoDbEnhancedClient.table(tableName, TableSchema.fromBean(clazz));
        return documentTable.scan().items().stream().collect(toList());
    }

    public List<T> findAllFilteredBy(Expression expression) {
        DynamoDbTable<T> documentTable = dynamoDbEnhancedClient.table(tableName, TableSchema.fromBean(clazz));
        return documentTable.scan(ScanEnhancedRequest.builder().filterExpression(expression).build()).items().stream().collect(toList());
    }

    public Collection<T> getAllByIds(Collection<String> ids) {
        DynamoDbTable<T> documentTable = dynamoDbEnhancedClient.table(tableName, TableSchema.fromBean(clazz));
        ReadBatch.Builder<T> readBatchBuilder = ReadBatch.builder(clazz)
                .mappedTableResource(documentTable);

        ids.forEach(id -> readBatchBuilder.addGetItem(key -> key.key(Key.builder().partitionValue(id).build())));

        BatchGetItemEnhancedRequest batchGetItemEnhancedRequest = BatchGetItemEnhancedRequest.builder()
                .readBatches(readBatchBuilder.build())
                .build();

        return dynamoDbEnhancedClient.batchGetItem(batchGetItemEnhancedRequest)
                .resultsForTable(documentTable)
                .stream()
                .collect(Collectors.toList());
    }

    public void save(T item) {
        DynamoDbTable<T> table = dynamoDbEnhancedClient.table(tableName, TableSchema.fromBean(clazz));
        table.putItem(item);
    }

    public void deleteAll() {

        DynamoDbTable<T> table = dynamoDbEnhancedClient.table(tableName, TableSchema.fromBean(clazz));
        List<T> itemsToDelete = new ArrayList<>();
        table.scan().items().forEach(item -> {
            itemsToDelete.add(item);

            if (itemsToDelete.size() == MAX_BULK_REMOVE_SIZE) {
                deleteItemsBatch(table, itemsToDelete);
                itemsToDelete.clear();
            }
        });

        if (!itemsToDelete.isEmpty()) {
            deleteItemsBatch(table, itemsToDelete);
        }
    }

    private void deleteItemsBatch(DynamoDbTable<T> table, List<T> items) {
        WriteBatch.Builder<T> writeBatch = WriteBatch.builder(clazz).mappedTableResource(table);

        items.forEach(writeBatch::addDeleteItem);

        BatchWriteItemEnhancedRequest batchWriteItemEnhancedRequest = BatchWriteItemEnhancedRequest.builder()
                .writeBatches(writeBatch.build())
                .build();

        dynamoDbEnhancedClient.batchWriteItem(batchWriteItemEnhancedRequest);
    }

    protected List<T> scanByPropertyValue(String field, String value) {

        DynamoDbTable<T> dynamoDbTable = dynamoDbEnhancedClient.table(tableName, TableSchema.fromBean(clazz));

        AttributeValue attributeValue = AttributeValue.builder().s(value).build();

        Map<String, AttributeValue> expressionValues = new HashMap<>();
        expressionValues.put(":value", attributeValue);

        Expression expression = Expression.builder()
                .expression(String.format("%s=:value", field))
                .expressionValues(expressionValues)
                .build();

        ScanEnhancedRequest scanEnhancedRequest = ScanEnhancedRequest.builder()
                .filterExpression(expression)
                .build();

        return dynamoDbTable.scan(scanEnhancedRequest).items().stream().collect(toList());

    }

    protected List<T> findByIndexKeyValue(String indexName, String value) {

        DynamoDbIndex<T> dynamoDbIndex = dynamoDbEnhancedClient.table(tableName, TableSchema.fromBean(clazz)).index(indexName);
        QueryConditional queryConditional = QueryConditional.keyEqualTo(Key.builder().partitionValue(value).build());

        return dynamoDbIndex.query(QueryEnhancedRequest.builder().queryConditional(queryConditional).build())
                .stream().map(Page::items).flatMap(List::stream).collect(toList());
    }

    protected List<T> findByIndexKeyValue(String indexName, String value, Expression expression) {

        DynamoDbIndex<T> dynamoDbIndex = dynamoDbEnhancedClient.table(tableName, TableSchema.fromBean(clazz)).index(indexName);
        QueryConditional queryConditional = QueryConditional.keyEqualTo(Key.builder().partitionValue(value).build());

        return dynamoDbIndex.query(QueryEnhancedRequest.builder().queryConditional(queryConditional).filterExpression(expression).build())
                .stream().map(Page::items).flatMap(List::stream).collect(toList());
    }

    protected List<T> findByIndexKeyValueWithFilterOut(String indexName, String value, String filterFiled, boolean booleanValue) {

        DynamoDbIndex<T> dynamoDbIndex = dynamoDbEnhancedClient.table(tableName, TableSchema.fromBean(clazz)).index(indexName);
        QueryConditional queryConditional = QueryConditional.keyEqualTo(Key.builder().partitionValue(value).build());

        return dynamoDbIndex.query(QueryEnhancedRequest.builder()
                        .queryConditional(queryConditional)
                        .filterExpression(prepareFilterOutExpression(filterFiled, booleanValue))
                        .build())
                .stream().map(Page::items).flatMap(List::stream).collect(toList());
    }

    protected List<T> findByCompositeIndexKeyValue(String indexName, String partitionValue, String sortValue) {

        DynamoDbIndex<T> dynamoDbIndex = dynamoDbEnhancedClient.table(tableName, TableSchema.fromBean(clazz)).index(indexName);

        QueryConditional queryConditional = QueryConditional
                .keyEqualTo(Key.builder()
                        .partitionValue(partitionValue)
                        .sortValue(sortValue)
                        .build());

        return dynamoDbIndex.query(QueryEnhancedRequest.builder()
                        .queryConditional(queryConditional)
                        .build())
                .stream().map(Page::items).flatMap(List::stream).collect(toList());
    }

    protected Expression prepareFilterOutExpression(String fieldFiltered, boolean unwantedSortValue) {
        return expressionBuilderNotEquals(fieldFiltered, AttributeValue.builder().bool(unwantedSortValue).build());
    }

    protected Expression prepareFilterOutExpression(String fieldFiltered, String unwantedSortValue) {
        return expressionBuilderNotEquals(fieldFiltered, AttributeValue.builder().s(unwantedSortValue).build());
    }


    protected Expression prepareExpressionAttributeExists(String attributeName) {
        return Expression.builder()
                .expression("attribute_exists(#attrName)")
                .expressionNames(Collections.singletonMap("#attrName", attributeName))
                .build();
    }

    protected Expression prepareExpressionAttributeNotExists(String attributeName) {
        return Expression.builder()
                .expression("attribute_not_exists(#attrName)")
                .expressionNames(Collections.singletonMap("#attrName", attributeName))
                .build();
    }

    protected Expression expressionBuilderIn(String fieldFiltered, List<String> values) {

       HashMap<String, Integer> test=new HashMap<>();
       AtomicInteger counter=new AtomicInteger();
        values.stream().forEach(value -> test.put(value, counter.incrementAndGet()));

        String filterValues = values.stream()
                .map(value -> "contains( " + fieldFiltered + ", :value" + test.get(value) + ")")
                .collect(Collectors.joining(" OR "));

        return Expression.builder()
                .expression(filterValues)
                .expressionValues(values.stream()
                        .collect(Collectors.toMap(
                                value -> ":value" + test.get(value),
                                value -> AttributeValue.builder().s(value).build()
                        )))
                .build();
    }


    private Expression expressionBuilderNotEquals(String fieldFiltered, AttributeValue attributeValue) {
        Map<String, AttributeValue> expressionValues = new HashMap<>();
        expressionValues.put(":value", attributeValue);
        return Expression.builder()
                .expression("#statusAttr <> :value")
                .expressionNames(Collections.singletonMap("#statusAttr", fieldFiltered))
                .expressionValues(expressionValues)
                .build();
    }

}
