package com.rentancy.integrations.servicies.enhanced;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.servicies.enhanced.entity.InvoiceProperty;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;

import java.util.List;

public class InvoicePropertyRepositoryImpl extends GenericRepository<InvoiceProperty, String> implements InvoicePropertyRepository {

    private static final String INVOICE_PROPERTY_TABLE = "INVOICE_PROPERTY_TABLE";

    public InvoicePropertyRepositoryImpl(DynamoDbEnhancedClient dynamoDbEnhancedClient, Config config) {
        super(dynamoDbEnhancedClient, config, INVOICE_PROPERTY_TABLE, InvoiceProperty.class);
    }

    @Override
    public List<InvoiceProperty> getAllByInvoiceIds(List<String> invoiceIds) {
        return findAllFilteredBy(expressionBuilderIn("invoicePropertyInvoiceId", invoiceIds));
    }

}
