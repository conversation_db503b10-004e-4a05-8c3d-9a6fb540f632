package com.rentancy.integrations.servicies;

import com.rentancy.integrations.pojos.MessageResponsePayload;

import java.util.Map;

public interface WhatsAppService {
    void postMessage(Map<String, Object> payload);
    void sendResponse(MessageResponsePayload payload);
    void sendWhatsAppIntegrationRequest(String organisationId, String userId);
    void postStatus(Map<String, Object> payload);
}
