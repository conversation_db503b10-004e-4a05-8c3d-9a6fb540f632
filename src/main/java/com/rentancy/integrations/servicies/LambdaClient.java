package com.rentancy.integrations.servicies;

import com.amazonaws.services.lambda.AWSLambda;
import com.amazonaws.services.lambda.AWSLambdaClientBuilder;
import com.amazonaws.services.lambda.model.InvokeRequest;
import com.rentancy.integrations.config.Config;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.nio.charset.StandardCharsets;

public class LambdaClient {

    private static final Logger log = LogManager.getLogger(LambdaClient.class);

    private final AWSLambda awsLambda;

    public LambdaClient(Config config) {
        this.awsLambda = AWSLambdaClientBuilder
                .standard()
                .withRegion(config.getLambdaRegion())
                .build();
    }

    public String invoke(String functionName, String payload) {
        try {
            var request = new InvokeRequest()
                    .withFunctionName(functionName)
                    .withPayload(payload);

            var result = this.awsLambda.invoke(request);
            var resultBody = new String(result.getPayload().array(), StandardCharsets.UTF_8);

            log.info("Status - {}, Result - {}", result.getStatusCode(), resultBody);

            return resultBody;
        } catch (Exception e) {
            log.error("Failed to invoke lambda - {} {}", functionName, payload, e);
            return null;
        }
    }
}
