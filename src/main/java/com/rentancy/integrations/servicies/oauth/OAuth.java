package com.rentancy.integrations.servicies.oauth;

import com.rentancy.integrations.pojos.ExchangeInput;
import com.rentancy.integrations.pojos.XeroAuthentication;
import com.rentancy.integrations.pojos.XeroExchangeResult;

import java.time.Instant;

public interface OAuth {

    XeroAuthentication getAuthorizationUrl(String organisationId, Instant startDate);

    XeroExchangeResult exchangeCode(ExchangeInput input);

    XeroAuthentication getAppStoreAuthorizationUrl();

    void disconnect(String cognitoId);

}
