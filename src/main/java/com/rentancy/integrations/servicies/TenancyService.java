package com.rentancy.integrations.servicies;

import com.rentancy.integrations.pojos.*;
import com.rentancy.integrations.pojos.Invoice.LineItem;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.temporal.Temporal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface TenancyService {

    List<String> NON_ACTIVE_TENANCY_STATUSES = List.of("CANCELLED", "ARCHIVED");
    List<String> ACTIVE_TENANCY_STATUSES = List.of("ACTIVE", "RENEWED", "PERIODIC");
    List<String> DEPOSIT_LEDGER_CODE_NAMES = List.of("Paid Deposit", "Deposits", "Zero Deposit Scheme");

    BigDecimal getLineItemAmount(Invoice.LineItem lineItem);

    TenancySummary calculateTenancySummary(String tenancyId);

    BigDecimal getLineItemTaxVatPaidAmount(Invoice.LineItem lineItem);

    boolean invoiceHasAmount(Invoice.LineItem lineItem);

    List<PropertyFinanceSummary> calculatePropertySummary(Property property, Organisation organisation);
    List<PropertyFinanceSummary> calculatePropertySummary(Property property, Organisation organisation, List<Invoice.LineItem> lineItems);

    List<PropertyFinanceSummary> calculatePropertySummary(String propertyId);

    PropertyFinanceSummary calculateParentPropertyFinanceSummary(String parentPropertyId);

    TenancySummary calculatePropertyTenanciesSummary(Organisation organisation,
                                                     Property property,
                                                     List<Tenancy> tenancies,
                                                     List<Invoice.LineItem> allInvoices,
                                                     Instant today);

    PropertySummary calculatePropertySummary(Organisation organisation, Property property, List<Tenancy> tenancies,
                                             List<Invoice.LineItem> allInvoices, List<Invoice.LineItem> oldInvoices,
                                             Instant now);

    PropertyExpenses calculateAllExpenses(List<Invoice.LineItem> invoices, String trackingName, List<String> excludingLedgerCodes);

    BigDecimal getLineItemPaidAmount(Invoice.LineItem lineItem);

    PropertySummary reducePropertySummaries(PropertySummary s1, PropertySummary s2);

    FinanceSummary calculateFinanceBalanceSummary(Organisation organisation, List<Invoice.LineItem> lineItems,
                                                  List<String> ledgerCodeNames, String trackingName, boolean income,
                                                  Instant today, @Nullable List<Tenancy> tenancies);

    Instant getToday();

    List<String> getBillLedgerCodeNames(Organisation organisation);

    String getParentReference(LineItem lineItem);

    Optional<String> getInvoiceLineItemTenancyReference(LineItem lineItem);

    String getParentNumber(LineItem lineItem);

    BigDecimal calculateTenantBalance(Organisation organisation, User tenant, Map<String, Property> propertyIdMap, List<Tenancy> tenancies, List<LineItem> lineItems);

    BigDecimal getTenancyMonthlyRent(Tenancy tenancy);

    BigDecimal getTenancyJournalAmount(Tenancy tenancy, BigDecimal tenantBalance, Temporal now);

    BigDecimal getTenancyMonthTransferAmount(List<LineItem> lineItems, int year, int month);

    BigDecimal getManagementFee(Tenancy tenancy, BigDecimal receivedRentAmount);

    int getDaysAmountForCurrentYear();

    Optional<Tenancy.DepositStatus> getDepositStatusForTenancy(Tenancy tenancy);

    boolean isInvoiceLineItemAgainstTenancy(LineItem lineItem, Property property, Tenancy tenancy);

    BigDecimal calculateTenantBalancePerformant(Organisation organisation, User tenant, List<Tenancy> tenancies, Map<String, List<LineItem>> lineItemsLookup);

    Optional<DepositManagementPayload.DepositStatus> getDashboardDepositStatusForTenancy(Tenancy tenancy);
}
