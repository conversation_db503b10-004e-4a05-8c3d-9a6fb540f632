package com.rentancy.integrations.servicies;

import com.rentancy.integrations.pojos.Authentication;
import com.rentancy.integrations.pojos.Oauth2ExchangeResponse;
import com.rentancy.integrations.pojos.XeroTokenPayload;

import java.io.IOException;
import java.util.List;

public interface Oauth2Client {
    Authentication getAuthorizeUrl(String state, List<String> scopes);
    Oauth2ExchangeResponse exchangeCode(String code, List<String> scopes);
    Oauth2ExchangeResponse refreshToken(String refreshToken);
    void revokeToken(String refreshToken);
}
