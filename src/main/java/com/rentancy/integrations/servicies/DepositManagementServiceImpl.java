package com.rentancy.integrations.servicies;

import com.rentancy.integrations.pojos.*;
import com.rentancy.integrations.servicies.persistence.DepositManagement;
import com.rentancy.integrations.servicies.persistence.InvoiceService;
import com.rentancy.integrations.servicies.persistence.PropertyService;
import com.rentancy.integrations.servicies.persistence.UserService;
import com.rentancy.integrations.util.Utils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.rentancy.integrations.util.Utils.getPrimaryTenantIds;

@RequiredArgsConstructor
@Deprecated
public class DepositManagementServiceImpl implements DepositManagement {
    private final PropertyService propertyService;
    private final InvoiceService invoiceService;
    private final TenancyService tenancyService;
    private final UserService userService;

    @Override
    public DepositItemsResponse getAllTypeDeposits(DepositManagementPayload body) {
        var organisationId = body.getOrganisationId();
        /*
        DEV NOTE: LET-301: filter tenancies by status: nullable
         */
        var tenancies = propertyService.findTenancies(organisationId, body.getTenancyStatus(), false);
        var organisationProperties = propertyService.findPropertiesWithOrganisation(organisationId);
        var filteredTenancies = new ArrayList<>(
                tenancies.stream()
                        .filter(tenancy -> Objects.nonNull(tenancy.getPrimaryTenant()))
                        .collect(Collectors.toUnmodifiableList())
        );
        /*
        DEV NOTE: LET-301: filter tenancies by tenant name: nullable
         */
        Optional.ofNullable(body.getPrimaryTenantId()).ifPresent(
                primaryTenantId -> filteredTenancies.removeIf(
                        tenancy -> !tenancy.getPrimaryTenant().equals(primaryTenantId)
                )
        );
        /*
        DEV NOTE: LET-301: filter tenancies by depositFrom and depositTo: nullable
         */
        Optional.ofNullable(body.getDepositFrom()).ifPresent(
                depositFrom -> filteredTenancies.removeIf(
                        tenancy -> tenancy.getDeposit().compareTo(body.getDepositFrom()) < 0
                )
        );
        Optional.ofNullable(body.getDepositTo()).ifPresent(
                depositTo -> filteredTenancies.removeIf(
                        tenancy -> tenancy.getDeposit().compareTo(body.getDepositTo()) > 0
                )
        );

        var tenantIds = getPrimaryTenantIds(tenancies);
        var invoiceLineItems = invoiceService.findOrganisationLineItems(organisationId, null, null);
        var invoiceLineItemsMap = invoiceLineItems.stream()
                .filter(item -> Objects.nonNull(item.getInvoiceId()) && Objects.nonNull(tenancyService.getParentReference(item)))
                .collect(Collectors.groupingBy(
                        tenancyService::getParentReference,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.isEmpty() ? null : list.get(0)
                        )
                ));
        var filteredInvoiceLineItems = invoiceLineItemsMap.values().stream().collect(Collectors.toUnmodifiableList());
        var tenancyMap = filteredTenancies.stream()
                .collect(Collectors.toUnmodifiableMap(Tenancy::getReference, tenancy -> tenancy, (t1, t2) -> t1));
        var propertyMap = organisationProperties.stream()
                .collect(Collectors.toUnmodifiableMap(Property::getId, property -> property, (t1, t2) -> t1));

        var tenantIdMap = userService.findUsers(tenantIds).stream().collect(Collectors.toUnmodifiableMap(User::getId, user -> user));

        var lineItemsByDepositStatus = getDepositStatusToInvoiceLineItems(filteredTenancies, filteredInvoiceLineItems);

        var depositResponseBuilder = DepositItemsResponse.builder();

        Stream.of(Tenancy.DepositStatus.RECEIVING, Tenancy.DepositStatus.REGISTERING, Tenancy.DepositStatus.REFUNDING)
                .map(depositStatus -> Pair.of(depositStatus, mapToDepositManagementBody(lineItemsByDepositStatus.getOrDefault(depositStatus, List.of()), propertyMap, tenancyMap, tenantIdMap)))
                .forEach(pair -> withDepositItemsFilters(depositResponseBuilder, pair.getKey(), pair.getValue(), body, tenancyMap, lineItemsByDepositStatus));

        return depositResponseBuilder
                .build();
    }

    private void withDepositItemsFilters(DepositItemsResponse.DepositItemsResponseBuilder depositResponseBuilder, Tenancy.DepositStatus fieldName,
                                         List<DepositItemsResponse.DepositManagementBody> deposits, DepositManagementPayload inputBody, Map<String, Tenancy> tenancyMap, Map<Tenancy.DepositStatus, List<Invoice.LineItem>> lineItemsByDepositStatus) {
        var registered = inputBody.getRegistered();
        var transferred = inputBody.getTransferred();
        var propertyReference = inputBody.getPropertyReference();
        var contractReference = inputBody.getContractReference();
        var filteredDepositType = inputBody.getDepositStatus();
        var page = inputBody.getPage();
        var limit = inputBody.getLimit();
        var dueDate = inputBody.getDueDate();
        var released = inputBody.getReleased();
        List<DepositItemsResponse.DepositManagementBody> items = new ArrayList<>();
        var contractEndDate = inputBody.getContractEndDate();
        var depositsToRegister = fieldName.equals(Tenancy.DepositStatus.REGISTERING) ? countDepositsToRegister(deposits, tenancyMap) : 0;
        var depositsToTransfer = fieldName.equals(Tenancy.DepositStatus.REGISTERING) ? countDepositsToTransfer(deposits, tenancyMap) : 0;
        var depositsToProcess = fieldName.equals(Tenancy.DepositStatus.REFUNDING) ? countDepositsToProcess(lineItemsByDepositStatus, tenancyMap) : 0;
        if (fieldName.equals(filteredDepositType)) {
            items.addAll(deposits.stream()
                    .filter(item -> Objects.isNull(propertyReference) || propertyReference.equals(item.getPropertyReference()))
                    .filter(item -> Objects.isNull(contractReference) || contractReference.equals(item.getContractReference()))
                    .filter(item -> Objects.isNull(dueDate) || dueDate.equals(item.getDueDate()))
                    .filter(item -> Objects.isNull(registered) || registered == item.isRegistered())
                    .filter(item -> Objects.isNull(transferred) || transferred == item.isTransferred())
                    .filter(item -> Objects.isNull(contractEndDate) || contractEndDate.equals(item.getContractEndDate()))
                    .filter(item -> Objects.isNull(released) || released == item.isReleased())
                    .collect(Collectors.toUnmodifiableList()));
        }
        var receivedDepositManagementItemsCount = fieldName.equals(Tenancy.DepositStatus.RECEIVING) ? deposits.size() : 0;
        var registeredDepositManagementItemsCount = fieldName.equals(Tenancy.DepositStatus.REGISTERING) ? deposits.size() : 0;
        var refundDepositManagementItemsCount = fieldName.equals(Tenancy.DepositStatus.REFUNDING) ? deposits.size() : 0;

        switch (fieldName) {
            case RECEIVING:
                var receivedItemsSorted = items.stream().sorted(Comparator.comparing(DepositItemsResponse.DepositManagementBody::getDueDateInstant)).collect(Collectors.toUnmodifiableList());
                depositResponseBuilder.receivedDepositManagementItems(getList(receivedItemsSorted, page, limit));
                depositResponseBuilder.receivedDepositManagementItemsCount(receivedDepositManagementItemsCount);
                depositResponseBuilder.depositsOwed(deposits.size());
                break;
            case REGISTERING:
                depositResponseBuilder.registeredDepositManagementItems(getList(items, page, limit));
                depositResponseBuilder.depositsToRegister(depositsToRegister);
                depositResponseBuilder.depositsToTransfer(depositsToTransfer);
                depositResponseBuilder.registeredDepositManagementItemsCount(registeredDepositManagementItemsCount);
                break;
            case REFUNDING:
                var refundingItemsSorted = items.stream().sorted((i1, i2) -> i2.getContractEndDate().compareTo(i1.getContractEndDate())).collect(Collectors.toUnmodifiableList());
                depositResponseBuilder.refundDepositManagementItems(getList(refundingItemsSorted, page, limit));
                depositResponseBuilder.depositsToProcess(depositsToProcess);
                depositResponseBuilder.refundDepositManagementItemsCount(refundDepositManagementItemsCount);
                break;
            default:
                throw new RuntimeException("Unknown fieldType:" + fieldName);
        }
    }

    private long countDepositsToProcess(Map<Tenancy.DepositStatus, List<Invoice.LineItem>> lineItemsByDepositStatus, Map<String, Tenancy> tenancyMap) {
        return lineItemsByDepositStatus.values().stream()
                .flatMap(List::stream)
                .filter(lineItem -> {
                    var relatedTenancy = tenancyMap.get(lineItem.getParentReference());
                    return !relatedTenancy.isDepositReturned() && relatedTenancy.isDepositTransferred() && relatedTenancy.isDepositRegistered() && checkContractEndDate(relatedTenancy.getEndDate());
                })
                .count();
    }

    private long countDepositsToRegister(List<DepositItemsResponse.DepositManagementBody> items, Map<String, Tenancy> tenancyMap) {
        return items.stream()
                .filter(lineItem -> {
                    var relatedTenancy = tenancyMap.get(lineItem.getTenancyReference());
                    return relatedTenancy.isDepositRegistered() == false;
                })
                .count();
    }

    private boolean checkContractEndDate(String endDate) {
        var currentInstant = Instant.now();
        var contractEndDate = Instant.parse(endDate);
        var fourteenDaysLater = currentInstant.plus(Duration.ofDays(14));

        return contractEndDate.isBefore(fourteenDaysLater);
    }

    private long countDepositsToTransfer(List<DepositItemsResponse.DepositManagementBody> items, Map<String, Tenancy> tenancyMap) {
        return items.stream()
                .filter(lineItem -> {
                    var relatedTenancy = tenancyMap.get(lineItem.getTenancyReference());
                    return relatedTenancy.isDepositTransferred() == false;
                })
                .count();
    }


    private List<DepositItemsResponse.DepositManagementBody> mapToDepositManagementBody(List<Invoice.LineItem> lineItems, Map<String, Property> propertyMap, Map<String, Tenancy> tenancyMap, Map<String, User> tenantIdMap) {
        //filter out line items that are not related to any property or are related to an archived property
        return lineItems.stream().filter(lineItem -> Objects.nonNull(propertyMap.get(tenancyMap.get(lineItem.getParentReference()).getProperty()))).map(lineItem -> {
            var relatedTenancy = tenancyMap.get(lineItem.getParentReference());
            var relatedProperty = propertyMap.get(relatedTenancy.getProperty());
            var relatedUser = tenantIdMap.get(relatedTenancy.getPrimaryTenant());
            var dueDate = Optional.of(lineItem).map(Invoice.LineItem::getParentDueDate).map(String::toString).orElse(null);

            return DepositItemsResponse.DepositManagementBody.builder()
                    .tenancyId(relatedTenancy.getId())
                    .propertyReference(relatedProperty.getReference())
                    .contractReference(relatedTenancy.getReference())
                    .tenantName(relatedTenancy.getPrimaryTenantName())
                    .tenantEmail(Optional.ofNullable(relatedUser.getCognitoEmail())
                            .orElse(relatedUser.getEmails().stream().findAny().map(User.Email::getEmail).orElse(null)))
                    .tenantId(relatedUser.getId())
                    .propertyId(relatedProperty.getId())
                    .lineItemId(lineItem.getId())
                    .inDispute(relatedTenancy.isDepositDisputed())
                    .depositValue(new BigDecimal(relatedTenancy.getDeposit()).movePointLeft(2).toString())
                    .transferred(relatedTenancy.isDepositTransferred())
                    .released(relatedTenancy.isDepositReleased())
                    .tenancyReference(relatedTenancy.getReference())
                    .dateRegistered(relatedTenancy.getDateDepositRegistered())
                    .registered(relatedTenancy.isDepositRegistered())
                    .dueDate(Utils.formatDate(dueDate))
                    .dueDateInstant(Utils.convertDateToInstant(dueDate))
                    .depositReturned(relatedTenancy.isDepositReturned())
                    .contractEndDate(Utils.formatDate(relatedTenancy.getEndDate()))
                    .contractEndDateInstant(Utils.convertDateToInstant(relatedTenancy.getEndDate()))
                    .build();
        }).collect(Collectors.toUnmodifiableList());
    }

    private Map<Tenancy.DepositStatus, List<Invoice.LineItem>> getDepositStatusToInvoiceLineItems(List<Tenancy> tenancies, List<Invoice.LineItem> invoiceLineItems) {
        return invoiceLineItems.stream()
                .filter(item -> Objects.nonNull(item.getParentReference()))
                .filter(item -> tenancies.stream()
                        .anyMatch(tenancy -> tenancy.getReference().equals(item.getParentReference()))
                )
                .flatMap(item -> {
                    var tenancyMatch = tenancies.stream()
                            .filter(tenancy -> tenancy.getReference().equals(item.getParentReference()))
                            .findFirst()
                            .orElseThrow();

                    return tenancyService.getDepositStatusForTenancy(tenancyMatch)
                            .stream()
                            .map(depositStatus -> Pair.of(depositStatus, item));
                }).collect(Collectors.groupingBy(Map.Entry::getKey,
                        Collectors.mapping(Map.Entry::getValue, Collectors.toList())));

    }

    private List<DepositItemsResponse.DepositManagementBody> getList(List<DepositItemsResponse.DepositManagementBody> depositList, int page, int limit) {
        var startIndex = Math.min(limit * page, depositList.size());
        var endIndex = Math.min(limit * (page + 1), depositList.size());
        return depositList.subList(startIndex, endIndex);
    }

}

