package com.rentancy.integrations.servicies;

import com.rentancy.integrations.pojos.Resource;

import java.io.InputStream;

class EmailDownloaderImpl implements EmailDownloader {

    public static final String DELIMITER = "/";

    private final S3Client s3Client;
    private final String emailBucket;
    private final String documentsBucket;

    EmailDownloaderImpl(S3Client s3Client,
                        String emailBucket,
                        String documentsBucket) {
        this.s3Client = s3Client;
        this.emailBucket = emailBucket;
        this.documentsBucket = documentsBucket;
    }

    @Override
    public Resource download(String key) {
        return new Resource(s3Client.download(emailBucket, key), getFileNameFromKey(key));
    }

    @Override
    public String uploadDocument(String key, InputStream content) {
        return s3Client.uploadPdf(documentsBucket, key, content, null);
    }

    private String getFileNameFromKey(String key) {
        return key.substring(key.lastIndexOf(DELIMITER) + 1);
    }
}
