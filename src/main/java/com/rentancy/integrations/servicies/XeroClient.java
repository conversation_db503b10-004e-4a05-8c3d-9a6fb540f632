package com.rentancy.integrations.servicies;

import com.rentancy.integrations.pojos.*;
import com.xero.models.accounting.Invoice;
import com.xero.models.accounting.Payment;
import com.xero.models.accounting.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.lang.Nullable;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.time.Instant;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.rentancy.integrations.util.JSONUtils.wrappedToJsonString;
import static com.rentancy.integrations.util.Utils.sleep;
import static java.util.stream.Collectors.toUnmodifiableList;

public class XeroClient {

    private static final Logger log = LogManager.getLogger(XeroClient.class);

    private static final int MAX_RETRY = 5;
    private static final int WAIT_TIME = 1013;

    public static final String DEFAULT_XERO_START_TIMESTAMP = "2000-01-01T00:00:00.000Z";
    private static final String XERO_DAILY_LIMIT_REMAINING_HEADER = "X-DayLimit-Remaining";

    private final RestTemplate restTemplate;
    private final BiFunction<String, String, String> updateRemainingCallsFunction;

    public XeroClient(RestTemplate restTemplate, BiFunction<String, String, String> updateRemainingCallsFunction) {
        this.restTemplate = restTemplate;
        this.updateRemainingCallsFunction = updateRemainingCallsFunction;
    }

    public List<XeroConnection> getConnections(String authEventId, String token) {
        var typeRef = new ParameterizedTypeReference<List<XeroConnection>>() {
        };
        return getForObject("/connections", typeRef, getAuthenticationHeader(token), Map.of("authEventId", authEventId), null);
    }

    public Contacts listContactsWithRetry(String tenantId, String token, @Nullable Instant lastUpdatedDate) {
        return this.makeRequestWithRetry(() -> listContacts(tenantId, token, lastUpdatedDate));
    }

    public Contacts listContactsNoPaginationWithRetry(String tenantId, String token, @Nullable Instant lastUpdatedDate) {
        return this.makeRequestWithRetry(() -> listContactsWithNoPagination(tenantId, token, lastUpdatedDate));
    }

    public XeroInvoices listInvoicesWithRetry(String tenantId, String token, @Nullable Instant lastUpdatedDate) {
        return this.makeRequestWithRetry(() -> listInvoices(tenantId, token, lastUpdatedDate));
    }

    public Optional<XeroInvoices.XeroInvoice> getInvoiceWithRetry(String tenantId, String token, String xeroInvoiceId) {
        return this.makeRequestWithRetry(() -> getInvoice(tenantId, token, xeroInvoiceId));
    }

    public XeroOverPayments listOverPaymentsWithRetry(String tenantId, String token, @Nullable Instant lastUpdatedDate) {
        return this.makeRequestWithRetry(() -> listOverPayments(tenantId, token, lastUpdatedDate));
    }

    public XeroPayments listPaymentsWithRetry(String tenantId, String token, @Nullable Instant lastUpdatedDate) {
        return this.makeRequestWithRetry(() -> listPayments(tenantId, token, lastUpdatedDate));
    }

    public XeroBankTransfers listBankTransfersWithRetry(String tenantId, String token, @Nullable Instant lastUpdatedDate) {
        return this.makeRequestWithRetry(() -> listBankTransfers(tenantId, token, lastUpdatedDate));
    }

    public XeroTransactions listTransactionsWithRetry(String tenantId, String token, @Nullable Instant lastUpdatedDate) {
        return this.makeRequestWithRetry(() -> listTransactions(tenantId, token, lastUpdatedDate));
    }

    public XeroAccounts listAccountsWithRetry(String tenantId, String token, @Nullable Instant lastUpdatedDate) {
        return this.makeRequestWithRetry(() -> listAccounts(tenantId, token, lastUpdatedDate));
    }

    public Journals listJournalsWithRetry(String tenantId, String token, @Nullable Instant lastUpdatedDate) {
        return this.makeRequestWithRetry(() -> listJournals(tenantId, token, lastUpdatedDate));
    }

    public Contact createContactWithRetry(Contact contact, String tenantId, String token) {
        return this.makeRequestWithRetry(() -> createContact(contact, tenantId, token));
    }

    public Contact updateContactWithRetry(String id, Contact contact, String tenantId, String token) {
        return this.makeRequestWithRetry(() -> updateContact(id, contact, tenantId, token));
    }

    public Invoice createInvoiceWithRetry(Invoice invoice, String tenantId, String token) {
        return this.makeRequestWithRetry(() -> createInvoice(invoice, tenantId, token));
    }

    public Payment createPaymentWithRetry(Payment payment, String tenantId, String token) {
        return this.makeRequestWithRetry(() -> createPayment(payment, tenantId, token));
    }

    public List<Invoice> createInvoicesWithRetry(List<Invoice> invoices, String tenantId, String token) {
        return this.makeRequestWithRetry(() -> createInvoices(invoices, tenantId, token));
    }

    public TrackingCategories getTrackingCategoriesWithRetry(String tenantId, String token) {
        return this.makeRequestWithRetry(() -> getTrackingCategories(tenantId, token));
    }

    public BankSummary getBankSummaryWithRetry(String tenantId, String token, Instant from, Instant to) {
        return this.makeRequestWithRetry(() -> getBankSummary(tenantId, token, from, to));
    }

    public byte[] getInvoicePdfWithRetry(String tenantId, String token, String invoiceId) {
        return this.makeRequestWithRetry(() -> getInvoicePdf(tenantId, token, invoiceId));
    }

    public List<TrackingOption> createBulkTrackingCategoryOptionsWithRetry(String tenantId, String token, UUID categoryId, List<String> names) {
        return this.makeRequestWithRetry(() -> createBulkTrackingCategories(tenantId, token, categoryId, names));
    }

    public UUID createTrackingCategoryOptionWithRetry(String tenantId, String token, String categoryId, String reference) {
        return this.makeRequestWithRetry(() -> createTrackingCategoryOption(tenantId, token, categoryId, reference));
    }

    public TrackingCategories createTrackingCategoryWithRetry(String tenantId, String token, String name) {
        return this.makeRequestWithRetry(() -> createTrackingCategory(tenantId, token, name));
    }

    public BrandingThemes getBrandingThemesWithRetry(String tenantId, String token) {
        return this.makeRequestWithRetry(() -> getBrandingThemes(tenantId, token));
    }

    public List<InvoiceAttachment> getInvoiceAttachmentsWithRetry(String invoiceId, String tenantId, String token) {
        return this.makeRequestWithRetry(() -> getInvoiceAttachments(invoiceId, tenantId, token));
    }

    public Contact getContactWithRetry(String id, String tenantId, String token) {
        return this.makeRequestWithRetry(() -> getContact(id, tenantId, token));
    }

    private Contacts listContacts(String tenantId, String token, @Nullable Instant lastUpdatedDate) {
        var typeRef = new ParameterizedTypeReference<Contacts>() {
        };
        var headers = getTenantHeaders(tenantId, token);
        var contacts = new ArrayList<Contact>();
        boolean finished;
        int page = 1;
        Optional.ofNullable(lastUpdatedDate).ifPresent(d -> addLastModifiedHeader(headers, lastUpdatedDate));
        do {
            var result = getForObject("/api.xro/2.0/Contacts?page=" + page, typeRef, headers, Map.of(), tenantId);
            finished = result.getContacts().isEmpty();
            contacts.addAll(result.getContacts());
            page++;
        } while (!finished);

        return new Contacts().contacts(contacts);
    }

    private Contacts listContactsWithNoPagination(String tenantId, String token, @Nullable Instant lastUpdatedDate) {
        var typeRef = new ParameterizedTypeReference<Contacts>() {
        };
        var headers = getTenantHeaders(tenantId, token);
        Optional.ofNullable(lastUpdatedDate).ifPresent(d -> addLastModifiedHeader(headers, lastUpdatedDate));
        var result = getForObject("/api.xro/2.0/Contacts", typeRef, headers, Map.of(), tenantId);
        var contacts = new ArrayList<Contact>(result.getContacts());

        return new Contacts().contacts(contacts);
    }

    private XeroInvoices listInvoices(String tenantId, String token, @Nullable Instant lastUpdatedDate) {
        var typeRef = new ParameterizedTypeReference<XeroInvoices>() {
        };
        var headers = getTenantHeaders(tenantId, token);
        var invoices = new ArrayList<XeroInvoices.XeroInvoice>();
        boolean finished;
        int page = 1;
        Optional.ofNullable(lastUpdatedDate).ifPresent(d -> addLastModifiedHeader(headers, lastUpdatedDate));
        do {
            var result = getForObject("/api.xro/2.0/Invoices?page=" + page, typeRef, headers, Map.of(), tenantId);
            finished = result.getInvoices().isEmpty();
            invoices.addAll(result.getInvoices());
            page++;
        } while (!finished);

        return new XeroInvoices(invoices);
    }

    private Optional<XeroInvoices.XeroInvoice> getInvoice(String tenantId, String token, String xeroInvoiceId) {
        var typeRef = new ParameterizedTypeReference<XeroInvoices>() {
        };
        var headers = getTenantHeaders(tenantId, token);

        return getForObject("/api.xro/2.0/Invoices/" + xeroInvoiceId, typeRef, headers, Map.of(), tenantId)
                .getInvoices()
                .stream()
                .findFirst();
    }

    private XeroOverPayments listOverPayments(String tenantId, String token, @Nullable Instant lastUpdatedDate) {
        var typeRef = new ParameterizedTypeReference<XeroOverPayments>() {
        };
        var headers = getTenantHeaders(tenantId, token);
        var overPayments = new ArrayList<XeroOverPayments.XeroOverPayment>();
        boolean finished;
        int page = 1;
        Optional.ofNullable(lastUpdatedDate).ifPresent(d -> addLastModifiedHeader(headers, lastUpdatedDate));
        do {
            var result = getForObject("/api.xro/2.0/Overpayments?page=" + page, typeRef, headers, Map.of(), tenantId);
            finished = result.getOverPayments().isEmpty();
            overPayments.addAll(result.getOverPayments());
            page++;
        } while (!finished);

        return new XeroOverPayments(overPayments);
    }

    private XeroPayments listPayments(String tenantId, String token, @Nullable Instant lastUpdatedDate) {
        var typeRef = new ParameterizedTypeReference<XeroPayments>() {
        };
        var headers = getTenantHeaders(tenantId, token);
        var payments = new ArrayList<XeroPayments.XeroPayment>();
        boolean finished;
        int page = 1;
        Optional.ofNullable(lastUpdatedDate).ifPresent(d -> addLastModifiedHeader(headers, lastUpdatedDate));
        do {
            var result = getForObject("/api.xro/2.0/Payments?page=" + page, typeRef, headers, Map.of(), tenantId);
            finished = result.getPayments().isEmpty();
            payments.addAll(result.getPayments());
            page++;
        } while (!finished);

        return new XeroPayments(payments);
    }

    private XeroBankTransfers listBankTransfers(String tenantId, String token, @Nullable Instant lastUpdatedDate) {
        var typeRef = new ParameterizedTypeReference<XeroBankTransfers>() {
        };
        var headers = getTenantHeaders(tenantId, token);

        Optional.ofNullable(lastUpdatedDate).ifPresent(d -> addLastModifiedHeader(headers, lastUpdatedDate));

        return getForObject("/api.xro/2.0/BankTransfers", typeRef, headers, Map.of(), tenantId);
    }

    private XeroTransactions listTransactions(String tenantId, String token, @Nullable Instant lastUpdatedDate) {
        var typeRef = new ParameterizedTypeReference<XeroTransactions>() {
        };
        var headers = getTenantHeaders(tenantId, token);
        var outputTransaction = new XeroTransactions();
        boolean finished;
        int page = 1;
        Optional.ofNullable(lastUpdatedDate).ifPresent(d -> addLastModifiedHeader(headers, lastUpdatedDate));
        do {
            var result = getForObject("/api.xro/2.0/BankTransactions?page=" + page, typeRef, headers, Map.of(), tenantId);
            finished = result.getTransactions().isEmpty();
            outputTransaction.append(result);
            page++;
        } while (!finished);

        return outputTransaction;
    }

    private Journals listJournals(String tenantId, String token, Instant lastUpdatedDate) {
        var typeRef = new ParameterizedTypeReference<Journals>() {
        };
        var headers = getTenantHeaders(tenantId, token);

        Optional.ofNullable(lastUpdatedDate).ifPresent(d -> addLastModifiedHeader(headers, lastUpdatedDate));

        return getForObject("/api.xro/2.0/Journals", typeRef, headers, Map.of(), tenantId);
    }

    private XeroAccounts listAccounts(String tenantId, String token, @Nullable Instant lastUpdatedDate) {
        var typeRef = new ParameterizedTypeReference<XeroAccounts>() {
        };
        var headers = getTenantHeaders(tenantId, token);
        Optional.ofNullable(lastUpdatedDate).ifPresent(d -> addLastModifiedHeader(headers, lastUpdatedDate));
        var result = getForObject("/api.xro/2.0/Accounts", typeRef, headers, Map.of(), tenantId);

        return new XeroAccounts(result.getAccounts());
    }

    private Contact createContact(Contact contact, String tenantId, String token) {
        var typeRef = new ParameterizedTypeReference<Contacts>() {
        };
        var response = postForObject("/api.xro/2.0/Contacts", typeRef, getTenantHeaders(tenantId, token), contact, Map.of(), tenantId);

        return findContact(response);
    }

    private Contact updateContact(String id, Contact contact, String tenantId, String token) {
        var typeRef = new ParameterizedTypeReference<Contacts>() {
        };

        var response = postForObject("/api.xro/2.0/Contacts/" + id, typeRef, getTenantHeaders(tenantId, token), contact, Map.of(), tenantId);

        return findContact(response);
    }

    private Invoice createInvoice(Invoice invoice, String tenantId, String token) {
        var typeRef = new ParameterizedTypeReference<Invoices>() {
        };

        var response = postForObject("/api.xro/2.0/Invoices", typeRef, getTenantHeaders(tenantId, token), invoice, Map.of(), tenantId);

        log.info("Create invoice response - {}", wrappedToJsonString(response));

        return response
                .getInvoices()
                .stream()
                .findAny()
                .orElseThrow();
    }

    private Payment createPayment(Payment payment, String tenantId, String token) {
        var typeRef = new ParameterizedTypeReference<Payments>() {
        };

        var response = postForObject("/api.xro/2.0/Payments", typeRef, getTenantHeaders(tenantId, token), payment, Map.of(), tenantId);

        log.info("Create payment response - {}", wrappedToJsonString(response));

        return response
                .getPayments()
                .stream()
                .findAny()
                .orElseThrow();
    }

    private List<Invoice> createInvoices(List<Invoice> invoices, String tenantId, String token) {
        var typeRef = new ParameterizedTypeReference<Invoices>() {
        };

        var response =
                postForObject("/api.xro/2.0/Invoices?summarizeErrors=" + false, typeRef, getTenantHeaders(tenantId, token), new Invoices().invoices(invoices), Map.of(), tenantId);

        return response.getInvoices();
    }

    private Contact getContact(String id, String tenantId, String token) {
        var typeRef = new ParameterizedTypeReference<Contacts>() {
        };
        var response = getForObject("/api.xro/2.0/Contacts/" + id, typeRef, getTenantHeaders(tenantId, token), Map.of(), tenantId);

        return findContact(response);
    }

    private TrackingCategories getTrackingCategories(String tenantId, String token) {
        var typeRef = new ParameterizedTypeReference<TrackingCategories>() {
        };

        return getForObject("/api.xro/2.0/TrackingCategories", typeRef, getTenantHeaders(tenantId, token), Map.of(), tenantId);
    }

    private BankSummary getBankSummary(String tenantId, String token, Instant from, Instant to) {
        var typeRef = new ParameterizedTypeReference<BankSummary>() {
        };
        var response = getForObject("/api.xro/2.0/Reports/BankSummary?fromDate=" + from.toString() + "&toDate=" + to.toString(), typeRef, getTenantHeaders(tenantId, token), Map.of(), tenantId);

        return response;
    }

    private byte[] getInvoicePdf(String tenantId, String token, String invoiceId) {
        var typeRef = new ParameterizedTypeReference<byte[]>() {
        };
        var headers = getTenantHeaders(tenantId, token);
        headers.add("Accept", "application/pdf");

        return getForObject("/api.xro/2.0/Invoices/" + invoiceId, typeRef, headers, Map.of(), tenantId);
    }

    public void deleteConnection(String connectionId, String token) {
        var typeRef = new ParameterizedTypeReference<Void>() {
        };
        var headers = getAuthenticationHeader(token);
        var result = restTemplate.exchange("/connections/" + connectionId, HttpMethod.DELETE, new HttpEntity<String>(headers), typeRef, Map.of());
        log.info("Delete connection status - {}", result.getStatusCodeValue());
    }

    private List<TrackingOption> createBulkTrackingCategories(String tenantId, String token, UUID categoryId, List<String> names) {
        var typeRef = new ParameterizedTypeReference<TrackingOptions>() {
        };
        List<TrackingOption> options = names.stream().map(
                name -> {
                    var trackingOption = new TrackingOption();
                    trackingOption.setName(name);
                    return trackingOption;
                }).collect(Collectors.toList());

        var body = wrappedToJsonString(Map.of("Options", options));
        log.info("Request - {}", body);
        var response = putForObject("/api.xro/2.0/TrackingCategories/" + categoryId + "/Options", typeRef, getTenantHeaders(tenantId, token), body, Map.of(), tenantId);

        log.info("Bulk Create category options response - {}", wrappedToJsonString(response));
        return options;
    }

    private UUID createTrackingCategoryOption(String tenantId, String token, String categoryId, String reference) {
        var typeRef = new ParameterizedTypeReference<TrackingCategories>() {
        };

        var optionId = UUID.randomUUID();
        var trackingOption = new TrackingOption();
        trackingOption.setTrackingOptionID(optionId);
        trackingOption.setName(reference);
        trackingOption.setStatus(TrackingOption.StatusEnum.ACTIVE);
        log.info("Request - {}", trackingOption);

        var response = postForObject("/api.xro/2.0/TrackingCategories/" + categoryId + "/Options", typeRef, getTenantHeaders(tenantId, token), trackingOption, Map.of(), tenantId);

        log.info("Create category option response - {}", response);

        return optionId;
    }

    private TrackingCategories createTrackingCategory(String tenantId, String token, String name) {
        var typeRef = new ParameterizedTypeReference<TrackingCategories>() {
        };

        var trackingCategory = new TrackingCategory();
        trackingCategory.setName(name);
        trackingCategory.setStatus(TrackingCategory.StatusEnum.ACTIVE);
        log.info("Request - {}", trackingCategory);

        var response = postForObject("/api.xro/2.0/TrackingCategories", typeRef, getTenantHeaders(tenantId, token), trackingCategory, Map.of(), tenantId);

        log.info("Create category response - {}", response);

        return response;
    }

    private BrandingThemes getBrandingThemes(String tenantId, String token) {
        var typeRef = new ParameterizedTypeReference<BrandingThemes>() {
        };

        return getForObject("/api.xro/2.0/BrandingThemes", typeRef, getTenantHeaders(tenantId, token), Map.of(), tenantId);
    }

    private List<InvoiceAttachment> getInvoiceAttachments(String invoiceId, String tenantId, String token) {
        var typeRef = new ParameterizedTypeReference<Attachments>() {
        };
        var headers = getTenantHeaders(tenantId, token);

        return getForObject("/api.xro/2.0/Invoices/" + invoiceId + "/Attachments", typeRef, headers, Map.of(), tenantId)
                .getAttachments()
                .stream()
                .map(t -> new InvoiceAttachment(t.getAttachmentID().toString(), t.getFileName(), t.getUrl()))
                .collect(toUnmodifiableList());
    }

    private Contact findContact(Contacts xeroContacts) {
        return xeroContacts
                .getContacts()
                .stream()
                .findAny()
                .orElse(null);
    }

    private <T> T getForObject(String path, ParameterizedTypeReference<T> responseType, HttpHeaders headers, Map<String, String> variables, @Nullable String tenantId) {
        var response = restTemplate.exchange(path, HttpMethod.GET, new HttpEntity<String>(headers), responseType, variables);

        Optional.ofNullable(tenantId)
                .flatMap(tenant ->
                        response.getHeaders().get(XERO_DAILY_LIMIT_REMAINING_HEADER)
                                .stream()
                                .findFirst()
                )
                .ifPresent(remainingCalls -> updateRemainingCallsFunction.apply(tenantId, remainingCalls));


        T responseBody = Optional.ofNullable(response.getBody())
                .orElseThrow(() -> new IllegalStateException("Failed to get - " + path));

        logWarningsIfAny(responseBody, path);

        return responseBody;
    }

    private <T, E> T postForObject(String path, ParameterizedTypeReference<T> responseType, HttpHeaders headers, E body, Map<String, String> variables, @Nullable String tenantId) {
        var response = restTemplate.exchange(path, HttpMethod.POST, new HttpEntity<E>(body, headers), responseType, variables);

        Optional.ofNullable(tenantId)
                .flatMap(tenant ->
                        response.getHeaders().get(XERO_DAILY_LIMIT_REMAINING_HEADER)
                                .stream()
                                .findFirst()
                )
                .ifPresent(remainingCalls -> updateRemainingCallsFunction.apply(tenantId, remainingCalls));

        T responseBody = Optional.ofNullable(response.getBody())
                .orElseThrow(() -> new IllegalStateException("Failed to post - " + path));

        logWarningsIfAny(responseBody, path);

        return response.getBody();
    }

    private <T, E> T putForObject(String path, ParameterizedTypeReference<T> responseType, HttpHeaders headers, E body, Map<String, String> variables, @Nullable String tenantId) {
        var response = restTemplate.exchange(path, HttpMethod.PUT, new HttpEntity<E>(body, headers), responseType, variables);

        Optional.ofNullable(tenantId)
                .flatMap(tenant ->
                        response.getHeaders().get(XERO_DAILY_LIMIT_REMAINING_HEADER)
                                .stream()
                                .findFirst()
                )
                .ifPresent(remainingCalls -> updateRemainingCallsFunction.apply(tenantId, remainingCalls));

        T responseBody = Optional.ofNullable(response.getBody())
                .orElseThrow(() -> new IllegalStateException("Failed to post - " + path));

        logWarningsIfAny(responseBody, path);

        return response.getBody();
    }

    private void addLastModifiedHeader(HttpHeaders headers, Instant date) {
        headers.add("If-Modified-Since", date.toString());
    }

    private HttpHeaders getTenantHeaders(String tenantId, String token) {
        var headers = getAuthenticationHeader(token);
        headers.set("xero-tenant-id", tenantId);
        return headers;
    }

    private HttpHeaders getAuthenticationHeader(String token) {
        var headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + token);
        return headers;
    }

    protected <T> T makeRequestWithRetry(Supplier<T> request) {
        return Stream.iterate(0, tryCount -> ++tryCount)
                .limit(MAX_RETRY)
                .map(tryCount -> {
                    try {
                        int waitTime = tryCount * WAIT_TIME * 5;
                        log.info("Waiting for {} ms and retrying", waitTime);
                        sleep(waitTime);

                        return request.get();
                    } catch (HttpClientErrorException ex) {
                        log.error("(1/2) Http exception while making request to Xero api, we will retry - " + tryCount + " " + ex.getStatusCode(), ex);
                        log.error("(2/2) Response body: " + ex.getResponseBodyAsString().replaceAll("[\\t\\n\\r]+", ""));
                        if (ex.getStatusCode().value() != 429) {
                            throw new IllegalStateException("Failed to make request to Xero api - " + request.get());
                        }
                    } catch (Exception ex) {
                        log.error("Unexpected error while making request to Xero api,  we will try - " + tryCount, ex);
                    }

                    return null;
                }).filter(Objects::nonNull)
                .findFirst()
                .orElseThrow(() -> new IllegalStateException("Failed to make request to Xero api - " + request.get()));
    }

    private <T> void logWarningsIfAny(T responseBody, String path) {
        if (responseBody instanceof XeroBaseResponse) {
            XeroBaseResponse res = (XeroBaseResponse) responseBody;
            if (res.getWarnings() != null) {
                for (XeroBaseResponse.Warning warning : res.getWarnings()) {
                    log.error("XERO DEPRECATION WARNING: Path: {}, Warning: {}", path, warning.getMessage());
                }
            }
        }
    }

}
