package com.rentancy.integrations.servicies.persistence;

import com.rentancy.integrations.pojos.Organisation;

import java.time.temporal.ChronoUnit;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.rentancy.integrations.pojos.OrganisationStripeCharge;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.Instant;
import java.util.List;

import static java.time.temporal.ChronoUnit.DAYS;
import static java.time.temporal.ChronoUnit.SECONDS;
import static java.time.temporal.ChronoUnit.WEEKS;

@RequiredArgsConstructor
public class OrganisationServiceImpl implements OrganisationService {

    private static final Logger log = LogManager.getLogger(OrganisationServiceImpl.class);

    private final OrganisationRepository organisationRepository;

    @Override
    public Organisation getOrganisation(String id) {
        return organisationRepository.getOrganisation(id);
    }

    @Override
    public List<Organisation> findAllOrganisations() {
        return organisationRepository.findAllOrganisations();
    }

    @Override
    public List<Organisation> findAllUnexpiredOrganisations() {
        return organisationRepository.findAllUnexpiredOrganisations();
    }

    @Override
    public List<OrganisationStripeCharge> findOrganisationStripeCharges(String fromData, String toDate) {
        return organisationRepository.findOrganisationStripeCharges(fromData, toDate);
    }

    @Override
    public List<Organisation> findAllJournalOrganisations() {
        return organisationRepository.findByJournalingOrganisations();
    }

    @Override
    public void numberStripeChargeItems() {
        var now = Instant.now();
        var from = now.minus(1, DAYS).truncatedTo(DAYS);
        var to = now.truncatedTo(DAYS).minus(1, SECONDS);
        var period = 0;

        while (period < 105) {
            log.info("From - {}, To - {}", from, to);
            var items = this.findOrganisationStripeCharges(from.toString(), to.toString());
            var itemsWithNumber = items
                    .stream()
                    .filter(item -> Objects.nonNull(item.getNumber()))
                    .sorted((o1, o2) -> {
                        var o1Number = Integer.parseInt(o1.getNumber().split("-")[2]);
                        var o2Number = Integer.parseInt(o2.getNumber().split("-")[2]);

                        return o2Number - o1Number;
                    }).map(OrganisationStripeCharge::getNumber)
                    .collect(Collectors.toUnmodifiableList());
            var itemsWithoutNumber = items.stream().filter(item -> Objects.isNull(item.getNumber())).collect(Collectors.toUnmodifiableList());
            log.info("Items with number - {}", itemsWithNumber.size());
            log.info("Items without number - {}", itemsWithoutNumber.size());
            if (itemsWithoutNumber.isEmpty()) {
                break;
            }
            if (itemsWithNumber.isEmpty()) {
                period += 7;
                from = now.minus(period, DAYS).truncatedTo(DAYS);
                continue;
            }
            var lastNumber = new AtomicInteger(Integer.parseInt(itemsWithNumber.stream().findFirst().orElseThrow().split("-")[2]));
            itemsWithoutNumber.forEach(item -> {
                var number = String.join("-", "INV", "SUB", String.valueOf(lastNumber.incrementAndGet()));
                log.info("Generated number - {}", number);
                organisationRepository.updateOrganisationChargeItemNumber(item.getId(), number);
            });
            break;
        }
    }

    @Override
    public Optional<OrganisationStripeCharge> getOrganisationStripeCharge(String chargeId) {
        return organisationRepository.findOrganisationStripeCharge(chargeId);
    }

    @Override
    public List<Organisation> findOrganisationsCreatedBetween(String from, String to) {
        return organisationRepository.findOrganisationsCreatedBetween(from, to);
    }

    @Override
    public List<Organisation> findOrganisationsWithWhatsAppAddress(String whatsAppAddress) {
        return organisationRepository.findByWhatsAppAddress(whatsAppAddress);
    }

    @Override
    public void updateXeroDetails(String organisationId, String xeroId) {
        organisationRepository.updateXeroDetails(organisationId, xeroId);
    }

    @Override
    public Stream<Organisation> getOrganisations(Set<String> organisationIds) {
        return organisationRepository.getOrganisations(organisationIds);
    }

    @Override
    public void updateFinanceIntegrationType(String organisationId, String value) {
        organisationRepository.updateFinanceIntegrationType(organisationId, value);
    }
}
