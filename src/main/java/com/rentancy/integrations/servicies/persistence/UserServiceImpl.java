package com.rentancy.integrations.servicies.persistence;

import com.rentancy.integrations.pojos.Invoice;
import com.rentancy.integrations.pojos.User;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private static final Logger LOG = LogManager.getLogger(UserServiceImpl.class);

    private final UserRepository userRepository;

    @Override
    public User findUser(String id, boolean includeAddress) {
        return userRepository.get(id, includeAddress);
    }

    @Override
    public User findUserWithEmail(String cognitoEmail) {
        return userRepository.findByCognitoEmail(cognitoEmail);
    }

    @Override
    public List<User> findOrganisationUsersWithAddresses(String organisationId) {
        return userRepository.findOrganisationUsers(organisationId, true);
    }

    @Override
    public List<User> findOrganisationUsers(String organisationId) {
        return userRepository.findOrganisationUsers(organisationId, false);
    }

    @Override
    public List<User> findUsers(Collection<String> ids) {
        var validIds = ids
                .stream()
                .filter(StringUtils::hasLength)
                .collect(Collectors.toUnmodifiableSet());
        return userRepository.getUsers(validIds);
    }

    @Override
    public Set<String> findOrganisationUserIdsWithRole(String organisationId, String role) {
        var users = userRepository.findOrganisationUsers(organisationId);

        return users
                .stream()
                .filter(user -> Objects.nonNull(user.getRoles()))
                .filter(user -> user.getRoles().stream().anyMatch(r -> r.equals(role)))
                .map(User::getId)
                .collect(Collectors.toUnmodifiableSet());
    }

    @Override
    public List<User> findOrganisationUsersWithRole(String organisationId, String role) {
        var users = userRepository.findOrganisationUsers(organisationId);

        return users
                .stream()
                .filter(user -> Objects.nonNull(user.getRoles()))
                .filter(user -> user.getRoles().stream().anyMatch(r -> r.equals(role)))
                .collect(Collectors.toUnmodifiableList());
    }

    @Override
    public User findUserWithCognitoId(String cognitoId) {
        return userRepository.findByCognitoId(cognitoId);
    }

    @Override
    public User findUserWithXeroId(String xeroId) {
        return userRepository.findByXeroId(xeroId);
    }

    @Override
    public User.UserAddress findUserPostalAddress(String userid) {
        return userRepository.getUserPostalAddress(userid);
    }

    @Override
    public User findUserWithOptionalEmail(String email) {
        return userRepository.findByEmail(email);
    }

    @Override
    public String createUser(User user) {
        return userRepository.createUser(user);
    }

    @Override
    public void updateXeroDetails(String userId, String xeroId) {
        userRepository.updateXeroDetails(userId, xeroId);
    }

    public void refreshUser(String userId) {
        userRepository.refreshUser(userId);
    }

    @Override
    public User findUserWithCompanyName(String organisation, String name) {
        return userRepository.findByCompanyName(organisation, name);
    }


    public Map<String, User> findContactsByInvoices(List<Invoice> invoices) {
        Map<String, User> result = new HashMap<>();
        for (Invoice invoice : invoices) {
            result.put(invoice.getId(), userRepository.findByXeroId(invoice.getContactId()));
        }
        return result;

    }

    @Override
    public List<User> findUsersWithAddressesByOrganisation(String organisationId) {
        return userRepository.findUsersWithAddressesByOrganisation(organisationId);
    }

    @Override
    public List<User> findUsersWithoutAddressesByOrganisation(String organisationId) {
        return userRepository.findUsersWithoutAddressesByOrganisation(organisationId);
    }
}
