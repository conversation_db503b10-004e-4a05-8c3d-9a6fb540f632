package com.rentancy.integrations.servicies.persistence;

import static com.rentancy.integrations.servicies.persistence.DDBClient.EQ;
import static java.util.stream.Collectors.toUnmodifiableList;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.OverPayment;
import com.rentancy.integrations.pojos.OverPayment.OverPaymentStatuses;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class OverPaymentRepository extends Repository {
    private static final Logger log = LogManager.getLogger(JournalRepository.class);
    private static final String OVER_PAYMENT_TABLE = "OVER_PAYMENT_TABLE";
    private static final String OVER_PAYMENT_INDEX = "OVER_PAYMENT_INDEX";
    private static final String OVER_PAYMENT_ORGANISATION_INDEX = "OVER_PAYMENT_ORGANISATION_INDEX";

    private final Config config;
    private final DDBClient ddbClient;

    public OverPaymentRepository(Config config, DDBClient ddbClient) {
        this.config = config;
        this.ddbClient = ddbClient;
    }

    public Optional<OverPayment> findOverPayment(String organisationId, String overPaymentId) {
        var tableName = config.getVariable(OVER_PAYMENT_TABLE);
        var indexName = config.getVariable(OVER_PAYMENT_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        var filterFieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("overPaymentId", new AttributeValue().withS(overPaymentId));
        filterFieldsValues.put("overPaymentOrganisationId", new AttributeValue().withS(organisationId));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());
        var filterExpression = getFilterExpression(filterFieldsValues.keySet());

        log.info("Key condition expression - {}", keyConditionExpression);
        log.info("Filter expression - {}", filterExpression);

        fieldsValues.putAll(filterFieldsValues);

        var result =
            this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), filterExpression);

        return result
            .stream()
            .map(this::mapToOverPayment)
            .findAny();
    }

    public Stream<OverPayment> findOrganisationUnallocatedOverPayments(String organisationId) {
        var tableName = config.getVariable(OVER_PAYMENT_TABLE);
        var indexName = config.getVariable(OVER_PAYMENT_ORGANISATION_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("overPaymentOrganisationId", new AttributeValue().withS(organisationId));
        fieldsValues.put("status", new AttributeValue().withS(OverPaymentStatuses.AUTHORISED.name()));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());

        log.info("Key condition expression - {}", keyConditionExpression);

        var result = this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), null);

        return result
            .stream()
            .map(this::mapToOverPayment);
    }

    private OverPayment mapToOverPayment(Map<String, AttributeValue> item) {
        return OverPayment.builder()
            .id(getIfPresent(item, "id"))
            .overPaymentId(getIfPresent(item, "overPaymentId"))
            .type(getIfPresent(item, "type"))
            .reference(getIfPresent(item, "reference"))
            .remainingCredit(getIfPresent(item, "remainingCredit"))
            .contactId(getIfPresent(item, "contactId"))
            .status(OverPaymentStatuses.valueOf(getIfPresent(item, "status")))
            .lineAmountTypes(getIfPresent(item, "lineAmountTypes"))
            .subTotal(getIfPresent(item, "subTotal"))
            .totalTax(getIfPresent(item, "totalTax"))
            .total(getIfPresent(item, "total"))
            .currency(getIfPresent(item, "currency"))
            .organisationId(getIfPresent(item, "overPaymentOrganisationId"))
            .date(getInstantIfPresent(item, "date"))
            .build();
    }

    public void deleteOverPaymentById(String id) {
        var tableName = config.getVariable(OVER_PAYMENT_TABLE);

        this.ddbClient.deleteItem(tableName, id);
    }

    private Map<String, AttributeValue> getAttributes(OverPayment overPayment) {
        var item = new HashMap<String, AttributeValue>();

        item.put("id", new AttributeValue().withS(overPayment.getId()));
        item.put("overPaymentId", new AttributeValue().withS(overPayment.getOverPaymentId()));

        addValueIfPresent("type", overPayment.getType(), item);
        addValueIfPresent("reference", overPayment.getReference(), item);
        addValueIfPresent("remainingCredit", overPayment.getRemainingCredit(), item);
        addValueIfPresent("contactId", overPayment.getContactId(), item);
        addValueIfPresent("status", overPayment.getStatus().name(), item);
        addValueIfPresent("lineAmountTypes", overPayment.getLineAmountTypes(), item);
        addValueIfPresent("subTotal", overPayment.getSubTotal(), item);
        addValueIfPresent("totalTax", overPayment.getTotalTax(), item);
        addValueIfPresent("total", overPayment.getTotal(), item);
        addValueIfPresent("currency", overPayment.getCurrency(), item);
        addValueIfPresent("overPaymentOrganisationId", overPayment.getOrganisationId(), item);

        addValueIfPresentInstant("date", overPayment.getDate(), item);

        return item;
    }

    public void createOverPayment(OverPayment overPayment) {
        var tableName = config.getVariable(OVER_PAYMENT_TABLE);

        this.ddbClient.put(tableName, getAttributes(overPayment));
    }

    public void updateOverPayment(OverPayment overPayment) {
        var tableName = config.getVariable(OVER_PAYMENT_TABLE);

        var items = getAttributes(overPayment);
        items.remove("id");

        this.ddbClient.update(tableName, overPayment.getId(), getUpdateExpression(items.keySet(), EQ), getAttributeNames(items), getAttributeValues(items));
    }

    public Stream<OverPayment> findByOrganisationAndOverPaymentIds(String organisationId, Set<String> overPaymentIds) {
        var tableName = config.getVariable(OVER_PAYMENT_TABLE);
        var indexName = config.getVariable(OVER_PAYMENT_ORGANISATION_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        var filterValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("overPaymentOrganisationId", new AttributeValue().withS(organisationId));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());

        var overPaymentIdList = overPaymentIds.stream().collect(toUnmodifiableList());
        for (var i = 0; i < overPaymentIdList.size(); i++) {
            filterValues.put("overPaymentId" + i, new AttributeValue().withS(overPaymentIdList.get(i)));
        }

        var attributeNames = getAttributeNames(Set.of("overPaymentOrganisationId", "overPaymentId"));
        var filterExpression = getFilterOrExpression("overPaymentId", filterValues.keySet());

        fieldsValues.putAll(filterValues);


        var result =
            this.ddbClient.query(tableName, indexName, keyConditionExpression, attributeNames, getAttributeValues(fieldsValues), filterExpression);

        return result
            .stream()
            .map(this::mapToOverPayment);
    }
}
