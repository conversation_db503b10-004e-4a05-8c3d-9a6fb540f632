package com.rentancy.integrations.servicies.persistence;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.Journal;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.rentancy.integrations.servicies.persistence.DDBClient.EQ;
import static java.util.stream.Collectors.toUnmodifiableList;

public class JournalRepository extends Repository {

    private static final Logger log = LogManager.getLogger(JournalRepository.class);

    private static final String JOURNAL_TABLE = "JOURNAL_TABLE";
    private static final String JOURNAL_INDEX = "JOURNAL_INDEX";

    private final Config config;
    private final DDBClient ddbClient;

    public JournalRepository(Config config, DDBClient ddbClient) {
        this.config = config;
        this.ddbClient = ddbClient;
    }

    public String create(Journal journal) {
        var tableName = config.getVariable(JOURNAL_TABLE);
        var item = new HashMap<String, AttributeValue>();
        var id = UUID.randomUUID().toString();

        item.put("id", new AttributeValue().withS(id));
        item.put("journalId", new AttributeValue().withS(journal.getJournalId()));
        item.put("createdAt", new AttributeValue().withS(Instant.now().toString()));

        addValueIfPresentN("number", journal.getNumber(), item);
        addValueIfPresent("sourceId", journal.getSourceId(), item);
        addValueIfPresent("sourceType", journal.getSourceType(), item);
        addValueIfPresent("reference", journal.getReference(), item);
        addValueIfPresent("tenantId", journal.getTenant(), item);
        addValueIfPresent("xeroJournalOrganisationId", journal.getOrganisation(), item);
        addValueIfPresent("date", journal.getDate().toString(), item);
        addValueIfPresent("createdDate", journal.getCreatedDate().toString(), item);
        addValueIfPresentL("lines", journal.getJournalLines().stream().map(journalLine -> {
            var line = new HashMap<String, AttributeValue>();
            addValueIfPresent("lineId", journalLine.getLineId(), line);
            addValueIfPresent("accountId", journalLine.getAccountId(), line);
            addValueIfPresent("accountCode", journalLine.getAccountCode(), line);
            addValueIfPresent("accountType", journalLine.getAccountType(), line);
            addValueIfPresent("accountName", journalLine.getAccountName(), line);
            addValueIfPresent("description", journalLine.getDescription(), line);
            addValueIfPresent("netAmount", journalLine.getNetAmount(), line);
            addValueIfPresent("grossAmount", journalLine.getGrossAmount(), line);
            addValueIfPresent("taxAmount", journalLine.getTaxAmount(), line);
            addValueIfPresent("taxType", journalLine.getTaxType(), line);
            addValueIfPresent("taxName", journalLine.getTaxName(), line);
            addValueIfPresentL("trackingCategories", journalLine.getTrackingCategories().stream().map(trackingCategory -> {
                var tracking = new HashMap<String, AttributeValue>();
                addValueIfPresent("categoryId", trackingCategory.getCategoryId(), tracking);
                addValueIfPresent("optionId", trackingCategory.getOptionId(), tracking);
                addValueIfPresent("name", trackingCategory.getName(), tracking);

                return new AttributeValue().withM(tracking);
            }).collect(toUnmodifiableList()), line);

            return new AttributeValue().withM(line);
        }).collect(toUnmodifiableList()), item);

        this.ddbClient.put(tableName, item);

        return id;
    }

    public void update(Journal journal) {
        var tableName = config.getVariable(JOURNAL_TABLE);
        var fieldsValues = new HashMap<String, AttributeValue>();

        fieldsValues.put("updatedAt", new AttributeValue().withS(Instant.now().toString()));
        addValueIfPresent("tenantId", journal.getTenant(), fieldsValues);

        this.ddbClient.update(tableName, journal.getId(), getUpdateExpression(fieldsValues.keySet(), EQ), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues));
    }

    public Journal findByOrganisationAndJournalId(String organisation, UUID journalId) {
        var tableName = config.getVariable(JOURNAL_TABLE);
        var indexName = config.getVariable(JOURNAL_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        var filterFieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("journalId", new AttributeValue().withS(journalId.toString()));
        filterFieldsValues.put("xeroJournalOrganisationId", new AttributeValue().withS(organisation));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());
        var filterExpression = getFilterExpression(filterFieldsValues.keySet());

        log.info("Key condition expression - {}", keyConditionExpression);
        log.info("Filter expression - {}", filterExpression);

        fieldsValues.putAll(filterFieldsValues);

        var result =
                this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), filterExpression);

        return result
                .stream()
                .map(this::mapToJournal)
                .findAny()
                .orElse(null);
    }

    private Journal mapToJournal(Map<String, AttributeValue> item) {
        return Journal
                .builder()
                .id(getIfPresent(item, "id"))
                .journalId(getIfPresent(item, "journalId"))
                .organisation(getIfPresent(item, "xeroJournalOrganisationId"))
                .tenant(getIfPresent(item, "tenantId"))
                .reference(getIfPresent(item, "reference"))
                .sourceId(getIfPresent(item, "sourceId"))
                .sourceType(getIfPresent(item, "sourceType"))
                .number(getIfPresentN(item, "number"))
                .date(getInstantIfPresent(item, "date"))
                .createdAt(getInstantIfPresent(item, "createdAt"))
                .updatedAt(getInstantIfPresent(item, "updatedAt"))
                .createdDate(getInstantIfPresent(item, "createdDate"))
                .journalLines(mapToJournalLine(getIfPresentL(item, "lines")))
                .build();
    }

    private List<Journal.JournalLine> mapToJournalLine(List<AttributeValue> lines) {
        return lines.stream().map(line -> {
            var item = line.getM();
            return Journal.JournalLine
                    .builder()
                    .lineId(getIfPresent(item, "lineId"))
                    .accountId(getIfPresent(item, "accountId"))
                    .accountType(getIfPresent(item, "accountType"))
                    .accountName(getIfPresent(item, "accountName"))
                    .accountCode(getIfPresent(item, "accountCode"))
                    .description(getIfPresent(item, "description"))
                    .netAmount(getIfPresent(item, "netAmount"))
                    .grossAmount(getIfPresent(item, "grossAmount"))
                    .taxType(getIfPresent(item, "taxType"))
                    .taxName(getIfPresent(item, "taxName"))
                    .taxAmount(getIfPresent(item, "taxAmount"))
                    .trackingCategories(mapToTrackingCategories(getIfPresentL(item, "trackingCategories")))
                    .build();
        }).collect(toUnmodifiableList());
    }

    private List<Journal.TrackingCategory> mapToTrackingCategories(List<AttributeValue> trackingCategories) {
        return trackingCategories.stream().map(trackingCategory -> {
            var item = trackingCategory.getM();

            return Journal.TrackingCategory
                    .builder()
                    .categoryId(getIfPresent(item, "categoryId"))
                    .optionId(getIfPresent(item, "optionId"))
                    .name(getIfPresent(item, "name"))
                    .build();
        }).collect(toUnmodifiableList());
    }
}
