package com.rentancy.integrations.servicies.persistence.config;

import com.rentancy.integrations.config.CheckEnvVars;
import com.rentancy.integrations.config.GetConfigVariable;

import java.util.List;

public class TenancyRepositoryConfig implements GetConfigVariable, CheckEnvVars {
    String TENANCY_TABLE = "TENANCY_TABLE";
    String TENANCY_SETTINGS_TABLE = "TENANCY_SETTINGS_TABLE";
    String TENANCY_INVOICE_TABLE = "TENANCY_INVOICE_TABLE";

    String TENANCY_INVOICE_INVOICE_INDEX = "TENANCY_INVOICE_INVOICE_INDEX";
    String TENANCY_REFERENCE_INDEX = "TENANCY_REFERENCE_INDEX";
    String TENANCY_ORGANISATION_INDEX = "TENANCY_ORGANISATION_INDEX";
    String TENANCY_PROPERTY_INDEX = "TENANCY_PROPERTY_INDEX";
    String TENANCY_INVOICE_INDEX = "TENANCY_INVOICE_INDEX";
    String TENANCY_SETTINGS_INDEX = "TENANCY_SETTINGS_INDEX";
    String INVOICE_TENANCY_INDEX = "INVOICE_TENANCY_INDEX";

    public TenancyRepositoryConfig() {
        var vars = List.of(TENANCY_TABLE, TENANCY_SETTINGS_TABLE, TENANCY_INVOICE_TABLE,
                TENANCY_INVOICE_INVOICE_INDEX, TENANCY_REFERENCE_INDEX, TENANCY_ORGANISATION_INDEX, TENANCY_PROPERTY_INDEX,
                TENANCY_INVOICE_INDEX, TENANCY_SETTINGS_INDEX, INVOICE_TENANCY_INDEX);
//        checkVars(vars);
    }

    public String getTenancyTableName() {
        return getVariable(TENANCY_TABLE);
    }

    public String getTenancySettingsTableName() {
        return getVariable(TENANCY_SETTINGS_TABLE);
    }

    public String getTenancyInvoiceTableName() {
        return getVariable(TENANCY_INVOICE_TABLE);
    }

    public String getTenancyInvoiceInvoiceIndex() {
        return getVariable(TENANCY_INVOICE_INVOICE_INDEX);
    }

    public String getTenancyReferenceIndex() {
        return getVariable(TENANCY_REFERENCE_INDEX);
    }

    public String getTenancyOrganizationIndex() {
        return getVariable(TENANCY_ORGANISATION_INDEX);
    }

    public String getTenancyPropertyIndex() {
        return getVariable(TENANCY_PROPERTY_INDEX);
    }

    public String getTenancyInvoiceIndex() {
        return getVariable(TENANCY_INVOICE_INDEX);
    }

    public String getTenancySettingsIndex() {
        return getVariable(TENANCY_SETTINGS_INDEX);
    }

    public String getInvoiceTenancyIndex() {
        return getVariable(INVOICE_TENANCY_INDEX);
    }

    @Override
    public List<String> checkVars(List<String> varNames) {
        return this.getEmptyVars(varNames);
    }
}
