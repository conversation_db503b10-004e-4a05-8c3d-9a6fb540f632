package com.rentancy.integrations.servicies.persistence;

import com.amazonaws.services.eventbridge.model.PutEventsRequest;
import com.amazonaws.services.eventbridge.model.PutEventsRequestEntry;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.*;
import com.rentancy.integrations.pojos.Invoice.LineItem;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Stream;

import com.rentancy.integrations.servicies.EventBridgeClient;
import lombok.RequiredArgsConstructor;

import java.time.ZonedDateTime;
import java.util.stream.Collectors;

@RequiredArgsConstructor
public class InvoiceServiceImpl implements InvoiceService {

    private final InvoiceRepository invoiceRepository;
    private final AccountRepository accountRepository;
    private final LandlordBillRepository landlordBillRepository;
    private final PaymentRepository paymentRepository;
    private final TransactionRepository transactionRepository;
    private final TransferRepository transferRepository;
    private final JournalRepository journalRepository;
    private final InvoiceWebhookEventsRepository webhookEventsRepository;
    private final PropertyRepository propertyRepository;
    private final OverPaymentRepository overPaymentRepository;
    private final TenancyRepository tenancyRepository;
    private final EventBridgeClient eventBridgeClient;
    private final Config config;
    private static final String EVENT_SOURCE = "EVENT_SOURCE";
    private static final String EVENT_BUS_NAME = "EVENT_BUS_NAME";

    @Override
    public boolean autoInvoiceExists(String tenancyId, ZonedDateTime period, RentInvoiceHistory.RentHistoryType type) {
        var historyItems = invoiceRepository.findHistoryItems(tenancyId, period, type);

        return !historyItems.isEmpty() && historyItems.stream().noneMatch(RentInvoiceHistory::isManual);
    }

    @Override
    public void saveInvoiceHistory(List<RentInvoiceHistory> history) {
        invoiceRepository.saveInvoiceHistory(history);
    }

    @Override
    public void saveLandlordBill(LandlordBill bill) {
        landlordBillRepository.save(bill);
    }

    @Override
    public List<LandlordBill> findOrganisationLandlordBills(String organisationId) {
        return landlordBillRepository.findByOrganisationId(organisationId);
    }

    @Override
    public long findOrganisationLandlordBillsCount(String organisationId) {
        return landlordBillRepository.findCountOfLandlordBillsByOrganisationId(organisationId);
    }

    @Override
    public LandlordBill getLandlordBill(String id) {
        return landlordBillRepository.get(id);
    }

    @Override
    public List<LandlordBill> findLandlordBills(Set<String> ids) {
        return landlordBillRepository.getLandlordBills(ids);
    }

    @Override
    public void updateLandlordBill(LandlordBill landlordBill) {
        landlordBillRepository.updatedBill(landlordBill);
    }

    @Override
    public Invoice getInvoice(String id) {
        return invoiceRepository.getInvoice(id);
    }

    @Override
    public boolean invoiceRecordProcessed(String messageId) {
        return invoiceRepository.invoiceRecordProcessed(messageId);
    }

    @Override
    public List<Account> findAccountsWithOrganisationId(String organisationId, String tenantId) {
        return accountRepository.findAccountsByOrganisationId(organisationId, tenantId);
    }

    @Override
    public List<Invoice> findInvoices(String organisationId, String ref) {
        return invoiceRepository.findByOrganisationIdAndReference(organisationId, ref);
    }

    @Override
    public List<RentInvoiceHistory> findHistoryItems(String createdAfter) {
        return invoiceRepository.findHistoryItems(createdAfter);
    }

    @Override
    public List<RentInvoiceHistory> findHistoryItemsForInvoice(String invoiceId) {
        return invoiceRepository.findHistoryItemsByXeroInvoiceId(invoiceId);
    }

    @Override
    public List<Invoice.LineItem> findOrganisationLineItems(String organisationId, String startDate, String endDate) {
        return invoiceRepository.findLineItemsByOrganisationId(organisationId, startDate, endDate);
    }

    @Override
    public List<LineItem> findIncomeLineItems(String organisationId) {
        return invoiceRepository.findIncomeLineItems(organisationId);
    }

    @Override
    public List<Invoice> findOrganisationInvoicesWithingDateRange(String organisationId, String startDate, String endDate, boolean includeLineItems) {
        return invoiceRepository.findByOrganisationIdAndDate(organisationId, startDate, endDate, includeLineItems);
    }

    @Override
    public Invoice findInvoiceWithOrganisationAndInvoiceId(String organisationId, String invoiceId) {
        return invoiceRepository.findByOrganisationAndInvoiceId(organisationId, invoiceId);
    }

    @Override
    public List<Invoice> findInvoicesWithPropertyIdAndStatus(String propertyId, List<String> statuses) {
        var invoiceIds = new HashSet<>(propertyRepository.findPropertyInvoices(propertyId));

        return invoiceRepository
                .getInvoices(invoiceIds)
                .stream()
                .filter(invoice -> statuses.stream().anyMatch(s -> s.equals(invoice.getStatus())))
                .collect(Collectors.toUnmodifiableList());
    }

    @Override
    public Set<Invoice> getInvoices(Set<String> invoiceIds) {
        return invoiceRepository
            .getInvoices(invoiceIds);
    }

    @Override
    public Optional<OverPayment> findOverPayment(String organisationId, String overPaymentId) {
        return overPaymentRepository.findOverPayment(organisationId, overPaymentId);
    }

    @Override
    public void deleteOverPayment(String id) {
        overPaymentRepository.deleteOverPaymentById(id);
    }

    @Override
    public void createOverPayment(OverPayment overPayment) {
        overPaymentRepository.createOverPayment(overPayment);
    }

    @Override
    public void updateOverPayment(OverPayment overPayment) {
        overPaymentRepository.updateOverPayment(overPayment);
    }

    @Override
    public Stream<OverPayment> getOverPaymentsByBatchIds(String organisationId,
                                                         Set<String> batchPaymentIds) {
        var overPaymentIds = transactionRepository.findByOrganisationAndBatchPaymentIds(organisationId, batchPaymentIds)
            .map(Transaction::getOverPaymentId).filter(Objects::nonNull)
            .collect(Collectors.toUnmodifiableSet());

        if (overPaymentIds.isEmpty()) {
            return Stream.of();
        }

        return overPaymentRepository.findByOrganisationAndOverPaymentIds(organisationId, overPaymentIds);
    }

    @Override
    public Map<String, BigDecimal> getXeroIdOverPayments(String organisationId) {
        return overPaymentRepository.findOrganisationUnallocatedOverPayments(organisationId)
            .filter(overPayment -> Objects.nonNull(overPayment.getContactId()))
            .collect(Collectors.groupingBy(OverPayment::getContactId,
                                           Collectors.mapping(t -> Optional.ofNullable(t.getRemainingCredit()).map(BigDecimal::new).orElse(BigDecimal.ZERO),
                                                              Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))
            ));
    }

    @Override
    public void updateLineItemsFinalizedStatus(List<String> lineItemIds, boolean finalized) {
        invoiceRepository.updateLineItemsFinalizedStatus(lineItemIds, finalized);
    }

    @Override
    public List<LineItem> getLineItems(Set<String> lineItemIds) {
        return invoiceRepository.findLineItemsByIds(lineItemIds);
    }

    @Override
    public void deletePaymentsWithOrganisationIdAndInvoiceId(String organisationId, String invoiceId) {
        paymentRepository.deleteByInvoiceId(organisationId, invoiceId);
    }

    @Override
    public void deleteInvoice(Invoice invoice) {
        invoiceRepository.deleteInvoice(invoice);
    }

    @Override
    public void deleteAccounts(Set<String> accountIds) {
        accountRepository.deleteAccounts(accountIds);
    }

    @Override
    public List<String> deleteLandlordBillsWithOriginalInvoiceId(String invoiceId) {
        return landlordBillRepository.deleteByOriginalInvoiceId(invoiceId);
    }

    @Override
    public void updateInvoice(Invoice invoice, String organisationId) {
        invoiceRepository.update(invoice, organisationId);
    }

    @Override
    public List<LineItem> findLineItemsByTrackingName(String organisationId, String trackingName) {
        return invoiceRepository.findLineItemsByTrackingName(organisationId, trackingName);
    }

    @Override
    public String createInvoice(Invoice invoice) {
        return invoiceRepository.create(invoice);
    }

    @Override
    public void createAllocation(String invoiceId, String total, String propertyId, String organisationId) {
        invoiceRepository.createAllocation(invoiceId, total, propertyId, organisationId);
    }

    @Override
    public void addBillProperties(String invoiceId, Property property) {
        invoiceRepository.addBillProperties(invoiceId, List.of(property));
    }

    @Override
    public Account findAccountsWithOrganisationIdAndAccountId(String organisationId, String accountId) {
        return accountRepository.findByOrganisationAndAccountId(organisationId, accountId);
    }

    @Override
    public void updateAccount(Account account) {
        accountRepository.update(account);
    }

    @Override
    public String createAccount(Account account) {
        return accountRepository.create(account);
    }

    @Override
    public Transaction findTransactionWithOrganisationIdAndAccountId(String organisationId, String transactionId) {
        return transactionRepository.findByOrganisationAndTransactionId(organisationId, transactionId);
    }

    @Override
    public void deleteTransaction(String transactionId) {
        transactionRepository.deleteTransaction(transactionId);
    }

    @Override
    public void updateTransaction(Transaction transaction) {
        transactionRepository.update(transaction);
    }

    @Override
    public String createTransaction(Transaction transaction) {
        return transactionRepository.create(transaction);
    }

    @Override
    public Payment findPaymentWithOrganisationAndPaymentId(String organisation, String paymentId) {
        return paymentRepository.findByOrganisationAndPaymentId(organisation, paymentId);
    }

    @Override
    public void deletePayment(String paymentId) {
        paymentRepository.deletePayment(paymentId);
    }

    @Override
    public void updatePayment(Payment payment) {
        paymentRepository.update(payment);
    }

    @Override
    public String createPayment(Payment payment) {
        String createdPaymentId = paymentRepository.create(payment);

        Invoice invoice = invoiceRepository.findByOrganisationAndInvoiceId(payment.getOrganisationId(), payment.getInvoiceId());
        if (invoice == null) {
            return createdPaymentId;
        }

        InvoiceTenancy invoiceTenancy = invoiceRepository.findInvoiceTenancyByInvoiceId(invoice.getId());
        if (invoiceTenancy == null) {
            return createdPaymentId;
        }

        Tenancy tenancy = tenancyRepository.getTenancy(invoiceTenancy.getInvoiceTenancyTenancyId());
        if (tenancy == null) {
            return createdPaymentId;
        }

        triggerPaymentRecordedEvent(payment, tenancy.getProperty());

        return createdPaymentId;
    }


    private void triggerPaymentRecordedEvent(Payment payment, String propertyId) {
        String eventDetailJson = payment.createRentPaidEvent(propertyId);

        PutEventsRequestEntry eventEntry = new PutEventsRequestEntry()
                .withEventBusName(config.getVariable(EVENT_BUS_NAME))
                .withSource(config.getVariable(EVENT_SOURCE))
                .withDetailType("RentPaid")
                .withDetail(eventDetailJson);

        PutEventsRequest eventRequest = new PutEventsRequest().withEntries(eventEntry);

        eventBridgeClient.putEvent(eventRequest);
    }

    @Override
    public Transfer findTransferWithOrganisationAndTransferId(String organisationId, String transferId) {
        return transferRepository.findByOrganisationAndTransferId(organisationId, transferId);
    }

    @Override
    public String createTransfer(Transfer transfer) {
        return transferRepository.create(transfer);
    }

    @Override
    public Journal findJournalWithOrganisationAndJournalId(String organisationUd, UUID journalId) {
        return journalRepository.findByOrganisationAndJournalId(organisationUd, journalId);
    }

    @Override
    public void updateJournal(Journal journal) {
        journalRepository.update(journal);
    }

    @Override
    public String createJournal(Journal journal) {
        return journalRepository.create(journal);
    }

    @Override
    public void updateAccountBalance(String organisationId, String accountId, String balance) {
        accountRepository.updateBalance(organisationId, accountId, balance);
    }

    @Override
    public void createWebhookEvent(String organisationId, String invoiceId, String payload) {

    }

    @Override
    public void updateWarnings(String invoiceId, List<String> warnings) {
        invoiceRepository.updateWarnings(invoiceId, warnings);
    }

}
