package com.rentancy.integrations.servicies.persistence;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.rentancy.integrations.exceptions.EntityNotFoundException;
import com.rentancy.integrations.pojos.Organisation;
import com.rentancy.integrations.pojos.Tenancy;
import com.rentancy.integrations.pojos.Tenancy.Settings;
import com.rentancy.integrations.servicies.persistence.config.TenancyRepositoryConfig;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.rentancy.integrations.servicies.persistence.DDBClient.*;
import static java.util.stream.Collectors.toUnmodifiableList;
import static java.util.stream.Collectors.toUnmodifiableSet;

public class TenancyRepository extends Repository {

    private static final Logger log = LogManager.getLogger(TenancyRepository.class);

    private final TenancyRepositoryConfig config;
    private final DDBClient ddbClient;

    public TenancyRepository(TenancyRepositoryConfig config, DDBClient ddbClient) {
        this.config = config;
        this.ddbClient = ddbClient;
    }

    public Tenancy getTenancy(String id) {
        var tableName = config.getTenancyTableName();

        var tenancy = this.ddbClient.getItem(tableName, id).getItem();

        return mapToTenancy(tenancy, List.of());
    }

    public Tenancy getTenancyWithSettings(String id) {
        var tableName = config.getTenancyTableName();
        var tenancy = this.ddbClient.getItem(tableName, id).getItem();
        if (tenancy == null) {
            throw new EntityNotFoundException(Tenancy.class, id);
        }

        var settings = Optional.ofNullable(getIfPresent(tenancy, "tenancySettingsId"))
                .map(this::getSettings)
                .map(List::of)
                .orElse(List.of());
        return mapToTenancy(tenancy, settings);
    }

    public Optional<Tenancy> findByReference(String organisationId, String reference) {
        var tableName = config.getTenancyTableName();
        var indexName = config.getTenancyReferenceIndex();

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("reference", new AttributeValue().withS(reference));
        fieldsValues.put("tenancyOrganisationId", new AttributeValue().withS(organisationId));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());

        var result =
                this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), null);

        return result
                .stream()
                .findFirst()
                .map(tenancy -> {
                    var settings = Optional.ofNullable(getIfPresent(tenancy, "tenancySettingsId"))
                            .map(this::getSettings)
                            .map(List::of)
                            .orElse(List.of());

                    return mapToTenancy(tenancy, settings);
                });
    }

    public List<Tenancy> findTenanciesByOrganisationIdAndStatus(String organisationId, @Nullable String status, boolean querySettings) {
        var tableName = config.getTenancyTableName();
        var indexName = config.getTenancyOrganizationIndex();

        var fieldsValues = new HashMap<String, AttributeValue>();
        var filterFieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("tenancyOrganisationId", new AttributeValue().withS(organisationId));
        if (status != null) {
            filterFieldsValues.put("status", new AttributeValue().withS(status));
        }

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());
        var archivedFilterExpression = getFilterExpression(Set.of("archived"), NE);
        var filterExpression = filterFieldsValues.isEmpty() ? archivedFilterExpression :
                getFilterExpression(filterFieldsValues.keySet())
                        .concat(AND)
                        .concat(archivedFilterExpression);

        filterFieldsValues.put("archived", new AttributeValue().withBOOL(true));
        fieldsValues.putAll(filterFieldsValues);

        var result =
                this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), filterExpression);

        var settings = querySettings ? this.findAllTenancySettingsByOrganisation(organisationId) : List.<Settings>of();
        var settingsLookup = settings.stream()
                .filter(it -> it.getTenancyId() != null)
                .collect(Collectors.groupingBy(Settings::getTenancyId));

        return result
                .stream()
                .map(tenancy -> {
                    var matchingSettings = settingsLookup.getOrDefault(getIfPresent(tenancy, "id"), List.of());
                    return mapToTenancy(tenancy, matchingSettings);
                })
                .collect(toUnmodifiableList());
    }

    public String createTenancyInvoice(String tenancyId, String invoiceId) {
        var tenancyInvoices = findTenancyInvoices(tenancyId, invoiceId);
        if (!tenancyInvoices.isEmpty()) {
            log.info("Tenancy invoice item already exists - {}, {}", tenancyId, invoiceId);
            return tenancyInvoices.stream().findAny().map(item -> getIfPresent(item, "id")).orElse(null);
        }

        var tableName = config.getTenancyInvoiceTableName();

        var item = new HashMap<String, AttributeValue>();
        var id = UUID.randomUUID().toString();

        item.put("id", new AttributeValue().withS(id));
        item.put("invoiceTenancyInvoiceId", new AttributeValue().withS(invoiceId));
        item.put("invoiceTenancyTenancyId", new AttributeValue().withS(tenancyId));
        item.put("createdAt", new AttributeValue().withS(Instant.now().toString()));

        this.ddbClient.put(tableName, item);

        return id;
    }

    public List<Settings> findAllTenancySettingsByOrganisation(String organisationId) {
        var tableName = config.getTenancySettingsTableName();
        var indexName = config.getTenancySettingsIndex();

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("organisationId", new AttributeValue().withS(organisationId));

        var result =
                this.ddbClient.query(tableName, indexName, getFilterExpression(fieldsValues.keySet()), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), null);
        return result
                .stream()
                .map(this::toSettings)
                .collect(Collectors.toList());
    }

    Settings getSettings(String settingsId) {
        var tableName =config.getTenancySettingsTableName();
        var settings = this.ddbClient.getItem(tableName, settingsId).getItem();

        return toSettings(settings);
    }

    public List<Tenancy> findByProperty(String propertyId, boolean querySettings) {
        var tableName = config.getTenancyTableName();
        var indexName = config.getTenancyPropertyIndex();

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("tenancyPropertyId", new AttributeValue().withS(propertyId));
        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());

        fieldsValues.put("archived", new AttributeValue().withBOOL(true));

        var archivedFilterExpression = getFilterExpression(Set.of("archived"), NE);

        var result =
                this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), archivedFilterExpression);

        List<Settings> settings;

        if (querySettings) {
            var settingIds = result.stream()
                    .flatMap(item -> Optional.ofNullable(getIfPresent(item, "tenancySettingsId")).stream())
                    .collect(toUnmodifiableSet());
            settings = getTenancySettings(settingIds);
        } else {
            settings = List.of();
        }

        return result
                .stream()
                .map(tenancy -> mapToTenancy(tenancy, settings))
                .collect(toUnmodifiableList());
    }

    private Tenancy mapToTenancy(Map<String, AttributeValue> item, List<Settings> potentialSettings) {
        var id = getIfPresent(item, "id");
        var settings = potentialSettings
                .stream()
                .filter(it -> id.equals(it.getTenancyId()))
                .findAny()
                .orElse(null);
        var tenancyPeriod = Optional.ofNullable(getIfPresent(item, "period"))
                .map(Tenancy.TenancyPeriod::valueOf)
                .orElse(null);
        var depositStatus = Optional.ofNullable(getIfPresent(item, "depositStatus"))
                .map(Tenancy.DepositStatus::valueOf)
                .orElse(null);
        var tenancyType = Optional.ofNullable(getIfPresent(item, "type"))
                .map(Tenancy.TenancyType::valueOf)
                .orElse(Tenancy.TenancyType.NA);

        return Tenancy
                .builder()
                .id(id)
                .accounting(getIfPresentBool(item, "accounting"))
                .addLandlordDetails(getIfPresentBool(item, "addLandlordDetails"))
                .address(getIfPresent(item, "address"))
                .agent(getIfPresent(item, "agent"))
                .archived(getIfPresentBool(item, "archived"))
                .attachInvoicesToContract(getIfPresentBool(item, "attachInvoicesToContract"))
                .currency(getIfPresent(item, "currency"))
                .deposit(getIfPresentN(item, "deposit"))
                .guarantors(toStringList(item, "guarantors"))
                .landlords(toStringList(item, "landlords"))
                .landlordVat(getIfPresentBool(item, "landlordVat"))
                .lastAction(getIfPresent(item, "lastAction"))
                .mutator(getIfPresent(item, "mutator"))
                .number(getIfPresent(item, "number"))
                .organisation(getIfPresent(item, "tenancyOrganisationId"))
                .invoiceStartDate(getIfPresent(item, "invoiceStartDate"))
                .startDate(getIfPresent(item, "startDate"))
                .endDate(getIfPresent(item, "endDate"))
                .paymentDay(getIfPresentN(item, "paymentDay"))
                .period(tenancyPeriod)
                .primaryTenant(getIfPresent(item, "primaryTenant"))
                .property(getIfPresent(item, "tenancyPropertyId"))
                .reference(getIfPresent(item, "reference"))
                .rent(getIfPresentN(item, "rent"))
                .rentNote(getIfPresent(item, "rentNote"))
                .reservation(getIfPresentN(item, "reservation"))
                .openingBalance(getIfPresentN(item, "openingBalance"))
                .area(getIfPresentN(item, "area"))
                .shareInvoicesToContract(getIfPresentBool(item, "shareInvoicesToContract"))
                .shareInvoicesWithLandlord(getIfPresentBool(item, "shareInvoicesWithLandlord"))
                .dontCollectRent(getIfPresentBool(item, "dontCollectRent"))
                .enableJournal(getIfPresentBool(item, "enableJournal"))
                .enableProRataJournal(getIfPresentBool(item, "enableProRataJournal"))
                .status(getIfPresent(item, "status"))
                .depositStatus(depositStatus)
                .addLandlordDetails(getIfPresentBool(item, "addLandlordDetails"))
                .teamMembers(toStringList(item, "teamMembers"))
                .tenants(toStringList(item, "tenants"))
                .title(getIfPresent(item, "title"))
                .type(tenancyType)
                .settings(settings)
                .primaryTenantName(getIfPresent(item, "primaryTenantName"))
                .tenancySettingsId(getIfPresent(item, "tenancySettingsId"))
                .rentReview(getIfPresent(item, "rentReview"))
                .tags(toStringList(item, "tags"))
                .breakClauseItems(toBreakClauseItems(getIfPresentL(item, "breakClauseItems")))
                .depositReference(getIfPresent(item, "depositReference"))
                .dateDepositRegistered(getIfPresent(item, "dateDepositRegistered"))
                .depositReturned(getIfPresentBool(item, "depositReturned"))
                .depositReleased(getIfPresentBool(item, "depositReleased"))
                .depositRegistered(getIfPresentBool(item, "depositRegistered"))
                .depositDisputed(getIfPresentBool(item, "depositDisputed"))
                .depositTransferred(getIfPresentBool(item, "depositTransferred"))
                .certificateNumber(getIfPresent(item, "certificateNumber"))
                .depositProtectionScheme(getIfPresent(item, "depositProtectionScheme"))
                .build();
    }

    private List<Tenancy.BreakClauseItem> toBreakClauseItems(List<AttributeValue> list) {
        return list
                .stream()
                .map(AttributeValue::getM)
                .map(attributeValue -> Tenancy
                        .BreakClauseItem
                        .builder()
                        .who(getIfPresent(attributeValue, "who"))
                        .date(getIfPresent(attributeValue, "date"))
                        .noticeDate(getIfPresent(attributeValue, "noticeDate"))
                        .build())
                .collect(Collectors.toUnmodifiableList());
    }

    private Settings toSettings(Map<String, AttributeValue> item) {
        return Settings
                .builder()
                .id(getIfPresent(item, "id"))
                .autoInvoiceRent(getIfPresentBool(item, "autoInvoiceRent"))
                .autoJournalArrears(toAutoJournalArrears(getIfPresentSet(item, "autoJournalArrears")))
                .currency(getIfPresent(item, "currency"))
                .tenancyId(getIfPresent(item, "tenancySettingsTenancyId"))
                .firstJournalRunDate(getIfPresent(item, "firstJournalRunDate"))
                .lastJournalRunDate(getIfPresent(item, "lastJournalRunDate"))
                .feeType(getIfPresent(item, "feeType"))
                .fixedFee(getIfPresentN(item, "fixedFee"))
                .percentageFee(getIfPresentN(item, "percentageFee"))
                .invoiceRentInAdvanceDays(getIfPresentN(item, "invoiceRentInAdvanceDays"))
                .rentCommission(getIfPresentDec(item, "rentCommission"))
                .sendInvoiceToTenant(getIfPresentBool(item, "sendInvoiceToTenant"))
                .tenancyLedgerCode(parseLedgerCodes(getIfPresentMap(item, "tenancyledgerCode")))
                .feeLedgerCode(parseLedgerCodes(getIfPresentMap(item, "feeLedgerCode")))
                .addCommissionBillVat(getIfPresentBool(item, "addCommissionBillVat"))
                .organisationId(getIfPresent(item, "organisationId"))
                .fundDistribution(toFundDistribution(getIfPresentL(item, "fundDistribution")))
                .lastPaymentCoveredTo(getInstantIfPresent(item, "lastPaymentCoveredTo"))
                .lastPayment(getInstantIfPresent(item, "lastPayment"))
                .build();
    }

    private List<Tenancy.FundDistribution> toFundDistribution(List<AttributeValue> list) {
        return list
                .stream()
                .map(AttributeValue::getM)
                .map(attributeValue -> {
                    // For some reason for YPP this 'ledger' is of type Number in DDB instead of String
                    var ledger = Optional.ofNullable(getIfPresent(attributeValue, "ledger"))
                            .orElseGet(() -> String.valueOf(getIfPresentN(attributeValue, "ledger")));
                    return Tenancy
                        .FundDistribution
                            .builder()
                            .amount(getIfPresentN(attributeValue, "amount"))
                            .ledger(ledger)
                            .build();
                })
                .collect(Collectors.toUnmodifiableList());
    }

    private List<Tenancy.AutoJournalArrears> toAutoJournalArrears(Set<AttributeValue> set) {
        return set
                .stream()
                .map(AttributeValue::getM)
                .map(attributeValue -> Tenancy
                        .AutoJournalArrears
                        .builder()
                        .amount(getIfPresentN(attributeValue, "amount"))
                        .ledgerCode(getIfPresent(attributeValue, "ledgerCode"))
                        .ledgerName(getIfPresent(attributeValue, "ledgerName"))
                        .build())
                .collect(Collectors.toList());
    }

    private boolean isLedgerCodeFieldPresent(@Nullable Organisation.LedgerCode ledgerCode) {
        return Objects.nonNull(ledgerCode) &&
                Objects.nonNull(ledgerCode.getCode()) &&
                Objects.nonNull(ledgerCode.getDisplayName()) &&
                Objects.nonNull(ledgerCode.getName());
    }

    private Map<String, AttributeValue> getSettingsItem(Settings setting) {
        var fieldsValues = new HashMap<String, AttributeValue>();

        addValueIfPresent("id", setting.getId(), fieldsValues);
        addValueIfPresentN("rentCommission", setting.getRentCommission().intValue(), fieldsValues);
        addValueIfPresentN("fixedFee", setting.getFixedFee(), fieldsValues);
        addValueIfPresent("feeType", setting.getFeeType(), fieldsValues);
        addValueIfPresentN("percentageFee", setting.getPercentageFee(), fieldsValues);
        addValueIfPresent("currency", setting.getCurrency(), fieldsValues);
        addValueIfPresent("tenancySettingsTenancyId", setting.getTenancyId(), fieldsValues);
        addValueIfPresent("firstJournalRunDate", setting.getFirstJournalRunDate(), fieldsValues);
        addValueIfPresent("organisationId", setting.getOrganisationId(), fieldsValues);
        addValueIfPresent("lastJournalRunDate", setting.getLastJournalRunDate(), fieldsValues);
        addValueIfPresentN("invoiceRentInAdvanceDays", setting.getInvoiceRentInAdvanceDays(), fieldsValues);
        addValueIfPresentB("autoInvoiceRent", setting.isAutoInvoiceRent(), fieldsValues);
        addValueIfPresentB("sendInvoiceToTenant", setting.isSendInvoiceToTenant(), fieldsValues);
        addValueIfPresentB("addCommissionBillVat", setting.isAddCommissionBillVat(), fieldsValues);
        addValueIfPresentInstant("lastPaymentCoveredTo", setting.getLastPaymentCoveredTo(), fieldsValues);
        addValueIfPresentInstant("lastPayment", setting.getLastPayment(), fieldsValues);
        addValueIfPresentSet("autoJournalArrears", Optional.ofNullable(setting.getAutoJournalArrears()).map(autoJournalArrears -> {
            return autoJournalArrears
                    .stream()
                    .map(fundDistribution -> new AttributeValue()
                            .withM(Map.of("amount", new AttributeValue().withN(String.valueOf(fundDistribution.getAmount())),
                                    "ledgerCode", new AttributeValue().withS(fundDistribution.getLedgerCode()),
                                    "ledgerName", new AttributeValue().withS(fundDistribution.getLedgerName()))))
                    .collect(Collectors.toSet());
        }).orElse(null), fieldsValues);
        addValueIfPresentL("fundDistribution", Optional.ofNullable(setting.getFundDistribution()).map(fundDistributions -> {
            return fundDistributions
                    .stream()
                    .map(fundDistribution -> new AttributeValue()
                            .withM(Map.of("amount", new AttributeValue().withN(String.valueOf(fundDistribution.getAmount())),
                                    "ledger", new AttributeValue().withS(fundDistribution.getLedger()))))
                    .collect(Collectors.toList());
        }).orElse(null), fieldsValues);

        if (isLedgerCodeFieldPresent(setting.getTenancyLedgerCode())) {
            var tenancyLedgerCode = Map.of(
                    "code", new AttributeValue().withS(setting.getTenancyLedgerCode().getCode()),
                    "displayName", new AttributeValue().withS(setting.getTenancyLedgerCode().getDisplayName()),
                    "name", new AttributeValue().withS(setting.getTenancyLedgerCode().getName())
            );

            addValueIfPresentM("tenancyLedgerCode", tenancyLedgerCode, fieldsValues);
        }

        if (isLedgerCodeFieldPresent(setting.getFeeLedgerCode())) {
            var feeLedgerCode = Map.of(
                    "code", new AttributeValue().withS(setting.getFeeLedgerCode().getCode()),
                    "displayName", new AttributeValue().withS(setting.getFeeLedgerCode().getDisplayName()),
                    "name", new AttributeValue().withS(setting.getFeeLedgerCode().getName())
            );

            addValueIfPresentM("feeLedgerCode", feeLedgerCode, fieldsValues);
        }

        return fieldsValues;
    }

    public void updateSettings(List<Settings> settings) {
        var tableName = config.getTenancySettingsTableName();

        var settingUpdates = settings.stream()
                .map(this::getSettingsItem)
                .collect(toUnmodifiableList());

        this.ddbClient.batchPut(tableName, settingUpdates);
    }

    private Organisation.LedgerCode parseLedgerCodes(Map<String, AttributeValue> map) {
        return new Organisation.LedgerCode(getIfPresent(map, "code"), getIfPresent(map, "displayName"), getIfPresent(map, "name"));
    }

    private List<Map<String, AttributeValue>> findTenancyInvoices(String tenancyId, String invoiceId) {
        var tableName = config.getTenancyInvoiceTableName();
        var indexName = config.getTenancyInvoiceIndex();

        var fieldsValues = new HashMap<String, AttributeValue>();
        var filterFieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("invoiceTenancyTenancyId", new AttributeValue().withS(tenancyId));
        filterFieldsValues.put("invoiceTenancyInvoiceId", new AttributeValue().withS(invoiceId));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());
        var filterExpression = getFilterExpression(filterFieldsValues.keySet());

        fieldsValues.putAll(filterFieldsValues);

        return this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), filterExpression);
    }

    public void removeTenancyInvoices(String tenancyId, String invoiceId) {
        var tableName = config.getTenancyInvoiceTableName();
        var result = findTenancyInvoices(tenancyId, invoiceId);
        result
                .stream()
                .map(item -> getIfPresent(item, "id"))
                .forEach(id -> this.ddbClient.deleteItem(tableName, id));
    }

    void deleteByInvoiceId(String invoiceId) {
        var tableName = config.getTenancyInvoiceTableName();
        var indexName = config.getTenancyInvoiceInvoiceIndex();

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("invoiceTenancyInvoiceId", new AttributeValue().withS(invoiceId));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());

        this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), null)
                .stream()
                .map(item -> getIfPresent(item, "id"))
                .forEach(id -> ddbClient.deleteItem(tableName, id));
    }

    private List<Settings> getTenancySettings(Set<String> ids) {
        var tenancySettingsTableName = config.getTenancySettingsTableName();

        var keyConditions = ids.stream()
                .map(id -> Map.of("id", new AttributeValue().withS(id)))
                .collect(toUnmodifiableList());

        return ddbClient.batchQuery(tenancySettingsTableName, keyConditions)
                .stream()
                .map(this::toSettings)
                .collect(toUnmodifiableList());
    }

    public List<Tenancy> findTenancies(List<String> ids, boolean includeSettings) {
        var tenancyTableName = config.getTenancyTableName();

        var keyConditions = ids.stream()
                .map(id -> Map.of("id", new AttributeValue().withS(id)))
                .collect(toUnmodifiableList());

        var tenancyItems = ddbClient.batchQuery(tenancyTableName, keyConditions);

        List<Settings> settings;

        if (includeSettings) {
            var settingIds = tenancyItems.stream()
                    .flatMap(item -> Optional.ofNullable(getIfPresent(item, "tenancySettingsId")).stream())
                    .collect(toUnmodifiableSet());
            settings = getTenancySettings(settingIds);
        } else {
            settings = List.of();
        }

        return tenancyItems
                .stream()
                .map(item -> mapToTenancy(item, settings))
                .collect(toUnmodifiableList());
    }

    public List<Tenancy> findAllTenancies() {
        var tableName = config.getTenancyTableName();

        return ddbClient.scan(tableName)
                .stream()
                .map(tenancy -> mapToTenancy(tenancy, List.of()))
                .collect(toUnmodifiableList());
    }

    public void updateTenancyDepositStatus(String tenancyId, @Nullable Tenancy.DepositStatus depositTenancyStatus) {
        var tableName = config.getTenancyTableName();

        var fieldsValues = new HashMap<String, AttributeValue>();

        if (Objects.isNull(depositTenancyStatus)) {
            fieldsValues.put("depositRegistered", null);
            fieldsValues.put("depositTransferred", null);
            fieldsValues.put("depositDisputed", null);
            fieldsValues.put("depositStatus", null);

            var deleteExpression = getRemoveExpression(fieldsValues.keySet());
            this.ddbClient.update(tableName, tenancyId, deleteExpression, getAttributeNames(fieldsValues), null);
            return;
        }

        fieldsValues.put("depositStatus", new AttributeValue().withS(depositTenancyStatus.name()));
        this.ddbClient.update(tableName, tenancyId, getUpdateExpression(fieldsValues.keySet(), EQ), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues));
    }

    public void updateTenancyDepositTransferredStatus(String tenancyId, boolean transferred) {
        var tableName = config.getTenancyTableName();
        var fieldValues = new HashMap<String, AttributeValue>();

        addValueIfPresentB("depositTransferred", transferred, fieldValues);
        String updateExpression;

        if (transferred) {
            addValueIfPresent("transferredDate", Instant.now().toString(), fieldValues);
            updateExpression = getUpdateExpression(fieldValues.keySet(), EQ);
        } else {
            updateExpression = getUpdateExpression(fieldValues.keySet(), EQ);
            updateExpression += " " + getRemoveExpression(Set.of("transferredDate"));
        }

        this.ddbClient.update(tableName, tenancyId, updateExpression, getAttributeNames(fieldValues.keySet()), getAttributeValues(fieldValues));
    }

    public void updateTenancyDepositReturnedStatus(String tenancyId, boolean returned) {
        var tableName = config.getTenancyTableName();
        var fieldValues = new HashMap<String, AttributeValue>();

        addValueIfPresentB("depositReturned", returned, fieldValues);
        this.ddbClient.update(tableName, tenancyId, getUpdateExpression(fieldValues.keySet(), EQ), getAttributeNames(fieldValues.keySet()), getAttributeValues(fieldValues));
    }

    public List<Tenancy> findTenanciesByInvoiceId(String invoiceId) {
        var tableName = config.getTenancyInvoiceTableName();
        var indexName = config.getTenancyInvoiceInvoiceIndex();

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("invoiceTenancyInvoiceId", new AttributeValue().withS(invoiceId));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());

        var result = this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), null);
        return result.stream().map(t -> mapToTenancy(t, List.of())).collect(Collectors.toList());
    }
}
