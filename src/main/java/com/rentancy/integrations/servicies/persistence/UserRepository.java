package com.rentancy.integrations.servicies.persistence;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.User;
import com.rentancy.integrations.pojos.User.UserAddress;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.rentancy.integrations.servicies.persistence.DDBClient.*;
import static java.util.stream.Collectors.*;

@Slf4j
public class UserRepository extends Repository {

    private static final String USER_TABLE = "USER_TABLE";
    private static final String USER_ORGANISATION_INDEX = "USER_ORGANISATION_INDEX";
    private static final String ORGANISATION_USER_TABLE = "ORGANISATION_USER_TABLE";
    private static final String ORGANISATION_USER_INDEX = "ORGANISATION_USER_INDEX";
    private static final String USER_EMAIL_INDEX = "USER_EMAIL_INDEX";
    private static final String USER_COGNITOID_INDEX = "USER_COGNITOID_INDEX";
    private static final String USER_XERO_INDEX = "USER_XERO_INDEX";
    private static final String ADDRESS_TABLE = "ADDRESS_TABLE";
    private static final String ADDRESS_PARENT_INDEX = "ADDRESS_PARENT_INDEX";
    private static final String ADDRESS_ORGANISATION_INDEX = "ADDRESS_ORGANISATION_INDEX";
    private static final String USER_TABLE_DENORMALIZED_EMAILS_INDEX = "USER_TABLE_DENORMALIZED_EMAILS_INDEX";

    private final Config config;
    private final DDBClient ddbClient;

    public UserRepository(Config config, DDBClient ddbClient) {
        this.config = config;
        this.ddbClient = ddbClient;
    }

    public String createUser(User user) {
        var tableName = config.getVariable(USER_TABLE);

        var item = new HashMap<String, AttributeValue>();
        var id = UUID.randomUUID().toString();
        var emails = user.getEmails();
        var phones = user.getPhones();
        var homeAddress = user.getHomeAddress();
        var postalAddress = user.getPostalAddress();

        if (Objects.nonNull(emails)) {
            item.put("emails", new AttributeValue().withL(emails
                    .stream()
                    .filter(email -> Objects.nonNull(email.getEmail()))
                    .map(email -> new AttributeValue()
                            .withM(Map.of("email", new AttributeValue().withS(email.getEmail()),
                                    "type", new AttributeValue().withS(email.getType()))))
                    .collect(toUnmodifiableList())));
        }

        if (Objects.nonNull(phones)) {
            item.put("phones", new AttributeValue().withL(phones
                    .stream()
                    .filter(phone -> Objects.nonNull(phone.getPhone()) && Objects.nonNull(phone.getType()))
                    .map(phone -> new AttributeValue()
                            .withM(Map.of("phone", new AttributeValue().withS(phone.getPhone()),
                                    "type", new AttributeValue().withS(phone.getType()))))
                    .collect(toUnmodifiableList())));
        }

        if (Objects.nonNull(homeAddress)) {
            createUserAddress(user.getCurrentOrganisation(), id, homeAddress, User.AddressType.HOME);
        }

        if (Objects.nonNull(postalAddress)) {
            createUserAddress(user.getCurrentOrganisation(), id, postalAddress, User.AddressType.POSTAL);
        }

        item.put("id", new AttributeValue().withS(id));
        item.put("public", new AttributeValue().withBOOL(true));

        addValueIfPresent("xeroId", user.getXeroId(), item);
        addValueIfPresent("fname", user.getFname(), item);
        addValueIfPresent("sname", user.getSname(), item);
        addValueIfPresent("companyName", user.getCompanyName(), item);
        addValueIfPresent("type", user.getType(), item);
        addValueIfPresent("currentOrganisation", user.getCurrentOrganisation(), item);
        addValueIfPresent("createdDate", Instant.now().toString(), item);

        this.ddbClient.put(tableName, item);

        return id;
    }

    public void createUserAddress(String organisationId, String userId, UserAddress userAddress, User.AddressType type) {
        var tableName = config.getVariable(ADDRESS_TABLE);
        var item = new HashMap<String, AttributeValue>();
        var id = UUID.randomUUID().toString();

        item.put("id", new AttributeValue().withS(id));
        item.put("__typename", new AttributeValue().withS("Address"));
        item.put("addressOrganisationId", new AttributeValue().withS(organisationId));
        item.put("createdAt", new AttributeValue().withS(Instant.now().toString()));
        item.put("parentId", new AttributeValue().withS(userId));
        item.put("parentType", new AttributeValue().withS("USER"));
        item.put("type", new AttributeValue().withS(type.name()));

        addValueIfPresent("addressLine1", userAddress.getAddressLine1(), item);
        addValueIfPresent("addressLine2", userAddress.getAddressLine2(), item);
        addValueIfPresent("addressLine3", userAddress.getAddressLine3(), item);
        addValueIfPresent("city", userAddress.getCity(), item);
        addValueIfPresent("country", userAddress.getCountry(), item);
        addValueIfPresent("state", userAddress.getState(), item);
        addValueIfPresent("postcode", userAddress.getPostcode(), item);

        this.ddbClient.put(tableName, item);
    }

    public User findByCognitoEmail(String email) {
        var tableName = config.getVariable(USER_TABLE);
        var indexName = config.getVariable(USER_EMAIL_INDEX);

        Map<String, AttributeValue> fieldsValues = new HashMap<>();
        fieldsValues.put("cognitoEmail", new AttributeValue().withS(email));

        List<Map<String, AttributeValue>> result =
                this.ddbClient.query(tableName, indexName, getFilterExpression(fieldsValues.keySet()), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), null);

        return result
                .stream()
                .map(this::mapToUser)
                .findAny()
                .orElse(null);
    }

    public User findByEmail(String email) {
        var tableName = config.getVariable(USER_TABLE);
        var denormalizedEmailsIndex = config.getVariable(USER_TABLE_DENORMALIZED_EMAILS_INDEX);

        var values = Map.of("denormalizedEmails", new AttributeValue().withS(email));
        System.out.println("Searching by single email with index");
        List<Map<String, AttributeValue>> result =   this.ddbClient.query(tableName,
                denormalizedEmailsIndex,
                getFilterExpression(values.keySet()),
                getAttributeNames(values),
                getAttributeValues(values),null);

        if(result.isEmpty()) {
            System.out.println("Searching by email is included in denormalizedEmails");
            result = this.ddbClient.scan(tableName,
                    getFilterExpression(values.keySet(), CONTAINS),
                    getAttributeNames(values),
                    getAttributeValues(values),
                    denormalizedEmailsIndex
            );
        }

        if(result.isEmpty()) {
            System.out.println("Getting all users with emails field present and filtering in memory");
            result = this.ddbClient.scan(tableName, ATTRIBUTE_EXISTS+"(emails)", null,null);
        }

        return result
                .stream()
                .map(this::mapToUser)
                .filter(user -> Objects.nonNull(user.getEmails()))
                .filter(user -> user
                        .getEmails()
                        .stream()
                        .filter(Objects::nonNull)
                        .anyMatch(em -> email.equals(em.getEmail())))
                .findAny()
                .orElse(null);
    }

    public User findByCognitoId(String cognitoId) {
        var tableName = config.getVariable(USER_TABLE);
        var indexName = config.getVariable(USER_COGNITOID_INDEX);

        Map<String, AttributeValue> fieldsValues = new HashMap<>();
        fieldsValues.put("cognitoId", new AttributeValue().withS(cognitoId));

        List<Map<String, AttributeValue>> result =
                this.ddbClient.query(tableName, indexName, getFilterExpression(fieldsValues.keySet()), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), null);

        return result
                .stream()
                .map(this::mapToUser)
                .findAny()
                .orElse(null);
    }

    public List<User> findOrganisationUsers(String organisationId) {
        var tableName = config.getVariable(ORGANISATION_USER_TABLE);
        var indexName = config.getVariable(ORGANISATION_USER_INDEX);

        Map<String, AttributeValue> fieldsValues = new HashMap<>();
        fieldsValues.put("organisationUserOrganisationId", new AttributeValue().withS(organisationId));

        List<Map<String, AttributeValue>> result =
                this.ddbClient.query(tableName, indexName, getFilterExpression(fieldsValues.keySet()), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), null);

        return result
                .stream()
                .map(item -> getIfPresent(item, "organisationUserUserId"))
                .filter(Objects::nonNull)
                .map(this::get)
                .filter(Objects::nonNull)
                .collect(toUnmodifiableList());
    }

    public List<User> findOrganisationUsers(String organisationId, boolean includeAddress) {
        var tableName = config.getVariable(ORGANISATION_USER_TABLE);
        var indexName = config.getVariable(ORGANISATION_USER_INDEX);

        Map<String, AttributeValue> fieldsValues = new HashMap<>();
        fieldsValues.put("organisationUserOrganisationId", new AttributeValue().withS(organisationId));

        log.debug("Getting organisation users...");
        var userIds =
                this.ddbClient.query(
                                tableName, indexName,
                                getFilterExpression(fieldsValues.keySet()), getAttributeNames(fieldsValues),
                                getAttributeValues(fieldsValues), null
                        ).stream()
                        .map(item -> getIfPresent(item, "organisationUserUserId"))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
        log.debug("Getting organisation users... Done");

        log.debug("Getting users...");
        var users = getUsers(userIds);
        var userIdToUser = users.stream().collect(toMap(User::getId, Function.identity()));
        log.debug("Getting users... Done");

        if (includeAddress) {
            log.debug("Getting addresses...");
            var userAddresses = getOrganisationUserAddresses(organisationId);
            log.debug("Getting addresses... Done");
            userAddresses.forEach((key, value) ->
                    Optional.ofNullable(userIdToUser.get(key)).ifPresent(user -> user.setAddresses(value))
            );
        }

        return users;
    }

    public User get(String id) {
        var table = config.getVariable(USER_TABLE);
        var result = this.ddbClient.getItem(table, id);

        return Optional
                .ofNullable(result.getItem())
                .map(this::mapToUser)
                .orElse(null);
    }

    public User get(String id, boolean includeAddress) {
        var user = get(id);
        if (includeAddress && user != null) {
            var addresses = getUserAddresses(user);
            user.setAddresses(addresses);
        }
        return user;
    }

    private Map<String, List<UserAddress>> getOrganisationUserAddresses(String organisationId) {
        var tableName = config.getVariable(ADDRESS_TABLE);
        var indexName = config.getVariable(ADDRESS_ORGANISATION_INDEX);
        Map<String, AttributeValue> fieldsValues = new HashMap<>();
        fieldsValues.put("addressOrganisationId", new AttributeValue().withS(organisationId));
        fieldsValues.put("parentType", new AttributeValue().withS("USER"));
        var keyConditionExpression = getFilterExpression(Set.of("addressOrganisationId"));
        var filterExpression = getFilterExpression(Set.of("parentType"));
        List<Map<String, AttributeValue>> result =
                this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), filterExpression);
        return result.stream().map(this::toUserAddress).collect(groupingBy(UserAddress::getParentId));
    }

    public List<UserAddress> getUserAddresses(User user) {
        var tableName = config.getVariable(ADDRESS_TABLE);
        var indexName = config.getVariable(ADDRESS_PARENT_INDEX);

        Map<String, AttributeValue> fieldsValues = new HashMap<>();
        fieldsValues.put("parentType", new AttributeValue().withS("USER"));
        fieldsValues.put("parentId", new AttributeValue().withS(user.getId()));

        List<Map<String, AttributeValue>> result =
                this.ddbClient.query(tableName, indexName, getFilterExpression(fieldsValues.keySet()), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), null);
        return result.stream().map(this::toUserAddress).collect(toList());
    }

    public UserAddress getUserPostalAddress(String userId) {
        var tableName = config.getVariable(ADDRESS_TABLE);
        var indexName = config.getVariable(ADDRESS_PARENT_INDEX);

        Map<String, AttributeValue> fieldsValues = new HashMap<>();
        fieldsValues.put("parentType", new AttributeValue().withS("USER"));
        fieldsValues.put("parentId", new AttributeValue().withS(userId));

        List<Map<String, AttributeValue>> result =
                this.ddbClient.query(tableName, indexName, getFilterExpression(fieldsValues.keySet()), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), null);

        return result.stream()
                .map(this::toUserAddress)
                .filter(userAddress -> userAddress.getType() == User.AddressType.POSTAL)
                .findFirst()
                .orElse(null);
    }

    private UserAddress toUserAddress(Map<String, AttributeValue> item) {
        return UserAddress.builder()
                .addressLine1(getIfPresent(item, "addressLine1"))
                .addressLine2(getIfPresent(item, "addressLine2"))
                .addressLine3(getIfPresent(item, "addressLine3"))
                .city(getIfPresent(item, "city"))
                .state(getIfPresent(item, "state"))
                .postcode(getIfPresent(item, "postcode"))
                .country(getIfPresent(item, "country"))
                .parentId(getIfPresent(item, "parentId"))
                .type(User.AddressType.valueOf(getIfPresent(item, "type")))
                .build();
    }

    private void updateDetails(String id, String filed, String value) {
        var tableName = config.getVariable(USER_TABLE);

        Map<String, AttributeValue> fieldsValues = new HashMap<>();
        fieldsValues.put(filed, new AttributeValue().withS(value));

        this.ddbClient.update(tableName, id, getUpdateExpression(fieldsValues.keySet(), EQ), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues));
    }

    public void updateXeroDetails(String id, String xeroId) {
        updateDetails(id, "xeroId", xeroId);
    }

    public void refreshUser(String id) {
        updateDetails(id, "updatedAt", Instant.now().toString());
    }

    private User mapToUser(Map<String, AttributeValue> item) {
        return User
                .builder()
                .id(getIfPresent(item, "id"))
                .xeroId(getIfPresent(item, "xeroId"))
                .cognitoEmail(getIfPresent(item, "cognitoEmail"))
                .cognitoId(getIfPresent(item, "cognitoId"))
                .currentOrganisation(getIfPresent(item, "currentOrganisation"))
                .phone(getIfPresent(item, "phone"))
                .fname(getIfPresent(item, "fname"))
                .sname(getIfPresent(item, "sname"))
                .companyName(getIfPresent(item, "companyName"))
                .phones(parsePhones(getIfPresentL(item, "phones")))
                .emails(parseEmails(getIfPresentL(item, "emails")))
                .ownedProperties(parseOwnedProperties(getIfPresentL(item, "ownedProperties")))
                .title(getIfPresent(item, "title"))
                .roles(parseRoles(getIfPresentL(item, "roles")))
                .type(getIfPresent(item, "type"))
                .reference(getIfPresent(item, "reference"))
                .vat(getIfPresent(item, "vat"))
                .internalNotes(getIfPresent(item, "internalNotes"))
                .beneficiaryName(getIfPresent(item, "beneficiaryName"))
                .bankName(getIfPresent(item, "bankName"))
                .bankAccountNumber(getIfPresent(item, "bankAccountNumber"))
                .bankAccountSortCode(getIfPresent(item, "bankAccountSortCode"))
                .bankIBAN(getIfPresent(item, "bankIBAN"))
                .bankSWIFT(getIfPresent(item, "bankSWIFT"))
                .userImageId(getIfPresent(item, "userImageId"))
                .overseasResident(getIfPresentBool(item, "overseasResident"))
                .overseasResidentExemptionCertificate(getIfPresent(item, "overseasResidentExemptionCertificate"))
                .overseasResidentExemptionDate(getInstantIfPresent(item, "overseasResidentExemptionDate"))
                .overseasResidentTax(getIfPresent(item, "overseasResidentTax"))
                .tags(getIfPresentL(item, "tags").stream().map(AttributeValue::getS).collect(toList()))
                .build();
    }

    private List<User.OwnedProperty> parseOwnedProperties(List<AttributeValue> ownedProperties) {
        return ownedProperties
                .stream()
                .map(ownedProperty -> new User.OwnedProperty(getIfPresentBool(ownedProperty.getM(), "parentProperty"), getIfPresent(ownedProperty.getM(), "propertyId"), getIfPresentN(ownedProperty.getM(), "percentage")))
                .collect(toUnmodifiableList());
    }

    private List<String> parseRoles(List<AttributeValue> roles) {
        return roles.stream().map(AttributeValue::getS).collect(toUnmodifiableList());
    }

    private List<User.Phone> parsePhones(List<AttributeValue> phones) {
        return phones
                .stream()
                .map(phone -> new User.Phone(getIfPresent(phone.getM(), "type"), getIfPresent(phone.getM(), "phone")))
                .collect(toList());
    }

    private List<User.Email> parseEmails(List<AttributeValue> emails) {
        return emails
                .stream()
                .map(phone -> new User.Email(getIfPresent(phone.getM(), "type"), getIfPresent(phone.getM(), "email")))
                .collect(toList());
    }

    public User findByName(String fname, String sname) {
        var tableName = config.getVariable(USER_TABLE);

        List<Map<String, AttributeValue>> result =
                this.ddbClient.scan(tableName);

        return result
                .stream()
                .map(this::mapToUser)
                .filter(user -> fname.equals(user.getFname()) && sname.equals(user.getSname()))
                .findAny()
                .orElse(null);
    }

    public User findByXeroId(String xeroId) {
        var tableName = config.getVariable(USER_TABLE);
        var indexName = config.getVariable(USER_XERO_INDEX);

        Map<String, AttributeValue> fieldsValues = new HashMap<>();
        fieldsValues.put("xeroId", new AttributeValue().withS(xeroId));

        List<Map<String, AttributeValue>> result =
                this.ddbClient.query(tableName, indexName, getFilterExpression(fieldsValues.keySet()), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), null);

        return result
                .stream()
                .map(this::mapToUser)
                .findAny()
                .orElse(null);
    }

    public List<User> getUsers(Set<String> userIds) {
        if (userIds.isEmpty()) {
            return List.of();
        }

        var tableName = config.getVariable(USER_TABLE);

        var keyConditions = userIds.stream()
                .map(userId -> Map.of("id", new AttributeValue().withS(userId))).collect(toUnmodifiableList());

        return ddbClient.batchQuery(tableName, keyConditions)
                .stream()
                .map(this::mapToUser)
                .collect(toUnmodifiableList());
    }

    public User findByCompanyName(String organisation, String name) {
        var organisationUsers = findOrganisationUsers(organisation);
        return organisationUsers.stream()
                .filter(user -> user.getCompanyName() != null)
                .filter(user -> user.getCompanyName().equals(name))
                .findFirst()
                .orElse(null);
    }

    public List<User> findUsersWithAddressesByOrganisation(String organisationId) {
        var tableName = config.getVariable(USER_TABLE);
        var indexName = config.getVariable(USER_ORGANISATION_INDEX);

        Map<String, AttributeValue> fieldsValues = new HashMap<>();
        fieldsValues.put("currentOrganisation", new AttributeValue().withS(organisationId));

        var usersFuture = CompletableFuture.supplyAsync(() -> this.ddbClient.query(
                tableName,
                indexName,
                getFilterExpression(fieldsValues.keySet()),
                getAttributeNames(fieldsValues),
                getAttributeValues(fieldsValues),
                null
        ).stream().map(this::mapToUser).collect(toUnmodifiableList()));
        var addressesFuture = CompletableFuture.supplyAsync(() -> getOrganisationUserAddresses(organisationId));

        return usersFuture.thenCombine(addressesFuture, (users, addresses) -> {
            users.forEach(user -> Optional.ofNullable(addresses.get(user.getId())).ifPresent(user::setAddresses));
            return users;
        }).join();
    }

    public List<User> findUsersWithoutAddressesByOrganisation(String organisationId) {
        var tableName = config.getVariable(USER_TABLE);
        var indexName = config.getVariable(USER_ORGANISATION_INDEX);

        Map<String, AttributeValue> fieldsValues = new HashMap<>();
        fieldsValues.put("currentOrganisation", new AttributeValue().withS(organisationId));

        return this.ddbClient.query(
                tableName,
                indexName,
                getFilterExpression(fieldsValues.keySet()),
                getAttributeNames(fieldsValues),
                getAttributeValues(fieldsValues),
                null
        ).stream().map(this::mapToUser).collect(toUnmodifiableList());
    }
}
