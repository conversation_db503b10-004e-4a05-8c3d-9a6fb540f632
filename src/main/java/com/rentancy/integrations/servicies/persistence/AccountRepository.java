package com.rentancy.integrations.servicies.persistence;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.Account;
import com.rentancy.integrations.pojos.Organisation;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.rentancy.integrations.servicies.persistence.DDBClient.EQ;

public class AccountRepository extends Repository {
    private static final String ACCOUNT_TABLE = "ACCOUNT_TABLE";
    private static final String ACCOUNT_ORGANISATION_INDEX = "ACCOUNT_ORGANISATION_INDEX";

    private final Config config;
    private final DDBClient ddbClient;

    public AccountRepository(Config config, DDBClient ddbClient) {
        this.config = config;
        this.ddbClient = ddbClient;
    }

    public String create(Account account) {
        var tableName = config.getVariable(ACCOUNT_TABLE);
        var item = new HashMap<String, AttributeValue>();
        var id = UUID.randomUUID().toString();
        var organisationId = account.getOrganisation();

        item.put("id", new AttributeValue().withS(id));
        item.put("accountId", new AttributeValue().withS(account.getAccountId()));
        item.put("accountOrganisationId", new AttributeValue().withS(organisationId));
        item.put("tenantId", new AttributeValue().withS(account.getTenant()));
        item.put("createdAt", new AttributeValue().withS(Instant.now().toString()));

        addValueIfPresent("type", account.getType(), item);
        addValueIfPresent("name", account.getName(), item);
        addValueIfPresent("code", account.getCode(), item);

        this.ddbClient.put(tableName, item);

        return id;
    }

    public void updateBalance(String organisationId, String accountId, String balance) {
        var tableName = config.getVariable(ACCOUNT_TABLE);

        var fieldsValues = new HashMap<String, AttributeValue>();
        var account = findByOrganisationAndAccountId(organisationId, accountId);

        addValueIfPresent("updatedAt", Instant.now().toString(), fieldsValues);
        addValueIfPresent("balance", balance, fieldsValues);

        String updateExpression = getUpdateExpression(fieldsValues.keySet(), EQ);

        this.ddbClient.update(tableName, account.getId(), updateExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues));
    }

    public Account findByOrganisationAndAccountId(String organisationId, String accountId) {
        var tableName = config.getVariable(ACCOUNT_TABLE);
        var indexName = config.getVariable(ACCOUNT_ORGANISATION_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        var filterFieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("accountOrganisationId", new AttributeValue().withS(organisationId));
        filterFieldsValues.put("accountId", new AttributeValue().withS(accountId));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());
        var filterExpression = getFilterExpression(filterFieldsValues.keySet());

        fieldsValues.putAll(filterFieldsValues);

        var result =
                this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), filterExpression);

        return result
                .stream()
                .map(this::mapToAccount)
                .findAny()
                .orElse(null);
    }
    public List<Account> findAccountsByOrganisationId(String accountOrganisationId, String tenantId) {
        var tableName = config.getVariable(ACCOUNT_TABLE);
        var indexName = config.getVariable(ACCOUNT_ORGANISATION_INDEX);
        var fieldsValues = new HashMap<String, AttributeValue>();
        var filterFieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("accountOrganisationId", new AttributeValue().withS(accountOrganisationId));
        filterFieldsValues.put("tenantId", new AttributeValue().withS(tenantId));
        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());
        var filterExpression = getFilterExpression(filterFieldsValues.keySet());

        fieldsValues.putAll(filterFieldsValues);

        var result =
                this.ddbClient.query(tableName, indexName, keyConditionExpression,getAttributeNames(fieldsValues) ,getAttributeValues(fieldsValues), filterExpression);

        return result.stream()
                .map(this::mapToAccount)
                .collect(Collectors.toList());
    }
    private Account mapToAccount(Map<String, AttributeValue> item) {
        return Account
                .builder()
                .id(getIfPresent(item, "id"))
                .accountId(getIfPresent(item, "accountId"))
                .type(getIfPresent(item, "type"))
                .name(getIfPresent(item, "name"))
                .code(getIfPresent(item, "code"))
                .rentancyLedgerCode(parseLedgerCodes(getIfPresentMap(item, "rentancyLedgerCode")))
                .organisation(getIfPresent(item, "accountOrganisationId"))
                .createdAt(getInstantIfPresent(item, "createdAt"))
                .build();
    }

    private Organisation.LedgerCode parseLedgerCodes(Map<String, AttributeValue> ledgerCodes) {
        // displayName will never be in Account table. If somehow, magically you end up needing it, create a migration.
        return  new Organisation.LedgerCode(getIfPresent(ledgerCodes, "code"), getIfPresent(ledgerCodes, "displayName"), getIfPresent(ledgerCodes, "name"));
    }
    public void update(Account account) {
        var tableName = config.getVariable(ACCOUNT_TABLE);

        var fieldsValues = new HashMap<String, AttributeValue>();

        fieldsValues.put("updatedAt", new AttributeValue().withS(Instant.now().toString()));
        addValueIfPresent("tenantId", account.getTenant(), fieldsValues);
        addValueIfPresent("type", account.getType(), fieldsValues);
        addValueIfPresent("name", account.getName(), fieldsValues);
        addValueIfPresent("code", account.getCode(), fieldsValues);

        var updateExpression = getUpdateExpression(fieldsValues.keySet(), EQ);

        this.ddbClient.update(tableName, account.getId(), updateExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues));
    }

    public void deleteAccounts(Set<String> accountIds) {
        var tableName = config.getVariable(ACCOUNT_TABLE);

        accountIds.forEach(accountId -> this.ddbClient.deleteItem(tableName, accountId));
    }
}
