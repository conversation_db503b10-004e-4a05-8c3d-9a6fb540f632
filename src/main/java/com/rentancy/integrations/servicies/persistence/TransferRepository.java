package com.rentancy.integrations.servicies.persistence;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.Transfer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static com.rentancy.integrations.util.Utils.getInstantStringIfPresent;

public class TransferRepository extends Repository {

    private static final Logger log = LogManager.getLogger(TransferRepository.class);

    private static final String TRANSFER_TABLE = "TRANSFER_TABLE";
    private static final String TRANSFER_INDEX = "TRANSFER_INDEX";

    private final Config config;
    private final DDBClient ddbClient;

    public TransferRepository(Config config, DDBClient ddbClient) {
        this.config = config;
        this.ddbClient = ddbClient;
    }

    public String create(Transfer transfer) {
        var item = new HashMap<String, AttributeValue>();
        var id = UUID.randomUUID().toString();

        item.put("id", new AttributeValue().withS(id));
        item.put("transferId", new AttributeValue().withS(transfer.getTransferId()));
        item.put("transferOrganisationId", new AttributeValue().withS(transfer.getOrganisationId()));
        item.put("tenantId", new AttributeValue().withS(transfer.getTenant()));
        item.put("transferAccountId", new AttributeValue().withS(transfer.getAccountId()));
        item.put("transferTransactionId", new AttributeValue().withS(transfer.getTransactionId()));
        item.put("fromAccount", new AttributeValue().withS(transfer.getFromAccount()));
        item.put("fromTransaction", new AttributeValue().withS(transfer.getFromTransaction()));
        item.put("amount", new AttributeValue().withN(transfer.getAmount()));
        item.put("hasAttachments", new AttributeValue().withBOOL(transfer.isHasAttachments()));
        item.put("createdAt", new AttributeValue().withS(Instant.now().toString()));

        addValueIfPresent("date", getInstantStringIfPresent(transfer.getDate()), item);

        this.ddbClient.put(config.getVariable(TRANSFER_TABLE), item);

        return id;
    }

    public Transfer findByOrganisationAndTransferId(String organisationId, String transferId) {
        var tableName = config.getVariable(TRANSFER_TABLE);
        var indexName = config.getVariable(TRANSFER_INDEX);
        log.info("Transfer table - " + tableName);

        var fieldsValues = new HashMap<String, AttributeValue>();
        var filterFieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("transferId", new AttributeValue().withS(transferId));
        filterFieldsValues.put("transferOrganisationId", new AttributeValue().withS(organisationId));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());
        var filterExpression = getFilterExpression(filterFieldsValues.keySet());

        fieldsValues.putAll(filterFieldsValues);

        var result =
                this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), filterExpression);

        return result
                .stream()
                .map(this::mapToTransfer)
                .findAny()
                .orElse(null);
    }

    private Transfer mapToTransfer(Map<String, AttributeValue> item) {
        return Transfer
                .builder()
                .id(getIfPresent(item, "id"))
                .transferId(getIfPresent(item, "transferId"))
                .amount(getIfPresent(item, "amount"))
                .fromTransaction(getIfPresent(item, "fromTransaction"))
                .transactionId(getIfPresent(item, "transferTransactionId"))
                .fromAccount(getIfPresent(item, "fromAccount"))
                .accountId(getIfPresent(item, "transferAccountId"))
                .organisationId(getIfPresent(item, "transferOrganisationId"))
                .tenant(getIfPresent(item, "tenantId"))
                .hasAttachments(getIfPresentBool(item, "hasAttachments"))
                .date(getInstantIfPresent(item, "date"))
                .build();
    }
}
