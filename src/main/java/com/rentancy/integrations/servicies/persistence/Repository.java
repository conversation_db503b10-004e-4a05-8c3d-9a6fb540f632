package com.rentancy.integrations.servicies.persistence;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.rentancy.integrations.servicies.persistence.DDBClient.AND;
import static com.rentancy.integrations.servicies.persistence.DDBClient.ATTRIBUTE_NAME_PREFIX;
import static com.rentancy.integrations.servicies.persistence.DDBClient.ATTRIBUTE_VALUE_PREFIX;
import static com.rentancy.integrations.servicies.persistence.DDBClient.BEGINS_WITH;
import static com.rentancy.integrations.servicies.persistence.DDBClient.BETWEEN;
import static com.rentancy.integrations.servicies.persistence.DDBClient.CONTAINS;
import static com.rentancy.integrations.servicies.persistence.DDBClient.EQ;
import static com.rentancy.integrations.servicies.persistence.DDBClient.OR;
import static com.rentancy.integrations.servicies.persistence.DDBClient.REMOVE;
import static com.rentancy.integrations.servicies.persistence.DDBClient.SET;
import static java.util.stream.Collectors.toUnmodifiableList;
import static java.util.stream.Collectors.toUnmodifiableMap;

abstract class Repository {

    protected Map<String, String> getAttributeNames(Map<String, AttributeValue> fieldsValues) {
        Map<String, String> attrNames = new HashMap<>();

        fieldsValues.keySet().forEach(field -> attrNames.put("#" + field, field));

        return attrNames;
    }

    protected Map<String, String> getAttributeNames(Collection<Map<String,AttributeValue>> FieldsValuesMaps) {
        Map<String, String> attrNames = new HashMap<>();

        FieldsValuesMaps.forEach(fieldsValues ->
            attrNames.putAll(getAttributeNames(fieldsValues))
        );
        return attrNames;
    }

    protected Map<String, String> getAttributeNames(Set<String> fieldsValues) {
        return fieldsValues.stream()
                .collect(toUnmodifiableMap(value -> "#" + value, Function.identity()));
    }

    protected Map<String, AttributeValue> getAttributeValues(Map<String, AttributeValue> fieldsValues) {
        Map<String, AttributeValue> values = new HashMap<>();

        fieldsValues.forEach((field, value) -> {
            values.put(":" + field, value);
        });

        return values;
    }

    protected Map<String, AttributeValue> getAttributeValues(Collection<Map<String,AttributeValue>> FieldsValuesMaps) {
        Map<String, AttributeValue> values = new HashMap<>();

        FieldsValuesMaps.forEach(fieldsValues ->
                values.putAll(getAttributeValues(fieldsValues))
        );
        return values;
    }

    protected String getUpdateExpression(Set<String> fields, String operator) {
        return SET + fields.stream().map(field -> addUpdateExpression(field, operator)).collect(Collectors.joining(","));
    }

    protected String getRemoveExpression(Set<String> fields) {
        return REMOVE + fields.stream().map(field -> ATTRIBUTE_NAME_PREFIX + field).collect(Collectors.joining(","));
    }

    protected String addUpdateExpression(String name, String operator) {
        return ATTRIBUTE_NAME_PREFIX + name + operator + ATTRIBUTE_VALUE_PREFIX + name;
    }

    protected void addOrDefault(String attributeName, String value, Map<String, AttributeValue> item, String defaultValue) {
        Optional.ofNullable(value)
                .filter(v -> !v.isEmpty() && !v.isBlank())
                .ifPresentOrElse(it -> item.put(attributeName, new AttributeValue().withS(it)), () -> item.put(attributeName, new AttributeValue().withS(defaultValue)));
    }

    protected void addValueIfPresent(String attributeName, String value, Map<String, AttributeValue> item) {
        Optional.ofNullable(value)
                .filter(v -> !v.isEmpty() && !v.isBlank())
                .ifPresent(it -> item.put(attributeName, new AttributeValue().withS(it)));
    }

    protected void addValueIfPresentInstant(String attributeName, Instant value, Map<String, AttributeValue> item) {
        Optional.ofNullable(value)
                .map(Instant::toString)
                .filter(v -> !v.isEmpty() && !v.isBlank())
                .ifPresent(it -> item.put(attributeName, new AttributeValue().withS(it)));
    }

    protected void addValueIfPresentB(String attributeName, Boolean value, Map<String, AttributeValue> item) {
        Optional.ofNullable(value)
                .ifPresent(it -> item.put(attributeName, new AttributeValue().withBOOL(it)));
    }

    protected void addValueIfPresentL(String attributeName, List<AttributeValue> value, Map<String, AttributeValue> item) {
        Optional.ofNullable(value)
                .ifPresent(it -> item.put(attributeName, new AttributeValue().withL(it)));
    }

    protected void addValueIfPresentSet(String attributeName, Set<AttributeValue> value, Map<String, AttributeValue> item) {
        Optional.ofNullable(value)
                .ifPresent(it -> item.put(attributeName, new AttributeValue().withL(it)));
    }

    protected void addValueIfPresentN(String attributeName, Integer value, Map<String, AttributeValue> item) {
        Optional.ofNullable(value)
                .map(String::valueOf)
                .ifPresent(it -> item.put(attributeName, new AttributeValue().withN(it)));
    }

    protected void addValueIfPresentM(String attributeName, Map<String, AttributeValue> value, Map<String, AttributeValue> item) {
        Optional.ofNullable(value).ifPresent(it -> item.put(attributeName, new AttributeValue().withM(it)));
    }

    protected String addFilterExpression(String name, String operator) {
        if (operator.equals(CONTAINS)) {
            return CONTAINS + "(" + ATTRIBUTE_NAME_PREFIX + name + "," + ATTRIBUTE_VALUE_PREFIX + name + ")";
        } else if (operator.equals(BEGINS_WITH)) {
            return BEGINS_WITH + "(" + ATTRIBUTE_NAME_PREFIX + name + "," + ATTRIBUTE_VALUE_PREFIX + name + ")";
        }
        return ATTRIBUTE_NAME_PREFIX + name + operator + ATTRIBUTE_VALUE_PREFIX + name;
    }

    protected String addFilterBetweenExpression(String attributeName, String name1, String name2) {
        return ATTRIBUTE_NAME_PREFIX + attributeName + BETWEEN + ATTRIBUTE_VALUE_PREFIX + name1 + AND
                + ATTRIBUTE_VALUE_PREFIX + name2;
    }

    protected Instant parseDate(String date) {
        return Instant.parse(date);
    }

    protected String getIfPresent(Map<String, AttributeValue> item, String key) {
        return Optional
                .ofNullable(item.get(key))
                .map(AttributeValue::getS)
                .orElse(null);
    }

    protected Map<String, AttributeValue> getIfPresentMap(Map<String, AttributeValue> item, String key) {
        return Optional
                .ofNullable(item.get(key))
                .map(AttributeValue::getM)
                .orElse(Map.of());
    }

    protected Instant getInstantIfPresent(Map<String, AttributeValue> item, String key) {
        return Optional
                .ofNullable(getIfPresent(item, key))
                .map(this::parseDate)
                .orElse(null);
    }

    protected List<AttributeValue> getIfPresentL(Map<String, AttributeValue> item, String key) {
        return Optional
                .ofNullable(item.get(key))
                .map(AttributeValue::getL)
                .orElse(new ArrayList<>());
    }

    protected Set<AttributeValue> getIfPresentSet(Map<String, AttributeValue> item, String key) {
        return Optional
                .ofNullable(item.get(key))
                .map(AttributeValue::getL)
                .map(HashSet::new)
                .orElse(new HashSet<>());
    }

    protected List<String> toStringList(Map<String, AttributeValue> item, String key) {
        return getIfPresentL(item, key)
                .stream()
                .map(AttributeValue::getS)
                .collect(toUnmodifiableList());
    }

    protected int getIfPresentN(Map<String, AttributeValue> item, String key) {
        return Optional
                .ofNullable(item.get(key))
                .map(AttributeValue::getN)
                .map(BigDecimal::new)
                .map(BigDecimal::intValue)
                .orElse(0);
    }

    protected Optional<Integer> getIfPresentNOptional(Map<String, AttributeValue> item, String key) {
        return Optional
                .ofNullable(item.get(key))
                .map(AttributeValue::getN)
                .map(BigDecimal::new)
                .map(BigDecimal::intValue);
    }

    protected int getIfPresentN(AttributeValue value) {
        return Optional.ofNullable(value)
                .map(AttributeValue::getN)
                .map(Integer::valueOf)
                .orElse(0);
    }

    protected BigDecimal getIfPresentDec(Map<String, AttributeValue> item, String key) {
        return Optional
                .ofNullable(item.get(key))
                .map(AttributeValue::getN)
                .map(BigDecimal::new)
                .orElse(BigDecimal.ZERO);
    }

    protected boolean getIfPresentBool(Map<String, AttributeValue> item, String key) {
        return Optional
                .ofNullable(item.get(key))
                .map(AttributeValue::getBOOL)
                .orElse(false);
    }

    protected BigInteger getIfPresentBI(Map<String, AttributeValue> item, String key) {
        return Optional
                .ofNullable(item.get(key))
                .map(v -> new BigInteger(v.getS()))
                .orElse(BigInteger.ZERO);
    }

    protected BigDecimal getIfPresentBD(Map<String, AttributeValue> item, String key) {
        return Optional
                .ofNullable(item.get(key))
                .map(v -> new BigDecimal(v.getS()))
                .orElse(BigDecimal.ZERO);
    }

    protected List<AttributeValue> mapList(List<String> strings) {
        return strings
                .stream()
                .map(s -> new AttributeValue().withS(s))
                .collect(toUnmodifiableList());
    }

    protected String getFilterExpression(Set<String> names) {
        return getFilterExpression(names, EQ, AND);
    }


    protected String getFilterExpression(Map<String, Map<String, AttributeValue>> operatorFieldsValues, String joiner) {
        Set<String> operatorKeys = operatorFieldsValues.keySet();
        return operatorKeys.stream().map(operator -> {
            Map<String, AttributeValue> fieldsValues = operatorFieldsValues.get(operator);
            return getFilterExpression(fieldsValues.keySet(),operator);
        }).collect(Collectors.joining(joiner));
    }

    protected String getFilterExpression(Set<String> names, String operator) {
        return getFilterExpression(names, operator, AND);
    }

    protected String getFilterExpression(Set<String> names, String operator, String joiner) {
        return names
                .stream()
                .map(name -> addFilterExpression(name, operator))
                .collect(Collectors.joining(joiner));
    }

    protected String getFilterOrExpression(String fieldName, Set<String> fieldOrValues) {
        return fieldOrValues
            .stream()
            .map(name -> ATTRIBUTE_NAME_PREFIX + fieldName + EQ + ATTRIBUTE_VALUE_PREFIX + name)
            .collect(Collectors.joining(OR));
    }

    protected <T> T mapOrThrow(Map<String, AttributeValue> item, Function<Map<String, AttributeValue>, T> function) {
        return Optional.ofNullable(item).map(function).orElseThrow(() -> new IllegalArgumentException("Item not found"));
    }
}
