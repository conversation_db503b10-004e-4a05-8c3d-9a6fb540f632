package com.rentancy.integrations.servicies.persistence;

import com.rentancy.integrations.pojos.*;
import com.rentancy.integrations.pojos.Invoice.LineItem;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Stream;

public interface InvoiceService {
    boolean autoInvoiceExists(String tenancyId, ZonedDateTime period, RentInvoiceHistory.RentHistoryType type);
    void saveInvoiceHistory(List<RentInvoiceHistory> history);
    void saveLandlordBill(LandlordBill bill);
    List<LandlordBill> findOrganisationLandlordBills(String organisationId);
    long findOrganisationLandlordBillsCount(String organisationId);

    LandlordBill getLandlordBill(String id);

    List<LandlordBill> findLandlordBills(Set<String> ids);

    void updateLandlordBill(LandlordBill landlordBill);

    Invoice getInvoice(String id);

    boolean invoiceRecordProcessed(String messageId);

    List<Account> findAccountsWithOrganisationId(String organisationId, String tenantId);

    List<Invoice> findInvoices(String organisationId, String ref);

    List<RentInvoiceHistory> findHistoryItems(String createdAfter);

    List<RentInvoiceHistory> findHistoryItemsForInvoice(String invoiceId);

    List<Invoice.LineItem> findOrganisationLineItems(String organisationId, String startDate, String endDate);

    List<Invoice.LineItem> findIncomeLineItems(String organisationId);

    List<Invoice> findOrganisationInvoicesWithingDateRange(String organisationId, String startDate, String endDate, boolean includeLineItems);

    Invoice findInvoiceWithOrganisationAndInvoiceId(String organisationId, String invoiceId);

    List<Invoice> findInvoicesWithPropertyIdAndStatus(String propertyId, List<String> statuses);

    void deletePaymentsWithOrganisationIdAndInvoiceId(String organisationId, String invoiceId);

    void deleteInvoice(Invoice invoice);

    List<String> deleteLandlordBillsWithOriginalInvoiceId(String invoiceId);

    void updateInvoice(Invoice invice, String organisationId);

    String createInvoice(Invoice invoice);

    void createAllocation(String invoiceId, String total, String propertyId, String organisationId);

    void addBillProperties(String invoiceId, Property property);

    Account findAccountsWithOrganisationIdAndAccountId(String organisationId, String accountId);

    void updateAccount(Account account);

    String createAccount(Account account);

    Transaction findTransactionWithOrganisationIdAndAccountId(String organisationId, String transactionId);

    void deleteTransaction(String transactionId);

    void updateTransaction(Transaction transaction);

    String createTransaction(Transaction transaction);

    Payment findPaymentWithOrganisationAndPaymentId(String organisation, String paymentId);

    void deletePayment(String paymentId);

    void updatePayment(Payment payment);

    String createPayment(Payment payment);

    Transfer findTransferWithOrganisationAndTransferId(String organisationId, String transferId);

    String createTransfer(Transfer transfer);

    Journal findJournalWithOrganisationAndJournalId(String organisationUd, UUID journalId);

    void updateJournal(Journal journal);

    String createJournal(Journal journal);

    void updateAccountBalance(String organisationId, String accountId, String balance);

    void createWebhookEvent(String organisationId, String invoiceId, String payload);

    void updateWarnings(String invoiceId, List<String> warnings);

    void deleteAccounts(Set<String> accountIds);

    List<LineItem> findLineItemsByTrackingName(String organisationId, String trackingName);

    Set<Invoice> getInvoices(Set<String> invoiceIds);

    Optional<OverPayment> findOverPayment(String organisation, String overPaymentId);

    void deleteOverPayment(String id);

    void createOverPayment(OverPayment overPayment);

    void updateOverPayment(OverPayment build);

    Stream<OverPayment> getOverPaymentsByBatchIds(String organisationId, Set<String> batchPaymentIds);

    Map<String, BigDecimal> getXeroIdOverPayments(String organisationId);

    void updateLineItemsFinalizedStatus(List<String> lineItemIds, boolean finalized);

    List<LineItem> getLineItems(Set<String> lineItemIds);
}
