package com.rentancy.integrations.servicies.persistence;

import com.rentancy.integrations.pojos.*;
import com.rentancy.integrations.servicies.enhanced.InvoicePropertyRepository;
import com.rentancy.integrations.servicies.enhanced.entity.InvoiceProperty;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.ListUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RequiredArgsConstructor
public class PropertyServiceImpl implements PropertyService {

    private static final Logger log = LogManager.getLogger(PropertyServiceImpl.class);

    private final PropertyRepository propertyRepository;
    private final TenancyRepository tenancyRepository;
    private final InvoicePropertyRepository invoicePropertyRepository;

    @Override
    public Property getProperty(String id) {
        return propertyRepository.getProperty(id);
    }

    @Override
    public ParentProperty getParentProperty(String id) {
        return propertyRepository.getParentProperty(id);
    }

    @Override
    public Stream<Property> getProperties(Set<String> ids) {
        return propertyRepository.getProperties(ids);
    }

    @Override
    public List<ParentProperty> getParentProperties(Set<String> ids) {
        return propertyRepository.getParentProperties(ids);
    }

    @Override
    public List<Property> findPropertiesWithOrganisation(String organisationId) {
        return propertyRepository.findByOrganisationId(organisationId);
    }

    @Override
    public List<Tenancy> findTenancies(String organisationId, String status, boolean includeSettings) {
        return tenancyRepository.findTenanciesByOrganisationIdAndStatus(organisationId, status, includeSettings);
    }

    @Override
    public List<Tenancy> findPropertyTenancies(String propertyId, boolean includeSettings) {
        return tenancyRepository.findByProperty(propertyId, includeSettings);
    }

    @Override
    public Tenancy getTenancy(String id, boolean includeSettings) {
        return includeSettings ? tenancyRepository.getTenancyWithSettings(id) : tenancyRepository.getTenancy(id);
    }

    @Override
    public List<PropertyBudget> findPropertyBudgets(String propertyId) {
        return propertyRepository.getPropertyBudgets(propertyId);
    }

    @Override
    public Optional<Property> findPropertyWithOrganisationAndReference(String organisationId, String reference) {
        return propertyRepository.findByReference(organisationId, reference).stream().findAny();
    }

    @Override
    public Optional<Tenancy> findTenancyWithOrganisationAndReference(String organisationId, String tenancyReference) {
        return tenancyRepository.findByReference(organisationId, tenancyReference);
    }

    @Override
    public List<Tenancy> findTenancies(List<String> ids, boolean includeSettings) {
        return tenancyRepository.findTenancies(ids, includeSettings);
    }

    @Override
    public void removePropertyInvoices(String propertyId, String invoiceId) {
        propertyRepository.removePropertyInvoices(propertyId, invoiceId);
    }

    @Override
    public void removeTenancyInvoices(String tenancyId, String invoiceId) {
        tenancyRepository.removeTenancyInvoices(tenancyId, invoiceId);
    }

    @Override
    public void createPropertyInvoice(String organisationId, String propertyId, String invoiceId) {
        propertyRepository.createPropertyInvoice(organisationId, propertyId, invoiceId);
    }

    @Override
    public void createTenancyInvoice(String tenancyId, String invoiceId) {
        tenancyRepository.createTenancyInvoice(tenancyId, invoiceId);
    }

    @Override
    public List<Property> findAllProperties() {
        return propertyRepository.findAllProperties();
    }

    public Collection<Property> findAllPropertiesByIds(Collection<String> ids){
        return new HashSet<>(propertyRepository.findAllByIds(ids));
    }

    @Override
    public void deletePropertyInvoiceWithInvoiceId(String invoiceId) {
        propertyRepository.deletePropertyInvoiceByInvoiceId(invoiceId);
    }

    public Map<String, List<InvoicePropertyView>> findPropertiesByInvoices(List<String> invoicesIds) {

        List<InvoiceProperty> propertiesIdsInInvoices = new ArrayList<>();
        var partitioned = ListUtils.partition(new ArrayList<>(new HashSet<>(invoicesIds)), 2);
        partitioned.forEach(partition -> propertiesIdsInInvoices.addAll(invoicePropertyRepository.getAllByInvoiceIds(partition)));

        var properitesIdsSet = propertiesIdsInInvoices.stream().map(InvoiceProperty::getInvoicePropertyPropertyId).collect(Collectors.toSet());
        var propertiesMap = findAllPropertiesByIds(properitesIdsSet).stream().collect(Collectors.toMap(Property::getId, Function.identity()));
        return propertiesIdsInInvoices.stream()
                .map(ip -> new InvoicePropertyView(ip, propertiesMap.get(ip.getInvoicePropertyPropertyId())))
                .filter(ip -> ip.getProperty() != null)
                .collect(Collectors.groupingBy(InvoicePropertyView::getInvoicePropertyInvoiceId, Collectors.toList()));
    }


    @Override
    public void deleteTenancyInvoicesWithInvoiceId(String invoiceId) {
        tenancyRepository.deleteByInvoiceId(invoiceId);
    }

    @Override
    public List<Tenancy> findAllTenancies() {
        return tenancyRepository.findAllTenancies();
    }

    @Override
    public void updateTenancySettings(List<Tenancy.Settings> updatedTenancySettings) {
        tenancyRepository.updateSettings(updatedTenancySettings);
    }

    @Override
    public List<Property> getParentPropertyProperties(String parentPropertyId) {
        return propertyRepository.getParentPropertyChildProperties(parentPropertyId);
    }

    @Override
    public void updateTenancyDepositStatus(String tenancyId, @Nullable Tenancy.DepositStatus depositTenancyStatus) {
        try {
            tenancyRepository.updateTenancyDepositStatus(tenancyId, depositTenancyStatus);
        } catch(Exception e) {
            log.error("Failed to update tenancy deposit status " + tenancyId + " " + depositTenancyStatus, e);
        }
    }

    @Override
    public void updateTenancyDepositTransferredStatus(String tenancyId, boolean transferred) {
        tenancyRepository.updateTenancyDepositTransferredStatus(tenancyId, transferred);
    }

    @Override
    public void updateTenancyDepositReturnedStatus(String tenancyId, boolean returned) {
        tenancyRepository.updateTenancyDepositReturnedStatus(tenancyId, returned);
    }
}
