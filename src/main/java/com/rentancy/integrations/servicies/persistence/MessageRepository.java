package com.rentancy.integrations.servicies.persistence;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.Message;
import com.rentancy.integrations.servicies.persistence.DDBClient;
import com.rentancy.integrations.servicies.persistence.Repository;

import java.util.HashMap;
import java.util.Map;

public class MessageRepository extends Repository {

    private static final String MESSAGE_TABLE = "MESSAGE_TABLE";
    private static final String MESSAGE_INDEX = "MESSAGE_INDEX";

    private final DDBClient ddbClient;
    private final Config config;

    public MessageRepository(DDBClient ddbClient,
                           Config config) {
        this.ddbClient = ddbClient;
        this.config = config;
    }
    
    public Message findByWhatsAppId(String id) {
        var tableName = config.getVariable(MESSAGE_TABLE);
        var indexName = config.getVariable(MESSAGE_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("whatsAppId", new AttributeValue().withS(id));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());

        var result =
                this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), null);

        return result
                .stream()
                .map(this::mapToMessage)
                .findAny()
                .orElse(null);
    }

    private Message mapToMessage(Map<String, AttributeValue> item) {
        return Message
                .builder()
                .id(getIfPresent(item, "id"))
                .status(getIfPresent(item, "status"))
                .whatsAppId(getIfPresent(item, "whatsAppId"))
                .conversationId(getIfPresent(item, "messageConversationId"))
                .organisationId(getIfPresent(item, "messageOrganisationId"))
                .build();
    }
}
