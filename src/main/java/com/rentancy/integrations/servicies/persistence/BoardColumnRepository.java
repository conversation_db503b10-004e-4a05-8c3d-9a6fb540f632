package com.rentancy.integrations.servicies.persistence;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.BoardColumn;
import lombok.RequiredArgsConstructor;

import java.util.Map;

@RequiredArgsConstructor
public class BoardColumnRepository extends Repository {
    private static final String COLUMN_TABLE = "COLUMN_TABLE";

    private final Config config;
    private final DDBClient ddbClient;

    public BoardColumn getColumn(String id) {
        var tableName = config.getVariable(COLUMN_TABLE);

        var column = this.ddbClient.getItem(tableName, id).getItem();

        return mapColumn(column);
    }

    private BoardColumn mapColumn(Map<String, AttributeValue> item) {
        return BoardColumn.builder()
                .id(getIfPresent(item, "id"))
                .name(getIfPresent(item, "name"))
                .build();
    }
}
