package com.rentancy.integrations.servicies.persistence;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.ParentProperty;
import com.rentancy.integrations.pojos.Property;
import com.rentancy.integrations.pojos.PropertyBudget;
import com.rentancy.integrations.util.mainservice.PropertyUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.Instant;
import java.util.*;
import java.util.stream.Stream;

import static com.rentancy.integrations.servicies.persistence.DDBClient.*;
import static java.util.stream.Collectors.toUnmodifiableList;

public class PropertyRepository extends Repository {

    private static final Logger log = LogManager.getLogger(PropertyRepository.class);

    private static final String PROPERTY_TABLE = "PROPERTY_TABLE";
    private static final String PARENT_PROPERTY_TABLE = "PARENT_PROPERTY_TABLE";
    private static final String INVOICE_PROPERTY_TABLE = "INVOICE_PROPERTY_TABLE";
    private static final String PROPERTY_BUDGET_TABLE = "PROPERTY_BUDGET_TABLE";

    private static final String PROPERTY_INVOICE_INDEX = "PROPERTY_INVOICE_INDEX";
    private static final String PROPERTY_INVOICE_INVOICE_INDEX = "PROPERTY_INVOICE_INVOICE_INDEX";
    private static final String PROPERTY_REFERENCE_INDEX = "PROPERTY_REFERENCE_INDEX";
    private static final String PROPERTY_BUDGET_INDEX = "PROPERTY_BUDGET_INDEX";
    private static final String PROPERTY_ORGANISATION_INDEX = "PROPERTY_ORGANISATION_INDEX";
    private static final String PROPERTY_PARENT_PROPERTY_INDEX = "PROPERTY_PARENT_PROPERTY_INDEX";
    private static final String PARENT_PROPERTY_BY_REFERENCE_INDEX = "gsi-ByReference";

    private final Config config;
    private final DDBClient ddbClient;

    public PropertyRepository(Config config, DDBClient ddbClient) {
        this.config = config;
        this.ddbClient = ddbClient;
    }

    public List<Property> findParentPropertyByReference( String reference) {
        var tableName = config.getVariable(PARENT_PROPERTY_TABLE);
        var indexName = PARENT_PROPERTY_BY_REFERENCE_INDEX;

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("reference", new AttributeValue().withS(reference));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());

        fieldsValues.put("archived", new AttributeValue().withBOOL(true));

        var archivedFilterExpression = getFilterExpression(Set.of("archived"), NE);

        var result =
                this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), archivedFilterExpression);

        return result
                .stream()
                .map(this::mapToProperty)
                .collect(toUnmodifiableList());
    }


    public List<Property> findByReference(String organisationId, String reference) {
        var tableName = config.getVariable(PROPERTY_TABLE);
        var indexName = config.getVariable(PROPERTY_REFERENCE_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("reference", new AttributeValue().withS(reference));
        fieldsValues.put("propertyOrganisationId", new AttributeValue().withS(organisationId));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());

        fieldsValues.put("archived", new AttributeValue().withBOOL(true));

        var archivedFilterExpression = getFilterExpression(Set.of("archived"), NE);

        var result =
                this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), archivedFilterExpression);

        return result
                .stream()
                .map(this::mapToProperty)
                .collect(toUnmodifiableList());
    }

    public List<Property> findByOrganisationId(String organisationId) {
        return PropertyUtils.getOrganisationProperties(organisationId);
    }

    public Property getProperty(String id) {
        return PropertyUtils.getPropertyById(id);
    }

    public Collection<Property> findAllByIds(Collection<String> ids) {
        return PropertyUtils.getProperties(new ArrayList<>(ids));

    }

    public List<Property> findAllProperties() {
        var tableName = config.getVariable(PROPERTY_TABLE);

        return ddbClient.scan(tableName)
                .stream()
                .map(this::mapToProperty)
                .collect(toUnmodifiableList());
    }

    private Property mapToProperty(Map<String, AttributeValue> item) {
        return Property
                .builder()
                .id(getIfPresent(item, "id"))
                .addressLine1(getIfPresent(item, "addressLine1"))
                .addressLine2(getIfPresent(item, "addressLine2"))
                .addressLine3(getIfPresent(item, "addressLine3"))
                .postcode(getIfPresent(item, "postcode"))
                .city(getIfPresent(item, "city"))
                .country(getIfPresent(item, "country"))
                .type(getIfPresent(item, "type"))
                .listingType(getIfPresent(item, "listingType"))
                .state(getIfPresent(item, "state"))
                .status(getIfPresent(item, "status"))
                .reference(getIfPresent(item, "reference"))
                .organisation(getIfPresent(item, "propertyOrganisationId"))
                .landlords(toUserIds(getIfPresentL(item, "landlords")))
                .managers(toUserIds(getIfPresentL(item, "managers")))
                .images(getIfPresentL(item, "images").stream().map(AttributeValue::getS).filter(Objects::nonNull).collect(toUnmodifiableList()))
                .floorPlans(getIfPresentL(item, "floorPlans").stream().map(AttributeValue::getS).filter(Objects::nonNull).collect(toUnmodifiableList()))
                .utilities(getIfPresentL(item, "utilities").stream().map(AttributeValue::getM).filter(Objects::nonNull).map(Property.Utility::fromDynamoMap).collect(toUnmodifiableList()))
                .amenities(getIfPresentL(item, "amenities").stream().map(AttributeValue::getS).filter(Objects::nonNull).collect(toUnmodifiableList()))
                .coverImage(getIfPresent(item, "coverImage"))
                .minimumBalance(getIfPresentDec(item, "minimumBalance"))
                .openingBalance(getIfPresentDec(item, "openingBalance"))
                .propertyOwnerId(getIfPresent(item,"propertyOwnerId"))
                .primaryLandlordId(getIfPresent(item, "primaryLandlordId"))
                .archived(getIfPresentBool(item, "archived"))
                .splitOwnershipEnabled(getIfPresentBool(item, "splitOwnershipEnabled"))
                .parentPropertyId(getIfPresent(item, "propertyParentPropertyEntityId"))
                .summary(getIfPresent(item, "summary"))
                .description(getIfPresent(item, "description"))
                .startDate(getIfPresent(item, "startDate"))
                .rentPeriod(getIfPresent(item, "rentPeriod"))
                .leaseTerm(getIfPresent(item, "leaseTerm"))
                .furnished(getIfPresent(item, "furnished"))
                .councilTax(getIfPresent(item, "councilTax"))
                .taxEPCRating(getIfPresent(item, "taxEPCRating"))
                .certificateNumber(getIfPresent(item, "certificateNumber"))
                .certificateExpirationDate(getIfPresent(item, "certificateExpirationDate"))
                .monthlyRent(getIfPresentN(item, "monthlyRent"))
                .securityDeposit(getIfPresentN(item, "securityDeposit"))
                .bedRooms(getIfPresentN(item, "bedRooms"))
                .bathRooms(getIfPresentN(item, "bathRooms"))
                .sqmt(getIfPresentN(item, "sqmt"))
                .build();
    }

    private List<String> toUserIds(List<AttributeValue> userIds) {
        return userIds
                .stream()
                .map(AttributeValue::getS)
                .collect(toUnmodifiableList());
    }

    public String createPropertyInvoice(String organisationId, String propertyId, String invoiceId) {
        var propertyInvoices = findPropertyInvoices(propertyId, invoiceId);
        if (!propertyInvoices.isEmpty()) {
            log.info("Property invoice item already exists - {}, {}", propertyId, invoiceId);
            return propertyInvoices.stream().findAny().map(item -> getIfPresent(item, "id")).orElse(null);
        }

        var tableName = config.getVariable(INVOICE_PROPERTY_TABLE);

        var item = new HashMap<String, AttributeValue>();
        var id = UUID.randomUUID().toString();

        item.put("id", new AttributeValue().withS(id));
        item.put("invoicePropertyInvoiceId", new AttributeValue().withS(invoiceId));
        item.put("invoicePropertyPropertyId", new AttributeValue().withS(propertyId));
        item.put("invoicePropertyOrganisationId", new AttributeValue().withS(organisationId));
        item.put("createdAt", new AttributeValue().withS(Instant.now().toString()));

        this.ddbClient.put(tableName, item);

        return id;
    }

    public void deletePropertyInvoiceByInvoiceId(String invoiceId) {
        var tableName = config.getVariable(INVOICE_PROPERTY_TABLE);
        var indexName = config.getVariable(PROPERTY_INVOICE_INVOICE_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("invoicePropertyInvoiceId", new AttributeValue().withS(invoiceId));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());

        this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), null)
                .stream()
                .findFirst()
                .map(item -> ddbClient.deleteItem(tableName, getIfPresent(item, "id")));
    }

    public List<String> findPropertyInvoices(String propertyId) {
        var tableName = config.getVariable(INVOICE_PROPERTY_TABLE);
        var indexName = config.getVariable(PROPERTY_INVOICE_INVOICE_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("invoicePropertyPropertyId", new AttributeValue().withS(propertyId));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet(), CONTAINS, OR);

        var result = this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), null);

        return result
                .stream()
                .map(item -> getIfPresent(item, "invoicePropertyInvoiceId"))
                .collect(toUnmodifiableList());
    }


    private List<Map<String, AttributeValue>> findPropertyInvoices(String propertyId, String invoiceId) {
        var tableName = config.getVariable(INVOICE_PROPERTY_TABLE);
        var indexName = config.getVariable(PROPERTY_INVOICE_INVOICE_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        var filterFieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("invoicePropertyInvoiceId", new AttributeValue().withS(invoiceId));
        filterFieldsValues.put("invoicePropertyPropertyId", new AttributeValue().withS(propertyId));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());
        var filterExpression = getFilterExpression(filterFieldsValues.keySet());

        fieldsValues.putAll(filterFieldsValues);

        return this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), filterExpression);
    }

    public void removePropertyInvoices(String propertyId, String invoiceId) {
        var tableName = config.getVariable(INVOICE_PROPERTY_TABLE);
        var result = findPropertyInvoices(propertyId, invoiceId);
        result
                .stream()
                .map(item -> getIfPresent(item, "id"))
                .forEach(id -> this.ddbClient.deleteItem(tableName, id));
    }

    List<PropertyBudget> getPropertyBudgets(String propertyId) {
        var tableName = config.getVariable(PROPERTY_BUDGET_TABLE);
        var indexName = config.getVariable(PROPERTY_BUDGET_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("propertyBudgetPropertyId", new AttributeValue().withS(propertyId));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());

        var result =
                this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), null);

        return result
                .stream()
                .map(this::mapToPropertyBudget)
                .collect(toUnmodifiableList());
    }

    private PropertyBudget mapToPropertyBudget(Map<String, AttributeValue> item) {
        return PropertyBudget.builder()
                .id(getIfPresent(item, "id"))
                .name(getIfPresent(item, "name"))
                .referenceNumber(getIfPresent(item, "referenceNumber"))
                .budgetNumber(getIfPresentN(item, "budgetNumber"))
                .references(getIfPresent(item, "references"))
                .build();
    }

    public Stream<Property> getProperties(Set<String> ids) {
        var tableName = config.getVariable(PROPERTY_TABLE);

        var keyConditions = ids.stream()
            .map(id -> Map.of("id", new AttributeValue().withS(id))).collect(toUnmodifiableList());

        return ddbClient.batchQuery(tableName, keyConditions)
            .stream()
            .map(this::mapToProperty);
    }

    public List<ParentProperty> getParentProperties(Set<String> ids) {
        if (ids.isEmpty()) {
            return List.of();
        }

        var tableName = config.getVariable(PARENT_PROPERTY_TABLE);

        var keyConditions = ids.stream()
                .map(id -> Map.of("id", new AttributeValue().withS(id))).collect(toUnmodifiableList());

        return ddbClient.batchQuery(tableName, keyConditions)
                .stream()
                .map(this::mapToParentProperty)
                .collect(toUnmodifiableList());
    }

    public ParentProperty getParentProperty(String id) {
        var tableName = config.getVariable(PARENT_PROPERTY_TABLE);

        var result = this.ddbClient.getItem(tableName, id);

        var item = result.getItem();

        return Optional.ofNullable(item).map(this::mapToParentProperty).orElse(null);
    }

    public List<Property> getParentPropertyChildProperties(String parentPropertyId) {
        var tableName = config.getVariable(PROPERTY_TABLE);
        var indexName = config.getVariable(PROPERTY_PARENT_PROPERTY_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("propertyParentPropertyEntityId", new AttributeValue().withS(parentPropertyId));
        var archivedFilterExpression = getFilterExpression(Set.of("archived"), NE);

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());
        fieldsValues.put("archived", new AttributeValue().withBOOL(true));
        log.info("Filter expression - {}", keyConditionExpression);

        var result =
                this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), archivedFilterExpression);

        return result
                .stream()
                .map(this::mapToProperty)
                .collect(toUnmodifiableList());
    }

    private ParentProperty mapToParentProperty(Map<String, AttributeValue> item) {
        return ParentProperty
                .builder()
                .id(getIfPresent(item, "id"))
                .name(getIfPresent(item, "name"))
                .type(getIfPresent(item, "type"))
                .reference(getIfPresent(item, "reference"))
                .addressLine1(getIfPresent(item, "addressLine1"))
                .city(getIfPresent(item, "city"))
                .postcode(getIfPresent(item, "postcode"))
                .country(getIfPresent(item, "country"))
                .notes(getIfPresent(item, "notes"))
                .primaryLandlordId(getIfPresent(item, "primaryLandlordId"))
                .organisationId(getIfPresent(item, "parentPropertyEntityOrganisationId"))
                .archived(getIfPresentBool(item, "archived"))
                .splitOwnershipEnabled(getIfPresentBool(item, "splitOwnershipEnabled"))
                .landlords(toUserIds(getIfPresentL(item, "landlords")))
                .managers(toUserIds(getIfPresentL(item, "managers")))
                .build();
    }
}
