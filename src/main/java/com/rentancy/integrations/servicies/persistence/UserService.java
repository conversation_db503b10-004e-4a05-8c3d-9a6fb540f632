package com.rentancy.integrations.servicies.persistence;

import com.rentancy.integrations.pojos.Invoice;
import com.rentancy.integrations.pojos.User;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface UserService {
    User findUser(String id, boolean includeAddress);

    User findUserWithEmail(String cognitoEmail);

    List<User> findOrganisationUsersWithAddresses(String organisationId);

    List<User> findOrganisationUsers(String organisationId);

    List<User> findUsers(Collection<String> ids);

    Set<String> findOrganisationUserIdsWithRole(String organisationId, String role);

    List<User> findOrganisationUsersWithRole(String organisationId, String role);

    User findUserWithCognitoId(String cognitoId);

    User findUserWithXeroId(String xeroId);

    User.UserAddress findUserPostalAddress(String userid);

    User findUserWithOptionalEmail(String email);

    String createUser(User user);

    void updateXeroDetails(String userId, String xeroId);

    void refreshUser(String userId);

    User findUserWithCompanyName(String organisation, String name);

    Map<String, User> findContactsByInvoices(List<Invoice> invoices);

    List<User> findUsersWithAddressesByOrganisation(String organisationId);

    List<User> findUsersWithoutAddressesByOrganisation(String organisationId);
}
