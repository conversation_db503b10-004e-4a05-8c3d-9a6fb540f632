package com.rentancy.integrations.servicies.persistence;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.Conversation;

import java.util.Map;
import java.util.Optional;

public class ConversationRepository extends Repository {

    private static final String CONVERSATION_TABLE = "CONVERSATION_TABLE";

    private final DDBClient ddbClient;
    private final Config config;

    public ConversationRepository(DDBClient ddbClient,
                           Config config) {
        this.ddbClient = ddbClient;
        this.config = config;
    }

    Conversation findConversation(String id) {
        String tableName = config.getVariable(CONVERSATION_TABLE);

        var result = this.ddbClient.getItem(tableName, id);

        return Optional.ofNullable(result.getItem())
                .map(this::mapToConversation)
                .orElse(null);
    }

    private Conversation mapToConversation(Map<String, AttributeValue> item) {
        return Conversation
                .builder()
                .id(getIfPresent(item, "id"))
                .organisationId(getIfPresent(item, "conversationOrganisationId"))
                .type(parseConversationType(getIfPresent(item, "type")))
                .build();
    }

    private Conversation.Type parseConversationType(String value) {
        return Optional.ofNullable(value).map(Conversation.Type::valueOf).orElse(null);
    }
}
