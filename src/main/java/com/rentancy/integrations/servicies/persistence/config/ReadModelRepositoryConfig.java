package com.rentancy.integrations.servicies.persistence.config;

import com.rentancy.integrations.config.CheckEnvVars;
import com.rentancy.integrations.config.GetConfigVariable;

import java.util.List;

public class ReadModelRepositoryConfig implements GetConfigVariable, CheckEnvVars {

    String READ_MODEL_PARENT_PROPERTY_SUMMARY_TABLE = "READ_MODEL_PARENT_PROPERTY_SUMMARY_TABLE";

    public ReadModelRepositoryConfig() {
        checkVars(List.of(READ_MODEL_PARENT_PROPERTY_SUMMARY_TABLE));
    }

    @Override
    public List<String> checkVars(List<String> varNames) {
        return getEmptyVars(varNames);
    }

    public String getReadModelParentPropertySummaryTable() {
        return getVariable(READ_MODEL_PARENT_PROPERTY_SUMMARY_TABLE);
    }
}
