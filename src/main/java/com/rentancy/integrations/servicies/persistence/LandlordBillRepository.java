package com.rentancy.integrations.servicies.persistence;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.LandlordBill;
import com.rentancy.integrations.servicies.persistence.DDBClient;
import com.rentancy.integrations.servicies.persistence.Repository;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static com.rentancy.integrations.servicies.persistence.DDBClient.EQ;
import static java.util.stream.Collectors.toUnmodifiableList;

@RequiredArgsConstructor
public class LandlordBillRepository extends Repository {
    private static final String LANDLORD_BILL_TABLE = "LANDLORD_BILL_TABLE";
    private static final String LANDLORD_BILL_ORGANISATION_INDEX = "LANDLORD_BILL_ORGANISATION_INDEX";
    private static final String LANDLORD_BILL_INVOICE_INDEX = "LANDLORD_BILL_INVOICE_INDEX";

    private final Config config;
    private final DDBClient ddbClient;

    public LandlordBill get(String id) {
        var table = config.getVariable(LANDLORD_BILL_TABLE);
        var result = this.ddbClient.getItem(table, id);

        return Optional
                .ofNullable(result.getItem())
                .map(this::mapToLandlordBill)
                .orElse(null);
    }

    public List<LandlordBill> findByOrganisationId(String organisationId) {
        var tableName = config.getVariable(LANDLORD_BILL_TABLE);
        var indexName = config.getVariable(LANDLORD_BILL_ORGANISATION_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("landlordBillOrganisationId", new AttributeValue().withS(organisationId));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());

        var result =
                this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), null);

        return result
                .stream()
                .map(this::mapToLandlordBill)
                .collect(toUnmodifiableList());
    }

    private LandlordBill mapToLandlordBill(Map<String, AttributeValue> item) {
        return LandlordBill.builder()
                .id(getIfPresent(item, "id"))
                .eventId(getIfPresent(item, "eventId"))
                .datePaid(getIfPresent(item, "datePaid"))
                .originalAmount(getIfPresentN(item, "originalAmount"))
                .propertyAddress(getIfPresent(item, "propertyAddress"))
                .propertyId(getIfPresent(item, "propertyId"))
                .tenancyReference(getIfPresent(item, "tenancyReference"))
                .billAmount(getIfPresentN(item, "billAmount"))
                .approved(getIfPresentBool(item, "approved"))
                .approvedBy(getIfPresent(item, "approvedBy"))
                .dateRaised(getIfPresent(item, "dateRaised"))
                .status(LandlordBill.LandlordBillStatus.valueOf(getIfPresent(item, "status")))
                .landlordBillOrganisationId(getIfPresent(item, "landlordBillOrganisationId"))
                .landlordId(getIfPresent(item, "landlordId"))
                .landlordName(getIfPresent(item, "landlordName"))
                .build();
    }

    public void save(LandlordBill bill) {
        var tableName = config.getVariable(LANDLORD_BILL_TABLE);
        var item = new HashMap<String, AttributeValue>();

        item.put("approved", new AttributeValue().withBOOL(bill.isApproved()));

        addValueIfPresent("id", bill.getId(), item);
        addValueIfPresent("eventId", bill.getEventId(), item);
        addValueIfPresent("propertyId", bill.getPropertyId(), item);
        addValueIfPresent("datePaid", bill.getDatePaid(), item);
        addValueIfPresentN("originalAmount", bill.getOriginalAmount(), item);
        addValueIfPresent("propertyAddress", bill.getPropertyAddress(), item);
        addValueIfPresent("tenancyReference", bill.getTenancyReference(), item);
        addValueIfPresentN("billAmount", bill.getBillAmount(), item);
        addValueIfPresent("approvedBy", bill.getApprovedBy(), item);
        addValueIfPresent("dateRaised", bill.getDateRaised(), item);
        addValueIfPresent("status", bill.getStatus().name(), item);
        addValueIfPresent("landlordBillOrganisationId", bill.getLandlordBillOrganisationId(), item);
        addValueIfPresent("landlordId", bill.getLandlordId(), item);
        addValueIfPresent("landlordName", bill.getLandlordName(), item);
        addValueIfPresent("originalInvoiceId", bill.getOriginalInvoiceId(), item);

        this.ddbClient.put(tableName, item);
    }

    void updatedBill(LandlordBill bill) {
        var tableName = config.getVariable(LANDLORD_BILL_TABLE);

        Map<String, AttributeValue> fieldsValues = new HashMap<>();

        addValueIfPresent("dateRaised", bill.getDateRaised(), fieldsValues);
        addValueIfPresent("invoiceId", bill.getInvoiceId(), fieldsValues);
        addValueIfPresent("status", Optional.ofNullable(bill.getStatus()).map(Enum::name).orElse(null), fieldsValues);

        this.ddbClient.update(tableName, bill.getId(), getUpdateExpression(fieldsValues.keySet(), EQ), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues));
    }

    List<String> deleteByOriginalInvoiceId(String originalInvoiceId) {
        var tableName = config.getVariable(LANDLORD_BILL_TABLE);
        var indexName = config.getVariable(LANDLORD_BILL_INVOICE_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("originalInvoiceId", new AttributeValue().withS(originalInvoiceId));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());

        var result =
                this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), null);

         return result.stream().map(this::mapToLandlordBill)
                .map(landlordBill -> {
                    this.ddbClient.deleteItem(tableName, landlordBill.getId());
                    return landlordBill.getId();
                }).collect(toUnmodifiableList());
    }

    public List<LandlordBill> getLandlordBills(Set<String> ids) {
        var tableName = config.getVariable(LANDLORD_BILL_TABLE);

        var keyConditions = ids.stream()
                .map(id -> Map.of("id", new AttributeValue().withS(id))).collect(toUnmodifiableList());

        return ddbClient.batchQuery(tableName, keyConditions)
                .stream()
                .map(this::mapToLandlordBill)
                .collect(toUnmodifiableList());
    }

    public long findCountOfLandlordBillsByOrganisationId(String organisationId) {
        var tableName = config.getVariable(LANDLORD_BILL_TABLE);
        var indexName = config.getVariable(LANDLORD_BILL_ORGANISATION_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("landlordBillOrganisationId", new AttributeValue().withS(organisationId));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());

        return ddbClient.queryCount(
                tableName,
                indexName,
                keyConditionExpression,
                getAttributeNames(fieldsValues),
                getAttributeValues(fieldsValues)
        );
    }
}
