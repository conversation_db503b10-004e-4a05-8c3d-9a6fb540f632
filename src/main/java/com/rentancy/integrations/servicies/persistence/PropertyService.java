package com.rentancy.integrations.servicies.persistence;

import com.rentancy.integrations.pojos.*;

import javax.annotation.Nullable;
import java.util.*;
import java.util.stream.Stream;

public interface PropertyService {
    Property getProperty(String id);
    ParentProperty getParentProperty(String id);
    Stream<Property> getProperties(Set<String> ids);
    List<ParentProperty> getParentProperties(Set<String> ids);

    List<Property> findPropertiesWithOrganisation(String organisationId);
    List<Tenancy> findTenancies(String organisationId, String status, boolean includeSettings);

    List<Tenancy> findPropertyTenancies(String propertyId, boolean includeSettings);

    Tenancy getTenancy(String id, boolean includeSettings);
    List<PropertyBudget> findPropertyBudgets(String propertyId);

    Optional<Property> findPropertyWithOrganisationAndReference(String organisationId, String reference);

    Optional<Tenancy> findTenancyWithOrganisationAndReference(String organisationId, String tenancyReference);

    List<Tenancy> findTenancies(List<String> ids, boolean includeSettings);

    void removePropertyInvoices(String sourceId, String invoiceId);

    void removeTenancyInvoices(String sourceId, String invoiceId);

    void createPropertyInvoice(String organisationId, String sourceId, String invoiceId);

    void createTenancyInvoice(String sourceId, String invoiceId);

    List<Property> findAllProperties();

    void deletePropertyInvoiceWithInvoiceId(String invoiceId);

    void deleteTenancyInvoicesWithInvoiceId(String invoiceId);

    List<Tenancy> findAllTenancies();

    void updateTenancySettings(List<Tenancy.Settings> updatedTenancySettings);

    List<Property> getParentPropertyProperties(String parentPropertyId);

    void updateTenancyDepositStatus(String tenancyId, @Nullable Tenancy.DepositStatus depositTenancyStatus);

    void updateTenancyDepositTransferredStatus(String tenancyId, boolean transferred);

    void updateTenancyDepositReturnedStatus(String tenancyId, boolean returned);

    Map<String, List<InvoicePropertyView>> findPropertiesByInvoices(List<String> invoicesIds);
}
