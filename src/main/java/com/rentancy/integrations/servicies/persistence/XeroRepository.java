package com.rentancy.integrations.servicies.persistence;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.rentancy.integrations.pojos.Invoice;
import com.rentancy.integrations.pojos.InvoiceTenancy;
import com.rentancy.integrations.pojos.Transaction;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;

abstract class XeroRepository extends Repository {
    protected static final String INVOICE_LINE_ITEM_TABLE = "INVOICE_LINE_ITEM_TABLE";
    protected static final String INVOICE_LINE_ITEM_INDEX = "INVOICE_LINE_ITEM_INDEX";
    protected static final String INVOICE_LINE_ITEM_INVOICE_INDEX = "INVOICE_LINE_ITEM_INVOICE_INDEX";
    protected static final String INVOICE_LINE_ITEM_TRANSACTION_INDEX = "INVOICE_LINE_ITEM_TRANSACTION_INDEX";
    protected static final String INVOICE_LINE_ITEM_TRACKING_NAME_INDEX = "INVOICE_LINE_ITEM_TRACKING_NAME_INDEX";


    private HashMap<String, AttributeValue> mapLineItems(String organisation, Invoice.LineItem lineItem) {
        var item = new HashMap<String, AttributeValue>();
        var id = UUID.randomUUID().toString();

        item.put("id", new AttributeValue().withS(id));
        item.put("lineItemId", new AttributeValue().withS(lineItem.getLineItemId()));
        item.put("invoiceLineItemOrganisationId", new AttributeValue().withS(organisation));
        item.put("createdAt", new AttributeValue().withS(Instant.now().toString()));

        addValueIfPresent("description", lineItem.getDescription(), item);
        addValueIfPresent("quantity", lineItem.getQuantity(), item);
        addValueIfPresent("unitAmount", lineItem.getUnitAmount(), item);
        addValueIfPresent("itemCode", lineItem.getItemCode(), item);
        addValueIfPresent("accountCode", lineItem.getAccountCode(), item);
        addValueIfPresent("taxType", lineItem.getTaxType(), item);
        addValueIfPresent("taxAmount", lineItem.getTaxAmount(), item);
        addValueIfPresent("lineAmount", lineItem.getLineAmount(), item);
        addValueIfPresent("discountRate", lineItem.getDiscountRate(), item);
        addValueIfPresent("discountAmount", lineItem.getDiscountAmount(), item);
        addValueIfPresent("trackingName", lineItem.getTrackingName(), item);

        return item;
    }

    private boolean isIncomeLineItem(Invoice.LineItem lineItem, boolean invoiceIsACCREC) {
        // If there's no amount, we don't really care, as it's not going to affect finance calculations
        if (Objects.isNull(lineItem.getLineAmount())) {
            return invoiceIsACCREC;
        }

        var isPositiveAmount = new BigDecimal(lineItem.getLineAmount()).compareTo(BigDecimal.ZERO) > 0;

        var invoiceIsACCPAY = !invoiceIsACCREC;
        var isNegativeAmount = !isPositiveAmount;
        return (invoiceIsACCREC && isPositiveAmount) || (invoiceIsACCPAY && isNegativeAmount);
    }

    protected void addInvoiceFieldsToLineItems(Invoice invoice, Invoice.LineItem lineItem, Map<String, AttributeValue> lineItems) {
        var isParentIncome = "ACCREC".equals(invoice.getType());

        var parentPaidAmount = Optional.ofNullable(invoice.getAmountPaid()).orElse("0.00");
        addValueIfPresent("parentTotal", invoice.getTotal(), lineItems);
        addValueIfPresent("parentAmountDue", invoice.getAmountDue(), lineItems);
        addValueIfPresent("parentPaidAmount", parentPaidAmount, lineItems);
        Optional.ofNullable(invoice.getDueDate()).map(Instant::toString).ifPresent(dueDate -> addValueIfPresent("parentDueDate", dueDate, lineItems));
        addValueIfPresent("parentReference", invoice.getReference(), lineItems);
        addValueIfPresent("parentStatus", invoice.getStatus(), lineItems);
        addValueIfPresent("parentDate", invoice.getDate().toString(), lineItems);
        addValueIfPresentB("taxExclusive", "Exclusive".equals(invoice.getLineAmountTypes()), lineItems);
        addValueIfPresent("parentContactId", invoice.getContactId(), lineItems);
        addValueIfPresent("parentNumber", invoice.getNumber(), lineItems);
        addValueIfPresent("parentAgainstUserXeroId", invoice.getContactId(), lineItems);
        addValueIfPresent("parentAmountCredited", Optional.ofNullable(invoice.getAmountCredited()).orElse("0.00"), lineItems);
        addValueIfPresentB("balanceTransfer", invoice.isBalanceTransfer(), lineItems);
        addValueIfPresentB("isIncome", isIncomeLineItem(lineItem, isParentIncome), lineItems);
        addValueIfPresentB("isParentIncome", isParentIncome, lineItems);
    }

    protected void addTransactionFieldsToLineItems(Transaction transaction, Invoice.LineItem lineItem, Map<String, AttributeValue> lineItems) {
        var isParentIncome = "RECEIVE".equals(transaction.getType()) || "RECEIVE-OVERPAYMENT".equals(transaction.getType());

        addValueIfPresent("parentTotal", transaction.getTotal(), lineItems);
        addValueIfPresent("parentAmountDue", "0.00", lineItems);
        addValueIfPresent("parentPaidAmount", transaction.getTotal(), lineItems);
        Optional.ofNullable(transaction.getDate()).map(Instant::toString).ifPresent(dueDate -> addValueIfPresent("parentDueDate", dueDate, lineItems));
        addValueIfPresent("parentReference", transaction.getReference(), lineItems);
        addValueIfPresent("parentStatus", transaction.getStatus(), lineItems);
        addValueIfPresent("parentDate", transaction.getDate().toString(), lineItems);
        addValueIfPresentB("taxExclusive", "Exclusive".equals(transaction.getLineAmountTypes()), lineItems);
        addValueIfPresent("parentContactId", transaction.getContactId(), lineItems);
        addValueIfPresent("parentNumber", transaction.getReference(), lineItems);
        addValueIfPresent("invoiceLineItemTransactionId", transaction.getId(), lineItems);
        addValueIfPresent("parentAgainstUserXeroId", transaction.getContactId(), lineItems);
        addValueIfPresentB("balanceTransfer", transaction.isBalanceTransfer(), lineItems);
        addValueIfPresentB("isIncome", isIncomeLineItem(lineItem, isParentIncome), lineItems);
        addValueIfPresentB("isParentIncome", isParentIncome, lineItems);
        // we can't credit amount for transactions
        addValueIfPresent("parentAmountCredited", "0.00", lineItems);
    }

    protected HashMap<String, AttributeValue> mapLineItem(Invoice invoice, String organisation, Invoice.LineItem lineItem) {
        var lineItems =  mapLineItems(organisation, lineItem);

        addInvoiceFieldsToLineItems(invoice, lineItem, lineItems);

        return lineItems;
    }

    protected HashMap<String, AttributeValue> mapLineItem(Transaction transaction, String organisation, Invoice.LineItem lineItem) {
        var lineItems =  mapLineItems(organisation, lineItem);

        addTransactionFieldsToLineItems(transaction, lineItem, lineItems);

        return lineItems;
    }

    protected InvoiceTenancy mapInvoiceTenancy(Map<String, AttributeValue> item) {
        return InvoiceTenancy.builder()
                .id(getIfPresent(item, "id"))
                .invoiceTenancyTenancyId(getIfPresent(item, "invoiceTenancyTenancyId"))
                .invoiceTenancyInvoiceId(getIfPresent(item, "invoiceTenancyInvoiceId"))
                .build();
    }

    protected Invoice.LineItem mapLineItem(Map<String, AttributeValue> item) {
        return Invoice.LineItem.builder()
                .id(getIfPresent(item, "id"))
                .invoiceId(getIfPresent(item, "invoiceLineItemInvoiceId"))
                .transactionId(getIfPresent(item, "invoiceLineItemTransactionId"))
                .lineItemId(getIfPresent(item, "lineItemId"))
                .description(getIfPresent(item, "description"))
                .quantity(getIfPresent(item, "quantity"))
                .unitAmount(getIfPresent(item, "unitAmount"))
                .itemCode(getIfPresent(item, "itemCode"))
                .accountCode(getIfPresent(item, "accountCode"))
                .taxType(getIfPresent(item, "taxType"))
                .taxAmount(getIfPresent(item, "taxAmount"))
                .lineAmount(getIfPresent(item, "lineAmount"))
                .discountRate(getIfPresent(item, "discountRate"))
                .discountAmount(getIfPresent(item, "discountAmount"))
                .createdAt(getInstantIfPresent(item, "createdAt"))
                .updatedAt(getInstantIfPresent(item, "updatedAt"))
                .trackingName(getIfPresent(item, "trackingName"))
                .parentTotal(getIfPresent(item, "parentTotal"))
                .parentAmountCredited(getIfPresent(item, "parentAmountCredited"))
                .parentAmountDue(getIfPresent(item, "parentAmountDue"))
                .parentPaidAmount(getIfPresent(item, "parentPaidAmount"))
                .parentDueDate(getIfPresent(item, "parentDueDate"))
                .parentReference(getIfPresent(item, "parentReference"))
                .parentStatus(getIfPresent(item, "parentStatus"))
                .isIncome(getIfPresentBool(item, "isIncome"))
                .isParentIncome(getIfPresentBool(item, "isParentIncome"))
                .parentDate(getIfPresent(item, "parentDate"))
                .taxExclusive(getIfPresentBool(item, "taxExclusive"))
                .parentContactId(getIfPresent(item, "parentContactId"))
                .parentNumber(getIfPresent(item, "parentNumber"))
                .parentAgainstUserXeroId(getIfPresent(item, "parentAgainstUserXeroId"))
                .balanceTransfer(getIfPresentBool(item, "balanceTransfer"))
                .build();
    }
}
