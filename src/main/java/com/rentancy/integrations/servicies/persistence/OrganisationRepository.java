package com.rentancy.integrations.servicies.persistence;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.amazonaws.services.dynamodbv2.model.GetItemResult;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.Integration;
import com.rentancy.integrations.pojos.Integration.IntegrationService;
import com.rentancy.integrations.pojos.Organisation;
import com.rentancy.integrations.pojos.Organisation.JournalPeriod;
import com.rentancy.integrations.pojos.OrganisationStripeCharge;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.rentancy.integrations.servicies.persistence.DDBClient.*;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toUnmodifiableList;

public class OrganisationRepository extends Repository {

    private static final String ORGANISATION_TABLE = "ORGANISATION_TABLE";
    private static final String ORGANISATION_STRIPE_CHARGE_TABLE = "ORGANISATION_STRIPE_CHARGE_TABLE";
    private static final String UK_RELEASE_TIME = "2023-07-10T00:00:00.000Z";

    private static final Logger log = LogManager.getLogger(OrganisationRepository.class);

    private final Config config;
    private final DDBClient ddbClient;

    public OrganisationRepository(Config config, DDBClient ddbClient) {
        this.config = config;
        this.ddbClient = ddbClient;
    }

    public Organisation getOrganisation(String id) {
        String tableName = config.getVariable(ORGANISATION_TABLE);
        GetItemResult result = this.ddbClient.getItem(tableName, id);
        var item = new HashMap<>(result.getItem());

        return mapToOrganisation(item);
    }

    public Stream<Organisation> getOrganisations(Set<String> organisationIds) {
        var tableName = config.getVariable(ORGANISATION_TABLE);

        var keyConditions = organisationIds.stream()
                .map(userId -> Map.of("id", new AttributeValue().withS(userId))).collect(toUnmodifiableList());

        return ddbClient.batchQuery(tableName, keyConditions)
                .stream()
                .map(this::mapToOrganisation);
    }
    private List<Organisation.LedgerCode> parseLedgerCodes(List<AttributeValue> ledgerCodes) {
        return ledgerCodes
                .stream()
                .map(AttributeValue::getM)
                .map(item -> new Organisation.LedgerCode(getIfPresent(item, "code"), getIfPresent(item, "displayName"), getIfPresent(item, "name")))
                .collect(Collectors.toUnmodifiableList());
    }

    private List<Organisation.TrackingCategory> parseTrackingCategories(List<AttributeValue> trackingCategories) {
        return trackingCategories
                .stream()
                .map(AttributeValue::getM)
                .map(item -> new Organisation.TrackingCategory(String.valueOf(getIfPresent(item, "name")), getIfPresent(item, "xeroName")))
                .collect(Collectors.toUnmodifiableList());
    }

    List<Organisation> findByWhatsAppAddress(String address) {
        String tableName = config.getVariable(ORGANISATION_TABLE);

        Map<String, AttributeValue> fieldsValues = new HashMap<>();
        fieldsValues.put("whatsAppAddress", new AttributeValue().withS(address));

        List<Map<String, AttributeValue>> items =
                this.ddbClient.scan(tableName, getFilterExpression(fieldsValues.keySet()), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues));

        return items
                .stream()
                .map(this::mapToOrganisation)
                .collect(toUnmodifiableList());
    }

    List<Organisation> findByJournalingOrganisations() {
        String tableName = config.getVariable(ORGANISATION_TABLE);

        Map<String, AttributeValue> fieldsValues = new HashMap<>();
        fieldsValues.put("enableJournal", new AttributeValue().withBOOL(true));
        fieldsValues.put("connectedFinanceIntegration", new AttributeValue().withS(IntegrationService.XERO.name()));

        List<Map<String, AttributeValue>> items =
                this.ddbClient.scan(tableName, getFilterExpression(fieldsValues.keySet()), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues));

        return items
                .stream()
                .map(this::mapToOrganisation)
                .collect(toUnmodifiableList());
    }

    private Organisation mapToOrganisation(Map<String, AttributeValue> item) {
        return Organisation
                .builder()
                .id(getIfPresent(item, "id"))
                .logo(getIfPresent(item, "logo"))
                .name(getIfPresent(item, "name"))
                .adminUser(getIfPresent(item, "adminUser"))
                .botUser(getIfPresent(item, "botUser"))
                .invoiceConversation(getIfPresent(item, "invoiceConversation"))
                .currency(getIfPresent(item, "currency"))
                .addressLine1(getIfPresent(item, "addressLine1"))
                .addressLine2(getIfPresent(item, "addressLine2"))
                .addressLine3(getIfPresent(item, "addressLine3"))
                .city(getIfPresent(item, "city"))
                .country(getIfPresent(item, "country"))
                .state(getIfPresent(item, "state"))
                .postcode(getIfPresent(item, "postcode"))
                .phone(getIfPresent(item, "phone"))
                .customEmail(getIfPresent(item, "customMail"))
                .website(getIfPresent(item, "website"))
                .invoiceConversation(getIfPresent(item, "invoiceConversation"))
                .reportConversation(getIfPresent(item, "reportConversation"))
                .whatsAppAddress(getIfPresent(item, "whatsAppAddress"))
                .twilioAccountSID(getIfPresent(item, "twilioAccountSID"))
                .twilioAuthToken(getIfPresent(item, "twilioAuthToken"))
                .xeroId(getIfPresent(item, "xeroId"))
                .tenancySettingsId(getIfPresent(item, "organisationTenancySettingsId"))
                .defaultCountryTaxBotId(getIfPresent(item, "defaultCountryTaxBotId"))
                .clientBatchPaymentsOnDemand(getIfPresentBool(item, "clientBatchPaymentsOnDemand"))
                .payer(getIfPresentBool(item, "payer"))
                .commissionBillVatNumber(getIfPresent(item, "commissionBillVatNumber"))
                .landlordStatementTemplateType(getIfPresent(item, "landlordStatementTemplateType"))
                .createdAt(getIfPresent(item, "createdAt"))
                .addCommissionBillVat(getIfPresentBool(item, "addCommissionBillVat"))
                .addCommissionBillVatInclusive(getIfPresentBool(item, "addCommissionBillVatInclusive"))
                .payoutStatementVersion(Optional.ofNullable(getIfPresent(item, "payoutStatementVersion")).orElse("DEFAULT"))
                .overseasResidentBillVAT(getIfPresentBool(item, "overseasResidentBillVAT"))
                .clientBatchPayments(parseClientBatchPaymentDays(item))
                .ledgerCodes(parseLedgerCodes(getIfPresentL(item, "ledgerCodes")))
                .incomeLedgerCodeNames(toStringList(item, "incomeLedgerCodeNames"))
                .invoiceTemplates(parseInvoiceTemplates(getIfPresentL(item, "invoiceTemplates")))
                .trackingCategories(parseTrackingCategories(getIfPresentL(item, "trackingCategories")))
                .connectedFinanceIntegration(Optional.ofNullable(getIfPresent(item, "connectedFinanceIntegration")).map(Integration.IntegrationService::fromValue).orElse(null))
                .balanceTransferContactUserId(getIfPresent(item, "balanceTransferContactUserId"))
                .type(Optional.ofNullable(getIfPresent(item, "type")).map(Organisation.Type::valueOf).orElse(null))
                .enableJournal(getIfPresentBool(item, "enableJournal"))
                .journalDate(getIfPresentN(item, "journalDate"))
                .depositSchemeLedgerContactUserId(getIfPresent(item, "depositSchemeLedgerContactUserId"))
                .journalPeriod(Optional.ofNullable(getIfPresent(item, "journalPeriod")).map(JournalPeriod::valueOf).orElse(null))
                .openingBalanceContactUserId(getIfPresent(item, "openingBalanceContactUserId"))
                .build();
    }

    private List<Integer> parseClientBatchPaymentDays(Map<String, AttributeValue> item) {
        if (item.get("clientBatchPayments") == null) return null;

        return item.get("clientBatchPayments").getL().stream().map(this::getIfPresentN).collect(toUnmodifiableList());
    }

    private List<Organisation.InvoiceTemplate> parseInvoiceTemplates(List<AttributeValue> invoiceTemplates) {
        return invoiceTemplates
                .stream()
                .map(AttributeValue::getM)
                .map(item -> new Organisation.InvoiceTemplate(
                        String.valueOf(getIfPresent(item, "type")),
                        getIfPresent(item, "templateName"),
                        getIfPresent(item, "rentDescription")))
                .collect(Collectors.toUnmodifiableList());
    }

    List<Organisation> findAllOrganisations() {
        var tableName = config.getVariable(ORGANISATION_TABLE);

        var items = this.ddbClient.scan(tableName);

        return items
                .stream()
                .map(this::mapToOrganisation)
                .collect(toList());
    }

    List<Organisation> findAllUnexpiredOrganisations() {
        var tableName = config.getVariable(ORGANISATION_TABLE);
        var fieldsValuesNE = new HashMap<String, AttributeValue>();
        fieldsValuesNE.put("expiredFlag", new AttributeValue().withS("EXPIRED"));
        var fieldsValuesGT = new HashMap<String, AttributeValue>();
        fieldsValuesGT.put("createdAt", new AttributeValue().withS(UK_RELEASE_TIME));
        var operatorFieldsValues = new HashMap<String, Map<String, AttributeValue>>();
        operatorFieldsValues.put(NE, fieldsValuesNE);
        operatorFieldsValues.put(GT, fieldsValuesGT);
        var items = this.ddbClient.scan(tableName, getFilterExpression(operatorFieldsValues, AND), getAttributeNames(operatorFieldsValues.values()), getAttributeValues(operatorFieldsValues.values()));
        return items
                .stream()
                .map(this::mapToOrganisation)
                .collect(toList());
    }

    public void updateXeroDetails(String id, String xeroId) {
        updateDetails(id, "xeroId", xeroId);
    }

    public void updateFinanceIntegrationType(String organisationId, String value) {
        updateDetails(organisationId, "connectedFinanceIntegration", value);
    }

    private void updateDetails(String id, String fieldName, @Nullable String fieldValue) {
        var tableName = config.getVariable(ORGANISATION_TABLE);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put(fieldName, new AttributeValue().withS(fieldValue));

        if (Objects.isNull(fieldValue)) {
            var removeExpression = getRemoveExpression(fieldsValues.keySet());

            this.ddbClient.update(tableName, id, removeExpression, getAttributeNames(fieldsValues), null);
        } else {
            this.ddbClient.update(tableName, id, getUpdateExpression(fieldsValues.keySet(), EQ), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues));
        }
    }



    public Optional<OrganisationStripeCharge> findOrganisationStripeCharge(String chargeId) {
        var tableName = config.getVariable(ORGANISATION_STRIPE_CHARGE_TABLE);
        var result = this.ddbClient.getItem(tableName, chargeId);

        return Optional.ofNullable(result.getItem()).map(this::mapToOrganisationStripeCharges);
    }

    public List<OrganisationStripeCharge> findOrganisationStripeCharges(String fromData, String toDate) {
        var tableName = config.getVariable(ORGANISATION_STRIPE_CHARGE_TABLE);
        var fieldValues = new HashMap<String, AttributeValue>();

        addValueIfPresent("from", fromData, fieldValues);
        addValueIfPresent("to", toDate, fieldValues);
        var names = Map.of("#occuredAt", "occuredAt");
        var items = this.ddbClient.scan(tableName, addFilterBetweenExpression("occuredAt", "from", "to"), names, getAttributeValues(fieldValues));
        return items
                .stream()
                .map(this::mapToOrganisationStripeCharges)
                .collect(toList());
    }

    private OrganisationStripeCharge mapToOrganisationStripeCharges(Map<String, AttributeValue> item) {
        return OrganisationStripeCharge
                .builder()
                .id(getIfPresent(item, "id"))
                .organisationId(getIfPresent(item, "organisationId"))
                .stripePaymentIntentId(getIfPresent(item, "stripePaymentIntentId"))
                .createdAt(getIfPresent(item, "createdAt"))
                .updatedAt(getIfPresent(item, "updatedAt"))
                .occuredAt(getIfPresent(item, "occuredAt"))
                .currency(getIfPresent(item, "currency"))
                .eventType(getIfPresent(item, "eventType"))
                .chargeType(getIfPresent(item, "chargeType"))
                .number(getIfPresent(item, "number"))
                .data(getIfPresent(item, "data"))
                .error(getIfPresent(item, "error"))
                .errorMessage(getIfPresent(item, "errorMessage"))
                .amount(getIfPresentN(item, "amount"))
                .propertyCount(getIfPresentN(item, "propertyCount"))
                .success(getIfPresentBool(item, "success"))
                .build();
    }

    public void updateOrganisationChargeItemNumber(String id, String number) {
        var tableName = config.getVariable(ORGANISATION_STRIPE_CHARGE_TABLE);

        var fieldValues = new HashMap<String, AttributeValue>();
        fieldValues.put("number", new AttributeValue().withS(number));

        var updateExpression = getUpdateExpression(fieldValues.keySet(), EQ);

        this.ddbClient.update(tableName, id, updateExpression, getAttributeNames(fieldValues), getAttributeValues(fieldValues));
    }

    public List<Organisation> findOrganisationsCreatedBetween(String from, String to) {
        var tableName = config.getVariable(ORGANISATION_TABLE);
        var fieldValues = new HashMap<String, AttributeValue>();

        addValueIfPresent("from", from, fieldValues);
        addValueIfPresent("to", to, fieldValues);
        var names = Map.of("#createdAt", "createdAt");
        var items = this.ddbClient.scan(tableName, addFilterBetweenExpression("createdAt", "from", "to"), names, getAttributeValues(fieldValues));
        return items
                .stream()
                .map(this::mapToOrganisation)
                .collect(toList());
    }
}
