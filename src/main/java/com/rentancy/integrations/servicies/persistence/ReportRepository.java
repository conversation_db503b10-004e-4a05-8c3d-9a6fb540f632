package com.rentancy.integrations.servicies.persistence;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.Statement;
import com.rentancy.integrations.pojos.User;
import com.rentancy.integrations.pojos.XeroInvoicesUpdateResponse;
import com.rentancy.integrations.util.JSONUtils;
import com.rentancy.integrations.util.Utils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

public class ReportRepository extends Repository {

    private static final Logger log = LogManager.getLogger(ReportRepository.class);

    private static final String STATEMENT_TABLE = "STATEMENT_TABLE";
    private static final String STATEMENT_INDEX = "STATEMENT_INDEX";
    private static final String STATEMENT_PROPERTY_INDEX = "STATEMENT_PROPERTY_INDEX";
    private static final String STATEMENT_LANDLORD_BILL_INDEX = "STATEMENT_LANDLORD_BILL_INDEX";

    private final Config config;
    private final DDBClient ddbClient;

    public ReportRepository(Config config, DDBClient ddbClient) {
        this.config = config;
        this.ddbClient = ddbClient;
    }

    public Statement get(String id) {
        var result = this.ddbClient.getItem(config.getVariable(STATEMENT_TABLE), id);

        return mapToStatement(result.getItem());
    }

    public void saveStatement(Statement statement) {
        var tableName = config.getVariable(STATEMENT_TABLE);
        var item = new HashMap<String, AttributeValue>();

        addValueIfPresent("id", statement.getId(), item);
        addValueIfPresent("statementOrganisationId", statement.getOrganisationId(), item);
        addValueIfPresent("statementPropertyId", statement.getPropertyId(), item);
        addValueIfPresent("statementClientId", statement.getClientId(), item);
        addValueIfPresent("statementLandlordBillId", statement.getLandlordBillId(), item);
        addValueIfPresent("reference", statement.getReference(), item);
        addValueIfPresent("type", statement.getType(), item);
        addValueIfPresent("fileKey", statement.getFileKey(), item);
        addValueIfPresent("finalisedBy", statement.getFinalisedBy(), item);
        addValueIfPresent("sentBy", statement.getSentBy(), item);
        addValueIfPresent("payedOutBy", statement.getPayedOutBy(), item);
        addValueIfPresent("billsUpdatedBy", statement.getBillsUpdatedBy(), item);
        addValueIfPresentInstant("from", statement.getFrom(), item);
        addValueIfPresentInstant("to", statement.getTo(), item);
        addValueIfPresentInstant("sentDate", statement.getSentDate(), item);
        addValueIfPresentInstant("payedOutDate", statement.getPayedOutDate(), item);
        addValueIfPresentInstant("billsUpdatedDate", statement.getBillsUpdatedDate(), item);
        addValueIfPresent("createdAt", Instant.now().toString(), item);
        addValueIfPresentB("sent", statement.isSent(), item);
        addValueIfPresentB("approved", statement.isApproved(), item);
        addValueIfPresentB("payedOut", statement.isPayedOut(), item);
        addValueIfPresentB("billsUpdated", statement.isBillsUpdated(), item);
        addValueIfPresentL("relatedProperties", Optional.ofNullable(statement.getRelatedProperties()).stream().flatMap(Collection::stream).map(id -> new AttributeValue().withS(id)).collect(Collectors.toUnmodifiableList()), item);
        addValueIfPresentL("relatedParentProperties", Optional.ofNullable(statement.getRelatedParentProperties()).stream().flatMap(Collection::stream).map(id -> new AttributeValue().withS(id)).collect(Collectors.toUnmodifiableList()), item);
        addValueIfPresentL("relatedLandlords", Optional.ofNullable(statement.getRelatedLandlords()).stream().flatMap(Collection::stream).map(id -> new AttributeValue().withS(id)).collect(Collectors.toUnmodifiableList()), item);

        Optional.ofNullable(statement.getBillsUpdateResult()).ifPresent(result -> {
            addValueIfPresentL("billsUpdateResult", result.stream().map(xeroInvoiceUpdateResponse -> {
                var responseItem = new HashMap<String, AttributeValue>();

                addValueIfPresent("invoiceId", xeroInvoiceUpdateResponse.getInvoiceId(), responseItem);
                addValueIfPresent("status", xeroInvoiceUpdateResponse.getStatus(), responseItem);
                addValueIfPresent("amountDue", Utils.getOrDefault(xeroInvoiceUpdateResponse.getAmountDue(), BigDecimal.ZERO).toString(), responseItem);

                Optional.ofNullable(xeroInvoiceUpdateResponse.getUser())
                        .ifPresent(user -> {
                            var userItem = new HashMap<String, AttributeValue>();
                            addValueIfPresent("id", user.getId(), userItem);
                            addValueIfPresent("xeroId", user.getXeroId(), userItem);
                            addValueIfPresent("fname", user.getFname(), userItem);
                            addValueIfPresent("sname", user.getSname(), userItem);
                            addValueIfPresent("title", user.getTitle(), userItem);
                            addValueIfPresent("companyName", user.getCompanyName(), userItem);
                            responseItem.put("user", new AttributeValue().withM(userItem));
                        });

                return new AttributeValue().withM(responseItem);
            }).collect(Collectors.toUnmodifiableList()), item);
        });

        log.info("Statement id to save - {}", JSONUtils.wrappedToJsonString(item));

        this.ddbClient.put(tableName, item);
    }

    public List<Statement> findStatements(String organisationId) {
        return this.queryStatements(STATEMENT_INDEX, "statementOrganisationId", organisationId);
    }

    public void deleteByLandlordBillId(String landlordBillId) {
        var tableName = config.getVariable(STATEMENT_TABLE);
        var indexName = config.getVariable(STATEMENT_LANDLORD_BILL_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("statementLandlordBillId", new AttributeValue().withS(landlordBillId));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());

        var result =
                this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), null);

        result.stream().map(this::mapToStatement)
                .forEach(statement -> this.ddbClient.deleteItem(tableName, statement.getId()));
    }

    private Statement mapToStatement(Map<String, AttributeValue> item) {
        return Statement
                .builder()
                .id(getIfPresent(item, "id"))
                .reference(getIfPresent(item, "reference"))
                .fileKey(getIfPresent(item, "fileKey"))
                .to(getInstantIfPresent(item, "to"))
                .sentDate(getInstantIfPresent(item, "sentDate"))
                .createdAt(getInstantIfPresent(item, "createdAt"))
                .payedOutDate(getInstantIfPresent(item, "payedOutDate"))
                .billsUpdatedDate(getInstantIfPresent(item, "billsUpdatedDate"))
                .from(getInstantIfPresent(item, "from"))
                .type(getIfPresent(item, "type"))
                .finalisedBy(getIfPresent(item, "finalisedBy"))
                .propertyId(getIfPresent(item, "statementPropertyId"))
                .clientId(getIfPresent(item, "statementClientId"))
                .organisationId(getIfPresent(item, "statementOrganisationId"))
                .landlordBillId(getIfPresent(item, "statementLandlordBillId"))
                .sent(getIfPresentBool(item, "sent"))
                .payedOut(getIfPresentBool(item, "payedOut"))
                .billsUpdated(getIfPresentBool(item, "billsUpdated"))
                .approved(getIfPresentBool(item, "approved"))
                .sentBy(getIfPresent(item, "sentBy"))
                .payedOutBy(getIfPresent(item, "payedOutBy"))
                .billsUpdatedBy(getIfPresent(item, "billsUpdatedBy"))
                .relatedProperties(getIfPresentL(item, "relatedProperties").stream().map(AttributeValue::getS).collect(Collectors.toUnmodifiableSet()))
                .relatedParentProperties(getIfPresentL(item, "relatedParentProperties").stream().map(AttributeValue::getS).collect(Collectors.toUnmodifiableSet()))
                .relatedLandlords(getIfPresentL(item, "relatedLandlords").stream().map(AttributeValue::getS).collect(Collectors.toUnmodifiableSet()))
                .billsUpdateResult(mapToBillsResult(getIfPresentL(item, "billsUpdateResult")))
                .build();
    }

    private List<XeroInvoicesUpdateResponse.XeroInvoiceUpdateResponse> mapToBillsResult(List<AttributeValue> items) {
        return items.stream().map(item -> new XeroInvoicesUpdateResponse.XeroInvoiceUpdateResponse(
                getIfPresent(item.getM(), "invoiceId"),
                getIfPresent(item.getM(), "status"),
                mapToUser(getIfPresentMap(item.getM(), "user")),
                new BigDecimal(Utils.getOrDefault(getIfPresent(item.getM(), "amountDue"), "0.00"))
        )).collect(Collectors.toUnmodifiableList());
    }

    private User mapToUser(Map<String, AttributeValue> item) {
        return Optional.ofNullable(item).map(it -> User
                .builder()
                .id(getIfPresent(it, "id"))
                .xeroId(getIfPresent(it, "xeroId"))
                .fname(getIfPresent(it, "fname"))
                .sname(getIfPresent(it, "sname"))
                .title(getIfPresent(it, "title"))
                .companyName(getIfPresent(it, "companyName"))
                .build()).orElse(null);
    }

    public List<Statement> findPropertyStatements(String propertyId) {
        return this.queryStatements(STATEMENT_PROPERTY_INDEX, "statementPropertyId", propertyId);
    }

    public List<Statement> findLandlordBillStatements(String landlordBillId) {
        return this.queryStatements(STATEMENT_LANDLORD_BILL_INDEX, "statementLandlordBillId", landlordBillId);
    }

    private List<Statement> queryStatements(String index, String queryKeyName, String queryKeyValue) {
        var tableName = config.getVariable(STATEMENT_TABLE);
        var indexName = config.getVariable(index);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put(queryKeyName, new AttributeValue().withS(queryKeyValue));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());

        log.info("Key condition expression - {}", keyConditionExpression);

        var result = this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), null);

        return result.stream().map(this::mapToStatement).collect(Collectors.toList());
    }
}
