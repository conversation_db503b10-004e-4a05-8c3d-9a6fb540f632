package com.rentancy.integrations.servicies.persistence;

import com.rentancy.integrations.pojos.Conversation;
import com.rentancy.integrations.pojos.Message;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class ConversationServiceImpl implements ConversationService {

    private final ConversationRepository conversationRepository;
    private final MessageRepository messageRepository;
    @Override
    public Conversation findConversation(String id) {
        return conversationRepository.findConversation(id);
    }

    @Override
    public Message findMessageWithWhatsAppId(String whatsAppMessageId) {
        return messageRepository.findByWhatsAppId(whatsAppMessageId);
    }
}
