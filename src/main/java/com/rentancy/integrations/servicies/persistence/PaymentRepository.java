package com.rentancy.integrations.servicies.persistence;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.Payment;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static com.rentancy.integrations.servicies.persistence.DDBClient.EQ;
import static com.rentancy.integrations.util.Utils.getInstantStringIfPresent;

public class PaymentRepository extends Repository {

    private static final Logger log = LogManager.getLogger(PaymentRepository.class);

    private static final String PAYMENT_TABLE = "PAYMENT_TABLE";
    private static final String PAYMENT_INDEX = "PAYMENT_INDEX";
    private static final String PAYMENT_ORGANISATION_INDEX = "PAYMENT_ORGANISATION_INDEX";

    private final Config config;
    private final DDBClient ddbClient;

    public PaymentRepository(Config config, DDBClient ddbClient) {
        this.config = config;
        this.ddbClient = ddbClient;
    }

    public String create(Payment payment) {
        var item = new HashMap<String, AttributeValue>();
        var id = UUID.randomUUID().toString();

        item.put("id", new AttributeValue().withS(id));
        item.put("paymentId", new AttributeValue().withS(payment.getPaymentId()));
        item.put("paymentOrganisationId", new AttributeValue().withS(payment.getOrganisationId()));
        item.put("tenantId", new AttributeValue().withS(payment.getTenant()));
        item.put("paymentAccountId", new AttributeValue().withS(payment.getAccountId()));
        item.put("invoiceId", new AttributeValue().withS(payment.getInvoiceId()));
        item.put("reconciled", new AttributeValue().withBOOL(payment.isReconciled()));
        item.put("hasAccount", new AttributeValue().withBOOL(payment.isHasAccount()));
        item.put("createdAt", new AttributeValue().withS(Instant.now().toString()));

        addValueIfPresent("batchPaymentId", payment.getBatchPaymentId(), item);
        addValueIfPresent("bankAmount", payment.getBankAmount(), item);
        addValueIfPresent("amount", payment.getAmount(), item);
        addValueIfPresent("reference", payment.getReference(), item);
        addValueIfPresent("currencyRate", payment.getCurrencyRate(), item);
        addValueIfPresent("paymentType", payment.getPaymentType(), item);
        addValueIfPresent("status", payment.getStatus(), item);
        addValueIfPresent("date", getInstantStringIfPresent(payment.getDate()), item);

        this.ddbClient.put(config.getVariable(PAYMENT_TABLE), item);
        payment.setId(id);

        return id;
    }

    public Payment findByOrganisationAndPaymentId(String organisationId, String paymentId) {
        var tableName = config.getVariable(PAYMENT_TABLE);
        var indexName = config.getVariable(PAYMENT_INDEX);
        log.info("Payment table - " + tableName);

        var fieldsValues = new HashMap<String, AttributeValue>();
        var filterFieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("paymentId", new AttributeValue().withS(paymentId));
        filterFieldsValues.put("paymentOrganisationId", new AttributeValue().withS(organisationId));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());
        var filterExpression = getFilterExpression(filterFieldsValues.keySet());

        fieldsValues.putAll(filterFieldsValues);

        var result =
                this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), filterExpression);

        return result
                .stream()
                .map(this::mapToPayment)
                .findAny()
                .orElse(null);
    }

    private Payment mapToPayment(Map<String, AttributeValue> item) {
        return Payment
                .builder()
                .id(getIfPresent(item, "id"))
                .paymentId(getIfPresent(item, "paymentId"))
                .status(getIfPresent(item, "status"))
                .reference(getIfPresent(item, "reference"))
                .paymentType(getIfPresent(item, "paymentType"))
                .organisationId(getIfPresent(item, "paymentOrganisationId"))
                .isReconciled(getIfPresentBool(item, "reconciled"))
                .hasAccount(getIfPresentBool(item, "hasAccount"))
                .date(getInstantIfPresent(item, "date"))
                .currencyRate(getIfPresent(item, "currencyRate"))
                .invoiceId(getIfPresent(item, "invoiceId"))
                .accountId(getIfPresent(item, "paymentAccountId"))
                .amount(getIfPresent(item, "amount"))
                .bankAmount(getIfPresent(item, "bankAmount"))
                .batchPaymentId(getIfPresent(item, "batchPaymentId"))
                .build();
    }

    public void update(Payment payment) {
        var tableName = config.getVariable(PAYMENT_TABLE);
        var fieldValues = new HashMap<String, AttributeValue>();

        fieldValues.put("tenantId", new AttributeValue().withS(payment.getTenant()));
        fieldValues.put("paymentAccountId", new AttributeValue().withS(payment.getAccountId()));
        fieldValues.put("invoiceId", new AttributeValue().withS(payment.getInvoiceId()));
        fieldValues.put("reconciled", new AttributeValue().withBOOL(payment.isReconciled()));
        fieldValues.put("hasAccount", new AttributeValue().withBOOL(payment.isHasAccount()));
        fieldValues.put("updatedAt", new AttributeValue().withS(Instant.now().toString()));

        addValueIfPresent("batchPaymentId", payment.getBatchPaymentId(), fieldValues);
        addValueIfPresent("bankAmount", payment.getBankAmount(), fieldValues);
        addValueIfPresent("amount", payment.getAmount(), fieldValues);
        addValueIfPresent("reference", payment.getReference(), fieldValues);
        addValueIfPresent("currencyRate", payment.getCurrencyRate(), fieldValues);
        addValueIfPresent("paymentType", payment.getPaymentType(), fieldValues);
        addValueIfPresent("status", payment.getStatus(), fieldValues);
        addValueIfPresent("date", getInstantStringIfPresent(payment.getDate()), fieldValues);

        this.ddbClient.update(tableName, payment.getId(), getUpdateExpression(fieldValues.keySet(), EQ), getAttributeNames(fieldValues), getAttributeValues(fieldValues));
    }

    public void deleteByInvoiceId(String organisationId, String invoiceId) {
        var tableName = config.getVariable(PAYMENT_TABLE);
        var indexName = config.getVariable(PAYMENT_ORGANISATION_INDEX);
        log.info("Payment table - " + tableName);

        var fieldsValues = new HashMap<String, AttributeValue>();
        var filterFieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("paymentOrganisationId", new AttributeValue().withS(organisationId));
        filterFieldsValues.put("invoiceId", new AttributeValue().withS(invoiceId));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());
        var filterExpression = getFilterExpression(filterFieldsValues.keySet());

        fieldsValues.putAll(filterFieldsValues);

        var result =
                this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), filterExpression);

        result.forEach(item -> this.ddbClient.deleteItem(tableName, getIfPresent(item, "id")));
    }

    public void deletePayment(String id) {
        ddbClient.deleteItem(config.getVariable(PAYMENT_TABLE), id);
    }
}
