package com.rentancy.integrations.servicies.persistence;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.*;
import com.rentancy.integrations.pojos.Invoice.LineItem;
import com.rentancy.integrations.pojos.Invoice.XeroInvoicePayment;
import com.rentancy.integrations.util.Utils;
import org.apache.commons.collections4.ListUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.rentancy.integrations.config.Config.*;
import static com.rentancy.integrations.servicies.persistence.DDBClient.*;
import static java.time.temporal.ChronoUnit.SECONDS;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toUnmodifiableList;

public class InvoiceRepository extends XeroRepository {

    private static final Logger log = LogManager.getLogger(InvoiceRepository.class);

    private static final String INVOICE_TABLE = "INVOICE_TABLE";
    private static final String INVOICE_ALLOCATION_TABLE = "INVOICE_ALLOCATION_TABLE";
    private static final String INVOICE_INDEX = "INVOICE_INDEX";
    private static final String INVOICE_ALLOCATION_PROPERTY_INDEX = "INVOICE_ALLOCATION_PROPERTY_INDEX";
    private static final String INVOICE_ALLOCATION_INDEX = "INVOICE_ALLOCATION_INDEX";
    private static final String INVOICE_ORGANISATION_INDEX = "INVOICE_ORGANISATION_INDEX";
    private static final String INVOICE_LINE_ITEM_ORGANISATION_INDEX = "INVOICE_LINE_ITEM_ORGANISATION_INDEX";
    private static final String INVOICE_TENANCY_TABLE = "INVOICE_TENANCY_TABLE";
    private static final String INVOICE_TENANCY_INDEX = "INVOICE_TENANCY_INDEX";

    private final Config config;
    private final DDBClient ddbClient;

    public InvoiceRepository(Config config, DDBClient ddbClient) {
        this.config = config;
        this.ddbClient = ddbClient;
    }

    private List<AttributeValue> mapInvoicePayments(List<XeroInvoicePayment> payments) {
        return payments.stream()
                .map(payment -> {
                    var paymentItem = new HashMap<String, AttributeValue>();

                    addValueIfPresent("date", Utils.getInstantStringIfPresent(payment.getDate()), paymentItem);
                    addValueIfPresent("amount", payment.getAmount(), paymentItem);
                    addValueIfPresent("paymentId", payment.getPaymentId(), paymentItem);
                    addValueIfPresent("batchPaymentId", payment.getBatchPaymentId(), paymentItem);
                    addValueIfPresent("reference", payment.getReference(), paymentItem);

                    return new AttributeValue().withM(paymentItem);
                })
                .collect(toUnmodifiableList());
    }

    public String create(Invoice invoice) {
        var item = new HashMap<String, AttributeValue>();
        var id = UUID.randomUUID().toString();
        var organisationId = invoice.getOrganisation();

        item.put("id", new AttributeValue().withS(id));
        item.put("invoiceId", new AttributeValue().withS(invoice.getInvoiceId()));
        item.put("sentToContact", new AttributeValue().withBOOL(invoice.isSentToContact()));
        item.put("hasAttachments", new AttributeValue().withBOOL(invoice.isHasAttachments()));
        item.put("createdAt", new AttributeValue().withS(Instant.now().toString()));

        addValueIfPresent("number", invoice.getNumber(), item);
        addValueIfPresent("reference", invoice.getReference(), item);
        addValueIfPresent("type", invoice.getType(), item);
        addValueIfPresent("status", invoice.getStatus(), item);
        addValueIfPresent("contactId", invoice.getContactId(), item);
        addValueIfPresent("lineAmountTypes", invoice.getLineAmountTypes(), item);
        addValueIfPresent("subTotal", invoice.getSubTotal(), item);
        addValueIfPresent("totalTax", invoice.getTotalTax(), item);
        addValueIfPresent("total", invoice.getTotal(), item);
        addValueIfPresent("amountDue", invoice.getAmountDue(), item);
        addValueIfPresent("amountPaid", invoice.getAmountPaid(), item);
        addValueIfPresent("totalDiscount", invoice.getTotalDiscount(), item);
        addValueIfPresent("amountCredited", invoice.getAmountCredited(), item);
        addValueIfPresent("currency", invoice.getCurrency(), item);
        addValueIfPresent("invoiceOrganisationId", organisationId, item);
        addValueIfPresent("tenantId", invoice.getTenant(), item);
        addValueIfPresent("contactFname", invoice.getContactFname(), item);
        addValueIfPresent("contactSname", invoice.getContactSname(), item);
        addValueIfPresent("contactCompanyName", invoice.getContactCompanyName(), item);
        addValueIfPresent("trackingOptionName", invoice.getTrackingOptionName(), item);
        addValueIfPresent("expectedPaymentDate", Utils.getInstantStringIfPresent(invoice.getExpectedPaymentDate()), item);
        addValueIfPresent("plannedPaymentDate", Utils.getInstantStringIfPresent(invoice.getPlannedPaymentDate()), item);
        addValueIfPresent("fullyPaidOnDate", Utils.getInstantStringIfPresent(invoice.getFullyPaidOnDate()), item);
        addValueIfPresent("date", Utils.getInstantStringIfPresent(invoice.getDate()), item);
        addValueIfPresent("dueDate", Utils.getInstantStringIfPresent(invoice.getDueDate()), item);
        addValueIfPresentB("balanceTransfer", invoice.isBalanceTransfer(), item);
        addValueIfPresentL("payments", mapInvoicePayments(invoice.getPayments()), item);

        var lineItems = invoice
                .getLineItems()
                .stream()
                .map(lineItem -> {
                    var attributes = mapLineItem(invoice, organisationId, lineItem);
                    attributes.put("invoiceLineItemInvoiceId", new AttributeValue().withS(id));

                    return attributes;
                })
                .collect(toUnmodifiableList());

        var items = new HashMap<String, List<HashMap<String, AttributeValue>>>();
        items.put(config.getVariable(INVOICE_TABLE), List.of(item));
        items.put(config.getVariable(INVOICE_LINE_ITEM_TABLE), lineItems);

        log.info("Items to put - " + items);
        this.ddbClient.transactPut(items);

        return id;
    }

    public void createLineItem(String organisation, Invoice invoice, LineItem lineItem) {
        var tableName = config.getVariable(INVOICE_LINE_ITEM_TABLE);

        var item = mapLineItem(invoice, organisation, lineItem);
        item.put("invoiceLineItemInvoiceId", new AttributeValue().withS(invoice.getId()));

        this.ddbClient.put(tableName, item);
    }

    public void update(Invoice invoice, String organisationId) {
        var tableName = config.getVariable(INVOICE_TABLE);
        var fieldsValues = new HashMap<String, AttributeValue>();

        fieldsValues.put("sentToContact", new AttributeValue().withBOOL(invoice.isSentToContact()));
        fieldsValues.put("hasAttachments", new AttributeValue().withBOOL(invoice.isHasAttachments()));
        fieldsValues.put("updatedAt", new AttributeValue().withS(Instant.now().toString()));

        addValueIfPresent("number", invoice.getNumber(), fieldsValues);
        addValueIfPresent("tenantId", invoice.getTenant(), fieldsValues);
        addValueIfPresent("reference", invoice.getReference(), fieldsValues);
        addValueIfPresent("type", invoice.getType(), fieldsValues);
        addValueIfPresent("status", invoice.getStatus(), fieldsValues);
        addValueIfPresent("contactId", invoice.getContactId(), fieldsValues);
        addValueIfPresent("contactFname", invoice.getContactFname(), fieldsValues);
        addValueIfPresent("contactSname", invoice.getContactSname(), fieldsValues);
        addValueIfPresent("contactCompanyName", invoice.getContactCompanyName(), fieldsValues);
        addValueIfPresent("lineAmountTypes", invoice.getLineAmountTypes(), fieldsValues);
        addValueIfPresent("subTotal", invoice.getSubTotal(), fieldsValues);
        addValueIfPresent("totalTax", invoice.getTotalTax(), fieldsValues);
        addValueIfPresent("total", invoice.getTotal(), fieldsValues);
        addValueIfPresent("amountDue", invoice.getAmountDue(), fieldsValues);
        addValueIfPresent("amountPaid", invoice.getAmountPaid(), fieldsValues);
        addValueIfPresent("totalDiscount", invoice.getTotalDiscount(), fieldsValues);
        addValueIfPresent("amountCredited", invoice.getAmountCredited(), fieldsValues);
        addValueIfPresent("currency", invoice.getCurrency(), fieldsValues);
        addValueIfPresent("trackingOptionName", invoice.getTrackingOptionName(), fieldsValues);
        addValueIfPresent("expectedPaymentDate", Utils.getInstantStringIfPresent(invoice.getExpectedPaymentDate()), fieldsValues);
        addValueIfPresent("plannedPaymentDate", Utils.getInstantStringIfPresent(invoice.getPlannedPaymentDate()), fieldsValues);
        addValueIfPresent("fullyPaidOnDate", Utils.getInstantStringIfPresent(invoice.getFullyPaidOnDate()), fieldsValues);
        addValueIfPresent("date", Utils.getInstantStringIfPresent(invoice.getDate()), fieldsValues);
        addValueIfPresent("dueDate", Utils.getInstantStringIfPresent(invoice.getDueDate()), fieldsValues);
        addValueIfPresentL("warnings", Utils.getOrEmpty(invoice.getWarnings()).stream().map(warning -> new AttributeValue().withS(warning)).collect(toUnmodifiableList()), fieldsValues);
        addValueIfPresentL("payments", mapInvoicePayments(invoice.getPayments()), fieldsValues);
        addValueIfPresentB("balanceTransfer", invoice.isBalanceTransfer(), fieldsValues);

        var invoiceLineItems = findLineItems(invoice.getId());

        invoice.getLineItems().forEach(lineItem -> invoiceLineItems
                .stream()
                .filter(lineItem1 -> lineItem.getLineItemId().equals(lineItem1.getLineItemId()))
                .findAny()
                .ifPresentOrElse(lineItem1 -> {
                    updateLineItem(invoice, lineItem.toBuilder().id(lineItem1.getId()).build());
                    log.info("Line item updated - {}", lineItem1);
                }, () -> {
                    createLineItem(organisationId, invoice, lineItem);
                    log.info("Line item created - {}", lineItem);
                }));

        invoiceLineItems
                .stream()
                .filter(lineItem1 -> invoice.getLineItems().stream().noneMatch(lineItem -> lineItem.getLineItemId().equals(lineItem1.getLineItemId())))
                .forEach(lineItem1 -> ddbClient.deleteItem(config.getVariable(INVOICE_LINE_ITEM_TABLE), lineItem1.getId()));

        this.ddbClient.update(tableName, invoice.getId(), getUpdateExpression(fieldsValues.keySet(), EQ), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues));
    }

    private void updateLineItem(Invoice invoice, LineItem lineItem) {
        var tableName = config.getVariable(INVOICE_LINE_ITEM_TABLE);
        var fieldValues = new HashMap<String, AttributeValue>();

        addValueIfPresent("description", lineItem.getDescription(), fieldValues);
        addValueIfPresent("quantity", lineItem.getQuantity(), fieldValues);
        addValueIfPresent("unitAmount", lineItem.getUnitAmount(), fieldValues);
        addValueIfPresent("itemCode", lineItem.getItemCode(), fieldValues);
        addValueIfPresent("accountCode", lineItem.getAccountCode(), fieldValues);
        addValueIfPresent("taxType", lineItem.getTaxType(), fieldValues);
        addValueIfPresent("taxAmount", lineItem.getTaxAmount(), fieldValues);
        addValueIfPresent("lineAmount", lineItem.getLineAmount(), fieldValues);
        addValueIfPresent("discountRate", lineItem.getDiscountRate(), fieldValues);
        addValueIfPresent("discountAmount", lineItem.getDiscountAmount(), fieldValues);
        addValueIfPresent("trackingName", lineItem.getTrackingName(), fieldValues);

        addInvoiceFieldsToLineItems(invoice, lineItem, fieldValues);

        var attributeNames = getAttributeNames(fieldValues);
        var updateExpression = getUpdateExpression(fieldValues.keySet(), EQ);

        if (Objects.isNull(lineItem.getTrackingName())) {
            updateExpression += " " + getRemoveExpression(Set.of("trackingName"));
            attributeNames.putAll(getAttributeNames(Set.of("trackingName")));
        }

        this.ddbClient.update(tableName, lineItem.getId(), updateExpression, attributeNames, getAttributeValues(fieldValues));
    }

    public Invoice getInvoice(String id) {
        var tableName = config.getVariable(INVOICE_TABLE);

        var result = this.ddbClient.getItem(tableName, id);

        return mapOrThrow(result.getItem(), this::mapToInvoiceWithLineItems);
    }

    public Set<Invoice> getInvoices(Set<String> ids) {
        var tableName = config.getVariable(INVOICE_TABLE);

        var keyConditions = ids.stream()
                .map(userId -> Map.of("id", new AttributeValue().withS(userId))).collect(toUnmodifiableList());

        return ddbClient.batchQuery(tableName, keyConditions)
                .stream()
                .map(this::mapToInvoice)
                .collect(Collectors.toUnmodifiableSet());
    }

    public Invoice findByOrganisationAndInvoiceId(String organisationId, String invoiceId) {
        var tableName = config.getVariable(INVOICE_TABLE);
        var indexName = config.getVariable(INVOICE_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        var filterFieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("invoiceId", new AttributeValue().withS(invoiceId));
        filterFieldsValues.put("invoiceOrganisationId", new AttributeValue().withS(organisationId));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());
        var filterExpression = getFilterExpression(filterFieldsValues.keySet());

        log.info("Key condition expression - {}", keyConditionExpression);
        log.info("Filter expression - {}", filterExpression);

        fieldsValues.putAll(filterFieldsValues);

        var result =
                this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), filterExpression);

        return result
                .stream()
                .map(this::mapToInvoiceWithLineItems)
                .findAny()
                .orElse(null);
    }

    public List<Invoice> findByOrganisationIdAndReference(String organisationId, String reference) {
        var tableName = config.getVariable(INVOICE_TABLE);
        var indexName = config.getVariable(INVOICE_ORGANISATION_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        var filterFieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("invoiceOrganisationId", new AttributeValue().withS(organisationId));
        filterFieldsValues.put("reference", new AttributeValue().withS(reference));
        filterFieldsValues.put("number", new AttributeValue().withS(reference));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());
        var filterExpression = getFilterExpression(filterFieldsValues.keySet(), EQ, OR);

        log.info("Key condition expression - {}", keyConditionExpression);
        log.info("Filter expression - {}", filterExpression);

        fieldsValues.putAll(filterFieldsValues);

        var result =
                this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), filterExpression);

        return result
                .stream()
                .map(this::mapToInvoiceWithLineItems)
                .collect(toUnmodifiableList());
    }

    private String nextDay(String date) {
        return OffsetDateTime.parse(date).toInstant()
                .plus(1, ChronoUnit.DAYS)
                .minus(1, SECONDS)
                .toString();
    }

    public List<Invoice> findByOrganisationIdAndDate(String organisationId, @Nullable String startDate, @Nullable String endDate, boolean includeLineItems) {
        var tableName = config.getVariable(INVOICE_TABLE);
        var indexName = config.getVariable(INVOICE_ORGANISATION_INDEX);


        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("invoiceOrganisationId", new AttributeValue().withS(organisationId));

        var queryExpression = getFilterExpression(fieldsValues.keySet());
        var attributeNames = new HashSet<String>();
        attributeNames.add("invoiceOrganisationId");

        if (startDate != null && endDate != null) {
            attributeNames.add("date");
            queryExpression = queryExpression.concat(AND).concat(addFilterBetweenExpression("date", "date1", "date2"));
            fieldsValues.put("date1", new AttributeValue().withS(startDate));
            fieldsValues.put("date2", new AttributeValue().withS(nextDay(endDate)));
        } else if (startDate != null) {
            attributeNames.add("date");
            queryExpression = queryExpression.concat(AND).concat(getFilterExpression(Set.of("date"), GT));
            fieldsValues.put("date", new AttributeValue().withS(startDate));
        } else if (endDate != null) {
            attributeNames.add("date");
            queryExpression = queryExpression.concat(AND).concat(getFilterExpression(Set.of("date"), LS));
            fieldsValues.put("date", new AttributeValue().withS(nextDay(endDate)));
        }

        fieldsValues.put("status", new AttributeValue().withS("VOIDED"));
        attributeNames.add("status");
        var filterExpression = getFilterExpression(Set.of("status"), NE);
        log.info("Invoice query resulted in {} {}", queryExpression, fieldsValues);

        var result =
                this.ddbClient.query(tableName, indexName, queryExpression, getAttributeNames(attributeNames), getAttributeValues(fieldsValues), filterExpression);

        var invoices = result.stream().map(this::mapToInvoice).collect(Collectors.toUnmodifiableList());

        if (includeLineItems) {
            var lineItems = findLineItemsByOrganisationId(organisationId, null, null);

            invoices.forEach(invoice -> {
                var relatedLineItems = lineItems.stream()
                        .filter(lineItem -> invoice.getId().equals(lineItem.getInvoiceId()))
                        .collect(toUnmodifiableList());

                invoice.setLineItems(relatedLineItems);
            });
        }

        return invoices;
    }

    public InvoiceTenancy findInvoiceTenancyByInvoiceId(String invoiceId) {
        var tableName = config.getVariable(INVOICE_TENANCY_TABLE);
        var indexName = config.getVariable(INVOICE_TENANCY_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("invoiceTenancyInvoiceId", new AttributeValue().withS(invoiceId));

        var result =
                this.ddbClient.query(tableName, indexName, getFilterExpression(fieldsValues.keySet()), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), null);

        return result
                .stream()
                .map(this::mapInvoiceTenancy)
                .findAny()
                .orElse(null);
    }

    List<LineItem> findLineItemsByOrganisationId(String organisationId, @Nullable String invoiceStartDate, @Nullable String invoiceEndDate) {
        var tableName = config.getVariable(INVOICE_LINE_ITEM_TABLE);
        var indexName = config.getVariable(INVOICE_LINE_ITEM_ORGANISATION_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("invoiceLineItemOrganisationId", new AttributeValue().withS(organisationId));

        var queryExpression = getFilterExpression(fieldsValues.keySet());
        var attributeNames = new HashSet<String>();
        attributeNames.add("invoiceLineItemOrganisationId");

        if (invoiceStartDate != null && invoiceEndDate != null) {
            queryExpression = queryExpression.concat(AND).concat(addFilterBetweenExpression("parentDate", "date1", "date2"));
            fieldsValues.put("date1", new AttributeValue().withS(invoiceStartDate));
            fieldsValues.put("date2", new AttributeValue().withS(nextDay(invoiceEndDate)));

            log.info("line item query=" + invoiceStartDate + " " + nextDay(invoiceEndDate) + " " + organisationId);
            attributeNames.add("parentDate");
        } else if (invoiceStartDate != null) {
            queryExpression = queryExpression.concat(AND).concat(getFilterExpression(Set.of("parentDate"), GT));
            fieldsValues.put("parentDate", new AttributeValue().withS(invoiceStartDate));
            attributeNames.add("parentDate");
        } else if (invoiceEndDate != null) {
            queryExpression = queryExpression.concat(AND).concat(getFilterExpression(Set.of("parentDate"), LS));
            fieldsValues.put("parentDate", new AttributeValue().withS(nextDay(invoiceEndDate)));
            attributeNames.add("parentDate");
        }

        var result =
                this.ddbClient.query(tableName, indexName, queryExpression, getAttributeNames(attributeNames), getAttributeValues(fieldsValues), null);

        return result
                .stream()
                .map(this::mapLineItem)
                .collect(toUnmodifiableList());
    }

    public List<LineItem> findIncomeLineItems(String organisationId) {
        var tableName = config.getVariable(INVOICE_LINE_ITEM_TABLE);
        var indexName = config.getVariable(INVOICE_LINE_ITEM_ORGANISATION_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("invoiceLineItemOrganisationId", new AttributeValue().withS(organisationId));
        var queryExpression = getFilterExpression(fieldsValues.keySet());
        fieldsValues.put("isIncome", new AttributeValue().withBOOL(true));

        var attributeNames = Set.of("invoiceLineItemOrganisationId", "accountCode", "trackingName", "isIncome");
        var filterExpression = "attribute_exists(#accountCode) AND attribute_exists(#trackingName) AND #isIncome = :isIncome";

        var result =
                this.ddbClient.query(tableName, indexName, queryExpression, getAttributeNames(attributeNames), getAttributeValues(fieldsValues), filterExpression);

        return result
                .stream()
                .map(this::mapLineItem)
                .collect(toUnmodifiableList());
    }

    List<LineItem> findLineItems(String invoiceId) {
        var tableName = config.getVariable(INVOICE_LINE_ITEM_TABLE);
        var indexName = config.getVariable(INVOICE_LINE_ITEM_INVOICE_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("invoiceLineItemInvoiceId", new AttributeValue().withS(invoiceId));

        var result =
                this.ddbClient.query(tableName, indexName, getFilterExpression(fieldsValues.keySet()), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), null);

        return result
                .stream()
                .map(this::mapLineItem)
                .collect(toUnmodifiableList());
    }

    List<LineItem> findLineItemsByTrackingName(String organisationId, String trackingName) {
        var tableName = config.getVariable(INVOICE_LINE_ITEM_TABLE);
        var indexName = config.getVariable(INVOICE_LINE_ITEM_TRACKING_NAME_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("trackingName", new AttributeValue().withS(trackingName));
        fieldsValues.put("invoiceLineItemOrganisationId", new AttributeValue().withS(organisationId));

        var result =
                this.ddbClient.query(tableName, indexName, getFilterExpression(fieldsValues.keySet()), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), null);

        return result
                .stream()
                .map(this::mapLineItem)
                .collect(toUnmodifiableList());
    }

    public List<Allocation> findAllocationsByPropertyId(String propertyId) {
        return findAllocations("invoiceAllocationPropertyId", propertyId, INVOICE_ALLOCATION_PROPERTY_INDEX);
    }

    private List<Allocation> findInvoiceAllocations(String invoiceId) {
        return findAllocations("invoiceAllocationInvoiceId", invoiceId, INVOICE_ALLOCATION_INDEX);
    }

    private List<Allocation> findAllocations(String keyName, String keyValue, String index) {
        var tableName = config.getVariable(INVOICE_ALLOCATION_TABLE);
        var indexName = config.getVariable(index);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put(keyName, new AttributeValue().withS(keyValue));

        var result =
                this.ddbClient.query(tableName, indexName, getFilterExpression(fieldsValues.keySet()), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), null);

        return result
                .stream()
                .map(this::mapToAllocation)
                .collect(toUnmodifiableList());
    }

    private Allocation mapToAllocation(Map<String, AttributeValue> item) {
        return Allocation
                .builder()
                .id(getIfPresent(item, "id"))
                .amount(getIfPresent(item, "amount"))
                .description(getIfPresent(item, "description"))
                .allocationDate(getInstantIfPresent(item, "allocationDate"))
                .propertyId(getIfPresent(item, "invoiceAllocationPropertyId"))
                .tenancyId(getIfPresent(item, "invoiceAllocationTenancyId"))
                .invoiceId(getIfPresent(item, "invoiceAllocationInvoiceId"))
                .build();
    }

    private Invoice mapToInvoiceWithLineItems(Map<String, AttributeValue> item) {
        var invoice = mapToInvoice(item);

        invoice.setLineItems(findLineItems(invoice.getId()));

        return invoice;
    }

    private Invoice mapToInvoice(Map<String, AttributeValue> item) {
        var payments = getIfPresentL(item, "payments")
                .stream()
                .map(AttributeValue::getM)
                .map(paymentItem -> XeroInvoicePayment.builder()
                        .date(getInstantIfPresent(paymentItem, "date"))
                        .amount(getIfPresent(paymentItem, "amount"))
                        .paymentId(getIfPresent(paymentItem, "paymentId"))
                        .batchPaymentId(getIfPresent(paymentItem, "batchPaymentId"))
                        .reference(getIfPresent(paymentItem, "reference"))
                        .build())
                .collect(toUnmodifiableList());

        return Invoice
                .builder()
                .id(getIfPresent(item, "id"))
                .invoiceId(getIfPresent(item, "invoiceId"))
                .number(getIfPresent(item, "number"))
                .reference(getIfPresent(item, "reference"))
                .type(getIfPresent(item, "type"))
                .status(getIfPresent(item, "status"))
                .contactId(getIfPresent(item, "contactId"))
                .contactFname(getIfPresent(item, "contactFname"))
                .contactSname(getIfPresent(item, "contactSname"))
                .contactCompanyName(getIfPresent(item, "contactCompanyName"))
                .lineAmountTypes(getIfPresent(item, "lineAmountTypes"))
                .subTotal(getIfPresent(item, "subTotal"))
                .totalTax(getIfPresent(item, "totalTax"))
                .total(getIfPresent(item, "total"))
                .amountDue(getIfPresent(item, "amountDue"))
                .amountPaid(getIfPresent(item, "amountPaid"))
                .totalDiscount(getIfPresent(item, "totalDiscount"))
                .amountCredited(getIfPresent(item, "amountCredited"))
                .currency(getIfPresent(item, "currency"))
                .organisation(getIfPresent(item, "invoiceOrganisationId"))
                .sentToContact(getIfPresentBool(item, "sentToContact"))
                .hasAttachments(getIfPresentBool(item, "hasAttachments"))
                .expectedPaymentDate(getInstantIfPresent(item, "expectedPaymentDate"))
                .plannedPaymentDate(getInstantIfPresent(item, "plannedPaymentDate"))
                .fullyPaidOnDate(getInstantIfPresent(item, "fullyPaidOnDate"))
                .date(getInstantIfPresent(item, "date"))
                .dueDate(getInstantIfPresent(item, "dueDate"))
                .createdAt(getInstantIfPresent(item, "createdAt"))
                .tenant(getIfPresent(item, "tenantId"))
                .updatedAt(getInstantIfPresent(item, "updatedAt"))
                .title(getIfPresent(item, "title"))
                .trackingOptionName(getIfPresent(item, "trackingOptionName"))
                .properties(getIfPresentL(item, "properties").stream().map(AttributeValue::getS).collect(toUnmodifiableList()))
                .payments(payments)
                .balanceTransfer(getIfPresentBool(item, "balanceTransfer"))
                .build();
    }

    public String createAllocation(String invoiceId, String amount, String propertyId, String organisationId) {
        var allocations = findInvoiceAllocations(invoiceId);
        var propertyAllocation = allocations
                .stream()
                .filter(allocation -> allocation.getPropertyId().equals(propertyId))
                .findAny();
        if (propertyAllocation.isPresent()) {
            log.info("Property allocation already exists - {}, {}", propertyId, invoiceId);
            return propertyAllocation.get().getId();
        }
        var tableName = config.getVariable(INVOICE_ALLOCATION_TABLE);

        var item = new HashMap<String, AttributeValue>();
        var id = UUID.randomUUID().toString();

        item.put("id", new AttributeValue().withS(id));
        item.put("__typename", new AttributeValue().withS("InvoiceAllocation"));
        item.put("amount", new AttributeValue().withS(amount));
        item.put("invoiceAllocationInvoiceId", new AttributeValue().withS(invoiceId));
        item.put("invoiceAllocationPropertyId", new AttributeValue().withS(propertyId));
        item.put("invoiceAllocationOrganisationId", new AttributeValue().withS(organisationId));
        item.put("description", new AttributeValue().withS("Property allocation"));
        item.put("allocationDate", new AttributeValue().withS(Instant.now().toString()));

        log.info("Creating allocation {}", item);

        this.ddbClient.put(tableName, item);

        return id;
    }

    public void saveInvoiceHistory(List<RentInvoiceHistory> entries) {
        var tableName = config.getVariable(RENT_INVOICE_HISTORY_TABLE);

        var items = entries.stream().map(entry -> {
            var item = new HashMap<String, AttributeValue>();

            var id = entry.getId() == null ? UUID.randomUUID().toString() : entry.getId();

            item.put("id", new AttributeValue().withS(id));

            if (entry.getXeroId() != null) {
                item.put("xeroId", new AttributeValue().withS(entry.getXeroId()));
            }

            item.put("createdAt", new AttributeValue().withS(Instant.now().toString()));
            item.put("type", new AttributeValue().withS(entry.getType().name()));
            item.put("successful", new AttributeValue().withBOOL(entry.isSuccessful()));

            addValueIfPresent("rentInvoiceHistoryPropertyId", entry.getPropertyId(), item);
            addValueIfPresent("rentInvoiceHistoryOrganisationId", entry.getOrganisationId(), item);
            addValueIfPresent("againstUser", entry.getAgainstUser(), item);
            addValueIfPresent("message", entry.getMessage(), item);
            addValueIfPresent("rentInvoiceHistoryTenancyId", entry.getTenancyId(), item);
            addValueIfPresent("periodFromDate", entry.getPeriodFromDate(), item);
            addValueIfPresent("periodEndDate", entry.getPeriodEndDate(), item);
            addValueIfPresent("tenantId", entry.getTenancyId(), item);
            addValueIfPresentB("manual", entry.isManual(), item);

            log.info("Creating invoice history entry {}", item);

            return item;
        }).collect(toUnmodifiableList());

        if (items.isEmpty()) {
            log.info("Empty payload when trying to save invoice history items");
            return;
        }

        // DynamoDB transact put can save up to 25 items in one call, so have to split it into chunks
        var batches = ListUtils.partition(items, 24);

        batches.forEach(batch -> {
            this.ddbClient.transactPut(Map.of(tableName, batch));
        });
    }

    public List<RentInvoiceHistory> findHistoryItems(String tenancyId, ZonedDateTime time, RentInvoiceHistory.RentHistoryType type) {
        var tableName = config.getVariable(RENT_INVOICE_HISTORY_TABLE);
        var indexName = config.getVariable(RENT_INVOICE_HISTORY_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("rentInvoiceHistoryTenancyId", new AttributeValue().withS(tenancyId));

        var queryExpression = getFilterExpression(fieldsValues.keySet());
        var attributeNames = new HashSet<String>();
        attributeNames.add("periodFromDate");
        attributeNames.add("periodEndDate");
        attributeNames.add("rentInvoiceHistoryTenancyId");
        attributeNames.add("type");
        attributeNames.add("successful");

        queryExpression = queryExpression
                .concat(AND)
                .concat(getFilterExpression(Set.of("periodFromDate"), LE));

        // In query we will end up with #periodFromDate >= :periodFromDate and in filter #periodEndDate <= :periodEndDate
        // We can't use 'Between' operator here because of dynamodb limitation: attribute has to be between <= and >= operators
        fieldsValues.put("periodFromDate", new AttributeValue().withS(time.toInstant().toString()));
        fieldsValues.put("periodEndDate", new AttributeValue().withS(time.toInstant().toString()));

        var filterFieldsValues = new HashMap<String, AttributeValue>();
        filterFieldsValues.put("successful", new AttributeValue().withBOOL(true));
        filterFieldsValues.put("type", new AttributeValue().withS(type.name()));

        fieldsValues.putAll(filterFieldsValues);

        var filterExpression = getFilterExpression(filterFieldsValues.keySet())
                .concat(AND)
                .concat(getFilterExpression(Set.of("periodEndDate"), GE));

        var result =
                this.ddbClient.query(tableName, indexName, queryExpression, getAttributeNames(attributeNames), getAttributeValues(fieldsValues), filterExpression);

        return result
                .stream()
                .map(this::mapToHistory)
                .collect(toUnmodifiableList());
    }

    public List<RentInvoiceHistory> findHistoryItems(String createdAfter) {
        var tableName = config.getVariable(RENT_INVOICE_HISTORY_TABLE);

        var filterFieldsValues = new HashMap<String, AttributeValue>();
        filterFieldsValues.put("createdAt", new AttributeValue().withS(createdAfter));

        var filterExpression = getFilterExpression(filterFieldsValues.keySet(), BEGINS_WITH);

        log.info("Filter expression - {}", filterExpression);

        List<Map<String, AttributeValue>> result = this.ddbClient.scan(
                tableName,
                filterExpression,
                getAttributeNames(filterFieldsValues),
                getAttributeValues(filterFieldsValues));

        return result
                .stream()
                .map(this::mapToHistory)
                .collect(toUnmodifiableList());
    }

    public List<RentInvoiceHistory> findHistoryItemsByXeroInvoiceId(String xeroInvoiceId) {
        var tableName = config.getVariable(RENT_INVOICE_HISTORY_TABLE);
        var indexName = config.getVariable(RENT_INVOICE_HISTORY_XERO_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("xeroId", new AttributeValue().withS(xeroInvoiceId));

        var result =
                this.ddbClient.query(tableName, indexName, getFilterExpression(fieldsValues.keySet()), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), null);

        return result
                .stream()
                .map(this::mapToHistory)
                .collect(toUnmodifiableList());
    }

    private RentInvoiceHistory mapToHistory(Map<String, AttributeValue> item) {
        return RentInvoiceHistory
                .builder()
                .id(getIfPresent(item, "id"))
                .organisationId(getIfPresent(item, "rentInvoiceHistoryOrganisationId"))
                .message(getIfPresent(item, "message"))
                .successful(getIfPresentBool(item, "successful"))
                .tenancyId(getIfPresent(item, "rentInvoiceHistoryTenancyId"))
                .propertyId(getIfPresent(item, "rentInvoiceHistoryPropertyId"))
                .type(Optional.ofNullable(getIfPresent(item, "type")).map(RentInvoiceHistory.RentHistoryType::valueOf).orElse(null))
                .againstUser(getIfPresent(item, "againstUser"))
                .xeroId(getIfPresent(item, "xeroId"))
                .periodFromDate(getIfPresent(item, "periodFromDate"))
                .periodEndDate(getIfPresent(item, "periodEndDate"))
                .createdAt(getIfPresent(item, "createdAt"))
                .manual(getIfPresentBool(item, "manual"))
                .build();
    }

    public boolean invoiceRecordProcessed(String messageId) {
        var tableName = config.getVariable(RENT_INVOICE_HISTORY_TABLE);

        var result = this.ddbClient.getItem(tableName, messageId);

        return result.getItem() != null;
    }

    void deleteInvoice(Invoice item) {
        ddbClient.deleteItem(config.getVariable(INVOICE_TABLE), item.getId());

        item.getLineItems().forEach(lineItem -> ddbClient.deleteItem(config.getVariable(INVOICE_LINE_ITEM_TABLE), lineItem.getId()));
    }

    public void addBillProperties(String invoiceId, List<Property> properties) {
        var tableName = config.getVariable(INVOICE_TABLE);
        var fieldsValues = new HashMap<String, AttributeValue>();

        var invoice = this.getInvoice(invoiceId);

        var propertyIds = properties.stream().map(Property::getId).collect(toList());

        propertyIds.addAll(Optional.ofNullable(invoice).map(Invoice::getProperties).orElse(new ArrayList<>()));

        addValueIfPresentL("properties", new HashSet<>(propertyIds).stream().map(pId -> new AttributeValue().withS(pId)).collect(toUnmodifiableList()), fieldsValues);
        fieldsValues.put("updatedAt", new AttributeValue().withS(Instant.now().toString()));

        this.ddbClient.update(tableName, invoiceId, getUpdateExpression(fieldsValues.keySet(), EQ), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues));
    }

    public void updateWarnings(String invoiceId, List<String> warnings) {
        var tableName = config.getVariable(INVOICE_TABLE);
        var fieldsValues = new HashMap<String, AttributeValue>();

        fieldsValues.put("updatedAt", new AttributeValue().withS(Instant.now().toString()));
        fieldsValues.put("warnings", new AttributeValue().withL(warnings.stream().map(warning -> new AttributeValue().withS(warning)).collect(toUnmodifiableList())));

        this.ddbClient.update(tableName, invoiceId, getUpdateExpression(fieldsValues.keySet(), EQ), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues));
    }

    public void updateLineItemsFinalizedStatus(List<String> lineItemIds, boolean finalized) {
        var tableName = config.getVariable(INVOICE_LINE_ITEM_TABLE);
        var fieldValues = new HashMap<String, AttributeValue>();

        lineItemIds.forEach(id -> {
            addValueIfPresentB("isFinalized", finalized, fieldValues);
            this.ddbClient.update(tableName, id, getUpdateExpression(fieldValues.keySet(), EQ), getAttributeNames(fieldValues.keySet()), getAttributeValues(fieldValues));
        });
    }

    public List<LineItem> findLineItemsByIds(Set<String> ids) {
        if (ids.isEmpty()) {
            return List.of();
        }

        var tableName = config.getVariable(INVOICE_LINE_ITEM_TABLE);

        var keyConditions = ids.stream()
                .map(userId -> Map.of("id", new AttributeValue().withS(userId))).collect(toUnmodifiableList());

        return ddbClient.batchQuery(tableName, keyConditions)
                .stream()
                .map(this::mapLineItem)
                .collect(Collectors.toUnmodifiableList());
    }

}
