package com.rentancy.integrations.servicies.persistence;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.servicies.persistence.DDBClient;
import com.rentancy.integrations.servicies.persistence.XeroRepository;

import java.time.Instant;
import java.util.HashMap;
import java.util.UUID;

public class InvoiceWebhookEventsRepository extends XeroRepository {

    private static final String INVOICE_WEBHOOK_EVENTS_TABLE = "INVOICE_WEBHOOK_EVENTS_TABLE";

    private final Config config;
    private final DDBClient ddbClient;

    public InvoiceWebhookEventsRepository(Config config, DDBClient ddbClient) {
        this.config = config;
        this.ddbClient = ddbClient;
    }

    public String create(String organisationId, String xeroInvoiceId, String data) {
        var item = new HashMap<String, AttributeValue>();
        var id = UUID.randomUUID().toString();
        item.put("id", new AttributeValue().withS(id));
        item.put("createdAt", new AttributeValue().withS(Instant.now().toString()));
        item.put("invoiceId", new AttributeValue().withS(xeroInvoiceId));
        item.put("invoiceWebhookEventsOrganisationId", new AttributeValue().withS(organisationId));
        item.put("data", new AttributeValue().withS(data));

        this.ddbClient.put(config.getVariable(INVOICE_WEBHOOK_EVENTS_TABLE), item);

        return id;
    }
}
