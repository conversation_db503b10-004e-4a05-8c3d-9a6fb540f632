package com.rentancy.integrations.servicies.persistence;

import com.rentancy.integrations.pojos.BoardColumn;
import com.rentancy.integrations.pojos.Task;
import lombok.RequiredArgsConstructor;

import java.util.List;

@RequiredArgsConstructor
public class BoardServiceImpl implements BoardService {

    private final TaskRepository taskRepository;
    private final BoardColumnRepository columnRepository;

    @Override
    public List<Task> findOrganisationTasks(String organisationId, String startDate, String endDate, String userId) {
        return taskRepository.findOrganisationTasks(organisationId, startDate, endDate, userId);
    }

    @Override
    public BoardColumn findColumn(String columnId) {
        return columnRepository.getColumn(columnId);
    }
}
