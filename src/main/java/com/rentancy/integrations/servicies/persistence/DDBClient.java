package com.rentancy.integrations.servicies.persistence;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB;
import com.amazonaws.services.dynamodbv2.AmazonDynamoDBClientBuilder;
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapper;
import com.amazonaws.services.dynamodbv2.model.*;
import com.rentancy.integrations.config.Config;
import org.apache.commons.collections4.ListUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.lang.Nullable;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static java.util.stream.Collectors.toUnmodifiableList;

public class DDBClient {

    private static final Logger log = LogManager.getLogger(DDBClient.class);

    static final String ATTRIBUTE_NAME_PREFIX = "#";
    static final String ATTRIBUTE_VALUE_PREFIX = ":";
    static final String AND = " AND ";
    static final String BETWEEN = " BETWEEN ";
    static final String OR = " OR ";
    static final String EQ = "=";
    static final String BEGINS_WITH = "begins_with";
    static final String NE = "<>";
    static final String LS = "<";
    static final String LE = "<=";
    static final String GT = ">";
    static final String GE = ">=";
    static final String CONTAINS = "contains";
    static final String SET = "SET ";
    static final String ATTRIBUTE_EXISTS = "attribute_exists";
    static final String ATTRIBUTE_NOT_EXISTS = "attribute_not_exists";

    static final String REMOVE = "REMOVE ";

    private final AmazonDynamoDB dynamoDB;

    public DDBClient(Config config) {
        this.dynamoDB = AmazonDynamoDBClientBuilder
                .standard()
                .withRegion(config.getDDBRegion())
                .withClientConfiguration(new ClientConfiguration().withTcpKeepAlive(false))
                .build();
    }

    protected DDBClient(AmazonDynamoDB dynamoDB) {
        this.dynamoDB = dynamoDB;
    }


    GetItemResult getItem(String tableName, String id) {
        return getItem(tableName, "id", id);
    }

    GetItemResult getItem(String tableName, String idAttributeName, String id) {
        Map<String, AttributeValue> key = new HashMap<>();
        key.put(idAttributeName, new AttributeValue().withS(id));
        GetItemRequest request = new GetItemRequest()
                .withTableName(tableName)
                .withKey(key);

        return this.dynamoDB.getItem(request);
    }

    DeleteItemResult deleteItem(String tableName, String id) {
        Map<String, AttributeValue> key = new HashMap<>();
        key.put("id", new AttributeValue().withS(id));
        var deleteItemRequest = new DeleteItemRequest()
                .withTableName(tableName)
                .withKey(key);

        return this.dynamoDB.deleteItem(deleteItemRequest);
    }

    List<Map<String, AttributeValue>> scan(String tableName, String filterExpression, Map<String, String> attributeNames, Map<String, AttributeValue> values, String indexName) {
        Map<String, AttributeValue> lastKey = null;
        List<Map<String, AttributeValue>> items = new ArrayList<>();
        do {
            ScanRequest request = new ScanRequest()
                    .withTableName(tableName)
                    .withExclusiveStartKey(lastKey);

            if (filterExpression != null && attributeNames != null && values != null) {
                request.withFilterExpression(filterExpression)
                        .withExpressionAttributeNames(attributeNames)
                        .withExpressionAttributeValues(values);
            }

            if (indexName != null) {
                request.withIndexName(indexName);
            }

            ScanResult result = this.dynamoDB.scan(request);

            items.addAll(result.getItems());

            lastKey = result.getLastEvaluatedKey();
        } while (lastKey != null);

        return items;
    }

    List<Map<String, AttributeValue>> scan(String tableName, String filterExpression, Map<String, String> attributeNames, Map<String, AttributeValue> values) {
        return scan(tableName, filterExpression, attributeNames, values, null);
    }

    List<Map<String, AttributeValue>> scan(String tableName) {
        return scan(tableName, null, null, null);
    }

    List<Map<String, AttributeValue>> query(String tableName, String index, String keyConditionExpression, Map<String, String> attributeNames, Map<String, AttributeValue> values, @Nullable String filterExpression) {
        Map<String, AttributeValue> lastKey = null;
        List<Map<String, AttributeValue>> items = new ArrayList<>();

        do {
            QueryRequest request = new QueryRequest()
                    .withTableName(tableName)
                    .withIndexName(index)
                    .withKeyConditionExpression(keyConditionExpression)
                    .withExpressionAttributeNames(attributeNames)
                    .withExpressionAttributeValues(values)
                    .withExclusiveStartKey(lastKey);

            Optional.ofNullable(filterExpression).ifPresent(request::withFilterExpression);

            QueryResult result = this.dynamoDB.query(request);

            items.addAll(result.getItems());

            lastKey = result.getLastEvaluatedKey();
        } while (lastKey != null);

        return items;
    }

    List<Map<String, AttributeValue>> query(String tableName, String keyConditionExpression, Map<String, AttributeValue> values, @Nullable String filterExpression) {
        Map<String, AttributeValue> lastKey = null;
        List<Map<String, AttributeValue>> items = new ArrayList<>();

        do {
            QueryRequest request = new QueryRequest()
                    .withTableName(tableName)
                    .withKeyConditionExpression(keyConditionExpression)
                    .withExpressionAttributeValues(values)
                    .withExclusiveStartKey(lastKey);

            Optional.ofNullable(filterExpression).ifPresent(request::withFilterExpression);

            QueryResult result = this.dynamoDB.query(request);

            items.addAll(result.getItems());

            lastKey = result.getLastEvaluatedKey();
        } while (lastKey != null);

        return items;
    }


    List<Map<String, AttributeValue>> querySpec(String tableName, String index, String keyConditionExpression, Map<String, AttributeValue> values) {
        Map<String, AttributeValue> lastKey = null;
        List<Map<String, AttributeValue>> items = new ArrayList<>();

        do {

            QueryRequest request = new QueryRequest()
                    .withTableName(tableName)
                    .withIndexName(index)
                    .withKeyConditionExpression(keyConditionExpression)
                    .withExpressionAttributeValues(values)
                    .withExclusiveStartKey(lastKey);

            QueryResult result = this.dynamoDB.query(request);

            items.addAll(result.getItems());

            lastKey = result.getLastEvaluatedKey();
        } while (lastKey != null);

        return items;
    }

    int queryCount(String tableName, String index, String keyConditionExpression, Map<String, String> attributeNames, Map<String, AttributeValue> values) {
        Map<String, AttributeValue> lastKey = null;
        var count = new AtomicInteger(0);

        do {
            QueryRequest request = new QueryRequest()
                    .withTableName(tableName)
                    .withIndexName(index)
                    .withKeyConditionExpression(keyConditionExpression)
                    .withExpressionAttributeNames(attributeNames)
                    .withExpressionAttributeValues(values)
                    .withSelect(Select.COUNT)
                    .withExclusiveStartKey(lastKey);

            QueryResult result = this.dynamoDB.query(request);

            count.addAndGet(result.getCount());

            lastKey = result.getLastEvaluatedKey();
        } while (lastKey != null);

        return count.get();
    }

    List<Map<String, AttributeValue>> batchQuery(String tableName, List<Map<String, AttributeValue>> entries) {
        return ListUtils.partition(entries, 50).stream().flatMap(part -> {
            var keys = new KeysAndAttributes().withKeys(part);

            var request = new BatchGetItemRequest()
                    .withRequestItems(Map.of(tableName, keys));

            var result = this.dynamoDB.batchGetItem(request);
            var response = result.getResponses();

            return response.values().stream().flatMap(Collection::stream);
        }).collect(toUnmodifiableList());
    }

    void batchPut(String tableName, List<Map<String, AttributeValue>> entries) {
        ListUtils.partition(entries, 25).forEach(part -> {
            var writeRequests = part.stream()
                    .map(item -> new PutRequest().withItem(item))
                    .map(t -> new WriteRequest().withPutRequest(t))
                    .collect(toUnmodifiableList());

            var request = new BatchWriteItemRequest()
                    .withRequestItems(Map.of(tableName, writeRequests));

            this.dynamoDB.batchWriteItem(request);
        });
    }

    void update(String tableName, String id, String updateExpression, Map<String, String> attributeNames, Map<String, AttributeValue> values) {
        var key = new HashMap<String, AttributeValue>();
        key.put("id", new AttributeValue().withS(id));
        UpdateItemRequest request = new UpdateItemRequest()
                .withTableName(tableName)
                .withKey(key)
                .withExpressionAttributeNames(attributeNames)
                .withUpdateExpression(updateExpression);

        if (Objects.nonNull(values)) {
            request = request.withExpressionAttributeValues(values);
        }

        this.dynamoDB.updateItem(request);
    }

    void put(String tableName, Map<String, AttributeValue> item) {
        PutItemRequest request = new PutItemRequest()
                .withTableName(tableName)
                .withItem(item);

        this.dynamoDB.putItem(request);
    }

    /**
     * tableName -> item
     */
    void transactPut(Map<String, List<HashMap<String, AttributeValue>>> items) {
        var requestItems = items.keySet().stream().flatMap(table ->
                items.get(table)
                        .stream()
                        .map(put -> new Put().withTableName(table).withItem(put))
                        .map(put -> new TransactWriteItem().withPut(put))
        ).collect(toUnmodifiableList());

        var request = new TransactWriteItemsRequest().withTransactItems(requestItems);
        this.dynamoDB.transactWriteItems(request);
        log.info("Items created - " + requestItems.size());
    }
}
