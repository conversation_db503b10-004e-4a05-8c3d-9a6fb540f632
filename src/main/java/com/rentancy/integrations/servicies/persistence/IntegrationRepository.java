package com.rentancy.integrations.servicies.persistence;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.Integration;
import com.rentancy.integrations.util.Utils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.rentancy.integrations.pojos.Integration.IntegrationStatus.CONNECTED;
import static com.rentancy.integrations.servicies.persistence.DDBClient.EQ;

public class IntegrationRepository extends Repository {

    private static final Logger log = LogManager.getLogger(IntegrationRepository.class);

    private static final String INTEGRATION_TABLE = "INTEGRATION_TABLE";
    private static final String INTEGRATION_INDEX = "INTEGRATION_INDEX";
    private static final String INTEGRATION_TENANT_INDEX = "INTEGRATION_TENANT_INDEX";
    private static final String INTEGRATION_STATE_INDEX = "INTEGRATION_STATE_INDEX";
    private static final String INTEGRATION_ORGANISATION_INDEX = "INTEGRATION_ORGANISATION_INDEX";

    private final Config config;
    private final DDBClient ddbClient;

    public IntegrationRepository(Config config, DDBClient ddbClient) {
        this.config = config;
        this.ddbClient = ddbClient;
    }

    public String create(Integration integration) {
        var tableName = config.getVariable(INTEGRATION_TABLE);

        var item = new HashMap<String, AttributeValue>();
        var id = UUID.randomUUID().toString();

        item.put("id", new AttributeValue().withS(id));
        item.put("state", new AttributeValue().withS(integration.getState()));
        item.put("url", new AttributeValue().withS(integration.getUrl()));
        item.put("organisationId", new AttributeValue().withS(integration.getOrganisationId()));
        item.put("userId", new AttributeValue().withS(integration.getUserId()));
        item.put("type", new AttributeValue().withS(integration.getType().name()));
        item.put("status", new AttributeValue().withS(integration.getStatus().name()));
        item.put("startDate", new AttributeValue().withS(Optional.ofNullable(integration.getStartDate()).map(Instant::toString).orElse(null)));
        item.put("createdAt", new AttributeValue().withS(Instant.now().toString()));
        item.put("remainingCallAmount", new AttributeValue().withN("9999"));

        this.ddbClient.put(tableName, item);

        return id;
    }

    public String update(Integration integration) {
        var tableName = config.getVariable(INTEGRATION_TABLE);
        var id = integration.getId();
        var metadata = Utils.getOrEmpty(integration.getMetadata())
                .stream()
                .map(this::toMetadataItem)
                .collect(Collectors.toUnmodifiableList());

        var fieldsValues = new HashMap<String, AttributeValue>();

        addValueIfPresent("updatedAt", Instant.now().toString(), fieldsValues);
        addValueIfPresent("accessToken", integration.getAccessToken(), fieldsValues);
        addValueIfPresent("refreshToken", integration.getRefreshToken(), fieldsValues);
        addValueIfPresent("state", integration.getState(), fieldsValues);
        addValueIfPresent("status", Optional.ofNullable(integration.getStatus()).map(Enum::name).orElse(null), fieldsValues);
        addValueIfPresent("url", integration.getUrl(), fieldsValues);
        addValueIfPresent("organisationId", integration.getOrganisationId(), fieldsValues);
        addValueIfPresent("userId", integration.getUserId(), fieldsValues);
        addValueIfPresent("type", integration.getType().name(), fieldsValues);
        addValueIfPresent("tenantId", integration.getTenantId(), fieldsValues);
        addValueIfPresent("tenantName", integration.getTenantName(), fieldsValues);
        addValueIfPresent("connectionId", integration.getConnectionId(), fieldsValues);
        addValueIfPresent("expirationDate", Optional.ofNullable(integration.getExpirationDate()).map(Instant::toString).orElse(null), fieldsValues);
        addValueIfPresent("startDate", Optional.ofNullable(integration.getStartDate()).map(Instant::toString).orElse(null), fieldsValues);
        addValueIfPresentL("metadata", metadata, fieldsValues);
        addValueIfPresentN("remainingCallAmount", integration.getRemainingCallAmount(), fieldsValues);

        String updateExpression = getUpdateExpression(fieldsValues.keySet(), EQ);

        this.ddbClient.update(tableName, id, updateExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues));

        return id;
    }

    public void delete(String id) {
        var tableName = config.getVariable(INTEGRATION_TABLE);
        this.ddbClient.deleteItem(tableName, id);
    }

    public Integration findByOrganisationAndUser(String organisationId, String userId, Integration.IntegrationService type) {
        var tableName = config.getVariable(INTEGRATION_TABLE);
        var indexName = config.getVariable(INTEGRATION_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("organisationId", new AttributeValue().withS(organisationId));
        fieldsValues.put("userId", new AttributeValue().withS(userId));
        return executeQuery(tableName, indexName, fieldsValues, null, type);
    }

    public List<Integration> findByOrganisation(String organisationId) {
        var tableName = config.getVariable(INTEGRATION_TABLE);
        var indexName = config.getVariable(INTEGRATION_ORGANISATION_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("organisationId", new AttributeValue().withS(organisationId));
        fieldsValues.put("status", new AttributeValue().withS(CONNECTED.name()));

        var filterExpression = getFilterExpression(Set.of("status"));

        var result =
                this.ddbClient.query(tableName, indexName, getFilterExpression(Set.of("organisationId")), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), filterExpression);

        return result
                .stream()
                .map(this::mapToIntegration)
                .collect(Collectors.toUnmodifiableList());
    }

    public Integration getIntegration(String id) {
        var tableName = config.getVariable(INTEGRATION_TABLE);

        var result = this.ddbClient.getItem(tableName, id);

        var item = result.getItem();

        return mapToIntegration(item);
    }

    public List<Integration> findConnectedIntegrations() {
        var tableName = config.getVariable(INTEGRATION_TABLE);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("status", new AttributeValue().withS(CONNECTED.name()));

        log.info("Field values - {}", fieldsValues);

        var result = this.ddbClient.scan(tableName, getFilterExpression(fieldsValues.keySet()), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues));

        return result
                .stream()
                .map(this::mapToIntegration)
                .collect(Collectors.toUnmodifiableList());
    }

    private Integration mapToIntegration(Map<String, AttributeValue> item) {
        return Integration
                .builder()
                .id(getIfPresent(item, "id"))
                .userId(getIfPresent(item, "userId"))
                .tenantId(getIfPresent(item, "tenantId"))
                .tenantName(getIfPresent(item, "tenantName"))
                .connectionId(getIfPresent(item, "connectionId"))
                .organisationId(getIfPresent(item, "organisationId"))
                .state(getIfPresent(item, "state"))
                .url(getIfPresent(item, "url"))
                .accessToken(getIfPresent(item, "accessToken"))
                .refreshToken(getIfPresent(item, "refreshToken"))
                .expirationDate(getInstantIfPresent(item, "expirationDate"))
                .startDate(getInstantIfPresent(item, "startDate"))
                .status(Integration.IntegrationStatus.valueOf(getIfPresent(item, "status")))
                .type(Integration.IntegrationService.valueOf(getIfPresent(item, "type")))
                .metadata(toMetadata(getIfPresentL(item, "metadata")))
                .allowedContactGroups(getIfPresentL(item, "allowedContactGroups").stream().map(AttributeValue::getS).collect(Collectors.toUnmodifiableList()))
                .updatedAt(getInstantIfPresent(item, "updatedAt"))
                .remainingCallAmount(getIfPresentNOptional(item, "remainingCallAmount").orElse(9999))
                .build();
    }

    private AttributeValue toMetadataItem(Integration.IntegrationMetadata metadata) {
        var item = new HashMap<String, AttributeValue>();
        addOrDefault("errorMessage", metadata.getErrorMessage(), item, " ");
        addValueIfPresentN("retryCount", metadata.getRetryCount(), item);
        addValueIfPresent("recourseType", Optional.ofNullable(metadata.getRecourseType()).map(Enum::name).orElse(null), item);
        addValueIfPresent("lastUpdatedDate", Optional.ofNullable(metadata.getLastUpdatedDate()).map(Instant::toString).orElse(null), item);

        return new AttributeValue().withM(item);
    }

    private List<Integration.IntegrationMetadata> toMetadata(List<AttributeValue> attributeValues) {
        return attributeValues
                .stream()
                .map(AttributeValue::getM)
                .map(attributeValue -> Integration
                        .IntegrationMetadata
                        .builder()
                        .retryCount(getIfPresentN(attributeValue, "retryCount"))
                        .recourseType(Optional.ofNullable(getIfPresent(attributeValue, "recourseType")).map(Integration.ResourceType::valueOf).orElse(null))
                        .lastUpdatedDate(getInstantIfPresent(attributeValue, "lastUpdatedDate"))
                        .build())
                .collect(Collectors.toUnmodifiableList());
    }

    public Integration findByTenantId(String tenantId, Integration.IntegrationService type) {
        var tableName = config.getVariable(INTEGRATION_TABLE);
        var indexName = config.getVariable(INTEGRATION_TENANT_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("tenantId", new AttributeValue().withS(tenantId));

        return executeQuery(tableName, indexName, fieldsValues, null, type);
    }

    private Integration executeQuery(String tableName, String indexName, Map<String, AttributeValue> fieldsValues, String filterExpression, Integration.IntegrationService type) {
        var result =
                this.ddbClient.query(tableName, indexName, getFilterExpression(fieldsValues.keySet()), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), filterExpression);

        return result
                .stream()
                .map(this::mapToIntegration)
                .filter(integration -> type == integration.getType())
                .findAny()
                .orElse(null);
    }

    public Integration findByState(String state, Integration.IntegrationService type) {
        var tableName = config.getVariable(INTEGRATION_TABLE);
        var indexName = config.getVariable(INTEGRATION_STATE_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("state", new AttributeValue().withS(state));

        return executeQuery(tableName, indexName, fieldsValues, null, type);
    }
}
