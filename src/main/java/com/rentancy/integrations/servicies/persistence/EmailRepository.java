package com.rentancy.integrations.servicies.persistence;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.EmailMessage;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.UUID;

import static com.rentancy.integrations.util.Utils.getOrDefault;

public class EmailRepository extends Repository {

    private static final Logger log = LogManager.getLogger(EmailRepository.class);

    private static final String EMAIL_MESSAGE_TABLE = "EMAIL_MESSAGE_TABLE";
    private static final String EMAIL_ATTACHMENT_TABLE = "EMAIL_ATTACHMENT_TABLE";

    private final Config config;
    private final DDBClient ddbClient;

    public EmailRepository(Config config, DDBClient ddbClient) {
        this.config = config;
        this.ddbClient = ddbClient;
    }

    public String putEmail(EmailMessage emailMessage) {
        log.info(emailMessage);

        var tableName = config.getVariable(EMAIL_MESSAGE_TABLE);
        var attachmentTableName = config.getVariable(EMAIL_ATTACHMENT_TABLE);

        var item = new HashMap<String, AttributeValue>();
        var id = UUID.randomUUID().toString();
        var attachments = emailMessage.getAttachments();

        item.put("id", new AttributeValue().withS(id));
        item.put("emailMessageOrganisationId", new AttributeValue().withS(getOrDefault(emailMessage.getOrganisationId(), null)));
        item.put("parentId", new AttributeValue().withS(getOrDefault(emailMessage.getParentId(), null)));
        item.put("to", new AttributeValue().withS(getOrDefault(emailMessage.getTo(), null)));
        item.put("from", new AttributeValue().withS(getOrDefault(emailMessage.getFrom(), null)));
        item.put("subject", new AttributeValue().withS(getOrDefault(emailMessage.getSubject(), null)));
        item.put("body", new AttributeValue().withS(getOrDefault(emailMessage.getBody(), null)));
        item.put("status", new AttributeValue().withN(String.valueOf(emailMessage.getStatus())));
        item.put("reply", new AttributeValue().withBOOL(emailMessage.isReply()));

        if (attachments != null && !attachments.isEmpty()) {
            for (EmailMessage.EmailAttachment attachment : attachments) {
                var attachmentItem = new HashMap<String, AttributeValue>();

                attachmentItem.put("id", new AttributeValue().withS(UUID.randomUUID().toString()));
                attachmentItem.put("emailAttachmentEmailMessageId", new AttributeValue().withS(id));
                attachmentItem.put("emailAttachmentDocumentId", new AttributeValue().withS(attachment.getDocumentId()));

                this.ddbClient.put(attachmentTableName, attachmentItem);
            }
        }

        this.ddbClient.put(tableName, item);

        return id;
    }
}
