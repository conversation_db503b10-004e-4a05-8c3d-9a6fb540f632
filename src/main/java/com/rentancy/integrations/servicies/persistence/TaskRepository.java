package com.rentancy.integrations.servicies.persistence;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.Task;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import static com.rentancy.integrations.servicies.persistence.DDBClient.AND;
import static com.rentancy.integrations.servicies.persistence.DDBClient.ATTRIBUTE_NAME_PREFIX;
import static com.rentancy.integrations.servicies.persistence.DDBClient.ATTRIBUTE_NOT_EXISTS;
import static com.rentancy.integrations.servicies.persistence.DDBClient.ATTRIBUTE_VALUE_PREFIX;
import static com.rentancy.integrations.servicies.persistence.DDBClient.EQ;
import static com.rentancy.integrations.servicies.persistence.DDBClient.OR;
import static java.util.stream.Collectors.toUnmodifiableList;

@RequiredArgsConstructor
public class TaskRepository extends Repository {

    private static final Logger log = LogManager.getLogger(TaskRepository.class);

    private static final String TASK_TABLE = "TASK_TABLE";
    private static final String TASK_ORGANISATION_INDEX = "TASK_ORGANISATION_INDEX";

    private final Config config;
    private final DDBClient ddbClient;

    public List<Task> findOrganisationTasks(String organisationId, @Nullable String startDate, @Nullable String endDate, @Nullable String userId) {
        var tableName = config.getVariable(TASK_TABLE);
        var indexName = config.getVariable(TASK_ORGANISATION_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("taskOrganisationId", new AttributeValue().withS(organisationId));
        var queryExpression = getFilterExpression(fieldsValues.keySet());
        var attributeNames = new HashSet<>(fieldsValues.keySet());
        attributeNames.add("status");
        attributeNames.add("parentType");

        var attributeValues = new HashMap<>(fieldsValues);
        attributeValues.put("PROPERTY", new AttributeValue().withS("PROPERTY"));
        attributeValues.put("TENANCY", new AttributeValue().withS("TENANCY"));
        attributeValues.put("activeStatus", new AttributeValue().withS("ACTIVE"));
        attributeValues.put("draftStatus", new AttributeValue().withS("DRAFT"));

        var statusFilterValues = "(".concat( ATTRIBUTE_NOT_EXISTS + "(#status)")
                .concat(OR)
                .concat(ATTRIBUTE_NAME_PREFIX + "status " + EQ + ATTRIBUTE_VALUE_PREFIX + "activeStatus")
                .concat(OR)
                .concat(ATTRIBUTE_NAME_PREFIX + "status " + EQ + ATTRIBUTE_VALUE_PREFIX + "draftStatus")
                .concat(")");
        var parentTypeFilterValues = "( " + ATTRIBUTE_NAME_PREFIX + "parentType " + EQ + ATTRIBUTE_VALUE_PREFIX + "PROPERTY"
                .concat(OR)
                .concat(ATTRIBUTE_NAME_PREFIX + "parentType" + EQ + ATTRIBUTE_VALUE_PREFIX + "TENANCY )");

        var filterFieldsValues = statusFilterValues.concat(AND).concat(parentTypeFilterValues);

        if (userId != null) {
            attributeNames.add("taskUserId");
            attributeValues.put("userId", new AttributeValue().withS(userId));
            filterFieldsValues = filterFieldsValues.concat(AND).concat(ATTRIBUTE_NAME_PREFIX + "taskUserId" + EQ + ":userId");
        }

        if (startDate != null && endDate != null) {
            attributeNames.add("deadline");
            filterFieldsValues = filterFieldsValues.concat(AND).concat(addFilterBetweenExpression("deadline", "date1", "date2"));
            attributeValues.put("date1", new AttributeValue().withS(startDate));
            attributeValues.put("date2", new AttributeValue().withS(endDate));
        }

        var result =
                this.ddbClient.query(tableName, indexName, queryExpression, getAttributeNames(attributeNames), getAttributeValues(attributeValues), filterFieldsValues);

        return result
                .stream()
                .map(this::mapToTask)
                .collect(toUnmodifiableList());
    }

    private Task mapToTask(Map<String, AttributeValue> item) {
        return Task.builder()
                .id(getIfPresent(item, "id"))
                .deadline(getIfPresent(item, "deadline"))
                .columnId(getIfPresent(item, "taskColumnId"))
                .name(getIfPresent(item, "name"))
                .description(getIfPresent(item, "description"))
                .assigneeUserId(getIfPresent(item, "taskUserId"))
                .parentType(getIfPresent(item, "parentType"))
                .parentId(getIfPresent(item, "parentId"))
                .build();
    }
}
