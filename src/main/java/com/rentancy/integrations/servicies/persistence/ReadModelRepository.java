package com.rentancy.integrations.servicies.persistence;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.Integration;
import com.rentancy.integrations.pojos.PropertyFinanceSummary;
import com.rentancy.integrations.pojos.sqs.ReadModelParentPropertySummaryFillCommand;
import com.rentancy.integrations.servicies.persistence.config.ReadModelRepositoryConfig;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.checkerframework.checker.units.qual.A;
import org.xml.sax.helpers.AttributesImpl;
import software.amazon.awssdk.services.dynamodb.endpoints.internal.Value;

import javax.swing.text.html.Option;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.Optional;

import static com.rentancy.integrations.util.JSONUtils.wrappedToJsonString;

@AllArgsConstructor
public class ReadModelRepository extends Repository {

    private final DDBClient ddbClient;
    private final ReadModelRepositoryConfig config;

    public static ReadModelRepository create(Config config) {
        DDBClient ddbClient = new DDBClient(config);
        return new ReadModelRepository(ddbClient, new ReadModelRepositoryConfig());
    }

    /**
     * This method uses <b>READ_MODEL_PARENT_PROPERTY_SUMMARY_TABLE</b>
     * @param propertyId
     * @return Pair of PropertyFinanceSummary and Instant
     */
    public Pair<PropertyFinanceSummary, Instant> findByPropertyId(String propertyId) {
        var table = config.getReadModelParentPropertySummaryTable();
        var map = ddbClient.getItem(table, propertyId).getItem();
        if (map == null) {
            return Pair.of(null, null);
        }
        return Pair.of(
                PropertyFinanceSummary.builder()
                        .name(getIfPresent(map, "name"))
                        .budgetId(getIfPresent(map, "budgetId"))
                        .reference(getIfPresent(map, "reference"))
                        .inArrears(getIfPresentBD(map, "inArrears"))
                        .income(getIfPresentBD(map, "income"))
                        .paidDeposit(getIfPresentBD(map, "paidDeposit"))
                        .expenses(getIfPresentBD(map, "expenses"))
                        .minimumBalance(getIfPresentBD(map, "minimumBalance"))
                        .dueToClient(getIfPresentBD(map, "dueToClient"))
                        .balance(getIfPresentBD(map, "balance"))
                        .reservers(getIfPresentBD(map, "reservers"))
                        .suspense(getIfPresentBD(map, "suspense"))
                        .incomeRaisedNotPaid(getIfPresentBD(map, "incomeRaisedNotPaid"))
                        .billsOutstanding(getIfPresentBD(map, "billsOutstanding"))
                        .activeContracts(getIfPresentN(map, "activeContracts"))
                        .totalArea(getIfPresentN(map, "totalArea"))
                        .monthsRemaining(getIfPresentN(map, "monthsRemaining"))
                        .currency(getIfPresent(map, "currency"))
                        .openingBalance(getIfPresentBD(map, "openingBalance"))
                        .build(),
                getInstantIfPresent(map, "lastUpdatedAt")
        );
    }

    public void saveParentPropertySummary(ReadModelParentPropertySummaryFillCommand command, PropertyFinanceSummary parentPropertyFinanceSummary) {
        var table = config.getReadModelParentPropertySummaryTable();

        var summary = new HashMap<String, AttributeValue>();

        summary.put("id", new AttributeValue().withS(command.parentPropertyId));
        putIfExists(summary, "lastUpdatedAt", Optional.ofNullable(command.itemUpdatedAt).orElse(Instant.now().toString()));

        putIfExists(summary, "name", parentPropertyFinanceSummary.getName());
        putIfExists(summary, "budgetId", (parentPropertyFinanceSummary.getBudgetId()));
        putIfExists(summary, "reference", (parentPropertyFinanceSummary.getReference()));
        putIfExists(summary, "inArrears", (parentPropertyFinanceSummary.getInArrears()));
        putIfExists(summary, "income", (parentPropertyFinanceSummary.getIncome()));
        putIfExists(summary, "paidDeposit", (parentPropertyFinanceSummary.getPaidDeposit()));
        putIfExists(summary, "expenses", (parentPropertyFinanceSummary.getExpenses()));
        putIfExists(summary, "minimumBalance", (parentPropertyFinanceSummary.getMinimumBalance()));
        putIfExists(summary, "dueToClient", (parentPropertyFinanceSummary.getDueToClient()));
        putIfExists(summary, "balance", (parentPropertyFinanceSummary.getBalance()));
        putIfExists(summary, "reservers", (parentPropertyFinanceSummary.getReservers()));
        putIfExists(summary, "suspense", (parentPropertyFinanceSummary.getSuspense()));
        putIfExists(summary, "incomeRaisedNotPaid", (parentPropertyFinanceSummary.getIncomeRaisedNotPaid()));
        putIfExists(summary, "billsOutstanding", (parentPropertyFinanceSummary.getBillsOutstanding()));
        putIfExists(summary, "activeContracts", (parentPropertyFinanceSummary.getActiveContracts()));
        putIfExists(summary, "totalArea", (parentPropertyFinanceSummary.getTotalArea()));
        putIfExists(summary, "monthsRemaining", (parentPropertyFinanceSummary.getMonthsRemaining()));
        putIfExists(summary, "currency", (parentPropertyFinanceSummary.getCurrency()));
        putIfExists(summary, "openingBalance", (parentPropertyFinanceSummary.getOpeningBalance()));

        ddbClient.put(table, summary);
    }

    private <T> void putIfExists(HashMap<String, AttributeValue> map, String field, T value) {
        if (value == null) return;
        map.put(field, new AttributeValue().withS(String.valueOf(value)));
    }


}
