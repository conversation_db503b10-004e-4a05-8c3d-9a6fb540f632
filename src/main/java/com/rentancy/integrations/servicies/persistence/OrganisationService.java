package com.rentancy.integrations.servicies.persistence;

import com.rentancy.integrations.pojos.Organisation;
import com.rentancy.integrations.pojos.OrganisationStripeCharge;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;

public interface OrganisationService {

    Organisation getOrganisation(String id);

    List<Organisation> findAllOrganisations();

    List<Organisation> findAllUnexpiredOrganisations();

    List<OrganisationStripeCharge> findOrganisationStripeCharges(String fromData, String toDate);

    List<Organisation> findOrganisationsWithWhatsAppAddress(String whatsAppAddress);

    void updateFinanceIntegrationType(String organisationId, String value);

    void updateXeroDetails(String organisationId, String xeroId);

    Stream<Organisation> getOrganisations(Set<String> organisationIds);

    List<Organisation> findAllJournalOrganisations();

    void numberStripeChargeItems();

    Optional<OrganisationStripeCharge> getOrganisationStripeCharge(String chargeId);

    List<Organisation> findOrganisationsCreatedBetween(String from, String to);
}
