package com.rentancy.integrations.servicies.persistence;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.Invoice;
import com.rentancy.integrations.pojos.Transaction;
import com.rentancy.integrations.util.Utils;
import java.math.BigDecimal;
import java.util.stream.Stream;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.Instant;
import java.util.*;

import static com.rentancy.integrations.servicies.persistence.DDBClient.EQ;
import static java.util.stream.Collectors.toUnmodifiableList;

public class TransactionRepository extends XeroRepository {

    private static final Logger log = LogManager.getLogger(TransactionRepository.class);

    private static final String TRANSACTION_TABLE = "TRANSACTION_TABLE";
    private static final String TRANSACTION_INDEX = "TRANSACTION_INDEX";
    private static final String TRANSACTION_ORGANISATION_INDEX = "TRANSACTION_ORGANISATION_INDEX";

    private final Config config;
    private final DDBClient ddbClient;

    public TransactionRepository(Config config, DDBClient ddbClient) {
        this.config = config;
        this.ddbClient = ddbClient;
    }

    public String create(Transaction transaction) {
        var item = new HashMap<String, AttributeValue>();
        var id = UUID.randomUUID().toString();
        var organisationId = transaction.getOrganisation();

        item.put("id", new AttributeValue().withS(id));
        item.put("transactionId", new AttributeValue().withS(transaction.getTransactionId()));
        item.put("reconciled", new AttributeValue().withBOOL(transaction.isReconciled()));
        item.put("hasAttachments", new AttributeValue().withBOOL(transaction.isHasAttachments()));
        item.put("createdAt", new AttributeValue().withS(Instant.now().toString()));

        addValueIfPresent("prePaymentId", transaction.getPrePaymentId(), item);
        addValueIfPresent("overPaymentId", transaction.getOverPaymentId(), item);
        addValueIfPresent("type", transaction.getType(), item);
        addValueIfPresent("contactId", transaction.getContactId(), item);
        addValueIfPresent("lineAmountTypes", transaction.getLineAmountTypes(), item);
        addValueIfPresent("subTotal", transaction.getSubTotal(), item);
        addValueIfPresent("totalTax", transaction.getTotalTax(), item);
        addValueIfPresent("total", transaction.getTotal(), item);
        addValueIfPresent("currency", transaction.getCurrency(), item);
        addValueIfPresent("transactionOrganisationId", organisationId, item);
        addValueIfPresent("tenantId", transaction.getTenant(), item);
        addValueIfPresent("date", Utils.getInstantStringIfPresent(transaction.getDate()), item);
        addValueIfPresent("currencyRate", transaction.getCurrencyRate(), item);
        addValueIfPresent("url", transaction.getUrl(), item);
        addValueIfPresent("status", transaction.getStatus(), item);
        addValueIfPresent("transactionAccountId", transaction.getAccountId(), item);
        addValueIfPresent("reference", transaction.getReference(), item);
        addValueIfPresentB("balanceTransfer", transaction.isBalanceTransfer(), item);
        addValueIfPresent("batchPaymentId", transaction.getBatchPaymentId(), item);

        var lineItems = transaction
                .getLineItems()
                .stream()
                .map(lineItem -> {
                    var attributes = mapLineItem(transaction, organisationId, lineItem);
                    attributes.put("invoiceLineItemTransactionId", new AttributeValue().withS(id));
                    return attributes;
                })
                .collect(toUnmodifiableList());

        var items = new HashMap<String, List<HashMap<String, AttributeValue>>>();
        items.put(config.getVariable(TRANSACTION_TABLE), List.of(item));
        items.put(config.getVariable(INVOICE_LINE_ITEM_TABLE), lineItems);

        log.info("Items to put - " + items);
        this.ddbClient.transactPut(items);

        return id;
    }

    public Transaction findByOrganisationAndTransactionId(String organisationId, String transactionId) {
        var tableName = config.getVariable(TRANSACTION_TABLE);
        var indexName = config.getVariable(TRANSACTION_INDEX);
        log.info("Transaction table - " + tableName);

        var fieldsValues = new HashMap<String, AttributeValue>();
        var filterFieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("transactionId", new AttributeValue().withS(transactionId));
        filterFieldsValues.put("transactionOrganisationId", new AttributeValue().withS(organisationId));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());
        var filterExpression = getFilterExpression(filterFieldsValues.keySet());

        fieldsValues.putAll(filterFieldsValues);

        var result =
                this.ddbClient.query(tableName, indexName, keyConditionExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), filterExpression);

        return result
                .stream()
                .map(this::mapToTransaction)
                .findAny()
                .orElse(null);
    }

    private Transaction mapToTransaction(Map<String, AttributeValue> item) {
        return Transaction
                .builder()
                .id(getIfPresent(item, "id"))
                .type(getIfPresent(item, "type"))
                .status(getIfPresent(item, "status"))
                .contactId(getIfPresent(item, "contactId"))
                .hasAttachments(getIfPresentBool(item, "hasAttachments"))
                .date(getInstantIfPresent(item, "date"))
                .reference(getIfPresent(item, "reference"))
                .currency(getIfPresent(item, "currency"))
                .lineAmountTypes(getIfPresent(item, "lineAmountTypes"))
                .subTotal(getIfPresent(item, "subTotal"))
                .totalTax(getIfPresent(item, "totalTax"))
                .total(getIfPresent(item, "total"))
                .organisation(getIfPresent(item, "organisation"))
                .updatedAt(getInstantIfPresent(item, "updatedAt"))
                .prePaymentId(getIfPresent(item, "prePaymentId"))
                .overPaymentId(getIfPresent(item, "overPaymentId"))
                .currencyRate(getIfPresent(item, "currencyRate"))
                .url(getIfPresent(item, "url"))
                .accountId(getIfPresent(item, "accountId"))
                .tenant(getIfPresent(item, "tenant"))
                .reconciled(getIfPresentBool(item, "reconciled"))
                .balanceTransfer(getIfPresentBool(item, "balanceTransfer"))
                .batchPaymentId(getIfPresent(item, "batchPaymentId"))
                .transactionId(getIfPresent(item, "transactionId"))
                .overPaymentTransaction(getIfPresentBool(item, "overPaymentTransaction"))
                .build();
    }

    List<Invoice.LineItem> findLineItems(String transactionId) {
        var tableName = config.getVariable(INVOICE_LINE_ITEM_TABLE);
        var indexName = config.getVariable(INVOICE_LINE_ITEM_TRANSACTION_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("invoiceLineItemTransactionId", new AttributeValue().withS(transactionId));

        var result =
                this.ddbClient.query(tableName, indexName, getFilterExpression(fieldsValues.keySet()), getAttributeNames(fieldsValues), getAttributeValues(fieldsValues), null);

        return result
                .stream()
                .map(this::mapLineItem)
                .collect(toUnmodifiableList());
    }

    private void updateLineItem(Transaction transaction, String id, Invoice.LineItem lineItem) {
        var tableName = config.getVariable(INVOICE_LINE_ITEM_TABLE);
        var fieldValues = new HashMap<String, AttributeValue>();
        var resultTrackingName = Optional.ofNullable(lineItem.getTrackingName())
                .filter(trackingName -> trackingName.length() > 0)
                .orElse("");

        addValueIfPresent("description", lineItem.getDescription(), fieldValues);
        addValueIfPresent("quantity", lineItem.getQuantity(), fieldValues);
        addValueIfPresent("unitAmount", lineItem.getUnitAmount(), fieldValues);
        addValueIfPresent("itemCode", lineItem.getItemCode(), fieldValues);
        addValueIfPresent("accountCode", lineItem.getAccountCode(), fieldValues);
        addValueIfPresent("taxType", lineItem.getTaxType(), fieldValues);
        addValueIfPresent("taxAmount", lineItem.getTaxAmount(), fieldValues);
        addValueIfPresent("lineAmount", lineItem.getLineAmount(), fieldValues);
        addValueIfPresent("discountRate", lineItem.getDiscountRate(), fieldValues);
        addValueIfPresent("discountAmount", lineItem.getDiscountAmount(), fieldValues);
        addValueIfPresent("trackingName", resultTrackingName, fieldValues);

        addTransactionFieldsToLineItems(transaction, lineItem, fieldValues);

        var attributeNames = getAttributeNames(fieldValues);
        var updateExpression = getUpdateExpression(fieldValues.keySet(), EQ);

        if (Objects.isNull(lineItem.getTrackingName())) {
            updateExpression += " " + getRemoveExpression(Set.of("trackingName"));
            attributeNames.putAll(getAttributeNames(Set.of("trackingName")));
        }

        this.ddbClient.update(tableName, id, updateExpression, attributeNames, getAttributeValues(fieldValues));
    }

    public void update(Transaction transaction) {
        var tableName = config.getVariable(TRANSACTION_TABLE);
        var lineItemTable = config.getVariable(INVOICE_LINE_ITEM_TABLE);

        var fieldsValues = new HashMap<String, AttributeValue>();

        fieldsValues.put("updatedAt", new AttributeValue().withS(Instant.now().toString()));
        addValueIfPresent("tenantId", transaction.getTenant(), fieldsValues);
        addValueIfPresent("prePaymentId", transaction.getPrePaymentId(), fieldsValues);
        addValueIfPresent("overPaymentId", transaction.getOverPaymentId(), fieldsValues);
        addValueIfPresent("type", transaction.getType(), fieldsValues);
        addValueIfPresent("contactId", transaction.getContactId(), fieldsValues);
        addValueIfPresent("lineAmountTypes", transaction.getLineAmountTypes(), fieldsValues);
        addValueIfPresent("subTotal", transaction.getSubTotal(), fieldsValues);
        addValueIfPresent("totalTax", transaction.getTotalTax(), fieldsValues);
        addValueIfPresent("total", transaction.getTotal(), fieldsValues);
        addValueIfPresent("currency", transaction.getCurrency(), fieldsValues);
        addValueIfPresent("transactionOrganisationId", transaction.getOrganisation(), fieldsValues);
        addValueIfPresent("date", Utils.getInstantStringIfPresent(transaction.getDate()), fieldsValues);
        addValueIfPresent("currencyRate", transaction.getCurrencyRate(), fieldsValues);
        addValueIfPresent("url", transaction.getUrl(), fieldsValues);
        addValueIfPresent("status", transaction.getStatus(), fieldsValues);
        addValueIfPresent("transactionAccountId", transaction.getAccountId(), fieldsValues);
        addValueIfPresent("reference", transaction.getReference(), fieldsValues);
        addValueIfPresentB("balanceTransfer", transaction.isBalanceTransfer(), fieldsValues);
        addValueIfPresent("batchPaymentId", transaction.getBatchPaymentId(), fieldsValues);

        var updateExpression = getUpdateExpression(fieldsValues.keySet(), EQ);

        this.ddbClient.update(tableName, transaction.getId(), updateExpression, getAttributeNames(fieldsValues), getAttributeValues(fieldsValues));

        var existingLineItems = findLineItems(transaction.getId());

        transaction.getLineItems()
                .forEach(lineItem -> existingLineItems.stream().filter(current -> current.getLineItemId().equals(lineItem.getLineItemId())).findFirst()
                        .ifPresentOrElse( (item) -> {
                            updateLineItem(transaction, item.getId(), lineItem);
                        }, () -> {
                            ddbClient.put(lineItemTable, mapLineItem(transaction, transaction.getOrganisation(), lineItem));
                        }));

        existingLineItems
                .stream()
                .filter(lineItem -> transaction.getLineItems().stream().noneMatch(current -> current.getLineItemId().equals(lineItem.getLineItemId())))
                .forEach(lineItem -> ddbClient.deleteItem(lineItemTable, lineItem.getId()));
    }

    void deleteTransaction(String id) {
        ddbClient.deleteItem(config.getVariable(TRANSACTION_TABLE), id);

        findLineItems(id).forEach(lineItem -> ddbClient.deleteItem(config.getVariable(INVOICE_LINE_ITEM_TABLE), lineItem.getId()));
    }

    public Stream<Transaction> findByOrganisationAndBatchPaymentIds(String organisationId, Set<String> batchPaymentIds) {
        var tableName = config.getVariable(TRANSACTION_TABLE);
        var indexName = config.getVariable(TRANSACTION_ORGANISATION_INDEX);

        var fieldsValues = new HashMap<String, AttributeValue>();
        var filterValues = new HashMap<String, AttributeValue>();
        fieldsValues.put("transactionOrganisationId", new AttributeValue().withS(organisationId));

        var keyConditionExpression = getFilterExpression(fieldsValues.keySet());

        var batchPaymentIdList = batchPaymentIds.stream().collect(toUnmodifiableList());
        for (var i = 0; i < batchPaymentIdList.size(); i++) {
            filterValues.put("batchPaymentId" + i, new AttributeValue().withS(batchPaymentIdList.get(i)));
        }

        var attributeNames = getAttributeNames(Set.of("transactionOrganisationId", "batchPaymentId"));
        var filterExpression = getFilterOrExpression("batchPaymentId", filterValues.keySet());

        fieldsValues.putAll(filterValues);

        log.info("Query {}", keyConditionExpression);
        log.info("Query {}", attributeNames);
        log.info("Query {}", getAttributeValues(fieldsValues));
        log.info("Query {}", filterExpression);

        var result =
            this.ddbClient.query(tableName, indexName, keyConditionExpression, attributeNames, getAttributeValues(fieldsValues), filterExpression);

        return result
            .stream()
            .map(this::mapToTransaction);
    }
}
