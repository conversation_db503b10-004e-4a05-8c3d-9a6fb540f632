package com.rentancy.integrations.servicies.payments.bacs;

import com.rentancy.integrations.pojos.DataExporterResponse;
import com.rentancy.integrations.servicies.payments.view.PaymentReportView;

import java.util.ArrayList;
import java.util.List;

public interface BacsFactory {

    String BACS_END_LINE = "99";

    DataExporterResponse prepareFile(List<PaymentReportView> data);

    boolean supports(BacsFileBank isSupporting);

    default List<String> defaultList(PaymentReportView item) {
        List<String> list = new ArrayList<>();
        list.add(formatNull(item.getBeneficiarySortCode()));
        list.add(formatNull(item.getBeneficiaryAccountName()));
        list.add(formatNull(item.getBeneficiaryAccountNumber()));
        list.add(formatNull(item.getAmount()));
        list.add(formatNull(item.getDescription()));
        list.add(BACS_END_LINE);
        return list;
    }

    default String formatNull(String value) {
        if (value == null || value.isEmpty()) {
            return "";
        }
        return value;
    }

}
