package com.rentancy.integrations.servicies.payments.view;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
public class PaymentReportView {

    private String beneficiarySortCode;
    private String beneficiaryAccountName;
    private String beneficiaryAccountNumber;

    private String clientSortCode;
    private String description;

    private String amount;
    private String paymentDate;
}
