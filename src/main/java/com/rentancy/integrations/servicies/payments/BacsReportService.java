package com.rentancy.integrations.servicies.payments;

import com.rentancy.integrations.pojos.BacsReportPayoutRequest;
import com.rentancy.integrations.pojos.DataExporterResponse;
import com.rentancy.integrations.servicies.payments.bacs.BacsFactory;
import com.rentancy.integrations.servicies.payments.bacs.BacsFileBank;
import com.rentancy.integrations.servicies.payments.bacs.PaymentReportService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class BacsReportService {

    private final PaymentReportService paymentReportService;
    private final List<BacsFactory> bacsFactories;

    public DataExporterResponse prepareBacsFile(BacsReportPayoutRequest bacsReportPayoutRequest) {

        if (bacsReportPayoutRequest.getBank() == null) {
            throw new IllegalArgumentException("No bank format provided");
        }

        var bankFileFormat = BacsFileBank.valueOf(bacsReportPayoutRequest.getBank());
        var reportDataList = paymentReportService.generateBACSReport(bacsReportPayoutRequest.getOrganisationId(), bacsReportPayoutRequest.getStartDate(), bacsReportPayoutRequest.getEndDate());

        return bacsFactories.stream().filter(it -> it.supports(bankFileFormat))
                .map(factory -> factory.prepareFile(reportDataList)).findFirst().orElseThrow(()
                        -> new IllegalArgumentException(String.format("Unsupported bank %s", bacsReportPayoutRequest.getBank())));
    }

}

