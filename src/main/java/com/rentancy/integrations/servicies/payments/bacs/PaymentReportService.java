package com.rentancy.integrations.servicies.payments.bacs;

import com.rentancy.integrations.exceptions.BacsCalculationException;
import com.rentancy.integrations.pojos.Invoice;
import com.rentancy.integrations.pojos.InvoicePropertyView;
import com.rentancy.integrations.pojos.User;
import com.rentancy.integrations.pojos.XeroModel;
import com.rentancy.integrations.servicies.payments.view.PaymentReportView;
import com.rentancy.integrations.servicies.persistence.InvoiceService;
import com.rentancy.integrations.servicies.persistence.OrganisationService;
import com.rentancy.integrations.servicies.persistence.PropertyService;
import com.rentancy.integrations.servicies.persistence.UserService;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.text.MessageFormat;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public class PaymentReportService {

    private static final Logger log = LogManager.getLogger(PaymentReportService.class);

    private final OrganisationService organisationService;
    private final InvoiceService invoiceService;
    private final PropertyService propertyService;
    private final UserService userService;

    public PaymentReportService(OrganisationService organisationService, InvoiceService invoiceService, PropertyService propertyService, UserService userService) {
        this.organisationService = organisationService;
        this.invoiceService = invoiceService;
        this.propertyService = propertyService;
        this.userService = userService;
    }

    public List<PaymentReportView> generateBACSReport(String organisationId, String startDate, String endDate) {

        var organisation = Optional.ofNullable(organisationService.getOrganisation(organisationId))
                .orElseThrow(() -> new IllegalArgumentException(MessageFormat.format("Unknown organization {0}", organisationId)));

        try {
            var invoices = invoiceService.findOrganisationInvoicesWithingDateRange(organisation.getId(), startDate, endDate, false);
            log.info("Invoices loaded count: {}", invoices.size());
            var invoiceIdsList = invoices.stream().map(XeroModel::getId).collect(Collectors.toList());
            var propertiesInInvoicesRelation = propertyService.findPropertiesByInvoices(invoiceIdsList);
            var contactMap = userService.findContactsByInvoices(invoices);

            return invoices.stream().map(invoice -> preparePaymentReport(invoice, propertiesInInvoicesRelation, contactMap)).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("PaymentReportService error " + e.getMessage(), e);
            throw new BacsCalculationException("PaymentReportService error " + e.getMessage(), e);
        }
    }

    private String getPropertiesReferenceByInvoiceId(Invoice invoice, Map<String, List<InvoicePropertyView>> propertiesInInvoiceMap) {
        return Optional.ofNullable(propertiesInInvoiceMap.get(invoice.getId())).orElse(List.of()).stream().map(ip -> ip.getProperty().getReference()).collect(Collectors.joining(", "));
    }

    private PaymentReportView preparePaymentReport(Invoice invoice, Map<String, List<InvoicePropertyView>> propertiesInInvoicesRelation, Map<String, User> contactMap) {

        var reference = getPropertiesReferenceByInvoiceId(invoice, propertiesInInvoicesRelation);
        var beneficiary = contactMap.get(invoice.getId());

        if (beneficiary == null) {
            beneficiary = User.builder().fname("").sname("").build();
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("ddMMyyyy");

        var beneficiaryName = getBeneficiaryName(beneficiary);

        return PaymentReportView.builder()
                .clientSortCode(StringUtils.EMPTY) //to be filled
                .amount(invoice.getTotal())
                .description(reference)
                .beneficiaryAccountNumber(beneficiary.getBankAccountNumber())
                .beneficiarySortCode(beneficiary.getBankAccountSortCode())
                .beneficiaryAccountName(beneficiaryName)
                .paymentDate(Optional.ofNullable(invoice.getDate())
                        .map(instant -> ZonedDateTime.ofInstant(instant, ZoneId.systemDefault()))
                        .map(formatter::format).orElse(""))
                .build();
    }

    private String getBeneficiaryName(User beneficiary) {
        if (!StringUtils.isEmpty(beneficiary.getCompanyName())) {
            return beneficiary.getCompanyName();
        } else {
            return (beneficiary.getFname() + " " + beneficiary.getSname()).strip();
        }
    }


}
