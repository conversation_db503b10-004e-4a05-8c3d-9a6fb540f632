package com.rentancy.integrations.servicies.payments.bacs;

import com.rentancy.integrations.pojos.DataExporterResponse;
import com.rentancy.integrations.pojos.ExportFormat;
import com.rentancy.integrations.servicies.DataExporterService;
import com.rentancy.integrations.servicies.payments.view.PaymentReportView;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;

import static java.util.stream.Collectors.toUnmodifiableList;

@RequiredArgsConstructor
public class BarclaysBacsFactory implements BacsFactory {

    private static final Logger log = LogManager.getLogger(BarclaysBacsFactory.class);
    private final static BacsFileBank supported = BacsFileBank.BARCLAYS;
    private final DataExporterService dataExporterService;

    public DataExporterResponse prepareFile(List<PaymentReportView> data) {
        log.info("preparing {} report from {} items", BacsFileBank.BARCLAYS, data.size());
        var res = data.stream().map(this::defaultList).collect(toUnmodifiableList());

        return dataExporterService.doExport(ExportFormat.CSV, null, "bacs.csv", res);
    }

    public boolean supports(BacsFileBank isSupporting) {
        return supported == isSupporting;
    }
}
