package com.rentancy.integrations.servicies.payments.bacs;

import com.rentancy.integrations.pojos.DataExporterResponse;
import com.rentancy.integrations.pojos.ExportFormat;
import com.rentancy.integrations.servicies.DataExporterService;
import com.rentancy.integrations.servicies.payments.view.PaymentReportView;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

import static java.util.stream.Collectors.toUnmodifiableList;

@RequiredArgsConstructor
public class MetroBacsFactory implements BacsFactory {

    private static final Logger log = LogManager.getLogger(MetroBacsFactory.class);
    private final static BacsFileBank supported = BacsFileBank.METRO;

    private final DataExporterService dataExporterService;

    public DataExporterResponse prepareFile(List<PaymentReportView> inputList) {

        log.info("preparing {} report from {} items", BacsFileBank.METRO, inputList.size());
        var res = inputList.stream().map(this::defaultList).collect(toUnmodifiableList());

        return dataExporterService.doExport(ExportFormat.XLSX, null,"bacs.xlsx", res);
    }

    public boolean supports(BacsFileBank isSupporting) {
        return supported == isSupporting;
    }
}
