package com.rentancy.integrations.servicies.payments.bacs;

import com.rentancy.integrations.pojos.DataExporterResponse;
import com.rentancy.integrations.pojos.ExportFormat;
import com.rentancy.integrations.servicies.DataExporterService;
import com.rentancy.integrations.servicies.payments.view.PaymentReportView;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.List;

import static java.util.stream.Collectors.toUnmodifiableList;

@RequiredArgsConstructor
public class NatWestBacsFactory implements BacsFactory {

    private final static BacsFileBank supported = BacsFileBank.NAT_WEST;
    private final static String EMPTY = "";
    private final static String END_LINE = ",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,";
    private final DataExporterService dataExporterService;

    public DataExporterResponse prepareFile(List<PaymentReportView> data) {

        var res = data
                .stream()
                .map(item -> {
                    List<String> row = new ArrayList<>();
                    row.add(EMPTY);
                    row.add(EMPTY);
                    row.add(EMPTY);
                    row.add("01");
                    row.add(EMPTY);
                    row.add(EMPTY);
                    row.add(EMPTY);
                    row.add(EMPTY);
                    row.add(EMPTY);
                    row.add(EMPTY);
                    row.add(EMPTY);
                    row.add(EMPTY);
                    row.add(formatNull(item.getClientSortCode()));
                    row.add(EMPTY);
                    row.add(EMPTY);
                    row.add(EMPTY);
                    row.add(formatNull(item.getAmount()));
                    row.add(EMPTY);
                    row.add(formatNull(item.getPaymentDate()));
                    row.add(EMPTY);
                    row.add(EMPTY);
                    row.add(EMPTY);
                    row.add(EMPTY);
                    row.add(EMPTY);
                    row.add(formatNull(item.getBeneficiarySortCode()));
                    row.add(EMPTY);
                    row.add(EMPTY);
                    row.add(EMPTY);
                    row.add(EMPTY);
                    row.add(EMPTY);
                    row.add(formatNull(item.getBeneficiaryAccountNumber()));
                    row.add(EMPTY);
                    row.add(formatNull(item.getBeneficiaryAccountName()));
                    row.add(EMPTY);
                    row.add(EMPTY);
                    row.add(EMPTY);
                    row.add(formatNull(item.getDescription()));
                    row.add(END_LINE);

                    return row;
                }).collect(toUnmodifiableList());

        return dataExporterService.doExport(ExportFormat.CSV, null, "bacs.csv", res);

    }

    public boolean supports(BacsFileBank isSupporting) {
        return supported == isSupporting;
    }

}
