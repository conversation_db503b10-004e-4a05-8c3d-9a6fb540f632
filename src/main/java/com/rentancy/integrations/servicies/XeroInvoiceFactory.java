package com.rentancy.integrations.servicies;

import com.xero.models.accounting.*;

import java.time.Instant;
import java.util.List;

import static com.rentancy.integrations.util.Utils.generateRandomString;
import static com.rentancy.integrations.util.Utils.toXeroDate;

public class XeroInvoiceFactory {

    public static Invoice createAuthorisedBill(Contact contact, String organisationCurrency, List<LineItem> lineItems) {
        var now = Instant.now();
        var dueDate = toXeroDate(now.toEpochMilli());

        return new Invoice()
                .type(Invoice.TypeEnum.ACCPAY)
                .status(Invoice.StatusEnum.AUTHORISED)
                .date(dueDate)
                .dueDate(dueDate)
                .currencyCode(CurrencyCode.fromValue(organisationCurrency))
                .invoiceNumber(generateRandomString())
                .contact(contact.isSupplier(true).isCustomer(false))
                .lineAmountTypes(LineAmountTypes.NOTAX)
                .lineItems(lineItems);

    }

}
