package com.rentancy.integrations.servicies;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rentancy.integrations.pojos.Document;
import com.rentancy.integrations.pojos.DocumentDTO;
import com.rentancy.integrations.servicies.mainservice.BaseResp;
import com.rentancy.integrations.servicies.persistence.UserService;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


import javax.annotation.Nullable;


public class MainServiceClient {
  private static final Logger log = LogManager.getLogger(MainServiceClient.class);

  private final HttpClient client;
  private final ObjectMapper mapper;
  private final String mainServiceEndpoint;
  private final UserService userService;

  public MainServiceClient(HttpClient client, UserService userService) {
    this.client = client;
    this.mapper = new ObjectMapper();
    this.mainServiceEndpoint = System.getenv("MAIN_SERVICE_ENDPOINT");
    this.userService = userService;
  }

  public Document createConversationAttachment(String conversationId, String userId, String key, String name, String mimeType) {
    var reqData = new HashMap<String, Object>();
    reqData.put("conversationId", conversationId);
    var document = Document.builder()
        .key(key)
        .name(name)
        .description("Conversation attachment")
        .mimeType(mimeType)
        .build();
    reqData.put("document", document);
    try {
      var resData = httpPost("/main/document?userId=" + userId, reqData);
      BaseResp<DocumentDTO> resp = mapper.readValue(resData, new TypeReference<BaseResp<DocumentDTO>>() {
      });
      DocumentDTO documentDTO = resp.getData();
      return documentDTO.mapToDocument();
    } catch (Exception ex) {
      log.error("create conversation attachment fails: ", ex);
    }

    return null;
  }

  public String createDocument(Document document) {
    try {
      var resData = httpPost("/main/document", document);
      BaseResp<DocumentDTO> resp = mapper.readValue(resData, new TypeReference<BaseResp<DocumentDTO>>() {
      });
      return resp.getData().getId();
    } catch (Exception ex) {
      log.error("create document fails: ", ex);
    }

    return null;
  }

  public List<Document> findOrganisationDocuments(String organisationId, @Nullable String startDate, @Nullable String endDate) {
    Map<String,String> params = new HashMap<>();
    if (startDate != null) {
      params.put("startDate", startDate);
    }
    if (endDate != null) {
      params.put("endDate", endDate);
    }
    try {
      var resData = httpGet(String.format("/main/document/organisation/%s/document", organisationId), params);
      BaseResp<List<DocumentDTO>> resp = mapper.readValue(resData, new TypeReference<BaseResp<List<DocumentDTO>>>() {
      });
      return resp.getData().stream().map(DocumentDTO::mapToDocument).collect(Collectors.toList());
    } catch (Exception ex) {
      log.error("find organisation documents fails: ", ex);
    }
    return Collections.emptyList();
  }

  private <T> String httpPost(String path, T body) throws Exception {
    try {
      var url = this.mainServiceEndpoint + path;
      var post = new HttpPost(url);
      post.setHeader(new BasicHeader("Accept", "application/json"));
      post.setHeader(new BasicHeader("Content-Type", "application/json"));
      post.setEntity(new StringEntity(mapper.writeValueAsString(body), StandardCharsets.UTF_8));
      final HttpResponse response = client.execute(post);
      if (response.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
        log.error("http request failed.");
        throw new Exception("http request failure");
      }

      HttpEntity entity = response.getEntity();
      if (entity != null) {
        // Convert the entity content to a string
        return EntityUtils.toString(entity, "UTF-8");
      }
      return null;
    } catch (MalformedURLException e) {
      log.error("The constructed URL is invalid. This is a fundamental problem and should be fixed ASAP", e);
      throw new Exception("http request failure", e);
    } catch (ClientProtocolException e) {
      log.error("There was a problem creating the HTTP client", e);
      throw new Exception("http request failure", e);
    } catch (IOException e) {
      log.error("Http request error: ", e);
      throw new Exception("http request failure", e);
    }
  }

  private String httpGet(String path) throws Exception {
    return httpGet(path, null);
  }

  private String httpGet(String path, Map<String,String> params) throws Exception {
    try {
      URIBuilder uriBuilder = new URIBuilder(this.mainServiceEndpoint + path);
      if (!CollectionUtils.isEmpty(params)) {
        params.forEach(uriBuilder::addParameter);
      }
      var get = new HttpGet(uriBuilder.build());
      get.setHeader(new BasicHeader("Accept", "application/json"));
      get.setHeader(new BasicHeader("Content-Type", "application/json"));

      final HttpResponse response = client.execute(get);
      if (response.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
        log.error("http request failed: %s");
        throw new Exception("http request failure");
      }

      HttpEntity entity = response.getEntity();
      if (entity != null) {
        // Convert the entity content to a string
        return EntityUtils.toString(entity, "UTF-8");
      }
      return null;
    } catch (MalformedURLException e) {
      log.error("The constructed URL is invalid. This is a fundamental problem and should be fixed ASAP", e);
      throw new Exception("http request failure", e);
    } catch (ClientProtocolException e) {
      log.error("There was a problem creating the HTTP client", e);
      throw new Exception("http request failure", e);
    } catch (IOException e) {
      log.error("Http request error: ", e);
      throw new Exception("http request failure", e);
    }
  }
}
