package com.rentancy.integrations.servicies;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.servicies.persistence.*;
import com.rentancy.integrations.servicies.rent.NextInvoiceDateCalculator;
import org.apache.http.impl.client.HttpClients;

public class IntegrationFactory extends AbstractFactory {

    public static WhatsAppService getWhatsAppService(Config config) {
        var ddbClient = new DDBClient(config);
        var userService = new UserServiceImpl(new UserRepository(config, ddbClient));
        return new WhatsAppServiceImpl(
                config,
                new TwilioClient(config),
                new ConversationServiceImpl(new ConversationRepository(ddbClient, config),
                        new MessageRepository(ddbClient, config)),
                new OrganisationServiceImpl(new OrganisationRepository(config, ddbClient)),
                userService,
                new AppSyncServiceProvider(new LambdaClient(config), config),
                new S3Client(config),
                new SQSClient(),
                new MainServiceClient(HttpClients.createDefault(), userService)
        );
    }

    public static DataExporterService getDataExporterService(Config config) {
        var ddbClient = new DDBClient(config);
        return new DataExporterServiceImpl(
                config,
                new UserServiceImpl(new UserRepository(config, ddbClient)),
                buildPropertyService(config, ddbClient),
                new OrganisationServiceImpl(new OrganisationRepository(config, ddbClient)),
                new NextInvoiceDateCalculator()
        );
    }

    public static CloudwatchLogsService getCloudwatchLogsService(Config config) {
        var ddbClient = new DDBClient(config);
        return new CloudwatchLogsServiceImpl(config,
                new OrganisationServiceImpl(new OrganisationRepository(config, ddbClient)),
                new ConversationServiceImpl(new ConversationRepository(ddbClient, config),
                        new MessageRepository(ddbClient, config)),
                new AppSyncServiceProvider(new LambdaClient(config), config));
    }

    public static AsyncJobService getAsyncJobService(ConfigImpl config) {
        return new AsyncJobServiceImpl(config, new SQSClient());
    }


    public static OrganisationService getOrganisationService(Config config) {
        var ddbClient = new DDBClient(config);
        return new OrganisationServiceImpl(new OrganisationRepository(config, ddbClient));
    }
}
