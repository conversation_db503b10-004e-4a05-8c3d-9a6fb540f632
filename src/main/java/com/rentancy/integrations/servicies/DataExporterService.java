package com.rentancy.integrations.servicies;

import com.rentancy.integrations.pojos.DataExporterInput;
import com.rentancy.integrations.pojos.DataExporterResponse;
import com.rentancy.integrations.pojos.ExportFormat;
import com.rentancy.integrations.pojos.IncomeArrearsSummary;
import com.rentancy.integrations.pojos.JournalForecastReport;
import com.rentancy.integrations.pojos.JournalResult;
import com.rentancy.integrations.pojos.Organisation;
import com.rentancy.integrations.pojos.OrganisationStripeCharge;
import com.rentancy.integrations.pojos.PropertyLedgersSummary;

import javax.annotation.Nullable;
import java.util.List;
import java.util.function.Supplier;

public interface DataExporterService {

    DataExporterResponse exportContacts(DataExporterInput input);

    DataExporterResponse exportProperties(DataExporterInput input);

    DataExporterResponse exportContracts(DataExporterInput input);

    DataExporterResponse exportOrganisationProperties(String organisationId);

    DataExporterResponse generateTenantLedgerReport(PropertyLedgersSummary summary, ExportFormat format);

    DataExporterResponse generateIncomeArrearsReport(IncomeArrearsSummary arrearsSummary, ExportFormat format);

    DataExporterResponse generateMonthlyJournalReport(List<JournalResult> journalResults, ExportFormat exportFormat);

    DataExporterResponse generateJournalForecastReport(Organisation organisation, JournalForecastReport forecastReport, ExportFormat exportFormat);

    DataExporterResponse generateOrganisationReportAsExcel();

    DataExporterResponse generateOrganisationChargesReport(List<OrganisationStripeCharge> organisationStripeCharges);

    DataExporterResponse generateOrganisationSubscriptionReport(List<OrganisationStripeCharge> organisationStripeCharges);

    DataExporterResponse generateOrganisationSignupReport(List<Organisation> organisations);

    DataExporterResponse doExport(ExportFormat format, @Nullable List<String> headers, String fileName, List<List<String>> rows);
}
