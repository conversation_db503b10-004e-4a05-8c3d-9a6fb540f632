package com.rentancy.integrations.servicies.persistence;

import com.rentancy.integrations.pojos.Document;
import com.rentancy.integrations.servicies.MainServiceClient;

import lombok.RequiredArgsConstructor;

import java.util.List;

@RequiredArgsConstructor
public class DocumentServiceImpl implements DocumentService {

    private final MainServiceClient mainServiceClient;

    @Override
    public String create(Document document) {
        return mainServiceClient.createDocument(document);
    }

    @Override
    public List<Document> findOrganisationDocuments(String organisationId, String startDate, String endDate) {
        return mainServiceClient.findOrganisationDocuments(organisationId, startDate, endDate);
    }
}
