package com.rentancy.integrations.servicies;

import com.rentancy.integrations.pojos.*;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

public interface ReportService {

    LandlordReport generateLandlordReport(String propertyId, String startDate, String endDate);

    byte[] generateLandlordReportPdf(String propertyId, String startDate, String endDate, String format, StatementType statementType);

    byte[] generateDocumentReportPdf(String organisationId, String startDate, String endDate, String format, String sortBy);

    byte[] generateTaskReportPdf(String organisationId, String startDate, String endDate, String format, String sortBy, String userId);

    void generateClientGeneralReport(ClientStatementCommand command);

    void generatePropertyBalanceReport(PropertyReportCommand command);

    void generateClientBalanceReport(PropertyReportCommand payload);

    void generateClientStatementReport(ClientStatementCommand payload);

    byte[] generateTenantStatementReport(String organisationId, String tenantId, String startDate, String endDate, String format);

    CompletableFuture<AllClientSummary> generateAllClientSummary(String organisationId);

    List<PropertySummaryResponse> generatePropertySummary(String organisationId, PropertySummaryRequest propertyId);

    List<UserSummary> generateUserSummary(String organisationId, String ledgerCode);

    void sendTenancySchedule(TenancyScheduleCreationCommand command);

    void generateCashBalanceReport(CashBalanceReportCommand payload);

    String saveStatement(String cognitoId,
                       String propertyId,
                       String startDate,
                       String endDate,
                       String type,
                       String landlordBillId,
                       byte[] data,
                       boolean approved);

    void saveStatement(Statement statement);

    void sendStatementReport(String cognitoId, String statementId, boolean skip);

    void sendStatementReport(String landlordBillItemId);

    void generateOverseasResidentReport(String organisationId, String senderId);


    void processReportCaller(String organisationId, String recieverId, byte[] report, Instant now, Map<String, Object> parameters, String reportName, String reportType, String attachmentName, String emailContent);

    void generateSupplierLandlordStatementReport(SupplierLandlordStatementReportCommand payload);

    void sendRevenueReport();

    void sendOrganisationReportAsExcel();

    void sendOrganisationStripeChargesReport();

    void sendOrganisationDailySignupReport();

    List<Statement> findOrganisationStatements(String organisationId);

    Optional<Statement> findLastFinalizedStatement(String propertyId);

    Statement getStatement(String statementId);

    void deleteByLandlordBillId(String id);

    byte[] generateTenantLedgerReport(Organisation organisation, PropertyLedgersSummary summary);

    void exportOrganisationProperties(OrganisationPropertyReportCommand payload);

    byte[] generatePropertyLedgersSummary(Organisation organisation, String propertyId, PropertyLedgersSummary summary, BackendResponseFormat format);

    void generateIncomeArrearsSummary(String organisationId, String receiverId, IncomeArrearsSummary arrearsSummary);

    byte[] getIncomeArrearsSummaryReport(IncomeArrearsSummary arrearsSummary);

    void generateMonthlyJournalReport(Organisation organisation, List<JournalResult> journalResults);

    byte[] generateForecastReport(String organisationId, String startDate, int periodLengthInMonths);

    byte[] sendStatementReport(PayoutStatementGenerationCommand payload);

    String generatePropertyExtractReport(String propertyId);

    byte[] generateStripeChargeInvoicePdf(String organisationId, String chargeId);

    void publishReportViaEmail(byte[] reportData, String organisationId, String title, String senderId, String fileExt);
}
