package com.rentancy.integrations.servicies.factory;


import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.servicies.AbstractFactory;
import com.rentancy.integrations.servicies.DataExporterServiceImpl;
import com.rentancy.integrations.servicies.EventBridgeClient;
import com.rentancy.integrations.servicies.payments.BacsReportService;
import com.rentancy.integrations.servicies.payments.bacs.BarclaysBacsFactory;
import com.rentancy.integrations.servicies.payments.bacs.MetroBacsFactory;
import com.rentancy.integrations.servicies.payments.bacs.NatWestBacsFactory;
import com.rentancy.integrations.servicies.payments.bacs.PaymentReportService;
import com.rentancy.integrations.servicies.persistence.*;
import com.rentancy.integrations.servicies.rent.NextInvoiceDateCalculator;

import java.util.List;

//FIXME: replace with light DI
public class BacsReportFactory extends AbstractFactory {

    public static BacsReportService build(Config config, EventBridgeClient eventBridgeClient) {
        var ddbClient = new DDBClient(config);
        var organisationService = new OrganisationServiceImpl(new OrganisationRepository(config, ddbClient));
        var userService = new UserServiceImpl(new UserRepository(config, ddbClient));
        var propertyService = buildPropertyService(config, ddbClient);
        var invoiceService = buildInvoiceService(config, ddbClient);
        var dataExporterService = new DataExporterServiceImpl(config, userService, propertyService, organisationService, new NextInvoiceDateCalculator());
        var bacsFactory = List.of(new MetroBacsFactory(dataExporterService), new NatWestBacsFactory(dataExporterService), new BarclaysBacsFactory(dataExporterService));

        return new BacsReportService(new PaymentReportService(organisationService, invoiceService, propertyService, userService), bacsFactory);
    }


}
