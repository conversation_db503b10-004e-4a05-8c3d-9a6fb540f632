package com.rentancy.integrations.servicies;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.servicies.persistence.AccountRepository;
import com.rentancy.integrations.servicies.persistence.DDBClient;
import com.rentancy.integrations.servicies.persistence.InvoiceRepository;
import com.rentancy.integrations.servicies.persistence.InvoiceServiceImpl;
import com.rentancy.integrations.servicies.persistence.InvoiceWebhookEventsRepository;
import com.rentancy.integrations.servicies.persistence.JournalRepository;
import com.rentancy.integrations.servicies.persistence.LandlordBillRepository;
import com.rentancy.integrations.servicies.persistence.OrganisationRepository;
import com.rentancy.integrations.servicies.persistence.OrganisationServiceImpl;
import com.rentancy.integrations.servicies.persistence.OverPaymentRepository;
import com.rentancy.integrations.servicies.persistence.PaymentRepository;
import com.rentancy.integrations.servicies.persistence.PropertyRepository;
import com.rentancy.integrations.servicies.persistence.PropertyServiceImpl;
import com.rentancy.integrations.servicies.persistence.TenancyRepository;
import com.rentancy.integrations.servicies.persistence.TransactionRepository;
import com.rentancy.integrations.servicies.persistence.TransferRepository;
import com.rentancy.integrations.servicies.persistence.config.TenancyRepositoryConfig;

public class TenancyServiceFactory extends AbstractFactory{

    public static TenancyServiceImpl getTenancyService(Config config) {
        var ddbClient = new DDBClient(config);

        return new TenancyServiceImpl(
                buildPropertyService(config, ddbClient),
                new OrganisationServiceImpl(new OrganisationRepository(config, ddbClient)),
                new InvoiceServiceImpl(new InvoiceRepository(config, ddbClient),
                        new AccountRepository(config, ddbClient),
                        new LandlordBillRepository(config, ddbClient),
                        new PaymentRepository(config, ddbClient),
                        new TransactionRepository(config, ddbClient),
                        new TransferRepository(config, ddbClient),
                        new JournalRepository(config, ddbClient),
                        new InvoiceWebhookEventsRepository(config, ddbClient),
                        new PropertyRepository(config, ddbClient),
                        new OverPaymentRepository(config, ddbClient),
                        new TenancyRepository(new TenancyRepositoryConfig(), ddbClient),
                        new EventBridgeClient(),
                        config)
        );
    }
}
