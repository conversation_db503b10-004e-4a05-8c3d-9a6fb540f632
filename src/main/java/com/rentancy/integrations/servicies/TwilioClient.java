package com.rentancy.integrations.servicies;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.TwilioMessage;
import com.twilio.Twilio;
import com.twilio.rest.api.v2010.account.Message;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;


import java.util.List;
import java.util.Optional;
import java.net.URI;

class TwilioClient {

    private static final Logger log = LogManager.getLogger(TwilioClient.class);

    private final Config config;

    TwilioClient(Config config) {
        this.config = config;
    }

    TwilioMessage sendWhatsAppMessage(String accountSID, String authToken, String from, String to, String sender, String body, String presignedURL) {
        try {
            String content = null;
            List<URI> url = null;
            Twilio.init(accountSID, authToken);
            if (body != null) {
                content = Optional
                        .ofNullable(sender)
                        .map(s -> body + "\n" + s)
                        .orElse(body);
            }

            if (presignedURL != null) {
                url = List.of(URI.create(presignedURL));
            }
            var attachmentBuilder = Message.creator(
                    new com.twilio.type.PhoneNumber(to),
                    new com.twilio.type.PhoneNumber(from),
                    content
            );
            attachmentBuilder.setMediaUrl(url);
            var message = attachmentBuilder.create();
            log.info("Response - {}", message);
            return new TwilioMessage(message.getSid(), message.getStatus().toString());
        } catch (Exception e) {
            throw new IllegalStateException(e.getMessage());
        }
    }
}
