package com.rentancy.integrations.servicies;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.exceptions.IntegrationNotFoundException;
import com.rentancy.integrations.pojos.AppSyncLambdaPayload;
import com.rentancy.integrations.pojos.Integration;
import com.rentancy.integrations.servicies.persistence.IntegrationRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static com.rentancy.integrations.pojos.Integration.IntegrationStatus.CONNECTED;
import static com.rentancy.integrations.util.JSONUtils.wrappedToJsonString;
import static java.util.stream.Collectors.toUnmodifiableList;

@RequiredArgsConstructor
public class IntegrationServiceImpl implements IntegrationService {

    private static final Logger log = LogManager.getLogger(IntegrationServiceImpl.class);

    private static final String UPDATE_INTEGRATION_MUTATION = "update-integration.graphql";

    private final Oauth2Client oauth2Client;
    private final IntegrationRepository integrationRepository;
    private final LambdaClient lambdaClient;
    private final Config config;

    @Override
    public void findOrCreateIntegration(String organisationId, String userId, String url, String state, Instant startDate, com.rentancy.integrations.pojos.Integration.IntegrationService type) {
        Optional.ofNullable(integrationRepository.findByOrganisationAndUser(organisationId, userId, type))
                .map(integration -> integrationRepository.update(integration.update(url, state, startDate, type)))
                .orElseGet(() -> integrationRepository.create(Integration.create(organisationId, userId, url, state, startDate, type)));
    }

    @Override
    public Integration findIntegration(String organisationId, String userId, Integration.IntegrationService type) {
        return Optional.ofNullable(integrationRepository.findByOrganisationAndUser(organisationId, userId, type))
                .orElseThrow(() -> new IntegrationNotFoundException("Integration not found for " + organisationId));
    }

    @Override
    public Integration findConnectedIntegration(String organisationId, String userId, Integration.IntegrationService type) {
        return Optional.ofNullable(integrationRepository.findByOrganisationAndUser(organisationId, userId, type))
                .filter(integration -> CONNECTED == integration.getStatus())
                .map(this::updateToken)
                .orElseThrow(() -> new IntegrationNotFoundException("Integration not found for " + organisationId));
    }

    @Override
    public Integration findConnectedIntegrationByOrgId(String organisationId) {
        return integrationRepository.findByOrganisation(organisationId)
                .stream()
                // Organisation can only be connected with one integration, therefore updateToken will be executed once
                .map(this::updateToken)
                .findAny()
                .orElseThrow(() -> new IntegrationNotFoundException("Integration not found for " + organisationId));
    }

    @Override
    public List<Integration> findConnectedIntegrations(Integration.IntegrationService type) {
        return integrationRepository
                .findConnectedIntegrations()
                .stream()
                .filter(integration -> type == integration.getType())
                .map(this::updateToken)
                .collect(toUnmodifiableList());
    }

    @Override
    public Integration findIntegration(String tenantId, Integration.IntegrationService type) {
        return Optional
                .ofNullable(integrationRepository.findByTenantId(tenantId, type))
                .map(this::updateToken)
                .orElse(null);
    }

    @Override
    public Integration findIntegrationByOrganisationId(String organisationId, Integration.IntegrationService type) {
        return integrationRepository.findByOrganisation(organisationId)
                .stream()
                .filter(integration -> integration.getType().equals(type))
                .findAny()
                .orElse(null);
    }

    @Override
    public Integration findByOrganisationWithTokenRefresh(String organisationId, Integration.IntegrationService type) {
        return integrationRepository.findByOrganisation(organisationId)
                .stream()
                .filter(integration -> integration.getType().equals(type))
                .findAny()
                .map(this::updateToken)
                .orElse(null);
    }

    @Override
    public Integration findIntegrationItem(String tenantId, Integration.IntegrationService type) {
        return integrationRepository.findByTenantId(tenantId, type);
    }

    private Integration updateToken(Integration integration) {
        try {
            var refreshToken = integration.getRefreshToken();
            var expirationDate = integration.getExpirationDate();
            var organisationId = integration.getOrganisationId();

            if (Objects.nonNull(refreshToken) && Objects.nonNull(expirationDate) && Duration.between(Instant.now().plus(11, ChronoUnit.MINUTES), expirationDate).isNegative()) {
                var response = oauth2Client.refreshToken(refreshToken);
                var updated = integration
                        .toBuilder()
                        .accessToken(response.getAccessToken())
                        .refreshToken(response.getRefreshToken())
                        .expirationDate(Instant.now().plus(response.getExpiration(), ChronoUnit.SECONDS))
                        .build();
                integrationRepository.update(updated);
                log.info("Token refreshed - {}", organisationId);

                return updated;
            }
        } catch (Exception e) {
            log.error("Failed to update token - " + integration.getOrganisationId(), e);
        }

        return integration;
    }

    @Override
    public void updateIntegration(Integration integration) {
        integrationRepository.update(integration);
    }

    @Override
    public String updateIntegrationCallXeroCounter(String tenant, String remainingCalls) {
        var integration = findIntegrationItem(tenant, Integration.IntegrationService.XERO);
        log.info("Updating remaining calls for Xero - {} {}", integration.getOrganisationId(), remainingCalls);
        updateIntegration(integration.toBuilder().remainingCallAmount(Integer.parseInt(remainingCalls)).build());
        return integration.getId();
    }

    @Override
    public void updateIntegrationStatus(String organisationId, Integration.IntegrationStatus status, Integration.IntegrationService type) {
        //todo Add the type parameter of xero or quick_books?
        lambdaClient.invoke(config.getAppsyncLambdaName(), wrappedToJsonString(getLambdaPayload(organisationId, type, status)));
    }

    private AppSyncLambdaPayload getLambdaPayload(String organisationId, Integration.IntegrationService type, Integration.IntegrationStatus status) {
        var mutation = new String(config.getResource(UPDATE_INTEGRATION_MUTATION));
        var variables = Map.of("organisationId", organisationId, "type", type.name(), "status", status.name());

        return new AppSyncLambdaPayload(mutation, variables, true);
    }

    @Override
    public void createMetadata(String integrationId, Integration.IntegrationMetadata metadata) {
        var integration = integrationRepository.getIntegration(integrationId);
        var integrationMetadata = new ArrayList<>(integration.getMetadata());
        integrationMetadata.add(metadata);
        integration.setMetadata(integrationMetadata);
        integrationRepository.update(integration);
    }

    @Override
    public void updateMetadata(String integrationId, Integration.IntegrationMetadata metadata) {
        var integration = integrationRepository.getIntegration(integrationId);
        var item = integration
                .getMetadata()
                .stream()
                .filter(meta -> meta.getRecourseType() == metadata.getRecourseType())
                .findAny()
                .orElseThrow(() -> new IllegalStateException("Failed to find metadata - " + integrationId + " " + metadata));
        item.setLastUpdatedDate(metadata.getLastUpdatedDate());
        item.setRetryCount(metadata.getRetryCount());
        item.setErrorMessage(metadata.getErrorMessage());
        integrationRepository.update(integration);
    }

    @Override
    public void deleteIntegration(String id) {
        integrationRepository.delete(id);
    }
}
