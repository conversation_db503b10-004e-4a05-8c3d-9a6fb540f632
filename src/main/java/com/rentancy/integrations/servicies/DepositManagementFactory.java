package com.rentancy.integrations.servicies;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.servicies.persistence.*;
import com.rentancy.integrations.servicies.persistence.config.TenancyRepositoryConfig;

public class DepositManagementFactory extends AbstractFactory {
    public static DepositManagementServiceImpl2 getDepositManagement(Config config) {
        var ddbClient = new DDBClient(config);
        var propertyRepository = new PropertyRepository(config, ddbClient);
        var propertyService = buildPropertyService(config, ddbClient);
        var invoiceService = new InvoiceServiceImpl(new InvoiceRepository(config, ddbClient),
                new AccountRepository(config, ddbClient),
                new LandlordBillRepository(config, ddbClient),
                new PaymentRepository(config, ddbClient),
                new TransactionRepository(config, ddbClient),
                new TransferRepository(config, ddbClient),
                new JournalRepository(config, ddbClient),
                new InvoiceWebhookEventsRepository(config, ddbClient),
                propertyRepository,
                new OverPaymentRepository(config, ddbClient),
                new TenancyRepository(new TenancyRepositoryConfig(), ddbClient),
                new EventBridgeClient(),
                config);

        return new DepositManagementServiceImpl2(propertyService, invoiceService,
                new TenancyServiceImpl(
                        propertyService,
                        new OrganisationServiceImpl(new OrganisationRepository(config, ddbClient)),
                        invoiceService
                ),
                new UserServiceImpl(new UserRepository(config, ddbClient))
        );
    }

    public static DepositManagementExportServiceImpl getDepositManagementExport(DepositManagement depositManagement) {
        return new DepositManagementExportServiceImpl(depositManagement);
    }
}