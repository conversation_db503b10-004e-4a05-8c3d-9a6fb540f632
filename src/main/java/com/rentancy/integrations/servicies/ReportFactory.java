package com.rentancy.integrations.servicies;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.servicies.persistence.*;
import com.rentancy.integrations.servicies.persistence.DocumentServiceImpl;
import com.rentancy.integrations.servicies.rent.NextInvoiceDateCalculator;

import org.apache.http.impl.client.HttpClients;
import org.springframework.web.client.RestTemplate;

import static com.rentancy.integrations.config.Config.JASPER_SERVER_URL;

public class ReportFactory extends AbstractFactory{

    public static ReportService getReportService(Config config) {
        var objectMapper = new ObjectMapper();
        var restTemplate = new RestTemplate();
        var jasperUrl = config.getVariable(JASPER_SERVER_URL);
        var ddbClient = new DDBClient(config);
        var lambdaClient = new LambdaClient(config);
        var eventBridgeClient = new EventBridgeClient();

        var organisationService = new OrganisationServiceImpl(new OrganisationRepository(config, ddbClient));
        var userService = new UserServiceImpl(new UserRepository(config, ddbClient));
        var propertyService = buildPropertyService(config, ddbClient);

        return new ReportServiceImpl(
                config,
                propertyService,
                organisationService,
                buildInvoiceService(config, ddbClient),
                userService,
                new DocumentServiceImpl(new MainServiceClient(HttpClients.createDefault(), userService)),
                new BoardServiceImpl(new TaskRepository(config, ddbClient),
                        new BoardColumnRepository(config, ddbClient)),
                new JasperServerClient(objectMapper, restTemplate, jasperUrl),
                TenancyServiceFactory.getTenancyService(config),
                new SQSClient(),
                new S3Client(config),
                new AppSyncServiceProvider(lambdaClient,config),
                new ReportRepository(config, ddbClient),
                new DataExporterServiceImpl(config, userService, propertyService, organisationService, new NextInvoiceDateCalculator())
        );
    }
}
