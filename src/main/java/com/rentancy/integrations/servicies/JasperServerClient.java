package com.rentancy.integrations.servicies;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Nullable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RequiredArgsConstructor
class JasperServerClient {

    private static final String DEFAULT_FORMAT = "pdf";

    private static final Logger log = LogManager.getLogger(JasperServerClient.class);

    private final ObjectMapper objectMapper;
    private final RestTemplate template;
    private final String rootUrl;

    private byte[] generate(Map<String, Object> parameters, String fileName, @Nullable String format) {
        var queryParameters = new LinkedMultiValueMap<String, String>();
        queryParameters.put("inputFileName", List.of(fileName));
        queryParameters.put("format", List.of(Optional.ofNullable(format).orElse(DEFAULT_FORMAT)));

        var url = UriComponentsBuilder.fromUriString(rootUrl)
                .path("/generate")
                .queryParams(queryParameters)
                .build()
                .toUriString();

        try {
            log.info("Url = {} parameters= {}", url, objectMapper.writeValueAsString(parameters));
        } catch (Exception e) {
            log.info("Json serialisation failed", e);
        }

        return template.postForObject(url, parameters, byte[].class);
    }

    byte[] generateLandlordReport(Map<String, Object> parameters, String format) {
        return generate(parameters, "landlordStatement", format);
    }

    byte[] generateLandlordTemplateReport(Map<String, Object> parameters, String format) {
        return generate(parameters, "landlordStatementTemplate", format);
    }

    byte[] generateLandlordTemplateWithoutFirstColumnsReport(Map<String, Object> parameters, String format) {
        return generate(parameters, "landlordStatementWithoutFirstColumns", format);
    }

    public byte[] generateDocumentReport(Map<String, Object> parameters, String format) {
        return generate(parameters, "documentReport", format);
    }

    byte[] generateTaskReport(Map<String, Object> parameters, String format) {
        return generate(parameters, "taskReport", format);
    }

    byte[] generateTenancySchedule(Map<String, Object> parameters, String format) {
        return generate(parameters, "tenancySchedule", format);
    }

    byte[] generatePropertyBalanceReport(Map<String, Object> parameters, String format) {
        return generate(parameters, "propertyBalanceReport", format);
    }

    byte[] generateClientBalanceReport(Map<String, Object> parameters, String format) {
        return generate(parameters, "clientBalanceReport", format);
    }

    byte[] generateClientStatement(Map<String, Object> map, String format) {
        return generate(map, "clientStatement", format);
    }

    byte[] generateTenantStatement(Map<String, Object> map, String format) {
        return generate(map, "tenantStatement", format);
    }

    public byte[] generateCashBalanceClientBalance(Map<String, Object> map, String format) {
        return generate(map, "cashBalanceClientBalance", format);
    }

    public byte[] generateCashBalanceMainPage(Map<String, Object> map, String format) {
        return generate(map, "cashBalanceMainPage", format);
    }

    public byte[] generateOverseasResidentReport(Map<String, Object> map) {
        return generate(map, "overseasResidentReport", null);
    }

    public byte[] generateSupplierLandlordReport(Map<String, Object> map) {
        return generate(map, "supplierLandlordStatementReport", null);
    }

    public byte[] generateRevenueReport(HashMap<String, Object> map) {
        return generate(map, "revenueReport", null);
    }

    public byte[] generateTenantLedgerReport(Map<String, Object> map) {
        return generate(map, "tenantLedgerReport", null);
    }

    public byte[] generatePayoutStatementReport(HashMap<String, Object> map) {
        return generate(map, "parentPropertyStatementReport", null);
    }

    public byte[] generateSplitPayoutStatementReport(HashMap<String, Object> map) {
        return generate(map, "parentPropertyPayoutSplitStatementReport", null);
    }

    public byte[] generatePage2PayoutStatementReport(HashMap<String, Object> map) {
        return generate(map, "parentGroupByPropertiesHead", null);
    }

    public byte[] generatePropertyExtractReportPage1(HashMap<String, Object> map) {
        return generate(map, "propertyExtractStartPage", null);
    }

    public byte[] generatePropertyExtractReportPage2(HashMap<String, Object> map) {
        return generate(map, "propertyExtractReportFirstPage", null);
    }

    public byte[] generatePropertyExtractReportPage3(HashMap<String, Object> map) {
        return generate(map, "propertyExtractReportSecondPage", null);
    }

    public byte[] generateStripeChargeInvoice(HashMap<String, Object> map) {
        return generate(map, "stripeChargeInvoice", null);
    }

    public byte[] generatePayoutStatementYPPReport(HashMap<String, Object> map) {
        return generate(map, "parentPropertyYPPStatementReportSummaryPage", null);
    }

    public byte[] generatePayoutStatementYPPIncomeReport(HashMap<String, Object> map) {
        return generate(map, "parentPropertyYPPStatementReportIncomePage", null);
    }

    public byte[] generatePayoutStatementYPPExpenseReport(HashMap<String, Object> map) {
        return generate(map, "parentPropertyYPPStatementReportExpensePage", null);
    }
}
