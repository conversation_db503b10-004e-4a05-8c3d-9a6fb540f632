package com.rentancy.integrations.servicies;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.servicies.persistence.*;
import com.rentancy.integrations.servicies.persistence.config.TenancyRepositoryConfig;
import com.rentancy.integrations.servicies.xero_integration.queue.InvoiceOutboundSender;
import com.xero.api.ApiClient;
import org.springframework.boot.web.client.RestTemplateBuilder;

public class XeroFactory extends AbstractFactory {

    public static XeroService getXeroService(Config config) {
        var ddbClient = new DDBClient(config);
        var oauth2Client = new XeroOauth2Client(config, new ApiClient());
        var lambdaClient = new LambdaClient(config);
        var integrationService = new IntegrationServiceImpl(oauth2Client, new IntegrationRepository(config, ddbClient), lambdaClient, config);
        return getXeroService(config, ddbClient, oauth2Client, lambdaClient, integrationService);
    }

    public static XeroService getXeroService(
            Config config,
            DDBClient ddbClient,
            Oauth2Client oauth2Client,
            LambdaClient lambdaClient,
            IntegrationService integrationService) {
        var organisationService = new OrganisationServiceImpl(new OrganisationRepository(config, ddbClient));
        var invoiceService = new InvoiceServiceImpl(new InvoiceRepository(config, ddbClient),
                new AccountRepository(config, ddbClient),
                new LandlordBillRepository(config, ddbClient),
                new PaymentRepository(config, ddbClient),
                new TransactionRepository(config, ddbClient),
                new TransferRepository(config, ddbClient),
                new JournalRepository(config, ddbClient),
                new InvoiceWebhookEventsRepository(config, ddbClient),
                new PropertyRepository(config, ddbClient),
                new OverPaymentRepository(config, ddbClient),
                new TenancyRepository(new TenancyRepositoryConfig(), ddbClient),
                new EventBridgeClient(),
                config);

        return new XeroServiceImpl(
                config,
                oauth2Client,
                integrationService,
                new XeroDataMapperImpl(organisationService,
                        invoiceService,
                        new UserServiceImpl(new UserRepository(config, ddbClient)),
                        buildPropertyService(config, ddbClient),
                        ReportFactory.getReportService(config),
                        new IntegrationServiceImpl(new XeroOauth2Client(config, new ApiClient()), new IntegrationRepository(config, ddbClient), new LambdaClient(config), config),
                        new SQSClient(),
                        config),
                new UserServiceImpl(new UserRepository(config, ddbClient)),
                organisationService,
                invoiceService,
                ReportFactory.getReportService(config),
                getXeroClient(config, integrationService),
                new SQSClient(),
                new InvoiceOutboundSender(config, new SQSClient())
        );
    }

    public static XeroClient getXeroClient(Config config, IntegrationService integrationService) {
        var restTemplate = new RestTemplateBuilder()
                .rootUri(config.getXeroApiRootPath())
                .defaultHeader("Content-Type", "application/json")
                .defaultHeader("Accept", "application/json")
                .defaultHeader("Encoding-Type", "UTF-8")
                .build();

        return new XeroClient(restTemplate, integrationService::updateIntegrationCallXeroCounter);
    }
}
