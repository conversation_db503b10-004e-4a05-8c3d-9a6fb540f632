package com.rentancy.integrations.servicies;

import com.amazonaws.AmazonClientException;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.services.eventbridge.AmazonEventBridge;
import com.amazonaws.services.eventbridge.AmazonEventBridgeClientBuilder;
import com.amazonaws.services.eventbridge.model.PutEventsRequest;
import com.amazonaws.services.eventbridge.model.PutEventsResult;
import com.rentancy.integrations.exceptions.EventBridgeFailedOperationException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class EventBridgeClient {
    private static final Logger log = LogManager.getLogger(EventBridgeClient.class);

    private final AmazonEventBridge eventBridge;
    public EventBridgeClient() {
        this.eventBridge = AmazonEventBridgeClientBuilder
                .standard()
                .build();
    }

    public void putEvent(PutEventsRequest event) {
        try {
            PutEventsResult response = eventBridge.putEvents(event);
            response.getEntries().forEach(entry -> {
                if (entry.getEventId() == null) {
                    log.warn("Event failed: {}", entry.getErrorMessage());
                }
            });
            log.info("Event successfully put: {}", response);
        } catch (AmazonServiceException e) {
            log.error("AWS service error while putting event: {} - {}", e.getErrorCode(), e.getErrorMessage(), e);
            throw new EventBridgeFailedOperationException("AWS service error: " + e.getErrorMessage(), e);
        } catch (AmazonClientException e) {
            log.error("AWS client error while putting event: {}", e.getMessage(), e);
            throw new EventBridgeFailedOperationException("AWS client error: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Unexpected error while putting event", e);
            throw new EventBridgeFailedOperationException("Unexpected error while putting event", e);
        }
    }
}