package com.rentancy.integrations.servicies;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.*;
import com.rentancy.integrations.servicies.persistence.InvoiceService;
import com.rentancy.integrations.servicies.persistence.OrganisationService;
import com.rentancy.integrations.servicies.persistence.PropertyService;
import com.rentancy.integrations.servicies.persistence.UserService;
import com.rentancy.integrations.servicies.rent.NextInvoiceDateCalculator;
import com.rentancy.integrations.util.SentryErrors;
import com.rentancy.integrations.util.Utils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.Clock;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.rentancy.integrations.pojos.AutoInvoiceEvent.SourceType.*;
import static com.rentancy.integrations.pojos.Organisation.Type.AGENT;
import static com.rentancy.integrations.pojos.RentInvoiceHistory.RentHistoryType.*;
import static com.rentancy.integrations.util.JSONUtils.wrappedToJsonString;
import static com.rentancy.integrations.util.Utils.formatUser;
import static com.rentancy.integrations.util.Utils.getUserInitials;
import static java.lang.Thread.sleep;
import static java.time.ZoneOffset.UTC;
import static java.util.stream.Collectors.*;

@Slf4j
@AllArgsConstructor
abstract class PortfolioBaseProcessor {
    protected static final String DEFAULT_VALUE = "";
    static int DEFAULT_DELAY = 600;

    protected final Config config;
    protected final IntegrationService integrationService;
    protected final UserService userService;
    protected final InvoiceService invoiceService;
    protected final OrganisationService organisationService;
    protected final PropertyService propertyService;
    protected final SQSClient sqsClient;
    protected final TenancyService tenancyService;
    protected final ReportService reportService;
    protected final NextInvoiceDateCalculator nextInvoiceDateCalculator;
    private final AppSyncServiceProvider appSyncServiceProvider;
    private final Clock clock;

    public void collectAutoInvoiceTenancies(AutoInvoiceEvent.SourceType sourceType) {
        collectAutoInvoiceTenancies(sourceType, this.clock);
    }

    public void collectAutoInvoiceTenancies(AutoInvoiceEvent.SourceType sourceType, Clock clock) {
        var integrations = integrationService.findConnectedIntegrations(Integration.IntegrationService.XERO);
        var organisationIds = integrations.stream().map(Integration::getOrganisationId).collect(Collectors.toUnmodifiableSet());
        var organisationMap = organisationService.getOrganisations(organisationIds).collect(toMap(Organisation::getId, Function.identity()));

        log.info("Number of connected integrations - {}", integrations.size());

        var defaultDelay = DEFAULT_DELAY * 1000 / integrations.size();

        integrations.forEach(integration -> {
            try {
                var organisationId = integration.getOrganisationId();
                var organisation = organisationMap.get(organisationId);

                var tenantId = integration.getTenantId();
                var token = integration.getAccessToken();

                var invoiceTenancies = new ArrayList<Tenancy>();
                var tenancyCommission = new ArrayList<Tenancy>();
                var landlordCommissions = new ArrayList<Tenancy>();
                var overseasResidentBills = new ArrayList<Tenancy>();

                var now = Instant.now(clock).atZone(UTC);

                log.info("Organisation - {}", organisationId);

                propertyService
                        .findTenancies(organisationId, null, true)
                        .stream()
                        // TODO: mostly duplicated logic is already in the Tenancy class, probably can be simplified to tenancy.shouldAutoInvoiceForDay
                        .filter(tenancy -> "ACTIVE".equals(tenancy.getStatus()) || "PERIODIC".equals(tenancy.getStatus()))
                        .filter(tenancy -> Objects.nonNull(tenancy.getInvoiceStartDate())
                                && now.isAfter(Instant.parse(tenancy.getInvoiceStartDate()).atZone(UTC).withHour(0)))
                        .filter(tenancy -> "PERIODIC".equals(tenancy.getStatus()) || Objects.isNull(tenancy.getEndDate()) || now.isBefore(OffsetDateTime.parse(tenancy.getEndDate()).atZoneSameInstant(UTC)))
                        .filter(tenancy -> Objects.nonNull(tenancy.getSettings()))
                        .filter(tenancy -> tenancy.getSettings().isAutoInvoiceRent())
                        .forEach(tenancy -> {
                            var tenancyId = tenancy.getId();
                            var settings = tenancy.getSettings();
                            var dueDate = now.plusDays(settings.getInvoiceRentInAdvanceDays());
                            var nextInvoiceDate = nextInvoiceDateCalculator.calculate(now, tenancy);
                            var createInvoice = tenancy.shouldAutoInvoiceForDay(now, nextInvoiceDate);

                            var invoiceExists = invoiceService.autoInvoiceExists(tenancyId, dueDate, TENANCY_INVOICE);
                            log.info("Tenancy id - {}, Create invoice - {}, Invoice exists - {}", tenancyId, createInvoice, invoiceExists);
                            if (createInvoice && !tenancy.isDontCollectRent()) {
                                overseasResidentBills.add(tenancy);
                                if (!invoiceExists) {
                                    invoiceTenancies.add(tenancy);
                                }
                            }

                            var previousPaymentDate = dueDate.minusMonths(1);
                            var billExists = invoiceService.autoInvoiceExists(tenancy.getId(), previousPaymentDate, LANDLORD_COMMISSION);

                            // since tenancy.isDontCollectRent() is always false then !billExists is always true since
                            // LANDLORD_COMMISSION type of record in RentInvoiceHistory is only inserted for auto-invoicing
                            // if we add item to landlordCommissions list
                            if (createInvoice && !billExists && !settings.getFeeType().equals("NONE")) {
                                tenancyCommission.add(tenancy); // added always for createInvoice && !settings.getFeeType().equals("NONE") condition
                                if (tenancy.isDontCollectRent()) { // Hayley says that for HER clients this condition is always false
                                    landlordCommissions.add(tenancy);
                                }
                            }
                        });

                if (organisation.getType() == AGENT) {
                    if (!tenancyCommission.isEmpty() && sourceType == COMMISSION_BILL) {
                        log.info("Commission bill tenancy size - {}", tenancyCommission.size());
                        sendInvoiceTenancies(config.getCommissionInvoiceQueue(), tenantId, token, organisationId, tenancyCommission
                                .stream().filter(tenancy -> !"PERCENTAGE_OF_AMOUNT_RECEIVED".equals(tenancy.getSettings().getFeeType()))
                                .map(Tenancy::getId).collect(Collectors.toList()), clock);
                    }
                    if (!overseasResidentBills.isEmpty() && sourceType == OVERSEAS_RESIDENT_BILL) {
                        log.info("Overseas resident potential bills size - {}", overseasResidentBills.size());
                        sendInvoiceTenancies(config.getOverseasResidentBillQueue(), tenantId, token, organisationId, overseasResidentBills
                                .stream().map(Tenancy::getId).collect(Collectors.toList()), clock);
                    }
                }
                if (!invoiceTenancies.isEmpty() && sourceType == RENT_INVOICE) {
                    log.info("Rent invoice tenancy size - {}", invoiceTenancies.size());
                    sendInvoiceTenancies(config.getTenancyInvoiceQueue(), tenantId, token, organisationId, invoiceTenancies
                            .stream().map(Tenancy::getId).collect(Collectors.toList()), clock);
                }
                if (!landlordCommissions.isEmpty() && sourceType == LANDLORD_COMMISSION_BILL) {
                    log.info("Landlord commission bill tenancy size - {}", landlordCommissions.size());
                    sendInvoiceTenancies(config.getLandlordCommissionQueue(), tenantId, token, organisationId, landlordCommissions
                            .stream().map(Tenancy::getId).collect(Collectors.toList()), clock);
                }

                if (integrations.size() > 1) {
                    sleep(defaultDelay);
                }
            } catch (Exception e) {
                SentryErrors.catchException(e);
                log.error("Failed to get tenancies", e);
            }
        });
    }


    public void triggerAutoJournalNow(String organisationId) {
        var payload = TenancyInvoiceSenderPayload.builder()
                .organisationId(organisationId)
                .build();
        log.info("Payload size - {}", wrappedToJsonString(payload).getBytes().length);
        sqsClient.enqueue(config.getJournalBillSenderQueue(), wrappedToJsonString(payload));
    }

    public void collectAutoJournalTenancies() {
        var organisations = organisationService.findAllJournalOrganisations();

        var dayOfMonth = Instant.now(clock).atZone(UTC).getDayOfMonth();
        var isEndOfMonth = dayOfMonth == Instant.now(clock).atZone(UTC).toLocalDate().lengthOfMonth();

        var shouldRaiseJournalOrganisations = organisations.stream()
                .filter(organisation -> shouldRaiseForCurrentDay(organisation, isEndOfMonth, dayOfMonth))
                .collect(Collectors.toUnmodifiableList());

        if (shouldRaiseJournalOrganisations.isEmpty()) {
            log.info("No organisations with auto journaling enabled");
            return;
        }

        log.info("Number of organisations to journal - {}", shouldRaiseJournalOrganisations.size());

        var defaultDelay = DEFAULT_DELAY * 1000 / shouldRaiseJournalOrganisations.size();

        for (var organisation : shouldRaiseJournalOrganisations) {
            try {
                var payload = TenancyInvoiceSenderPayload.builder()
                        .organisationId(organisation.getId())
                        .build();

                log.info("Payload size - {}", wrappedToJsonString(payload).getBytes().length);

                sqsClient.enqueue(config.getJournalBillSenderQueue(), wrappedToJsonString(payload));
                sleep(defaultDelay);
            } catch (Exception e) {
                SentryErrors.catchException(e);
                log.error("Failed to generate journals:" + organisation.getId(), e);
            }
        }
    }

    private boolean shouldRaiseForCurrentDay(Organisation organisation, boolean isEndOfMonth, int dayOfMonth) {
        if (!isEndOfMonth) {
            return dayOfMonth == organisation.getJournalDate();
        } else {
            return organisation.getJournalDate() >= dayOfMonth;
        }
    }

    private void sendInvoiceTenancies(String queue, String tenant, String token, String organisation, List<String> tenancies, Clock clock) {
        var payload = TenancyInvoiceSenderPayload.builder()
                .tenant(tenant)
                .token(token)
                .organisationId(organisation)
                .tenancies(tenancies)
                .journalingTenantAmount(Map.of())
                .sendDate(Instant.now(clock))
                .issueDate(Instant.now(clock))
                .build();

        log.info("Payload size - {}", wrappedToJsonString(payload).getBytes().length);

        sqsClient.enqueue(queue, wrappedToJsonString(payload));
    }

    protected Organisation.InvoiceTemplate findInvoiceTemplate(Organisation organisation, String type) {
        return Optional
                .ofNullable(organisation)
                .map(Organisation::getInvoiceTemplates)
                .orElse(List.of())
                .stream()
                .filter(invoiceTemplate -> invoiceTemplate.getType().equals(type))
                .findAny()
                .orElse(null);
    }

    public void updateLandlordBill(String propertyId) {
        var property = propertyService.getProperty(propertyId);
        var organisationId = property.getOrganisation();
        var landlordBills = invoiceService.findOrganisationLandlordBills(organisationId);
        var statements = reportService.findOrganisationStatements(organisationId);

        landlordBills
                .stream()
                .filter(landlordBill -> propertyId.equals(landlordBill.getPropertyId()))
                .filter(landlordBill -> {
                    var landlordBillStatements = statements
                            .stream()
                            .filter(s -> landlordBill.getId().equals(s.getLandlordBillId()))
                            .sorted((s1, s2) -> s2.getCreatedAt().compareTo(s1.getCreatedAt()))
                            .limit(1)
                            .collect(Collectors.toList());

                    log.info("Statements - {}", landlordBillStatements);

                    return landlordBillStatements.isEmpty() || landlordBillStatements
                            .stream()
                            .anyMatch(s -> !s.isBillsUpdated() && !s.isPayedOut() && !s.isSent() && !s.isApproved());
                })
                .forEach(landlordBill -> {
                    var amount = calculateLandlordBillAmount(property);
                    invoiceService.saveLandlordBill(landlordBill.toBuilder().billAmount(amount.intValue()).build());
                    log.info("Updated landlord bill amount - {}", amount);
                });
    }

    /**
     * This function internally uses these ENV Variables, make sure you set them
     * <ul>
     *       <li>TENANCY_TABLE</li>
     *       <li>TENANCY_PROPERTY_INDEX</li>
     *       <li>ADDRESS_TABLE</li>
     *       <li>ADDRESS_PARENT_INDEX</li>
     *       <li>LANDLORD_BILL_TABLE</li>
     *       <li>LANDLORD_BILL_ORGANISATION_INDEX</li>
     * </ul>
     */
    public void saveLandlordBill(Tenancy tenancy, String amountPaid, String invoiceId, BigDecimal precalculatedAmount) {
        var property = propertyService.getProperty(tenancy.getProperty());
        // FIXME
        var originalAmount = precalculatedAmount == null ? calculateLandlordBillAmount(property) : precalculatedAmount;
        var amountPaidConverted = new BigDecimal(amountPaid).movePointRight(2);
        var landlordAmounts = splitLandlordAmount(property, amountPaidConverted, originalAmount);

        landlordAmounts.forEach(landlordAmount -> {
            var bill = LandlordBill.builder()
                    .id(UUID.randomUUID().toString())
                    .eventId("E-" + (invoiceService.findOrganisationLandlordBillsCount(tenancy.getOrganisation()) + 1))
                    .datePaid(Instant.now().toString())
                    .propertyId(property.getId())
                    .originalAmount(landlordAmount.getOriginalAmount().intValue())
                    .originalInvoiceId(invoiceId)
                    .propertyAddress(property.getAddressLine1())
                    .tenancyReference(tenancy.getReference())
                    .billAmount(landlordAmount.getBillAmount().intValue())
                    .approved(false)
                    .status(LandlordBill.LandlordBillStatus.NEW)
                    .landlordBillOrganisationId(tenancy.getOrganisation())
                    .landlordId(landlordAmount.getLandlord().getId())
                    .landlordName(formatUser(landlordAmount.getLandlord()))
                    .build();

            log.info("Saved landlord bill - {}, {}", bill.getId(), wrappedToJsonString(bill));
            invoiceService.saveLandlordBill(bill);
        });
    }

    private List<LandlordAmount> splitLandlordAmount(Property property, BigDecimal originalAmount, BigDecimal billAmount) {
        var splitOwnerShipEnabled = property.isSplitOwnershipEnabled();
        // Fixme
        if (true) {
            var landlordId = Utils.getPrimaryLandlordId(property).orElseThrow();
            var landlord = userService.findUser(landlordId, false);
            return List.of(new LandlordAmount(landlord, originalAmount, billAmount));
        }

        var landlords = property.getLandlords();
        var landlordUsers = userService.findUsers(new HashSet<>(landlords));

        return landlordUsers.stream().map(user -> {
            var ownedProperty = user
                    .getOwnedProperties()
                    .stream()
                    .filter(op -> property.getId().equals(op.getPropertyId()))
                    .findAny()
                    .orElseThrow();
            var percentage = BigDecimal.valueOf(ownedProperty.getPercentage()).movePointLeft(2);

            return new LandlordAmount(user,
                    originalAmount.multiply(percentage).movePointLeft(2),
                    billAmount.multiply(percentage).movePointLeft(2));
        }).collect(Collectors.toUnmodifiableList());
    }

    // FIXME: copied whole method
    public BigDecimal calculateLandlordBillAmount(Property property, List<Invoice.LineItem> lineItems) {
        var organisation = organisationService.getOrganisation(property.getOrganisation());
        var summary = tenancyService.calculatePropertySummary(property, organisation, lineItems);

        var originalAmount = summary.stream()
                .map(PropertyFinanceSummary::getDueToClient)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO)
                .movePointRight(2);

        if (originalAmount.intValue() < 0) {
            originalAmount = BigDecimal.ZERO;
        }

        return originalAmount;
    }

    protected BigDecimal calculateLandlordBillAmount(Property property) {
        var organisation = organisationService.getOrganisation(property.getOrganisation());
        var summary = tenancyService.calculatePropertySummary(property, organisation);

        var originalAmount = summary.stream()
                .map(PropertyFinanceSummary::getDueToClient)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO)
                .movePointRight(2);

        if (originalAmount.intValue() < 0) {
            originalAmount = BigDecimal.ZERO;
        }

        return originalAmount;
    }

    public void sendLandlordBill(String cognitoId, String id, String statementId) {
        var landlordBill = invoiceService.getLandlordBill(id);
        var sent = sendLandlordBill(landlordBill, null, null);

        if (statementId != null) {
            var statement = reportService.getStatement(statementId);
            var user = userService.findUserWithCognitoId(cognitoId);

            reportService.saveStatement(statement
                    .toBuilder()
                    .payedOut(sent)
                    .payedOutDate(Instant.now())
                    .payedOutBy(getUserInitials(formatUser(user)))
                    .build());

            if (sent && statement.isSent() && statement.isApproved() && statement.isBillsUpdated()) {
                landlordBill.setStatus(LandlordBill.LandlordBillStatus.COMPLETED);

                invoiceService.updateLandlordBill(landlordBill);
            }
        }
    }

    public void sendLandlordBills(String cognitoId, String statementId, LandlordBillsRequest request) {
        request.getLandlordBillIds().forEach(id -> sendLandlordBill(cognitoId, id, statementId));
    }

    protected boolean sendLandlordBill(LandlordBill bill, @Nullable String customLandlordId, @Nullable Integer billAmount) {
        var organisation = organisationService.getOrganisation(bill.getLandlordBillOrganisationId());

        return sendLandlordBill(organisation, bill, customLandlordId, billAmount);
    }

    public boolean invoiceRecordProcessed(String messageId) {
        return invoiceService.invoiceRecordProcessed(messageId);
    }

    public void saveInvoiceRecord(String messageId) {
        invoiceService.saveInvoiceHistory(List.of(RentInvoiceHistory
                .builder()
                .id(messageId)
                .type(RECORD)
                .successful(true)
                .message("OK")
                .build()));
    }

    public void matchInvoice(PortfolioInvoiceMatcherPayload payload, Organisation organisation) {
        var oldReference = payload.getPreviousReference();
        var newReference = payload.getCurrentReference();
        var organisationId = payload.getOrganisationId();
        var type = payload.getSourceType();
        var id = payload.getSourceId();
        var consumers = getInvoiceMatcherConsumers(id, organisationId, type);

        var oldInvoices = Optional
                .ofNullable(oldReference)
                .map(ref -> invoiceService.findInvoices(organisationId, ref))
                .orElse(List.of());
        var newInvoices = Optional
                .ofNullable(newReference)
                .map(ref -> invoiceService.findInvoices(organisationId, ref))
                .orElse(List.of());

        log.info("Old invoices - {}", oldInvoices);
        log.info("New invoices - {}", newInvoices);

        oldInvoices.forEach(consumers.getRemoveLinkMethod());
        newInvoices.forEach(consumers.getCreateLinkMethod());
    }

    public void postInvoiceHistory() {
        var dateFormatter = new SimpleDateFormat("yyyy-MM-dd");
        var date = dateFormatter.format(Date.from(Instant.now()));
        var invoiceHistory = invoiceService.findHistoryItems(date);

        log.info("History items - {}", wrappedToJsonString(invoiceHistory));

        var organisationHistoryItems = invoiceHistory
                .stream()
                .filter(rentInvoiceHistory -> Objects.nonNull(rentInvoiceHistory.getOrganisationId()))
                .collect(groupingBy(RentInvoiceHistory::getOrganisationId));

        organisationHistoryItems.forEach((organisationId, historyItems) -> {
            var organisation = organisationService.getOrganisation(organisationId);
            var botUser = organisation.getBotUser();
            var conversationId = organisation.getInvoiceConversation();
            var message = historyItems
                    .stream()
                    .filter(rentInvoiceHistory -> Objects.nonNull(rentInvoiceHistory.getTenancyId()))
                    .map(rentInvoiceHistory -> {
                        var tenancy = propertyService.getTenancy(rentInvoiceHistory.getTenancyId(), false);
                        String invoiceState;
                        String addingReportsManualy = "";
                        String decimalValue;
                        var property = propertyService.getProperty(tenancy.getProperty());
                        var user = userService.findUser(tenancy.getPrimaryTenant(), false);
                        var userLandlord = Optional.ofNullable(property.getPrimaryLandlordId())
                                .map(id -> userService.findUser(id, false))
                                .orElseGet(() -> {
                                    return userService.findUser(property.getLandlords().get(0), false);
                                });
                        var landlordName = formatUser(userLandlord);
                        var to = formatUser(user);

                        if (rentInvoiceHistory.isSuccessful()) {
                            invoiceState = "Invoice Added";
                        } else {
                            invoiceState = "Invoice failed";
                            addingReportsManualy = "Please add manually and report to Rentancy";
                        }

                        var formatter = new DecimalFormat("#0,000.00");
                        var formatLower = new DecimalFormat("#0.00");
                        double value = tenancy.getRent() / 100;
                        if (value >= 1000) {
                            decimalValue = formatter.format(value);
                        } else {
                            decimalValue = formatLower.format(value);
                        }

                        return invoiceState + "\n" + "\n" + "Property: " + property.getAddressLine1() + "\n" +
                                "Contract: " + tenancy.getReference() + "\n" +
                                "To: " + to + "\n" +
                                "Landlord: " + landlordName + "\n" +
                                "Value: " + decimalValue + "\n" +
                                addingReportsManualy + "\n";

                    }).collect(joining("\n"));
            appSyncServiceProvider.createMessage(organisationId, conversationId, botUser, message, "TEXT");
        });
    }

    public void bulkPayoutLandlordBills(LandlordBillBulkPayoutRequest request) {
        var items = request.getLandlordBillIds();
        var endDate = request.getEndDate();
        var sendStatement = request.isSendStatement();
        var cognitoId = request.getCognitoId();

        items.forEach(item -> {
            var startDate = item.getStartDate();
            var landlordBillId = item.getLandlordBillId();
            var landlordBill = invoiceService.getLandlordBill(landlordBillId);

            try {
                var propertyId = landlordBill.getPropertyId();
                var organisationId = landlordBill.getLandlordBillOrganisationId();
                var data = reportService.generateLandlordReportPdf(propertyId, startDate, endDate, "pdf", StatementType.FINALISED);

                var statementId = reportService.saveStatement(cognitoId, propertyId, startDate, endDate, "PROPERTY", landlordBillId, data, true);

                if (sendStatement) {
                    try {
                        reportService.sendStatementReport(cognitoId, statementId, false);
                    } catch (Exception e) {
                        log.error("Failed to send statement - " + statementId, e);
                    }
                }

                sendLandlordBill(cognitoId, landlordBillId, statementId);

                var invoices = invoiceService.findInvoicesWithPropertyIdAndStatus(propertyId, List.of(
                        com.xero.models.accounting.Invoice.StatusEnum.DRAFT.name(),
                        com.xero.models.accounting.Invoice.StatusEnum.SUBMITTED.name()
                ));

                if (!invoices.isEmpty()) {
                    updatePropertyBills(cognitoId, statementId, organisationId, invoices);
                }
            } catch (Exception e) {
                log.error("Failed to process landlord bill - " + landlordBillId, e);
            }
        });
    }

    private InvoiceMatcherConsumers getInvoiceMatcherConsumers(String sourceId, String organisationId, PortfolioInvoiceMatcherPayload.SourceType sourceType) {
        switch (sourceType) {
            case PROPERTY:
                return new InvoiceMatcherConsumers(invoice -> propertyService.removePropertyInvoices(sourceId, invoice.getId()), invoice -> propertyService.createPropertyInvoice(organisationId, sourceId, invoice.getId()));
            case TENANCY:
                return new InvoiceMatcherConsumers(invoice -> propertyService.removeTenancyInvoices(sourceId, invoice.getId()), invoice -> propertyService.createTenancyInvoice(sourceId, invoice.getId()));
            default:
                throw new IllegalArgumentException("Invalid source type - " + sourceType + " " + sourceId);
        }
    }

    protected String getRentDescription(Organisation.InvoiceTemplate invoiceTemplate,
                                        String tenancyTitle,
                                        String tenancyType,
                                        String invoiceStartDate,
                                        String invoiceEndDate) {
        var suffix = Optional.ofNullable(invoiceTemplate)
                .map(Organisation.InvoiceTemplate::getRentDescription)
                .filter(description -> !DEFAULT_VALUE.equals(description))
                .orElse(formatType(tenancyType) + ": " + tenancyTitle);

        return suffix + ", " + invoiceStartDate + " to " + invoiceEndDate;
    }

    private String formatType(String tenancyType) {
        try {
            if (Tenancy.TenancyType.AST.name().equals(tenancyType)) {
                return tenancyType;
            }
            var parts = tenancyType.split("_");

            var type = Arrays.stream(parts).map(String::toLowerCase).collect(joining(" "));
            var firstCharacter = String.valueOf(type.charAt(0)).toUpperCase();

            return type.length() > 1 ? firstCharacter.concat(type.substring(1)) : firstCharacter;
        } catch (Exception e) {
            log.error("Failed to format tenancy type - " + tenancyType, e);
        }

        return DEFAULT_VALUE;
    }

    abstract public void raiseTenancyInvoicesSafe(TenancyInvoiceSenderPayload payload, Organisation organisation);

    abstract protected boolean sendLandlordBill(Organisation organisation, LandlordBill bill, @Nullable String customLandlordId, @Nullable Integer billAmount);

    abstract public void createPropertyBill(PropertyBillCreationCommand body, Organisation organisation);

    abstract public void raiseCommissionBill(TenancyInvoiceSenderPayload payload, Organisation organisation);

    abstract public Map<String, String> raiseCommissionBillWithSqs(TenancyInvoiceSenderPayload payload, Organisation organisation);

    abstract public String createManualInvoice(String cognitoId, String tenancyId, Organisation organisation);

    abstract public void raiseLandlordCommission(String tenant, String token, List<String> tenancyIds, Organisation organisation);

    abstract protected void updatePropertyBills(String cognitoId, String statementId, String organisationId, List<com.rentancy.integrations.pojos.Invoice> invoices);

    abstract public void createTenancyInvoice(TenancyInvoiceInput invoiceInput, Organisation organisation);

    abstract public IncomeArrearsSummary getIncomeArrearsSummary(IncomeArrearsSummary.ArrearsSortBy sortBy,
                                                                 IncomeArrearsSummary.ArrearsSortOrder sortOrder, @Nullable String filter, int page, int limit, Organisation organisation);

    abstract public void raiseOverseasResidentBill(TenancyInvoiceSenderPayload payload, Organisation organisation);

    abstract public void addPropertyTrackingCode(PropertyTrackingCodePayload payload, Organisation organisation);

    abstract public List<InvoiceAttachment> getInvoiceAttachments(String invoiceId, Organisation organisation);

    abstract public void sendLandlordBillV2(Organisation organisation, LandlordBillsRequestV2 request);

    abstract public void sendLandlordBillV3(Organisation organisation, LandlordBillsRequestV3 request);

    @Data
    @AllArgsConstructor
    protected static class InvoiceMatcherConsumers {
        private Consumer<Invoice> removeLinkMethod;
        private Consumer<com.rentancy.integrations.pojos.Invoice> createLinkMethod;
    }

    @Data
    @AllArgsConstructor
    protected static class LandlordAmount {
        private User landlord;
        private BigDecimal originalAmount;
        private BigDecimal billAmount;
    }
}
