package com.rentancy.integrations.servicies.rent;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.exceptions.EntityNotFoundException;
import com.rentancy.integrations.pojos.Integration;
import com.rentancy.integrations.pojos.Organisation;
import com.rentancy.integrations.pojos.Tenancy;
import com.rentancy.integrations.pojos.TenancyInvoiceSenderPayload;
import com.rentancy.integrations.servicies.IntegrationService;
import com.rentancy.integrations.servicies.SQSClient;
import com.rentancy.integrations.servicies.persistence.OrganisationRepository;
import com.rentancy.integrations.servicies.persistence.TenancyRepository;
import com.rentancy.integrations.util.SecurityUtils;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.Value;
import lombok.With;
import lombok.extern.slf4j.Slf4j;

import java.time.Clock;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.List;

import static com.rentancy.integrations.pojos.Integration.IntegrationService.XERO;
import static com.rentancy.integrations.pojos.Organisation.Type.AGENT;
import static com.rentancy.integrations.util.JSONUtils.wrappedToJsonString;
import static com.rentancy.integrations.util.Utils.getOrFail;
import static java.time.ZoneOffset.UTC;
import static java.util.Optional.ofNullable;

@RequiredArgsConstructor
@Slf4j
public class EarlyInvoiceGenerator {

    private final NextInvoiceDateCalculator nextInvoiceDateCalculator;
    private final IntegrationService integrationService;
    private final OrganisationRepository organisationRepository;
    private final TenancyRepository tenancyRepository;
    private final SQSClient sqsClient;
    private final Config config;
    private final Clock clock;

    public EarlyInvoiceGeneratorResult generate(EarlyInvoiceGenerationPayload payload, String userOrganisation) {
        var tenancyId = payload.getTenancyId();
        var organisation = getOrFail(organisationRepository.getOrganisation(userOrganisation), new EntityNotFoundException(Organisation.class, userOrganisation));
        var tenancy = getOrFail(tenancyRepository.getTenancyWithSettings(tenancyId), new EntityNotFoundException(Tenancy.class, tenancyId));

        SecurityUtils.validateOrganizationAccess(userOrganisation, tenancy.getOrganisation(), Tenancy.class);

        var nextInvoiceDate = getOrFail(
                nextInvoiceDateCalculator.calculate(ZonedDateTime.now(clock), tenancy),
                new TenancyNotSuitableForEarlyInvoiceException("Could not calculate next invoice date for given tenancy")
        );

        log.debug("Next invoice date {} for tenancy {}", wrappedToJsonString(nextInvoiceDate), wrappedToJsonString(tenancy));
        var tenancyInvoiceSenderPayload = buildTenancyInvoiceSenderPayload(tenancy, nextInvoiceDate, userOrganisation);
        var nextSendDate = nextInvoiceDate.getSend().toInstant().atZone(UTC);

        if (!tenancy.shouldAutoInvoiceForDay(nextSendDate, nextInvoiceDate)) {
            throw new TenancyNotSuitableForEarlyInvoiceException("Tenancy does not meet requirements for early invoice generation");
        }
        var result = attemptToRaiseRentInvoice(tenancyInvoiceSenderPayload, tenancyId);
        if (organisation.getType() != AGENT) {
            log.warn("Commission and overseas resident bills generation skipped because organisation type is not {}", AGENT);
            return result;
        }
        result = attemptToRaiseOverseasResidentBill(tenancyInvoiceSenderPayload, tenancyId, result);
        if (tenancy.shouldRaiseCommission(nextSendDate, nextInvoiceDate)) {
            result = attemptToRaiseCommissionBill(tenancyInvoiceSenderPayload, tenancyId, result);
        }

        return result;
    }

    private EarlyInvoiceGeneratorResult attemptToRaiseRentInvoice(TenancyInvoiceSenderPayload payload, String tenancyId) {
        log.info("Sending async message to raise rent invoice for tenancy: {}...", tenancyId);
        sqsClient.enqueue(config.getTenancyInvoiceQueue(), wrappedToJsonString(payload));
        return EarlyInvoiceGeneratorResult.builder().rentInvoiceGenerationAttempted(true).build();
    }

    private EarlyInvoiceGeneratorResult attemptToRaiseOverseasResidentBill(TenancyInvoiceSenderPayload payload, String tenancyId, EarlyInvoiceGeneratorResult result) {
        log.info("Sending async message to raise overseas resident bill for tenancy: {}...", tenancyId);
        sqsClient.enqueue(config.getOverseasResidentBillQueue(), wrappedToJsonString(payload));
        return result.withOverseasResidentBillGenerationAttempted(true);
    }

    private EarlyInvoiceGeneratorResult attemptToRaiseCommissionBill(TenancyInvoiceSenderPayload payload, String tenancyId, EarlyInvoiceGeneratorResult result) {
        log.info("Sending async message to raise commission for tenancy: {}...", tenancyId);
        sqsClient.enqueue(config.getCommissionInvoiceQueue(), wrappedToJsonString(payload));
        return result.withCommissionBillGenerationAttempted(true);
    }

    private TenancyInvoiceSenderPayload buildTenancyInvoiceSenderPayload(Tenancy tenancy, NextInvoiceDate nextInvoiceDate, String userOrganisation) {
        log.info("Fetching {} credentials for organisation {}", userOrganisation, XERO);
        var integration = ofNullable(integrationService.findByOrganisationWithTokenRefresh(userOrganisation, XERO))
                .orElseThrow(() -> new EntityNotFoundException(Integration.class, "organisation", userOrganisation));

        return TenancyInvoiceSenderPayload.builder()
                .tenancies(List.of(tenancy.getId()))
                .token(integration.getAccessToken())
                .tenant(integration.getTenantId())
                .organisationId(userOrganisation)
                .sendDate(nextInvoiceDate.getSend().toInstant())
                .issueDate(Instant.now(clock))
                .build();
    }

    @Value
    @Builder
    @With
    public static class EarlyInvoiceGeneratorResult {
        boolean rentInvoiceGenerationAttempted;
        boolean commissionBillGenerationAttempted;
        boolean overseasResidentBillGenerationAttempted;
    }

}
