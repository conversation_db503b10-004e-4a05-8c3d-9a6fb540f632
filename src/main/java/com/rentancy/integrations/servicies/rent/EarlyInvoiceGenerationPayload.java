package com.rentancy.integrations.servicies.rent;

import com.rentancy.integrations.exceptions.InvalidRequestException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EarlyInvoiceGenerationPayload {
    private String tenancyId;

    public void validate() {
        if (tenancyId == null) {
            throw new InvalidRequestException("Tenancy ID is required");
        }
    }
}
