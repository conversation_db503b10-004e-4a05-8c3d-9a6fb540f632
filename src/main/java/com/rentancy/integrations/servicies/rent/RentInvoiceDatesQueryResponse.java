package com.rentancy.integrations.servicies.rent;

import com.rentancy.integrations.pojos.Tenancy;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.format.DateTimeFormatter;
import java.util.Optional;

import static java.time.ZoneOffset.UTC;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RentInvoiceDatesQueryResponse {
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE;

    private String nextInvoiceDueDate;
    private String nextInvoiceSendDate;
    private String lastPaymentCoveredTo;
    private String lastPayment;

    public RentInvoiceDatesQueryResponse(NextInvoiceDate nextInvoiceDate, Tenancy.Settings tenancySettings) {
        this.nextInvoiceDueDate = Optional.ofNullable(nextInvoiceDate)
                .map(NextInvoiceDate::getDue)
                .map(it -> it.toInstant().atZone(UTC))
                .map(FORMATTER::format)
                .orElse(null);
        this.nextInvoiceSendDate = Optional.ofNullable(nextInvoiceDate)
                .map(NextInvoiceDate::getSend)
                .map(it -> it.toInstant().atZone(UTC))
                .map(FORMATTER::format)
                .orElse(null);
        this.lastPaymentCoveredTo = Optional.ofNullable(tenancySettings)
                .map(Tenancy.Settings::getLastPaymentCoveredTo)
                .map(FORMATTER::format)
                .orElse(null);
        this.lastPayment = Optional.ofNullable(tenancySettings)
                .map(Tenancy.Settings::getLastPayment)
                .map(FORMATTER::format)
                .orElse(null);
    }

}
