package com.rentancy.integrations.servicies.rent;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Value;

import java.time.Instant;
import java.util.Date;
import java.util.Optional;

@Value
public class NextInvoiceDate {
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC")
    Date due;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC")
    Date send;

    public NextInvoiceDate(Instant due, Instant send) {
        if (due == null) {
            throw new IllegalStateException("Cannot construct NextInvoiceDate with null due date");
        }
        this.due = Date.from(due);
        this.send = Optional.ofNullable(send).map(Date::from).orElse(null);
    }

    public NextInvoiceDate(Date due, Date send) {
        if (due == null) {
            throw new IllegalStateException("Cannot construct NextInvoiceDate with null due date");
        }
        this.due = due;
        this.send = send;
    }
}
