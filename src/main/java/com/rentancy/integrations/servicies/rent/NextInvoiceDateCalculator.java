package com.rentancy.integrations.servicies.rent;

import com.rentancy.integrations.pojos.Tenancy;
import com.rentancy.integrations.pojos.Tenancy.TenancyPeriod;
import lombok.Value;
import lombok.experimental.Accessors;

import javax.annotation.Nullable;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.Date;

import static com.rentancy.integrations.pojos.Tenancy.TenancyPeriod.MONTHLY;
import static com.rentancy.integrations.pojos.Tenancy.TenancyPeriod.UK_QUARTERLY;
import static java.time.ZoneOffset.UTC;
import static java.time.temporal.ChronoUnit.*;
import static java.util.Optional.ofNullable;

public class NextInvoiceDateCalculator {

    @Nullable
    public NextInvoiceDate calculate(ZonedDateTime now, Tenancy tenancy) {
        var settings = tenancy.getSettings();
        if (tenancy.getPeriod() == null || settings.getInvoiceRentInAdvanceDays() == null || tenancy.getStartDate() == null) {
            return null;
        }

        var nextInvoiceDueDate = calculateDueDate(now, tenancy);
        var nextInvoiceSendDate = calculateSendDate(tenancy, nextInvoiceDueDate);
        return nextInvoiceSendDate == null ? null : new NextInvoiceDate(nextInvoiceDueDate, nextInvoiceSendDate);
    }

    public RentPeriod calculateRentPeriodForDueDate(ZonedDateTime currentDueDate, Tenancy tenancy) {
        if (tenancy.getPeriod() == null) {
            throw new IllegalArgumentException("Unable to calculate current rent period without tenancy's period");
        }

        if (tenancy.getPeriod() == UK_QUARTERLY) {
            var nextDueDate = currentDueDate.plusDays(1);
            while (!isUkQuarterlyDate(nextDueDate)) {
                nextDueDate = nextDueDate.plusDays(1);
            }
            return RentPeriod.of(currentDueDate, nextDueDate);
        }

        var increment = Increment.of(tenancy.getPeriod());
        var nextDueDate = currentDueDate.plus(increment.value(), increment.unit());
        nextDueDate = compensateDaysToMatchPaymentDay(nextDueDate, paymentDay(tenancy));

        return RentPeriod.of(currentDueDate, nextDueDate);
    }

    public RentPeriod calculateRentPeriodForSendDate(ZonedDateTime currentSendDate, Tenancy tenancy) {
        var inAdvanceDays = tenancy.getSettings().getInvoiceRentInAdvanceDays();
        if (inAdvanceDays == null) {
            throw new IllegalArgumentException("Unable to calculate current rent period without tenancy's \"in advance days\" option");
        }

        var currentDueDate = currentSendDate.plusDays(inAdvanceDays);
        return calculateRentPeriodForDueDate(currentDueDate, tenancy);
    }

    private ZonedDateTime compensateDaysToMatchPaymentDay(ZonedDateTime currentIncrement, int paymentDay) {
        if (currentIncrement.toLocalDate().lengthOfMonth() >= paymentDay) {
            var daysToAdd = Math.max(0, paymentDay - currentIncrement.getDayOfMonth());
            return currentIncrement.plusDays(daysToAdd);
        }
        return currentIncrement;
    }

    private int paymentDay(Tenancy tenancy) {
        return ofNullable(tenancy.getPaymentDay()).filter(it -> tenancy.getPeriod().equals(MONTHLY)).orElse(0);
    }

    private Date calculateDueDate(ZonedDateTime now, Tenancy tenancy) {
        var period = tenancy.getPeriod();
        if (period == UK_QUARTERLY) {
            return nextUKQuarterlyDate(now, tenancy);
        }

        var increment = Increment.of(period);
        return nextInvoiceDate(now, tenancy, increment);
    }

    private Date nextInvoiceDate(ZonedDateTime now, Tenancy tenancy, Increment increment) {
        var startDate = invoicingStart(tenancy);
        int paymentDay = paymentDay(tenancy);

        var currentIncrement = startDate.truncatedTo(DAYS);
        var daysInStartMonth = currentIncrement.toLocalDate().lengthOfMonth();
        currentIncrement = paymentDay > 0 ?
                currentIncrement.withDayOfMonth(Math.min(paymentDay, daysInStartMonth)) :
                currentIncrement.withDayOfMonth(startDate.getDayOfMonth());

        while (
                isCurrentIncrementBeforeNow(tenancy, now.truncatedTo(DAYS), currentIncrement) ||
                isCurrentIncrementWithinCoveredPeriod(tenancy.getSettings(), currentIncrement)
        ) {
            currentIncrement = currentIncrement.plus(increment.value(), increment.unit());
            if (currentIncrement.toLocalDate().lengthOfMonth() >= paymentDay) {
                var daysToAdd = Math.max(0, paymentDay - currentIncrement.getDayOfMonth());
                currentIncrement = currentIncrement.plusDays(daysToAdd);
            }
        }

        return Date.from(currentIncrement.toInstant());
    }

    private boolean isCurrentIncrementBeforeNow(Tenancy tenancy,
                                                ZonedDateTime now,
                                                ZonedDateTime currentIncrement) {
        var rentInAdvanceDays = tenancy.getSettings().getInvoiceRentInAdvanceDays();
        // we want due date to be after now and send date to be after or equal to now
        var currentIncrementInAdvance = currentIncrement.minusDays(rentInAdvanceDays);

        var invoicingStart = invoicingStart(tenancy);
        if (now.isBefore(invoicingStart) || now.isEqual(invoicingStart)) {
            return currentIncrementInAdvance.isBefore(invoicingStart) || currentIncrementInAdvance.isEqual(invoicingStart);
        }

        return currentIncrementInAdvance.isBefore(now);
    }

    private boolean isCurrentIncrementWithinCoveredPeriod(Tenancy.Settings settings, ZonedDateTime currentIncrement) {
        var lastPaymentCoveredTo = ofNullable(settings.getLastPaymentCoveredTo())
                .map(it -> it.truncatedTo(DAYS).atZone(UTC))
                .orElse(Instant.EPOCH.atZone(UTC));
        return currentIncrement.isBefore(lastPaymentCoveredTo) || currentIncrement.isEqual(lastPaymentCoveredTo);
    }

    private Date nextUKQuarterlyDate(ZonedDateTime now, Tenancy tenancy) {
        var currentIncrement = invoicingStart(tenancy).truncatedTo(DAYS);
        while (!isUkQuarterlyDate(currentIncrement) ||
                isCurrentIncrementBeforeNow(tenancy, now.truncatedTo(DAYS), currentIncrement) ||
                isCurrentIncrementWithinCoveredPeriod(tenancy.getSettings(), currentIncrement)
        ) {
            currentIncrement = currentIncrement.plusDays(1);
        }

        return Date.from(currentIncrement.toInstant());
    }

    private boolean isUkQuarterlyDate(ZonedDateTime date) {
        return (date.getMonth() == Month.MARCH && date.getDayOfMonth() == 25) ||
                (date.getMonth() == Month.JUNE && date.getDayOfMonth() == 24) ||
                (date.getMonth() == Month.SEPTEMBER && date.getDayOfMonth() == 29) ||
                (date.getMonth() == Month.DECEMBER && date.getDayOfMonth() == 25);
    }

    private Date calculateSendDate(Tenancy tenancy, Date nextInvoiceDueDate) {
        if (nextInvoiceDueDate == null) {
            return null;
        }

        var invoiceRentInAdvanceDays = tenancy.getSettings().getInvoiceRentInAdvanceDays();
        var sendDate = nextInvoiceDueDate.toInstant().atZone(UTC).minusDays(invoiceRentInAdvanceDays).toInstant();
        return Date.from(sendDate);
    }

    private ZonedDateTime invoicingStart(Tenancy tenancy) {
        var invoiceStartDate = ofNullable(tenancy.getInvoiceStartDate())
                .map(it -> Instant.parse(it).atZone(UTC).truncatedTo(DAYS))
                .orElse(null);
        var tenancyStart = OffsetDateTime.parse(tenancy.getStartDate()).atZoneSameInstant(UTC).truncatedTo(DAYS);

        if (tenancy.getInvoiceStartDate() == null) {
            return tenancyStart;
        }

        return tenancyStart.isBefore(invoiceStartDate) ? invoiceStartDate : tenancyStart;
    }

    @Value
    @Accessors(fluent = true)
    public static class RentPeriod {
        ZonedDateTime startTime;
        ZonedDateTime endTime;

        public static RentPeriod of(ZonedDateTime currentDueDate, ZonedDateTime nextDueDate) {
            return new RentPeriod(currentDueDate.with(LocalTime.MIN), nextDueDate.minusDays(1).with(LocalTime.MAX));
        }

        @Override
        public String toString() {
            return String.format("[%s, %s]", startTime.toInstant(), endTime.toInstant());
        }
    }

}

@Value
@Accessors(fluent = true)
class Increment {
    int value;
    ChronoUnit unit;

    static Increment of(TenancyPeriod period) {
        switch (period) {
            case WEEKLY:
                return new Increment(1, WEEKS);
            case TWO_WEEKLY:
                return new Increment(2, WEEKS);
            case MONTHLY:
                return new Increment(1, MONTHS);
            case QUARTERLY:
                return new Increment(3, MONTHS);
            case SIX_MONTHLY:
                return new Increment(6, MONTHS);
            case ANNUALLY:
                return new Increment(1, YEARS);
            case BI_ANNUALLY:
                return new Increment(2, YEARS);
            case FIVE_YEAR:
                return new Increment(5, YEARS);
            case TEN_YEAR:
                return new Increment(10, YEARS);
            case FIFTEEN_YEAR:
                return new Increment(15, YEARS);
            case TWENTY_YEAR:
                return new Increment(20, YEARS);
            case TWENTY_FIVE_YEAR:
                return new Increment(25, YEARS);
            default:
                throw new IllegalArgumentException("Unknown tenancy period " + period);
        }
    }
}