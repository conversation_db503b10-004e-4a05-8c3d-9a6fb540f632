package com.rentancy.integrations.servicies.xero_integration.queue;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.servicies.SQSClient;
import com.rentancy.integrations.servicies.xero_integration.queue.dto.SqsInvoiceMessagePattern;
import com.xero.models.accounting.Invoice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class InvoiceOutboundSender extends SqsSenderAbstract<SqsInvoiceMessagePattern<Invoice>> {

    public InvoiceOutboundSender(Config config, SQSClient sqsClient) {
        super(config.getXeroInvoicesQueueOutbound(), sqsClient, InvoiceOutboundSender.class);
    }
}
