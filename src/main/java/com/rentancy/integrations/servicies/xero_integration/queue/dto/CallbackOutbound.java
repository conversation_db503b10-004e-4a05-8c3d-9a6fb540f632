package com.rentancy.integrations.servicies.xero_integration.queue.dto;

import com.rentancy.integrations.pojos.Tenancy;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CallbackOutbound {
    private String queue;
    private CallbackType type;
    private String tenancyId;

    // AFTER_CREATION_OF_RAISE_COMMISSION_BILL
    private String tenantId;
    private Instant dueDate;

    // AFTER_CREATION_OF_BILL
    private String lastJournalRunDate;
    private String firstJournalRunDate;
    private List<Tenancy.AutoJournalArrears> autoJournalArrears;
    private BigDecimal preCalculatedOriginalAmount;

    public static CallbackOutbound afterCreationOfBill(String queue,
                                                       String tenancyId,
                                                       String lastJournalRunDate,
                                                       String firstJournalRunDate,
                                                       List<Tenancy.AutoJournalArrears> autoJournalArrears,
                                                       BigDecimal preCalculatedOriginalAmount) {
        return new CallbackOutbound(
                queue,
                CallbackType.AFTER_CREATION_OF_BILL,
                tenancyId,
                null,
                null,
                lastJournalRunDate,
                firstJournalRunDate,
                autoJournalArrears,
                preCalculatedOriginalAmount
        );
    }

    public static CallbackOutbound afterCreationOfRaiseCommissionBill(String queue,
                                                                      String tenancyId,
                                                                      String tenantId,
                                                                      Instant dueDate) {
        return new CallbackOutbound(
                queue,
                CallbackType.AFTER_CREATION_OF_RAISE_COMMISSION_BILL,
                tenancyId,
                tenantId,
                dueDate,
                null,
                null,
                null,
                null
        );
    }
}
