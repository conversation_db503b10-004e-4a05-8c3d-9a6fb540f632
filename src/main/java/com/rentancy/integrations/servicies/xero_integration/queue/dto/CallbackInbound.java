package com.rentancy.integrations.servicies.xero_integration.queue.dto;

import com.rentancy.integrations.pojos.Tenancy;
import com.xero.models.accounting.Invoice;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CallbackInbound {
    private String organisationId;
    private Invoice invoice;
    private CallbackType type;
    private String tenancyId;

    // AFTER_CREATION_OF_RAISE_COMMISSION_BILL
    private String tenantId;
    private Instant dueDate;

    // AFTER_CREATION_OF_BILL
    private String lastJournalRunDate;
    private String firstJournalRunDate;
    private List<Tenancy.AutoJournalArrears> autoJournalArrears;
    private BigDecimal preCalculatedOriginalAmount;
}
