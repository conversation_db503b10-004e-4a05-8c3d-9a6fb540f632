package com.rentancy.integrations.servicies.xero_integration.queue;

import com.rentancy.integrations.servicies.SQSClient;
import com.rentancy.integrations.util.JSONUtils;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class SqsSenderAbstract<MessagePattern> {
    private final String queueName;
    private final SQSClient sqsClient;

    protected SqsSenderAbstract(String queueName, SQSClient sqsClient, Class<?> clazz) {
        this.queueName = queueName;
        this.sqsClient = sqsClient;

        log.debug("{} initialized as SENDER for queue: {}", clazz.getSimpleName(), queueName);
    }

    public void sendMessageToTopic(MessagePattern message) {
        String payload = JSONUtils.wrappedToJsonString(message);
        log.debug("Sending message to queue: {}, message: {}", queueName, payload);
        sqsClient.enqueue(queueName, payload);
    }
}
