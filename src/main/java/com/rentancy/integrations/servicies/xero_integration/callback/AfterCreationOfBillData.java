package com.rentancy.integrations.servicies.xero_integration.callback;

import com.rentancy.integrations.pojos.Tenancy;
import com.xero.models.accounting.Invoice;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.List;

@Getter
@AllArgsConstructor
public class AfterCreationOfBillData {
    private String organisationId;
    private Invoice invoice;
    private String tenancyId;
    private String lastJournalRunDate;
    private String firstJournalRunDate;
    private List<Tenancy.AutoJournalArrears> autoJournalArrears;
    private BigDecimal preCalculatedOriginalAmount;
}
