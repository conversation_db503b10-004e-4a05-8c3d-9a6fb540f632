package com.rentancy.integrations.servicies.xero_integration.queue.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SqsInvoiceMessagePattern<Body> {
    private String organisationId;
    private String tenantId;
    private String entityId;
    private SqsInvoiceType type;
    private Body body;
    private CallbackOutbound callback;
}
