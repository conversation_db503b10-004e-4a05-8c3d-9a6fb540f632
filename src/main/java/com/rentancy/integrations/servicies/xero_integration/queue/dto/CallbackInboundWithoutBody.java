package com.rentancy.integrations.servicies.xero_integration.queue.dto;

import com.xero.models.accounting.Invoice;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CallbackInboundWithoutBody {
    private String organisationId;
    private Invoice invoice;
    private CallbackType type;
}
