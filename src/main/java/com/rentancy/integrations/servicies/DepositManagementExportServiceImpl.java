package com.rentancy.integrations.servicies;

import com.rentancy.integrations.pojos.DepositItemsResponse;
import com.rentancy.integrations.pojos.DepositItemsResponse.DepositManagementBody;
import com.rentancy.integrations.pojos.DepositManagementPayload;
import com.rentancy.integrations.servicies.persistence.DepositManagement;
import com.rentancy.integrations.util.WorkbookFluentApi;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Slf4j
public class DepositManagementExportServiceImpl {

    public static final String MONEY = "Money";
    public static final String TABLE_HEADER = "Header";
    private final DepositManagement depositManagement;

    public DepositManagementExportServiceImpl(DepositManagement depositManagement) {
        this.depositManagement = depositManagement;
    }

    public Report exportDepositsToExcelFile(DepositManagementPayload payload, String author) throws IOException {
        DepositItemsResponse response = depositManagement.getAllTypeDeposits(payload);
        DataForReport depositItems = findItemsToRender(response);

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            WorkbookFluentApi workbook = renderItemsToExcelFile(author, depositItems.getDepositItems());
            workbook.write(outputStream);
            return new Report(depositItems.getName(), outputStream.toByteArray());
        }
    }

    private WorkbookFluentApi renderItemsToExcelFile(String author, List<DepositManagementBody> depositItems) {
        Workbook xssfWorkbook = new XSSFWorkbook();
        WorkbookFluentApi workbook = new WorkbookFluentApi(xssfWorkbook);
        // @formatter:off
        workbook
            .createSheet("Deposit Management Report");
        createStyles(workbook);
        workbook.row(1)
            .cell(0)
                .style("ReportHeader")
                .cellValue("Deposit Management Report")
            .row(2).cell(0)
                .cellValue("Date generated: " + OffsetDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
            .row(3).cell(0)
                .cellValue("Generated by: " + author)
            .row(5)
                .cell(0).style(TABLE_HEADER).cellValue("Property Ref.")
                .cell(1).style(TABLE_HEADER).cellValue("Property")
                .cell(2).style(TABLE_HEADER).cellValue("Tenant")
                .cell(3).style(TABLE_HEADER).cellValue("Tenancy Status")
                .cell(4).style(TABLE_HEADER).cellValue("Tenancy Start")
                .cell(5).style(TABLE_HEADER).cellValue("Deposit")
                .cell(6).style(TABLE_HEADER).cellValue("Deposit Held")
                .cell(7).style(TABLE_HEADER).cellValue("Receive")
                .cell(8).style(TABLE_HEADER).cellValue("Protection scheme")
                .cell(9).style(TABLE_HEADER).cellValue("Reference")
                .cell(10).style(TABLE_HEADER).cellValue("Date Registered")
                .cell(11).style(TABLE_HEADER).cellValue("Transferred Date")
            .forEach(depositItems, (wb,i) ->
                wb.nextRow()
                    .cell(0).cellValue(i.getPropertyReference())
                    .cell(1).cellValue(i.getPropertyDisplayAddress())
                    .cell(2).cellValue(i.getTenantName())
                    .cell(3).cellValue(i.getTenancyStatus())
                    .cell(4).cellValue(i.getTenancyStartDate())
                    .cell(5).style(MONEY).cellValue(convertToMoney(i.getDepositValue()))
                    .cell(6).style(MONEY).cellValue(i.getDepositReturned() ? new BigDecimal("0.00") : convertToMoney(i.getDepositValue()))
                    .cell(7).cellValue("TO BE FILLED")
                    .cell(8).cellValue(i.getTenancyProtectionScheme())
                    .cell(9).cellValue(i.getContractReference())
                    .cell(10).cellValue(i.getDateRegistered())
                    .cell(11).cellValue("TO BE FILLED")
            )
            .autoSizeColumns(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11);
        // @formatter:on
        return workbook;
    }

    static void createStyles(WorkbookFluentApi workbook) {
        workbook.newStyle("ReportHeader")
            .font(20)
                .bold()
                .color(IndexedColors.BLACK.getIndex())
            .fontDone()
        .alignment(HorizontalAlignment.LEFT)
        .styleDone()
        .newStyle(TABLE_HEADER)
            .font()
                .bold()
            .fontDone()
            .borderBottom(BorderStyle.THIN)
        .styleDone()
        .newStyle(MONEY)
            .moneyFormat("GBP")
        .styleDone();
    }

    private BigDecimal convertToMoney(String depositValue) {
        return new BigDecimal(depositValue);
    }

    static DataForReport findItemsToRender(DepositItemsResponse response) {
        if (response.getRegisteredDepositManagementItems() != null) {
            return new DataForReport("registered_deposit", response.getRegisteredDepositManagementItems());
        } else if (response.getReceivedDepositManagementItems() != null) {
            return new DataForReport("received_deposit", response.getReceivedDepositManagementItems());
        } else if (response.getRefundDepositManagementItems() != null) {
            return new DataForReport("refund_deposit", response.getRefundDepositManagementItems());
        } else {
            log.error("No items found to render");
            return new DataForReport("no_data", List.of());
        }
    }

    @AllArgsConstructor
    @Getter
    static class DataForReport {
        String name;
        List<DepositManagementBody> depositItems;
    }

    @AllArgsConstructor
    @Getter
    public static class Report {
        String name;
        byte[] contents;
    }

}
