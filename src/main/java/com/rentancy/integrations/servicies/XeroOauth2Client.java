package com.rentancy.integrations.servicies;

import com.auth0.jwt.JWT;
import com.google.api.client.auth.oauth2.AuthorizationCodeFlow;
import com.google.api.client.auth.oauth2.BearerToken;
import com.google.api.client.auth.oauth2.ClientParametersAuthentication;
import com.google.api.client.auth.oauth2.RefreshTokenRequest;
import com.google.api.client.http.BasicAuthentication;
import com.google.api.client.http.GenericUrl;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.client.util.store.MemoryDataStoreFactory;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.Authentication;
import com.rentancy.integrations.pojos.Oauth2ExchangeResponse;
import com.rentancy.integrations.pojos.XeroTokenPayload;
import com.xero.api.ApiClient;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.util.List;

import static com.rentancy.integrations.util.JSONUtils.decodePayload;
import static com.rentancy.integrations.util.JSONUtils.deserializePayload;

public class XeroOauth2Client implements Oauth2Client {

    private static final Logger log = LogManager.getLogger(XeroOauth2Client.class);

    private final Config config;
    private final ApiClient apiClient;

    public XeroOauth2Client(Config config, ApiClient apiClient) {
        this.config = config;
        this.apiClient = apiClient;
    }

    @Override
    public Authentication getAuthorizeUrl(String state, List<String> scopes) {
        try {
            var dataStoreFactory = new MemoryDataStoreFactory();
            var flow = new AuthorizationCodeFlow.Builder(
                    BearerToken.authorizationHeaderAccessMethod(),
                    new NetHttpTransport(),
                    new JacksonFactory(),
                    new GenericUrl(config.getXeroTokenUrl()),
                    new ClientParametersAuthentication(config.getXeroAppClientId(), config.getXeroAppClientSecret()),
                    config.getXeroAppClientId(),
                    config.getXeroAuthorizationUrl()
            ).setScopes(scopes)
                    .setDataStoreFactory(dataStoreFactory)
                    .build();

            var url = flow
                    .newAuthorizationUrl()
                    .setClientId(config.getXeroAppClientId())
                    .setScopes(scopes)
                    .setState(state)
                    .setRedirectUri(config.getRedirectUrl())
                    .build();

            return new Authentication(state, url);
        } catch (Exception e) {
            throw new IllegalStateException("Unable to create authorization url", e);
        }
    }

    @Override
    public Oauth2ExchangeResponse exchangeCode(String code, List<String> scopes) {
        try {
            var DATA_STORE_FACTORY = new MemoryDataStoreFactory();

            var flow = new AuthorizationCodeFlow
                    .Builder(BearerToken.authorizationHeaderAccessMethod(),
                    new NetHttpTransport(), new JacksonFactory(), new GenericUrl(config.getXeroTokenUrl()),
                    new ClientParametersAuthentication(config.getXeroAppClientId(), config.getXeroAppClientSecret()), config.getXeroAppClientId(), config.getXeroAuthorizationUrl())
                    .setScopes(scopes).setDataStoreFactory(DATA_STORE_FACTORY)
                    .build();

            var tokenResponse = flow.newTokenRequest(code).setRedirectUri(config.getRedirectUrl()).execute();

            return new Oauth2ExchangeResponse(tokenResponse.getAccessToken(), String.valueOf(tokenResponse.get("id_token")), tokenResponse.getRefreshToken(), tokenResponse.getExpiresInSeconds());
        } catch (Exception e) {
            throw new IllegalStateException("Unable to exchange code", e);
        }
    }

    @Override
    public Oauth2ExchangeResponse refreshToken(String refreshToken) {
        try {
            var tokenResponse = new RefreshTokenRequest(new NetHttpTransport(), new JacksonFactory(),
                    new GenericUrl(config.getXeroTokenUrl()), refreshToken)
                    .setClientAuthentication(new BasicAuthentication(config.getXeroAppClientId(), config.getXeroAppClientSecret()))
                    .execute();

            log.info("Token refreshed");

            return new Oauth2ExchangeResponse(tokenResponse.getAccessToken(), String.valueOf(tokenResponse.get("id_token")), tokenResponse.getRefreshToken(), tokenResponse.getExpiresInSeconds());
        } catch (Exception e) {
            throw new IllegalStateException("Unable to refresh token", e);
        }
    }

    @Override
    public void revokeToken(String refreshToken) {
        try {
            var response = apiClient.revoke(config.getXeroAppClientId(), config.getXeroAppClientSecret(), refreshToken);
            log.info("Revoke status: {}", response.getStatusCode());
        } catch (Exception e) {
            throw new IllegalStateException("Unable to revoke token", e);
        }
    }
}
