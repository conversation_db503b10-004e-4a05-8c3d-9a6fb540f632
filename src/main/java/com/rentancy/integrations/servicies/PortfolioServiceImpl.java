package com.rentancy.integrations.servicies;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.*;
import com.rentancy.integrations.pojos.IncomeArrearsSummary.ArrearsSortBy;
import com.rentancy.integrations.pojos.IncomeArrearsSummary.ArrearsSortOrder;
import com.rentancy.integrations.pojos.PropertyLedgersSummary.PropertyLedgersTableType;
import com.rentancy.integrations.servicies.autojournal.AutoJournalService;
import com.rentancy.integrations.servicies.autojournal.TrackingCategoryService;
import com.rentancy.integrations.servicies.enhanced.InvoicePropertyRepositoryImpl;
import com.rentancy.integrations.servicies.payout.PayoutCommandValidator;
import com.rentancy.integrations.servicies.persistence.*;
import com.rentancy.integrations.servicies.persistence.config.TenancyRepositoryConfig;
import com.rentancy.integrations.servicies.rent.NextInvoiceDateCalculator;
import com.rentancy.integrations.servicies.xero_integration.queue.InvoiceOutboundSender;
import com.xero.api.ApiClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.util.StringUtils;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.Clock;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.rentancy.integrations.pojos.RentInvoiceHistory.RentHistoryType.RECORD;
import static com.rentancy.integrations.util.JSONUtils.wrappedToJsonString;
import static com.rentancy.integrations.util.Utils.formatUser;
import static com.rentancy.integrations.util.Utils.getDetail;
import static java.util.stream.Collectors.*;

@Slf4j
public class PortfolioServiceImpl implements PortfolioService {

    private final PortfolioXeroProcessor portfolioXeroService;

    private final UserService userService;
    private final OrganisationService organisationService;

    private final PropertyService propertyService;

    private final ReportService reportService;

    private final InvoiceService invoiceService;

    private final TenancyService tenancyService;

    private final AppSyncServiceProvider appSyncServiceProvider;

    private final XeroService xeroService;

    private final PayoutCommandValidator payoutCommandValidator;

    private final AutoJournalService autoJournalService;

    public PortfolioServiceImpl(Config config, Clock clock) {
        var lambdaClient = new LambdaClient(config);
        var ddbClient = new DDBClient(config);
        var integrationServiceImpl = new IntegrationServiceImpl(new XeroOauth2Client(config, new ApiClient()),
                new IntegrationRepository(config, ddbClient), lambdaClient, config);

        var dynamoDbEnhancedClient = DynamoDbEnhancedClient.builder()
                .dynamoDbClient(DynamoDbClient.builder()
                        .region(Region.of(config.getDDBRegion()))
                        .build())
                .build();
        var trackingCategoryService = new TrackingCategoryService();

        this.organisationService = new OrganisationServiceImpl(new OrganisationRepository(config, ddbClient));
        var sqsClient = new SQSClient();
        this.userService = new UserServiceImpl(new UserRepository(config, ddbClient));
        var paymentRepository = new PaymentRepository(config, ddbClient);
        var landlordBillRepository = new LandlordBillRepository(config, ddbClient);
        this.tenancyService = TenancyServiceFactory.getTenancyService(config);
        this.appSyncServiceProvider = new AppSyncServiceProvider(lambdaClient, config);
        this.invoiceService = new InvoiceServiceImpl(new InvoiceRepository(config, ddbClient),
                new AccountRepository(config, ddbClient),
                landlordBillRepository,
                paymentRepository,
                new TransactionRepository(config, ddbClient),
                new TransferRepository(config, ddbClient),
                new JournalRepository(config, ddbClient),
                new InvoiceWebhookEventsRepository(config, ddbClient),
                new PropertyRepository(config, ddbClient),
                new OverPaymentRepository(config, ddbClient),
                new TenancyRepository(new TenancyRepositoryConfig(), ddbClient),
                new EventBridgeClient(),
                config);
        this.reportService = ReportFactory.getReportService(config);
        this.xeroService = XeroFactory.getXeroService(config);
        this.propertyService = new PropertyServiceImpl(
                new PropertyRepository(config, ddbClient),
                new TenancyRepository(new TenancyRepositoryConfig(), ddbClient),
                new InvoicePropertyRepositoryImpl(dynamoDbEnhancedClient, config));
        this.portfolioXeroService = new PortfolioXeroProcessor(config, integrationServiceImpl, userService, invoiceService,
                organisationService, propertyService, sqsClient, tenancyService, reportService, appSyncServiceProvider,
                XeroFactory.getXeroClient(config, integrationServiceImpl), xeroService, new NextInvoiceDateCalculator(), clock,
                trackingCategoryService, new InvoiceOutboundSender(config, sqsClient));
        this.payoutCommandValidator = new PayoutCommandValidator();
        var xeroClient = XeroFactory.getXeroClient(config, integrationServiceImpl);
        this.autoJournalService = new AutoJournalService(
                organisationService, propertyService, reportService, integrationServiceImpl, userService,
                invoiceService, tenancyService, xeroClient,
                portfolioXeroService, trackingCategoryService,
                new InvoiceOutboundSender(config, sqsClient),
                config.getXeroInvoicesCallbackQueue()
        );
    }

    @Override
    public void collectAutoInvoiceTenancies(AutoInvoiceEvent.SourceType sourceType, Clock clock) {
        portfolioXeroService.collectAutoInvoiceTenancies(sourceType, clock);
    }

    @Override
    public void raiseTenancyInvoice(TenancyInvoiceSenderPayload payload) {
        var organisation = organisationService.getOrganisation(payload.getOrganisationId());

        switch (organisation.getConnectedFinanceIntegration()) {
            case XERO:
                portfolioXeroService.raiseTenancyInvoicesSafe(payload, organisation);
                break;
            case QUICK_BOOKS:
                throw new IllegalArgumentException("Quick Book integrations is not longer supported!");
            default:
                throw new IllegalStateException("Unknown integration type:" + organisation.getConnectedFinanceIntegration());
        }
    }

    @Override
    public void raiseCommissionBill(TenancyInvoiceSenderPayload payload) {
        var organisation = organisationService.getOrganisation(payload.getOrganisationId());

        switch (organisation.getConnectedFinanceIntegration()) {
            case XERO:
                portfolioXeroService.raiseCommissionBill(payload, organisation);
                break;
            case QUICK_BOOKS:
                throw new IllegalArgumentException("Quick Book integrations is not longer supported!");
            default:
                throw new IllegalStateException("Unknown integration type:" + organisation.getConnectedFinanceIntegration());
        }
    }

    @Override
    public String createManualInvoice(String cognitoId, String tenancyId) {
        var tenancy = propertyService.getTenancy(tenancyId, false);
        var organisation = organisationService.getOrganisation(tenancy.getOrganisation());

        switch (organisation.getConnectedFinanceIntegration()) {
            case XERO:
                return portfolioXeroService.createManualInvoice(cognitoId, tenancyId, organisation);
            case QUICK_BOOKS:
                throw new IllegalArgumentException("Quick Book integrations is not longer supported!");
            default:
                throw new IllegalStateException("Unknown integration type:" + organisation.getConnectedFinanceIntegration());
        }
    }

    @Override
    public void createTenancyInvoice(String organisationId, TenancyInvoiceInput invoiceInput) {
        var organisation = organisationService.getOrganisation(organisationId);

        switch (organisation.getConnectedFinanceIntegration()) {
            case XERO:
                portfolioXeroService.createTenancyInvoice(invoiceInput, organisation);
                break;
            case QUICK_BOOKS:
                throw new IllegalArgumentException("Quick Book integrations is not longer supported!");
            default:
                throw new IllegalStateException("Unknown integration type:" + organisation.getConnectedFinanceIntegration());
        }
    }

    @Override
    public void matchInvoice(PortfolioInvoiceMatcherPayload payload) {
        var organisation = organisationService.getOrganisation(payload.getOrganisationId());

        switch (organisation.getConnectedFinanceIntegration()) {
            case XERO:
                portfolioXeroService.matchInvoice(payload, organisation);
                break;
            case QUICK_BOOKS:
                throw new IllegalArgumentException("Quick Book integrations is not longer supported!");
            default:
                throw new IllegalStateException("Unknown integration type:" + organisation.getConnectedFinanceIntegration());
        }
    }

    @Override
    public void addPropertyTrackingCode(PropertyTrackingCodePayload payload) {
        var organisation = organisationService.getOrganisation(payload.getOrganisationId());

        switch (organisation.getConnectedFinanceIntegration()) {
            case XERO:
                portfolioXeroService.addPropertyTrackingCode(payload, organisation);
                break;
            case QUICK_BOOKS:
                throw new IllegalArgumentException("Quick Book integrations is not longer supported!");
            default:
                throw new IllegalStateException("Unknown integration type:" + organisation.getConnectedFinanceIntegration());
        }
    }

    @Override
    public void saveLandlordBill(Tenancy tenancy, String amountPaid, String invoiceId) {
        var organisation = organisationService.getOrganisation(tenancy.getOrganisation());

        switch (organisation.getConnectedFinanceIntegration()) {
            case XERO:
                portfolioXeroService.saveLandlordBill(tenancy, amountPaid, invoiceId, null);
                break;
            case QUICK_BOOKS:
                throw new IllegalArgumentException("Quick Book integrations is not longer supported!");
            default:
                throw new IllegalStateException("Unknown integration type:" + organisation.getConnectedFinanceIntegration());
        }
    }

    @Override
    public void updateLandlordBill(String propertyId) {
        var property = propertyService.getProperty(propertyId);
        var organisationId = property.getOrganisation();
        var landlordBills = invoiceService.findOrganisationLandlordBills(organisationId);
        var statements = reportService.findOrganisationStatements(organisationId);

        landlordBills
                .stream()
                .filter(landlordBill -> propertyId.equals(landlordBill.getPropertyId()))
                .filter(landlordBill -> {
                    var landlordBillStatements = statements
                            .stream()
                            .filter(s -> landlordBill.getId().equals(s.getLandlordBillId()))
                            .sorted((s1, s2) -> s2.getCreatedAt().compareTo(s1.getCreatedAt()))
                            .limit(1)
                            .collect(Collectors.toList());

                    log.info("Statements - {}", landlordBillStatements);

                    return landlordBillStatements.isEmpty() || landlordBillStatements
                            .stream()
                            .anyMatch(s -> !s.isBillsUpdated() && !s.isPayedOut() && !s.isSent() && !s.isApproved());
                })
                .forEach(landlordBill -> {
                    var amount = calculateLandlordBillAmount(property);
                    invoiceService.saveLandlordBill(landlordBill.toBuilder().billAmount(amount.intValue()).build());
                    log.info("Updated landlord bill amount - {}", amount);
                });
    }

    @Override
    public void sendLandlordBill(String cognitoId, String id, String statementId) {
        var landlordBill = invoiceService.getLandlordBill(id);
        var organisation = organisationService.getOrganisation(landlordBill.getLandlordBillOrganisationId());

        switch (organisation.getConnectedFinanceIntegration()) {
            case XERO:
                portfolioXeroService.sendLandlordBill(cognitoId, id, statementId);
                break;
            case QUICK_BOOKS:
                throw new IllegalArgumentException("Quick Book integrations is not longer supported!");
            default:
                throw new IllegalStateException("Unknown integration type:" + organisation.getConnectedFinanceIntegration());
        }
    }

    @Override
    public void sendLandlordBills(String cognitoId, String statementId, LandlordBillsRequest request) {
        request.getLandlordBillIds().forEach(id -> sendLandlordBill(cognitoId, id, statementId));
    }

    @Override
    public List<InvoiceAttachment> getInvoiceAttachments(String invoiceId, String organisationId) {
        var organisation = organisationService.getOrganisation(organisationId);

        switch (organisation.getConnectedFinanceIntegration()) {
            case XERO:
                return portfolioXeroService.getInvoiceAttachments(invoiceId, organisation);
            case QUICK_BOOKS:
                throw new IllegalArgumentException("Quick Book integrations is not longer supported!");
            default:
                throw new IllegalStateException("Unknown integration type:" + organisation.getConnectedFinanceIntegration());
        }
    }

    @Override
    public void raiseLandlordCommission(String organisationId, String tenant, String token, List<String> tenancyIds) {
        var organisation = organisationService.getOrganisation(organisationId);

        switch (organisation.getConnectedFinanceIntegration()) {
            case XERO:
                portfolioXeroService.raiseLandlordCommission(tenant, token, tenancyIds, organisation);
                break;
            case QUICK_BOOKS:
                throw new IllegalArgumentException("Quick Book integrations is not longer supported!");
            default:
                throw new IllegalStateException("Unknown integration type:" + organisation.getConnectedFinanceIntegration());
        }
    }

    @Override
    public boolean invoiceRecordProcessed(String messageId) {
        return invoiceService.invoiceRecordProcessed(messageId);
    }

    @Override
    public void saveInvoiceRecord(String messageId) {
        invoiceService.saveInvoiceHistory(List.of(RentInvoiceHistory
                .builder()
                .id(messageId)
                .type(RECORD)
                .successful(true)
                .message("OK")
                .build()));
    }

    @Override
    public void createPropertyBill(String organisationId, PropertyBillCreationCommand body) {
        var organisation = organisationService.getOrganisation(organisationId);

        switch (organisation.getConnectedFinanceIntegration()) {
            case XERO:
                portfolioXeroService.createPropertyBill(body, organisation);
                break;
            case QUICK_BOOKS:
                throw new IllegalArgumentException("Quick Book integrations is not longer supported!");
            default:
                throw new IllegalStateException("Unknown integration type:" + organisation.getConnectedFinanceIntegration());
        }
    }

    @Override
    public void postInvoiceHistory() {
        var dateFormatter = new SimpleDateFormat("yyyy-MM-dd");
        var date = dateFormatter.format(Date.from(Instant.now()));
        var invoiceHistory = invoiceService.findHistoryItems(date);

        log.info("History items - {}", wrappedToJsonString(invoiceHistory));

        var organisationHistoryItems = invoiceHistory
                .stream()
                .filter(rentInvoiceHistory -> Objects.nonNull(rentInvoiceHistory.getOrganisationId()))
                .collect(groupingBy(RentInvoiceHistory::getOrganisationId));

        organisationHistoryItems.forEach((organisationId, historyItems) -> {
            var organisation = organisationService.getOrganisation(organisationId);
            var botUser = organisation.getBotUser();
            var conversationId = organisation.getInvoiceConversation();
            var message = historyItems
                    .stream()
                    .filter(rentInvoiceHistory -> Objects.nonNull(rentInvoiceHistory.getTenancyId()))
                    .map(rentInvoiceHistory -> {
                        var tenancy = propertyService.getTenancy(rentInvoiceHistory.getTenancyId(), false);
                        String invoiceState;
                        String addingReportsManually = "";
                        String decimalValue;
                        var property = propertyService.getProperty(tenancy.getProperty());
                        var user = userService.findUser(tenancy.getPrimaryTenant(), false);
                        var userLandlord = Optional.ofNullable(property.getPrimaryLandlordId())
                                .map(id -> userService.findUser(id, false))
                                .orElseGet(() -> {
                                    return userService.findUser(property.getLandlords().get(0), false);
                                });
                        var landlordName = formatUser(userLandlord);
                        var to = formatUser(user);

                        if (rentInvoiceHistory.isSuccessful()) {
                            invoiceState = "Invoice Added";
                        } else {
                            invoiceState = "Invoice failed";
                            addingReportsManually = "Please add manually and report to Rentancy";
                        }

                        var formatter = new DecimalFormat("#0,000.00");
                        var formatLower = new DecimalFormat("#0.00");
                        double value = tenancy.getRent() / 100;
                        if (value >= 1000) {
                            decimalValue = formatter.format(value);
                        } else {
                            decimalValue = formatLower.format(value);
                        }

                        return invoiceState + "\n" + "\n" + "Property: " + property.getAddressLine1() + "\n" +
                                "Contract: " + tenancy.getReference() + "\n" +
                                "To: " + to + "\n" +
                                "Landlord: " + landlordName + "\n" +
                                "Value: " + decimalValue + "\n" +
                                addingReportsManually + "\n";

                    }).collect(joining("\n"));
            appSyncServiceProvider.createMessage(organisationId, conversationId, botUser, message, "TEXT");

        });
    }

    @Override
    public void raiseOverseasResidentBill(TenancyInvoiceSenderPayload payload) {
        var organisation = organisationService.getOrganisation(payload.getOrganisationId());

        switch (organisation.getConnectedFinanceIntegration()) {
            case XERO:
                portfolioXeroService.raiseOverseasResidentBill(payload, organisation);
                break;
            case QUICK_BOOKS:
                throw new IllegalArgumentException("Quick Book integrations is not longer supported!");
            default:
                throw new IllegalStateException("Unknown integration type:" + organisation.getConnectedFinanceIntegration());
        }
    }

    @Override
    public void bulkPayoutLandlordBills(LandlordBillBulkPayoutRequest request) {
        var items = request.getLandlordBillIds();
        var endDate = request.getEndDate();
        var sendStatement = request.isSendStatement();
        var cognitoId = request.getCognitoId();

        items.forEach(item -> {
            var startDate = item.getStartDate();
            var landlordBillId = item.getLandlordBillId();
            var landlordBill = invoiceService.getLandlordBill(landlordBillId);

            try {
                var propertyId = landlordBill.getPropertyId();
                var organisationId = landlordBill.getLandlordBillOrganisationId();
                var data = reportService.generateLandlordReportPdf(propertyId, startDate, endDate, "pdf", StatementType.FINALISED);

                var statementId = reportService.saveStatement(cognitoId, propertyId, startDate, endDate, "PROPERTY", landlordBillId, data, true);

                if (sendStatement) {
                    try {
                        reportService.sendStatementReport(cognitoId, statementId, false);
                    } catch (Exception e) {
                        log.error("Failed to send statement - {}", statementId, e);
                    }
                }

                sendLandlordBill(cognitoId, landlordBillId, statementId);

                var invoices = invoiceService.findInvoicesWithPropertyIdAndStatus(propertyId, List.of(
                        com.xero.models.accounting.Invoice.StatusEnum.DRAFT.name(),
                        com.xero.models.accounting.Invoice.StatusEnum.SUBMITTED.name()
                ));

                if (!invoices.isEmpty()) {
                    updatePropertyBills(cognitoId, statementId, organisationId, invoices);
                }
            } catch (Exception e) {
                log.error("Failed to process landlord bill - {}", landlordBillId, e);
            }
        });
    }

    @Override
    public IncomeArrearsSummary getIncomeArrearsSummary(String organisationId, IncomeArrearsSummary.ArrearsSortBy sortBy,
                                                        IncomeArrearsSummary.ArrearsSortOrder sortOrder, @Nullable String filter, int page, int limit) {
        var organisation = organisationService.getOrganisation(organisationId);

        switch (organisation.getConnectedFinanceIntegration()) {
            case XERO:
                return portfolioXeroService.getIncomeArrearsSummary(sortBy, sortOrder, filter, page, limit, organisation);
            case QUICK_BOOKS:
                throw new IllegalArgumentException("Quick Book integrations is not longer supported!");
            default:
                throw new IllegalStateException("Unknown integration type:" + organisation.getConnectedFinanceIntegration());
        }
    }

    @Override
    public void sendIncomeArrearsSummary(String organisationId, String receiverId, @Nullable String filter) {
        log.info("Start sending income arrears summary");
        var startTime = System.currentTimeMillis();
        var arrearsSummary = getIncomeArrearsSummary(organisationId, ArrearsSortBy.TENANT_NAME, ArrearsSortOrder.ASC, filter, 0, 0);
        log.info("Time taken to get income arrears summary: " + (System.currentTimeMillis() - startTime));
        reportService.generateIncomeArrearsSummary(organisationId, receiverId, arrearsSummary);
        log.info("Time to end sending income arrears summary: " + (System.currentTimeMillis() - startTime));
    }

    @Override
    public byte[] getIncomeArrearsSummaryReport(String organisationId, @Nullable String filter) {
        var arrearsSummary = getIncomeArrearsSummary(organisationId, ArrearsSortBy.TENANT_NAME, ArrearsSortOrder.ASC, filter, 0, 0);
        return reportService.getIncomeArrearsSummaryReport(arrearsSummary);
    }

    @Override
    public void triggerAutoJournalNow(String organisationId) {
        portfolioXeroService.triggerAutoJournalNow(organisationId);
    }

    @Override
    public void collectAutoJournalTenancies() {
        portfolioXeroService.collectAutoJournalTenancies();
    }

    @Override
    public void sendMonthlyJournalBills(String organisationId) {
        autoJournalService.sendMonthlyJournalBills(organisationId, List.of());
    }

    @Override
    public void sendLandlordBillV2(String organisationId, LandlordBillsRequestV2 request) {
        log.info("Organisation - {}", organisationId);
        var organisation = organisationService.getOrganisation(organisationId);

        switch (organisation.getConnectedFinanceIntegration()) {
            case XERO:
                portfolioXeroService.sendLandlordBillV2(organisation, request);
                break;
            case QUICK_BOOKS:
                throw new IllegalArgumentException("Quick Book integrations is not longer supported!");
            default:
                throw new IllegalStateException("Unknown integration type:" + organisation.getConnectedFinanceIntegration());
        }
    }

    @Override
    public void sendLandlordBillV3(String organisationId, LandlordBillsRequestV3 request) {
        log.info("Organisation - {}", organisationId);
        var organisation = organisationService.getOrganisation(organisationId);

        switch (organisation.getConnectedFinanceIntegration()) {
            case XERO:
                portfolioXeroService.sendLandlordBillV3(organisation, request);
                break;
            case QUICK_BOOKS:
                throw new IllegalArgumentException("Quick Book integrations is not longer supported!");
            default:
                throw new IllegalStateException("Unknown integration type:" + organisation.getConnectedFinanceIntegration());
        }
    }

    @Override
    public void createDepositBill(DepositBillCreationCommand body) {
        var organisation = organisationService.getOrganisation(body.getOrganisationId());

        switch (organisation.getConnectedFinanceIntegration()) {
            case XERO:
                portfolioXeroService.createDepositBill(body, organisation);
                break;
            case QUICK_BOOKS:
                throw new IllegalArgumentException("Quick Book integrations is not longer supported!");
            default:
                throw new IllegalStateException("Unknown integration type:" + organisation.getConnectedFinanceIntegration());
        }
    }

    @Override
    public void returnDeposit(DepositReturnCommand command) {
        var organisation = organisationService.getOrganisation(command.getOrganisationId());

        switch (organisation.getConnectedFinanceIntegration()) {
            case XERO:
                portfolioXeroService.returnDeposit(command, organisation);
                break;
            case QUICK_BOOKS:
                throw new IllegalArgumentException("Quick Book integrations is not longer supported!");
            default:
                throw new IllegalStateException("Unknown integration type:" + organisation.getConnectedFinanceIntegration());
        }
    }

    @Override
    public void payBillWithFloat(PayWithFloatPayload payload) {
        var organisation = organisationService.getOrganisation(payload.getOrganisationId());

        switch (organisation.getConnectedFinanceIntegration()) {
            case XERO:
                portfolioXeroService.payBillWithFloat(payload, organisation);
                break;
            case QUICK_BOOKS:
                throw new IllegalArgumentException("Quick Book integrations is not longer supported!");
            default:
                throw new IllegalStateException("Unknown integration type:" + organisation.getConnectedFinanceIntegration());
        }
    }

    private PropertyExpensesDetails calculateExpensesAgainstTrackingCode(String organisationId, String trackingCode, int limit, int page) {
        var parentIds = new HashSet<String>();
        var items = invoiceService.findLineItemsByTrackingName(organisationId, trackingCode)
                .stream()
                .filter(lineItem -> {
                    if (lineItem.isIncome()) {
                        return false;
                    }
                    var parentId = Objects.nonNull(lineItem.getInvoiceId()) ? lineItem.getInvoiceId() : lineItem.getTransactionId();

                    if (parentIds.contains(parentId)) {
                        return false;
                    }
                    parentIds.add(parentId);
                    return true;
                })
                .map(lineItem -> {
                    var parentId = Objects.nonNull(lineItem.getInvoiceId()) ? lineItem.getInvoiceId() : lineItem.getTransactionId();
                    var user = userService.findUserWithXeroId(lineItem.getParentAgainstUserXeroId());

                    return PropertyExpensesDetails.PropertyExpensesDetailItem.builder()
                            .id(parentId)
                            .reference(trackingCode)
                            .fromUser(formatUser(user))
                            .date(lineItem.getParentDate())
                            .dueDate(lineItem.getParentDueDate())
                            .paidAmount(new BigDecimal(lineItem.getParentPaidAmount()))
                            .dueAmount(new BigDecimal(lineItem.getParentAmountDue()))
                            .taxExclusive(lineItem.isTaxExclusive())
                            .status(Objects.nonNull(lineItem.getTransactionId()) ? "PAID" : lineItem.getParentStatus())
                            .build();
                }).collect(toUnmodifiableList());

        var batches = ListUtils.partition(items, limit);
        var returnedItems = batches.size() < page ? List.<PropertyExpensesDetails.PropertyExpensesDetailItem>of() : batches.get(page - 1);

        return PropertyExpensesDetails
                .builder()
                .limit(limit)
                .total(items.size())
                .pageCount((int) Math.ceil((double) items.size() / limit))
                .items(returnedItems)
                .build();
    }

    @Override
    public PropertyExpensesDetails calculateParentPropertyExpenses(String parentPropertyId, int limit, int page) {
        var parentProperty = propertyService.getParentProperty(parentPropertyId);

        return calculateExpensesAgainstTrackingCode(parentProperty.getOrganisationId(), parentProperty.getReference(), limit, page);
    }

    @Override
    public byte[] getPropertyLedgersSummary(String organisationId,
                                            String propertyId,
                                            PropertyLedgersTableType tableType,
                                            String startDate, String endDate,
                                            @Nullable String filterTenancyReference,
                                            BackendResponseFormat format) {
        var organisation = organisationService.getOrganisation(organisationId);
        PropertyLedgersSummary summary;

        switch (organisation.getConnectedFinanceIntegration()) {
            case XERO:
                summary = portfolioXeroService.getPropertyLedgersSummary(propertyId, tableType, startDate, endDate, filterTenancyReference, organisation);
                break;
            case QUICK_BOOKS:
                throw new IllegalArgumentException("Quick Book integrations is not longer supported!");
            default:
                throw new IllegalStateException("Unknown integration type:" + organisation.getConnectedFinanceIntegration());
        }

        return reportService.generatePropertyLedgersSummary(organisation, propertyId, summary, format);
    }

    private BigDecimal calculateLandlordBillAmount(Property property) {
        var organisation = organisationService.getOrganisation(property.getOrganisation());
        var summary = tenancyService.calculatePropertySummary(property, organisation);

        var originalAmount = summary.stream()
                .map(PropertyFinanceSummary::getDueToClient)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO)
                .movePointRight(2);

        if (originalAmount.compareTo(BigDecimal.ZERO) < 0) {
            originalAmount = BigDecimal.ZERO;
        }

        return originalAmount;
    }

    private void updatePropertyBills(String cognitoId, String statementId, String organisationId, List<Invoice> invoices) {
        var invoicesUpdateInput = new XeroInvoicesUpdateInput();
        invoicesUpdateInput.setOrganisationId(organisationId);
        invoicesUpdateInput.setStatus(com.xero.models.accounting.Invoice.StatusEnum.AUTHORISED.name());
        invoicesUpdateInput.setInvoiceIds(invoices
                .stream()
                .map(Invoice::getInvoiceId)
                .collect(toUnmodifiableList()));
        var organisation = organisationService.getOrganisation(organisationId);
        switch (organisation.getConnectedFinanceIntegration()) {
            case XERO:
                xeroService.updateInvoicesStatus(cognitoId, statementId, invoicesUpdateInput);
                break;
            case QUICK_BOOKS:
                throw new IllegalArgumentException("Quick Book integrations is not longer supported!");
            default:
                throw new IllegalStateException("Unknown integration type:" + organisation.getConnectedFinanceIntegration());
        }
    }

    private String getAddressDescription(String addressLine1, String addressLine2, String addressLine3, String postcode, String town, String delimiter) {
        return List.of(
                        getDetail(addressLine1),
                        getDetail(addressLine2),
                        getDetail(addressLine3),
                        getDetail(postcode),
                        getDetail(town))
                .stream()
                .filter(StringUtils::hasText)
                .collect(joining(delimiter));
    }
}
