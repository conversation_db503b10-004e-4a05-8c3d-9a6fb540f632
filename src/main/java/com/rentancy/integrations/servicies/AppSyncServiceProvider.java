package com.rentancy.integrations.servicies;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.AppSyncLambdaPayload;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import static com.rentancy.integrations.util.JSONUtils.wrappedToJsonString;

@RequiredArgsConstructor
public class AppSyncServiceProvider {

    private static final Logger log = LogManager.getLogger(AppSyncServiceProvider.class);

    private static final String CREATE_MESSAGE_MUTATION = "create-message.graphql";
    private static final String UPDATE_MESSAGE_MUTATION = "update-message.graphql";
    private static final String CREATE_CONVERSATION_MUTATION = "create-conversation.graphql";
    private static final String CREATE_CONVERSATION_ATTACHMENT_MUTATION = "create-conversation-attachment.graphql";
    private static final String CREATE_CONVOLINK_MUTATION = "create-convolink.graphql";

    private final LambdaClient lambdaClient;
    private final Config config;

    public void createMessage(String organisationId, String conversationId, String authorId, String payload, String type) {
        lambdaClient.invoke(config.getAppsyncLambdaName(), wrappedToJsonString(getCreateMessagePayload(payload, conversationId, organisationId, authorId, type)));
    }

    public void updateMessage(String messageId, String status, String whatsAppMessageId) {
        lambdaClient.invoke(config.getAppsyncLambdaName(), wrappedToJsonString(getUpdateMessagePayload(messageId, status, whatsAppMessageId)));
    }

    public void createConversation(String organisationId, String conversationId, String authorId, String name, Set<String> members, String type) {
        var conversationPayload = getCreateConversationPayload(conversationId, organisationId, authorId, name, members, type);

        lambdaClient.invoke(config.getAppsyncLambdaName(), wrappedToJsonString(conversationPayload));

        members.forEach(member -> {
            lambdaClient.invoke(config.getAppsyncLambdaName(), wrappedToJsonString(getCreateConvoLinkPayload(organisationId, conversationId, member)));
        });
    }

    private AppSyncLambdaPayload getCreateMessagePayload(String payload, String conversationId, String organisationId, String authorId, String type) {
        var mutation = new String(config.getResource(CREATE_MESSAGE_MUTATION));
        var variables = Map.of("input", Map.of(
                "authorId", authorId,
                "content", payload,
                "messageConversationId", conversationId,
                "messageOrganisationId", organisationId,
                "type", type));

        return new AppSyncLambdaPayload(mutation, variables, true);
    }

    private AppSyncLambdaPayload getUpdateMessagePayload(String messageId, String status, String whatsAppMessageId) {
        var mutation = new String(config.getResource(UPDATE_MESSAGE_MUTATION));
        var input = new HashMap<String, String>();
        input.put("id", messageId);
        input.put("status", status);

        var variables = Map.of("input", input);
        Optional.ofNullable(whatsAppMessageId).ifPresent(id -> input.put("whatsAppId", whatsAppMessageId));

        return new AppSyncLambdaPayload(mutation, variables, true);
    }

    private AppSyncLambdaPayload getCreateConversationPayload(String id, String organisationId, String authorId, String name, Set<String> members, String type) {
        var mutation = new String(config.getResource(CREATE_CONVERSATION_MUTATION));
        var variables = Map.of("input", Map.of(
                "id", id,
                "parentId", organisationId,
                "parentType", "ORGANISATION",
                "conversationOrganisationId", organisationId,
                "mutator", authorId,
                "name", name,
                "type", type,
                "members", members));

        return new AppSyncLambdaPayload(mutation, variables, true);
    }

    private AppSyncLambdaPayload getCreateConvoLinkPayload(String organisationId, String conversationId, String userId) {
        var mutation = new String(config.getResource(CREATE_CONVOLINK_MUTATION));
        var variables = Map.of("input", Map.of(
                "id", UUID.randomUUID().toString(),
                "convoLinkOrganisationId", organisationId,
                "convoLinkConversationId", conversationId,
                "convoLinkUserId", userId));

        return new AppSyncLambdaPayload(mutation, variables, true);
    }
}
