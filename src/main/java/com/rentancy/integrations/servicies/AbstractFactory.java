package com.rentancy.integrations.servicies;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.servicies.enhanced.InvoicePropertyRepositoryImpl;
import com.rentancy.integrations.servicies.persistence.AccountRepository;
import com.rentancy.integrations.servicies.persistence.DDBClient;
import com.rentancy.integrations.servicies.persistence.InvoiceRepository;
import com.rentancy.integrations.servicies.persistence.InvoiceService;
import com.rentancy.integrations.servicies.persistence.InvoiceServiceImpl;
import com.rentancy.integrations.servicies.persistence.InvoiceWebhookEventsRepository;
import com.rentancy.integrations.servicies.persistence.JournalRepository;
import com.rentancy.integrations.servicies.persistence.LandlordBillRepository;
import com.rentancy.integrations.servicies.persistence.OverPaymentRepository;
import com.rentancy.integrations.servicies.persistence.PaymentRepository;
import com.rentancy.integrations.servicies.persistence.PropertyRepository;
import com.rentancy.integrations.servicies.persistence.PropertyService;
import com.rentancy.integrations.servicies.persistence.PropertyServiceImpl;
import com.rentancy.integrations.servicies.persistence.TenancyRepository;
import com.rentancy.integrations.servicies.persistence.TransactionRepository;
import com.rentancy.integrations.servicies.persistence.TransferRepository;
import com.rentancy.integrations.servicies.persistence.config.TenancyRepositoryConfig;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;

public abstract class AbstractFactory {
    protected static InvoiceService buildInvoiceService(Config config, DDBClient ddbClient) {

        return new InvoiceServiceImpl(new InvoiceRepository(config, ddbClient),
                new AccountRepository(config, ddbClient),
                new LandlordBillRepository(config, ddbClient),
                new PaymentRepository(config, ddbClient),
                new TransactionRepository(config, ddbClient),
                new TransferRepository(config, ddbClient),
                new JournalRepository(config, ddbClient),
                new InvoiceWebhookEventsRepository(config, ddbClient),
                new PropertyRepository(config, ddbClient),
                new OverPaymentRepository(config, ddbClient),
                new TenancyRepository(new TenancyRepositoryConfig(), ddbClient),
                new EventBridgeClient(),
                config);
    }

    protected static PropertyService buildPropertyService(Config config, DDBClient ddbClient) {
        return new PropertyServiceImpl(new PropertyRepository(config, ddbClient),
                new TenancyRepository(new TenancyRepositoryConfig(), ddbClient),
                new InvoicePropertyRepositoryImpl(buildDynamoDbEnhancedClient(config), config));
    }

    protected static DynamoDbEnhancedClient buildDynamoDbEnhancedClient(Config config) {
        return DynamoDbEnhancedClient.builder()
                .dynamoDbClient(DynamoDbClient.builder()
                        .region(Region.of(config.getDDBRegion()))
                        .build())
                .build();
    }
}
