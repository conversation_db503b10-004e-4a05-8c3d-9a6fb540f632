package com.rentancy.integrations.servicies;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.servicies.persistence.ConversationRepository;
import com.rentancy.integrations.servicies.persistence.ConversationServiceImpl;
import com.rentancy.integrations.servicies.persistence.DDBClient;
import com.rentancy.integrations.servicies.persistence.DocumentServiceImpl;
import com.rentancy.integrations.servicies.persistence.MessageRepository;
import com.rentancy.integrations.servicies.persistence.OrganisationRepository;
import com.rentancy.integrations.servicies.persistence.OrganisationServiceImpl;
import com.rentancy.integrations.servicies.persistence.UserRepository;
import com.rentancy.integrations.servicies.persistence.UserServiceImpl;

import org.apache.http.impl.client.HttpClients;

public class EmailServiceFactory {

    private static final String EMAIL_BUCKET = "EMAIL_BUCKET";
    private static final String RENTANCY_DOCUMENT_UPLOADS = "RENTANCY_DOCUMENT_UPLOADS";


    public static EmailDownloader getDownloader(Config config) {
        return new EmailDownloaderImpl(new S3Client(config), config.getVariable(EMAIL_BUCKET), config.getVariable(RENTANCY_DOCUMENT_UPLOADS));
    }

    public static EmailMessageService getEmailService(Config config) {
        var ddbClient = new DDBClient(config);
        var userService = new UserServiceImpl(new UserRepository(config, ddbClient));
        return new EmailMessageServiceImpl(
                config,
                new OrganisationServiceImpl(new OrganisationRepository(config, ddbClient)),
                new UserServiceImpl(new UserRepository(config, ddbClient)),
                new DocumentServiceImpl(new MainServiceClient(HttpClients.createDefault(), userService)),
                getDownloader(config),
                new AppSyncServiceProvider(new LambdaClient(config), config),
                new ConversationServiceImpl(new ConversationRepository(ddbClient, config), new MessageRepository(ddbClient, config)),
                new SQSClient());
    }
}
