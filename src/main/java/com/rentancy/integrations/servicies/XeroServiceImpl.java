package com.rentancy.integrations.servicies;

import com.auth0.jwt.JWT;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.*;
import com.rentancy.integrations.pojos.Integration.IntegrationMetadata;
import com.rentancy.integrations.pojos.Organisation;
import com.rentancy.integrations.pojos.User;
import com.rentancy.integrations.pojos.Integration.ResourceType;
import com.rentancy.integrations.pojos.XeroAccounts.XeroAccount;
import com.rentancy.integrations.pojos.XeroInvoices.XeroInvoice;
import com.rentancy.integrations.pojos.XeroOverPayments.XeroOverPayment;
import com.rentancy.integrations.pojos.XeroOverPayments.XeroOverPaymentLoaderPayload;
import com.rentancy.integrations.pojos.XeroTransactions.XeroTransaction;
import com.rentancy.integrations.servicies.persistence.InvoiceService;
import com.rentancy.integrations.servicies.persistence.OrganisationService;
import com.rentancy.integrations.servicies.persistence.UserService;
import com.rentancy.integrations.servicies.xero_integration.queue.InvoiceOutboundSender;
import com.rentancy.integrations.servicies.xero_integration.queue.dto.SqsInvoiceMessagePattern;
import com.rentancy.integrations.servicies.xero_integration.queue.dto.SqsInvoiceType;
import com.rentancy.integrations.util.Utils;
import com.xero.models.accounting.Account;
import com.xero.models.accounting.Invoice;
import com.xero.models.accounting.Journal;
import com.xero.models.accounting.*;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.util.UriComponentsBuilder;

import javax.ws.rs.NotFoundException;
import java.math.BigDecimal;
import java.sql.Date;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Stream;

import static com.rentancy.integrations.pojos.Integration.IntegrationService.XERO;
import static com.rentancy.integrations.pojos.Integration.IntegrationStatus.FAILED;
import static com.rentancy.integrations.pojos.Integration.IntegrationStatus.NOT_CONNECTED;
import static com.rentancy.integrations.util.JSONUtils.*;
import static com.rentancy.integrations.util.Utils.*;
import static com.xero.models.accounting.Address.AddressTypeEnum.STREET;
import static com.xero.models.accounting.Phone.PhoneTypeEnum.DEFAULT;
import static java.lang.String.join;
import static java.util.Objects.isNull;
import static java.util.stream.Collectors.*;
import static org.springframework.util.StringUtils.trimLeadingWhitespace;
import static org.springframework.util.StringUtils.trimTrailingWhitespace;

@RequiredArgsConstructor
class XeroServiceImpl implements XeroService {

    private static final Logger log = LogManager.getLogger(XeroServiceImpl.class);

    private static final int ONE_HOUR = 3_600;

    private final Config config;
    private final Oauth2Client oauth2Client;
    private final IntegrationService integrationService;
    private final XeroDataMapper xeroDataMapper;
    private final UserService userService;
    private final OrganisationService organisationService;
    private final InvoiceService invoiceService;
    private final ReportService reportService;
    private final XeroClient xeroClient;
    private final SQSClient sqsClient;
    private final InvoiceOutboundSender invoiceOutboundSender;

    private void sendCustomerEmailOnXeroFailure(Organisation organisation) {
        var emailQueue = config.getEmailTemplateNotificationQueue();
        var queueRegion = config.getEmailNotificationRegion();
        var dateFormatter = new SimpleDateFormat("dd/MM/yyyy");
        var failureDate = dateFormatter.format(Date.from(Instant.now()));

        var adminUser = userService.findUser(organisation.getAdminUser(), false);
        var email = Optional
                .ofNullable(adminUser.getCognitoEmail())
                .orElseGet(() -> adminUser.getEmails().stream().findFirst().map(User.Email::getEmail)
                        .orElseThrow(() -> new IllegalStateException("Receiver email not found - " + organisation.getAdminUser())));

        var emailPayload = EmailSenderPayload.builder()
                .organisationId(organisation.getId())
                .email(email)
                .subject("LoftyWorks Update: Authorise your Xero Integration")
                .templateParameters(Map.of("currentDate", failureDate))
                .type("XERO_ON_INTEGRATION_FAILURE_CUSTOMER_EMAIL")
                .build();

        sqsClient.enqueue(emailQueue, queueRegion, wrappedToJsonString(emailPayload));
    }

    private void sendCustomerSupportEmailOnXeroFailure(Organisation organisation) {
        var emailQueue = config.getEmailNotificationQueue();
        var queueRegion = config.getEmailNotificationRegion();
        var supportEmail = config.getRentancySupportEmail();

        var emailPayload = EmailSenderPayload.builder()
                .organisationId(organisation.getId())
                .email(supportEmail)
                .subject("Xero Reconnection Required " + organisation.getName())
                .body(organisation.getName() + "Xero connection needs to be disconnected and reconnected in order to ensure transactions generate. Please contact them to ensure this has been completed to minimise support complaints")
                .type("XERO_ON_INTEGRATION_FAILURE_SUPPORT_EMAIL")
                .build();

        sqsClient.enqueue(emailQueue, queueRegion, wrappedToJsonString(emailPayload));
    }

    private void onXeroConnectionFailure(String organisationId) {
        var organisation = organisationService.getOrganisation(organisationId);
        log.info("Xero connection failed for {} disconnecting", organisationId);

        sendCustomerEmailOnXeroFailure(organisation);
        sendCustomerSupportEmailOnXeroFailure(organisation);
    }

    @Override
    public XeroAuthentication getAuthorizationUrl(String organisationId, Instant startDate) {
        var organisation = organisationService.getOrganisation(organisationId);
        var adminUser = organisation.getAdminUser();
        var authentication = oauth2Client.getAuthorizeUrl(generateState(XeroOauth2State.builder().organisationId(organisationId).userId(adminUser).build()), config.getXeroScopes());
        var url = authentication.getUrl();
        var state = authentication.getState();

        integrationService.findOrCreateIntegration(organisationId, adminUser, url, state, startDate, XERO);

        return new XeroAuthentication(url);
    }

    @Override
    public void disconnect(String cognitoId) {
        var organisation = findOrganisation(cognitoId);
        var adminUser = organisation.getAdminUser();

        var integration = integrationService.findConnectedIntegration(organisation.getId(), adminUser, XERO);
        log.info("Integration found - {}", integration);
        organisationService.updateFinanceIntegrationType(organisation.getId(), null);
        removeConnection(integration, true);
    }

    private void removeConnection(Integration integration, boolean removeIntegrationItem) {
        try {
            Optional.ofNullable(integration.getConnectionId())
                    .ifPresentOrElse(id -> xeroClient.deleteConnection(id, integration.getAccessToken()),
                            () -> log.info("Connection id not found, deleting integration only - {}", integration));
        } catch (Exception e) {
            log.error("Error deleting integration only - {}", e.getMessage(), e);
        }

        if (removeIntegrationItem) {
            integrationService.deleteIntegration(integration.getId());
            log.info("Integration item deleted - {}", integration);
        } else {
            integrationService.updateIntegration(integration.failed());
        }

        log.info("Connection deleted");
    }

    private Organisation findOrganisation(String cognitoId) {
        var user = userService.findUserWithCognitoId(cognitoId);
        var organisationId = user.getCurrentOrganisation();

        return organisationService.getOrganisation(organisationId);
    }

    @Override
    public byte[] getInvoicePdf(String cognitoId, String invoiceId, boolean xeroId) {
        var integration = findIntegration(cognitoId);
        if (null == integration) {
            throw new IllegalStateException("no xero connection exists for cognitoId: " + cognitoId);
        }
        return doGetInvoicePdf(integration, invoiceId, xeroId);
    }

    @Override
    public byte[] getInvoicePdf(String organisationId, String invoiceId) {
        var integration = integrationService.findIntegrationByOrganisationId(organisationId, XERO);

        return doGetInvoicePdf(integration, invoiceId, true);
    }

    private byte[] doGetInvoicePdf(Integration integration, String invoiceId, boolean xeroId) {
        try {
            var tenantId = integration.getTenantId();
            var token = integration.getAccessToken();

            if (xeroId) {
                return xeroClient.getInvoicePdfWithRetry(tenantId, token, invoiceId);
            }

            var invoice = invoiceService.getInvoice(invoiceId);

            return xeroClient.getInvoicePdfWithRetry(tenantId, token, invoice.getInvoiceId());
        } catch (HttpClientErrorException e) {
            var statusCode = e.getStatusCode();
            log.info("Status code - {}", statusCode);

            throw new IllegalStateException("Failed to get invoice", e);
        }
    }

    @Override
    public XeroExchangeResult exchangeCode(ExchangeInput input) {
        var state = input.getState();
        var code = input.getCode();
        var decodedState = decodeState(state);
        var organisationId = decodedState.getOrganisationId();
        var userId = decodedState.getUserId();

        var connectOrganisationFlow = Objects.nonNull(organisationId) && Objects.nonNull(userId);
        if (connectOrganisationFlow) {
            var integration = integrationService.findIntegration(organisationId, userId, XERO);
            try {
                if (!state.equals(integration.getState())) {
                    throw new IllegalArgumentException("State is not matching");
                }

                var response = oauth2Client.exchangeCode(code, config.getXeroScopes());

                var jwt = JWT.decode(response.getAccessToken());
                var tokenPayload = deserializePayload(decodePayload(jwt.getPayload()), XeroTokenPayload.class);
                log.info("Token payload - {}", tokenPayload);
                var connections = xeroClient.getConnections(tokenPayload.getAuthEventId(), response.getAccessToken());
                log.info("Connections - {}", connections);

                XeroConnection connection;
                var reconnectionAttempt = integration.getTenantId() != null;
                if (reconnectionAttempt) {
                    connection = findConnectionBy(connections, XeroConnection::getTenantId, integration.getTenantId());
                } else {
                    connection = findConnectionBy(connections, XeroConnection::getAuthEventId, tokenPayload.getAuthEventId());
                }

                if (connection == null && reconnectionAttempt) {
                    throw new IllegalStateException("No valid connection found\n" +
                            "Most likely integration was deleted on Xero side but exists on LoftyWorks side\n" +
                            "Please disconnect Xero connection in LoftyWorks settings manually and try again");
                }
                if (connection == null) {
                    throw new IllegalStateException("No valid connection found\n" +
                            "Most likely integration was deleted on LoftyWorks side but exists on Xero side. \n" +
                            "Please disconnect LoftyWorks connection in your Xero settings manually and try again");
                }

                integrationService.updateIntegration(integration.connected(response, connection));
                organisationService.updateFinanceIntegrationType(organisationId, "XERO");
                refreshIntegrationContacts(organisationId);

                return new XeroExchangeResult(XeroFlowType.ORGANIZATION, null);
            } catch (Exception e) {
                integrationService.updateIntegration(integration.failed());
                log.error("Failed to exchange code", e);
                throw new IllegalStateException("Failed to exchange code: " + e.getMessage(), e);
            }
        } else {
            var redirectUrl = UriComponentsBuilder
                    .newInstance()
                    .host(config.getRentancyDomain());
            try {
                var response = oauth2Client.exchangeCode(code, config.getXeroOpenIdScopes());
                log.info("Response - {}", response);
                var jwt = JWT.decode(response.getIdToken());
                var tokenPayload = deserializePayload(decodePayload(jwt.getPayload()), XeroTokenPayload.class);
                log.info("Token payload - {}", tokenPayload);
                var email = tokenPayload.getEmail();
                var fname = tokenPayload.getFname();
                var sname = tokenPayload.getSname();
                var user = userService.findUserWithEmail(email);
                var queryParams = new LinkedMultiValueMap<String, String>();
                queryParams.put("email", List.of(email));
                if (Objects.nonNull(fname) && Objects.nonNull(sname)) {
                    queryParams.put("fname", List.of(fname));
                    queryParams.put("sname", List.of(sname));
                }
                return new XeroExchangeResult(XeroFlowType.USER, redirectUrl
                        .path(isNull(user) ? "signUp" : "signIn")
                        .queryParams(queryParams)
                        .build()
                        .toUriString());
            } catch (Exception e) {
                log.error("Failed to get Xero user details", e);
            }

            return new XeroExchangeResult(XeroFlowType.USER, redirectUrl.path("signIn").build().toUriString());
        }
    }

    private XeroConnection findConnectionBy(List<XeroConnection> connections, Function<XeroConnection, String> getter, String value) {
        return connections
                .stream()
                .filter(xeroConnection -> getter.apply(xeroConnection).equals(value))
                .findFirst()
                .orElse(null);
    }

    @Override
    public void handleWebHookEvent(XeroWebHooksPayload payload) {
        try {
            sqsClient.enqueue(config.getXeroWebHookQueue(), toJsonString(new XeroWebHookHandlerPayload(payload.getEvents())));
        } catch (Exception e) {
            throw new IllegalStateException("Failed to send webhook events to sqs: " + payload, e);
        }
    }

    private void refreshIntegrationContacts(String organisationId) {
        var organisation = organisationService.getOrganisation(organisationId);

        try {
            userService.refreshUser(organisation.getBalanceTransferContactUserId());
            userService.refreshUser(organisation.getOpeningBalanceContactUserId());
            userService.refreshUser(organisation.getDepositSchemeLedgerContactUserId());
        } catch (Exception e) {
            throw new IllegalStateException("Failed to refresh Xero User", e);
        }
    }

    private Integration findIntegration(String cognitoId) {
        var user = userService.findUserWithCognitoId(cognitoId);
        var organisationId = user.getCurrentOrganisation();
        var organisation = organisationService.getOrganisation(organisationId);
        var adminUser = organisation.getAdminUser();

        return integrationService.findConnectedIntegration(organisationId, adminUser, XERO);
    }

    private String generateState(XeroOauth2State state) {
        return join("+", List.of(state.getOrganisationId(), state.getUserId()));
    }

    public XeroOauth2State decodeState(String state) {
        var builder = XeroOauth2State.builder();
        var parts = state.split("\\+");
        if (parts.length > 1) {
            return builder
                    .organisationId(parts[0])
                    .userId(parts[1])
                    .build();
        }
        return builder
                .uuid(parts[0])
                .build();
    }

    @Override
    public void handleWebHook(XeroWebHookHandlerPayload payload) {
        var tenantIds = payload.getEvents().stream().map(XeroWebHooksPayload.Event::getTenantId).collect(toUnmodifiableSet());
        var tenantIdIntegration = tenantIds.stream().map(tenantId -> integrationService.findIntegrationItem(tenantId, XERO))
                .collect(toUnmodifiableList());

        for (var event : payload.getEvents()) {
            try {
                if (!event.getEventCategory().equals(ResourceType.INVOICE)) {
                    log.warn("Invalid type - " + event.getEventCategory());
                    continue;
                }
                var tenantId = event.getTenantId();
                Integration integration = tenantIdIntegration.stream()
                        .filter(i -> tenantId.equals(i.getTenantId()))
                        .findAny()
                        .orElseThrow(() -> new NotFoundException("Coudnt find integration for tenantId:" + tenantId));

                SqsInvoiceMessagePattern<Invoice> message = SqsInvoiceMessagePattern.<Invoice>builder()
                        .organisationId(integration.getOrganisationId())
                        .type(SqsInvoiceType.GET_INVOICE)
                        .tenantId(event.getTenantId())
                        .entityId(event.getResourceId())
                        .build();
                invoiceOutboundSender.sendMessageToTopic(message);
            } catch (Exception e) {
                log.error("Failed to fetch invoice updates for - " + event.getTenantId(), e);
            }
        }
    }

    @Override
    public void handleContactUpdate(User user) {
        var eventName = new AtomicReference<>("UPDATE");
        var organisationId = user.getCurrentOrganisation();
        var xeroId = user.getXeroId();
        var userId = user.getId();
        var organisation = organisationService.getOrganisation(organisationId);
        var adminUser = organisation.getAdminUser();
        var integration = integrationService.findConnectedIntegration(organisationId, adminUser, XERO);

        var tenantId = integration.getTenantId();
        var token = integration.getAccessToken();

        var newContact = toXeroContact(user);
        log.info("Contact - {}", wrappedToJsonString(newContact));

        var contact = Optional
                .ofNullable(xeroId)
                .map(id -> xeroClient.getContactWithRetry(id, tenantId, token))
                .orElse(null);

        Optional.ofNullable(contact).ifPresentOrElse(item -> {
            var response = xeroClient.updateContactWithRetry(item.getContactID().toString(), newContact.contactID(null), tenantId, token);

            log.info("Update contact response - {}", wrappedToJsonString(response));
        }, () -> {
            var response = xeroClient.createContactWithRetry(newContact, tenantId, token);

            log.info("Create contact response - {}", wrappedToJsonString(response));

            userService.updateXeroDetails(userId, response.getContactID().toString());
            eventName.set("CREATE");
        });
    }

    @Override
    public void fetchData(ResourceType type) {
        integrationService
                .findConnectedIntegrations(XERO)
                .forEach(integration -> fetchInternal(type, integration, false));
    }

    @Override
    public void fetchAccountData(String organisationId) {
        var integration = integrationService.findIntegrationByOrganisationId(organisationId, XERO);
        fetchInternal(ResourceType.ACCOUNT, integration, true);
    }

    private void fetchInternal(ResourceType type, Integration integration, boolean ignoreLastUpdatedDate) {
        var organisationId = integration.getOrganisationId();
        var integrationId = integration.getId();
        var metadata = findMetadata(integration, type);
        var lastUpdatedDate = getLastUpdatedDate(integration, metadata);
        var status = integration.getStatus();
        log.info("Organisation - {}", organisationId);
        if (metadata.getRetryCount() > 2) {
            log.info("Retry limit for {} reached - {}", type, metadata.getRetryCount());
            if (status != FAILED) {
                integrationService.updateIntegrationStatus(integration.getOrganisationId(), FAILED, XERO);
                onXeroConnectionFailure(organisationId);
            }
            return;
        }

        if (!ignoreLastUpdatedDate && Objects.nonNull(lastUpdatedDate) && getDurationInSecond(lastUpdatedDate, Instant.now()) < ONE_HOUR) {
            log.info("Data was updated within the last hour - {}", lastUpdatedDate.toString());
            return;
        }

        if (integration.getRemainingCallAmount() < 500) {
            log.info("Remaining call amount below allowed threshold of 500 - {}, skipping update", integration.getRemainingCallAmount());
            return;
        }

        log.info("Last updated date - {}, type - {}", lastUpdatedDate, type);
        try {
            switch (type) {
                case CONTACT:
                    fetchContacts(integration, lastUpdatedDate);
                    integrationService.updateMetadata(integrationId, metadata
                            .toBuilder()
                            .lastUpdatedDate(Instant.now())
                            .retryCount(0)
                            .build());
                    break;
                case TRANSACTION:
                    fetchTransactions(integration, lastUpdatedDate, metadata);
                    break;
                case INVOICE:
                    fetchInvoices(integration, lastUpdatedDate);
                    integrationService.updateMetadata(integrationId, metadata
                            .toBuilder()
                            .lastUpdatedDate(Instant.now())
                            .retryCount(0)
                            .build());
                    break;
                case ACCOUNT:
                    fetchAccounts(integration, metadata);
                    break;
                case OVERPAYMENT:
                    fetchOverPayments(integration, lastUpdatedDate, metadata);
                    break;
                case PAYMENT:
                    fetchPayments(integration, lastUpdatedDate, metadata);
                    break;
                case BANK_TRANSFER:
                    fetchBankTransfers(integration, lastUpdatedDate, metadata);
                    break;
                case JOURNAL:
                    fetchJournals(integration, lastUpdatedDate, metadata);
                    break;
                default:
                    log.info("Incorrect type - " + type);
            }
        } catch (HttpClientErrorException e) {
            if (e.getStatusCode().equals(HttpStatus.FORBIDDEN) || e.getStatusCode().equals(HttpStatus.UNAUTHORIZED)) {
                var retryCount = metadata.getRetryCount() + 1;
                integrationService.updateMetadata(integrationId, metadata
                        .toBuilder()
                        .retryCount(retryCount)
                        .errorMessage("Failed to fetch " + type + ", retry count - " + retryCount)
                        .build());
                if (retryCount > 2) {
                    log.info("Failed to fetch data {} times, removing Xero connection - {}", retryCount, integration);
                    onXeroConnectionFailure(organisationId);
                    removeConnection(integration, false);
                }
                log.error("Failed to fetch {}, retry count - " + retryCount, type, e);
            }
        } catch (HttpServerErrorException e) {
            log.error("Xero is down and responded with 5XX. Failed to process {} {} ", type, organisationId);
        } catch (Exception e) {
            var retryCount = metadata.getRetryCount() + 1;
            integrationService.updateMetadata(integrationId, metadata
                    .toBuilder()
                    .retryCount(retryCount)
                    .errorMessage("Failed to fetch " + type + ", retry count - " + retryCount)
                    .build());
            if (retryCount > 2) {
                log.info("Failed to fetch data {} times, removing Xero connection - {}", retryCount, integration);
                removeConnection(integration, false);
            }
            log.error("Failed to fetch {}, retry count - " + retryCount, type, e);
        }
    }

    private static Instant getLastUpdatedDate(Integration integration, IntegrationMetadata metadata) {
        return Stream.of(
                        Optional.ofNullable(metadata.getLastUpdatedDate()),
                        Optional.ofNullable(integration.getStartDate())
                )
                .filter(Optional::isPresent)
                .map(Optional::get)
                .findFirst()
                .orElse(Instant.now());
    }

    private void fetchBankTransfers(Integration integration, Instant lastUpdatedDate, IntegrationMetadata metadata) {
        var integrationId = integration.getId();
        var startDate = integration.getStartDate();
        var tenantId = integration.getTenantId();
        var token = integration.getAccessToken();
        var bankTransfers = Optional
                .ofNullable(lastUpdatedDate)
                .map(d -> xeroClient.listBankTransfersWithRetry(tenantId, token, d))
                .orElseGet(() -> Optional
                        .ofNullable(startDate)
                        .map(d -> xeroClient.listBankTransfersWithRetry(tenantId, token, d))
                        .orElseGet(() -> xeroClient.listBankTransfersWithRetry(tenantId, token, null)));

        log.info("Transfers count - " + bankTransfers.getBankTransfers().size());

        var organisation = integration.getOrganisationId();

        bankTransfers
                .getBankTransfers()
                .forEach(bankTransfer -> sendBankTransferMessage(organisation, tenantId, bankTransfer));

        integrationService.updateMetadata(integrationId, metadata
                .toBuilder()
                .lastUpdatedDate(Instant.now())
                .retryCount(0)
                .build());
    }

    private void fetchJournals(Integration integration, Instant lastUpdatedDate, IntegrationMetadata metadata) {
        var integrationId = integration.getId();
        var startDate = integration.getStartDate();
        var tenantId = integration.getTenantId();
        var token = integration.getAccessToken();
        var journals = Optional
                .ofNullable(lastUpdatedDate)
                .map(d -> xeroClient.listJournalsWithRetry(tenantId, token, d))
                .orElseGet(() -> Optional
                        .ofNullable(startDate)
                        .map(d -> xeroClient.listJournalsWithRetry(tenantId, token, d))
                        .orElseGet(() -> xeroClient.listJournalsWithRetry(tenantId, token, null)));

        log.info("Journals count - " + journals.getJournals().size());

        var organisation = integration.getOrganisationId();

        journals
                .getJournals()
                .forEach(journal -> sendJournalMessage(organisation, tenantId, journal));

        integrationService.updateMetadata(integrationId, metadata
                .toBuilder()
                .lastUpdatedDate(Instant.now())
                .retryCount(0)
                .build());
    }

    private void sendBankTransferMessage(String organisation, String tenant, XeroBankTransfers.XeroBankTransfer bankTransfer) {
        try {
            var payload = new XeroBankTransferLoaderPayload(organisation, tenant, bankTransfer);
            sqsClient.enqueue(config.getBankTransferQueue(), toJsonString(payload));
        } catch (JsonProcessingException e) {
            throw new IllegalStateException(e);
        }
    }

    private void sendJournalMessage(String organisation, String tenant, Journal journal) {
        try {
            var payload = new XeroJournalLoaderPayload(organisation, tenant, journal);
            sqsClient.enqueue(config.getJournalQueue(), toJsonString(payload));
        } catch (JsonProcessingException e) {
            throw new IllegalStateException(e);
        }
    }

    private void fetchPayments(Integration integration, Instant lastUpdatedDate, IntegrationMetadata metadata) {
        var integrationId = integration.getId();
        var startDate = integration.getStartDate();
        var tenantId = integration.getTenantId();
        var token = integration.getAccessToken();
        var organisation = integration.getOrganisationId();
        var payments = Optional
                .ofNullable(lastUpdatedDate)
                .map(d -> xeroClient.listPaymentsWithRetry(tenantId, token, d))
                .orElseGet(() -> Optional
                        .ofNullable(startDate)
                        .map(d -> xeroClient.listPaymentsWithRetry(tenantId, token, d))
                        .orElseGet(() -> xeroClient.listPaymentsWithRetry(tenantId, token, null)));

        log.info("Payment count - " + payments.getPayments().size());

        payments
                .getPayments()
                .forEach(payment -> sendPaymentMessage(organisation, tenantId, payment));

        integrationService.updateMetadata(integrationId, metadata
                .toBuilder()
                .lastUpdatedDate(Instant.now())
                .retryCount(0)
                .build());
    }

    private void fetchOverPayments(Integration integration, Instant lastUpdatedDate, IntegrationMetadata metadata) {
        var integrationId = integration.getId();
        var startDate = integration.getStartDate();
        var tenantId = integration.getTenantId();
        var token = integration.getAccessToken();
        var organisation = integration.getOrganisationId();
        var lastFetchDate = Optional
                .ofNullable(lastUpdatedDate)
                .or(() -> Optional.ofNullable(startDate))
                .orElse(null);

        var overPayments = xeroClient.listOverPaymentsWithRetry(tenantId, token, lastFetchDate);

        log.info("Over Payment count - " + overPayments.getOverPayments().size());

        overPayments
                .getOverPayments()
                .forEach(overPayment -> sendOverPaymentMessage(organisation, tenantId, overPayment));

        integrationService.updateMetadata(integrationId, metadata
                .toBuilder()
                .lastUpdatedDate(Instant.now())
                .retryCount(0)
                .build());
    }

    private void sendPaymentMessage(String organisation, String tenant, XeroPayments.XeroPayment xeroPayment) {
        try {
            var payload = new XeroPaymentLoaderPayload(organisation, tenant, xeroPayment);
            sqsClient.enqueue(config.getPaymentsQueue(), toJsonString(payload));
        } catch (JsonProcessingException e) {
            throw new IllegalStateException(e);
        }
    }

    private void sendOverPaymentMessage(String organisation, String tenant, XeroOverPayments.XeroOverPayment xeroOverPayment) {
        try {
            var payload = new XeroOverPaymentLoaderPayload(organisation, tenant, xeroOverPayment);
            sqsClient.enqueue(config.getOverPaymentQueue(), toJsonString(payload));
        } catch (JsonProcessingException e) {
            throw new IllegalStateException(e);
        }
    }

    private void fetchContacts(Integration integration, Instant lastUpdatedDate) {
        var startDate = integration.getStartDate();
        var tenantId = integration.getTenantId();
        var token = integration.getAccessToken();
        var organisation = integration.getOrganisationId();
        var contacts = Optional
                .ofNullable(lastUpdatedDate)
                .map(d -> xeroClient.listContactsWithRetry(tenantId, token, d))
                .orElseGet(() -> Optional
                        .ofNullable(startDate)
                        .map(d -> xeroClient.listContactsWithRetry(tenantId, token, d))
                        .orElseGet(() -> xeroClient.listContactsWithRetry(tenantId, token, null)));
        log.info("Contact count - " + contacts.getContacts().size());

        contacts
                .getContacts()
                .stream()
                .filter(xeroContact -> Objects.nonNull(xeroContact.getName()) && !xeroContact.getName().isEmpty())
                .filter(contact -> Objects.isNull(integration.getAllowedContactGroups())
                        || integration.getAllowedContactGroups().isEmpty()
                        || integration.getAllowedContactGroups()
                        .stream()
                        .anyMatch(group -> contact.getContactGroups()
                                .stream()
                                .map(ContactGroup::getName)
                                .anyMatch(xGroup -> xGroup.equals(group)))
                ).forEach(xeroContact -> sendContactMessage(organisation, tenantId, xeroContact));
    }

    private void sendContactMessage(String organisation, String tenant, Contact xeroContact) {
        try {
            var payload = new XeroContactLoaderPayload(organisation, tenant, xeroContact);
            sqsClient.enqueue(config.getContactsQueue(), toJsonString(payload));
        } catch (JsonProcessingException e) {
            throw new IllegalStateException(e);
        }
    }

    private void fetchInvoices(Integration integration, Instant lastUpdatedDate) {
        var startDate = integration.getStartDate();
        var tenantId = integration.getTenantId();
        var token = integration.getAccessToken();
        var organisation = integration.getOrganisationId();
        var invoices = Optional
                .ofNullable(lastUpdatedDate)
                .map(d -> {
                    log.info("Calling list invoices with last updated date - {}", lastUpdatedDate.toString());
                    return xeroClient.listInvoicesWithRetry(tenantId, token, d);
                })
                .orElseGet(() -> Optional
                        .ofNullable(startDate)
                        .map(d -> {
                            log.info("Calling list invoices with startDate date - {}", startDate.toString());
                            return xeroClient.listInvoicesWithRetry(tenantId, token, d);
                        })
                        .orElseGet(() -> {
                            log.info("Calling list invoices without date");
                            return xeroClient.listInvoicesWithRetry(tenantId, token, null);
                        }));

        log.info("Invoice count - " + invoices.getInvoices().size());

        if (!invoices.getInvoices().isEmpty()) {
            var availableUserXeroIds = userService.findOrganisationUsers(integration.getOrganisationId())
                    .stream()
                    .map(User::getXeroId)
                    .filter(Objects::nonNull)
                    .collect(toUnmodifiableSet());
            var missingUserXeroIds = invoices.getInvoices()
                    .stream()
                    // These null checks are total overkill, but I would rather be safe then sorry
                    .flatMap(invoice -> Optional.ofNullable(invoice.getContact())
                            .flatMap(contact -> Optional.ofNullable(contact.getContactID()))
                            .flatMap(contactId -> Optional.ofNullable(contactId.toString()))
                            .stream()
                    )
                    .filter(userXeroId -> !availableUserXeroIds.contains(userXeroId))
                    .collect(toUnmodifiableSet());

            log.info("Xero user query count {}", missingUserXeroIds.size());

            for (var missingUserXeroId : missingUserXeroIds) {
                var contact = xeroClient.getContactWithRetry(missingUserXeroId, tenantId, integration.getAccessToken());
                xeroDataMapper.mapContact(organisation, contact);
            }
        }

        invoices
                .getInvoices()
                .forEach(invoice -> sendInvoiceMessage(organisation, tenantId, invoice));
    }

    private void sendInvoiceMessage(String organisation, String tenant, XeroInvoice invoice) {
        try {
            var payload = new XeroInvoiceLoaderPayload(organisation, tenant, invoice);
            sqsClient.enqueue(config.getInvoicesQueue(), toJsonString(payload));
        } catch (JsonProcessingException e) {
            throw new IllegalStateException(e);
        }
    }

    private void fetchTransactions(Integration integration, Instant lastUpdatedDate, IntegrationMetadata metadata) {
        var integrationId = integration.getId();
        var startDate = integration.getStartDate();
        var tenantId = integration.getTenantId();
        var token = integration.getAccessToken();
        var organisation = integration.getOrganisationId();
        log.info("Tenant id - " + tenantId);
        log.info("Token - " + token);
        var transactions = Optional
                .ofNullable(lastUpdatedDate)
                .map(d -> xeroClient.listTransactionsWithRetry(tenantId, token, d))
                .orElseGet(() -> Optional
                        .ofNullable(startDate)
                        .map(d -> xeroClient.listTransactionsWithRetry(tenantId, token, d))
                        .orElseGet(() -> xeroClient.listTransactionsWithRetry(tenantId, token, null)));

        log.info("Transaction count - " + transactions.getTransactions().size());

        var accounts = transactions
                .getTransactions()
                .stream()
                .map(transaction -> transaction.getAccount().getAccountId())
                .collect(toUnmodifiableSet());
        log.info("Accounts - " + accounts);
        updateAccountBalance(organisation, tenantId, token, accounts);

        transactions
                .getTransactions()
                .forEach(transaction -> {
                    try {
                        var payload = new XeroTransactionLoaderPayload(organisation, tenantId, transaction);
                        sqsClient.enqueue(config.getTransactionsQueue(), toJsonString(payload));
                    } catch (JsonProcessingException e) {
                        throw new IllegalStateException(e);
                    }
                });
        integrationService.updateMetadata(integrationId, metadata
                .toBuilder()
                .lastUpdatedDate(Instant.now())
                .retryCount(0)
                .build());
    }

    protected void updateAccountBalance(String organisationId, String tenantId, String token, Set<String> bankAccounts) {
        try {
            var bankSummary = xeroClient.getBankSummaryWithRetry(tenantId, token, Instant.now(), Instant.now());
            var summaryCells = bankSummary
                    .getSummaries()
                    .stream()
                    .findAny()
                    .orElseThrow()
                    .getRows()
                    .stream()
                    .filter(row -> row.getType().equals("Section"))
                    .findAny()
                    .orElseThrow()
                    .getRows()
                    .stream()
                    .filter(row -> row.getType().equals("Row"))
                    .map(BankSummary.Row::getCells)
                    .collect(toUnmodifiableList());

            bankAccounts.forEach(account -> {
                try {
                    summaryCells.forEach(cells -> {
                        log.info("Cells - " + cells);
                        var accountCells = cells
                                .stream()
                                .filter(cell -> Objects.nonNull(cell.getAttributes()) && cell.getAttributes().stream().anyMatch(attribute -> attribute.getValue().equals(account)))
                                .collect(toUnmodifiableList());
                        if (!accountCells.isEmpty()) {
                            var balance = cells.get(cells.size() - 1).getValue();
                            log.info("Balance - " + balance);
                            // TODO we may have race condition, because at first transactions may be fetched before accounts
                            invoiceService.updateAccountBalance(organisationId, account, balance);
                        }
                    });
                } catch (Exception e) {
                    log.error("Failed to update account balance - " + account, e);
                }
            });
        } catch (Exception e) {
            log.error("Failed to update accounts", e);
        }
    }

    private void fetchAccounts(Integration integration, IntegrationMetadata metadata) {
        var integrationId = integration.getId();
        var tenantId = integration.getTenantId();
        var token = integration.getAccessToken();
        var organisation = integration.getOrganisationId();
        log.info("Tenant id - " + tenantId);
        log.info("Token - " + token);
        // Deleted accounts aren't returned from Xero, so we need to query all of them and check what's missing
        var accounts = xeroClient.listAccountsWithRetry(tenantId, token, null)
                .getAccounts()
                .stream()
                .filter(account -> Account.StatusEnum.ACTIVE.getValue().equals(account.getStatus()))
                .collect(toUnmodifiableList());

        log.info("Account count - " + accounts.size());

        accounts.forEach(account -> {
            try {
                var payload = new XeroAccountLoaderPayload(organisation, tenantId, account);
                sqsClient.enqueue(config.getAccountsQueue(), toJsonString(payload));
            } catch (JsonProcessingException e) {
                throw new IllegalStateException(e);
            }
        });
        integrationService.updateMetadata(integrationId, metadata
                .toBuilder()
                .lastUpdatedDate(Instant.now())
                .retryCount(0)
                .build());

        var existingAccountIds = accounts.stream().map(XeroAccount::getAccountId).collect(toUnmodifiableSet());

        var toDeleteAccountIds = invoiceService.findAccountsWithOrganisationId(organisation, tenantId)
                .stream()
                .filter(account -> !existingAccountIds.contains(account.getAccountId()))
                .map(com.rentancy.integrations.pojos.Account::getId)
                .collect(toUnmodifiableSet());

        invoiceService.deleteAccounts(toDeleteAccountIds);

        log.info(wrappedToJsonString(toDeleteAccountIds));
        log.info(wrappedToJsonString(accounts));
    }

    @Override
    public void loadContact(String organisation, Contact contact) {
        log.info("Contact - {}", wrappedToJsonString(contact));
        xeroDataMapper.mapContact(organisation, contact);
    }

    @Override
    public void loadOverPayment(String organisation, XeroOverPayment overPayment) {
        log.info("OverPayment - {}", wrappedToJsonString(overPayment));
        xeroDataMapper.mapOverPayment(organisation, overPayment);
    }

    @Override
    public void loadInvoice(String organisation, String tenant, XeroInvoice xeroInvoice) {
        log.info("Invoice - {}", wrappedToJsonString(xeroInvoice));

        invoiceService.createWebhookEvent(organisation, xeroInvoice.getInvoiceId(), xeroInvoice.toString());

        var contactId = Optional.ofNullable(xeroInvoice.getContact()).map(contact -> contact.getContactID().toString()).orElse(null);
        User contactUser = null;

        if (Objects.nonNull(contactId)) {

            contactUser = userService.findUserWithXeroId(contactId);

            if (isNull(contactUser)) {
                var integration = integrationService.findIntegration(tenant, XERO);
                var contact = xeroClient.getContactWithRetry(contactId, tenant, integration.getAccessToken());

                log.info("Contact fetched from Xero from invoice loader - {} {}", organisation, contact.getContactID().toString());

                contactUser = userService.findUserWithXeroId(contactId);
                if (isNull(contactUser)) {
                    contactUser = xeroDataMapper.mapContact(organisation, contact);
                }
            }
        }

        xeroDataMapper.mapInvoice(organisation, tenant, xeroInvoice, contactUser);
    }

    @Override
    public void loadTransaction(String organisation, String tenant, XeroTransaction xeroTransaction) {
        log.info("Transaction - " + xeroTransaction.toString());

        var contactUser = Optional.ofNullable(xeroTransaction.getContact())
                .map(contact -> contact.getContactID().toString())
                .map(contactId -> {
                    User user = userService.findUserWithXeroId(contactId);

                    if (Objects.nonNull(user)) {
                        return user;
                    }

                    var integration = integrationService.findIntegration(tenant, XERO);
                    var contact = xeroClient.getContactWithRetry(contactId, tenant, integration.getAccessToken());

                    user = userService.findUserWithXeroId(contactId);
                    if (isNull(user)) {
                        user = xeroDataMapper.mapContact(organisation, contact);
                    }

                    return user;
                })
                .orElse(null);

        xeroDataMapper.mapTransaction(organisation, tenant, xeroTransaction, contactUser);
    }

    @Override
    public void loadAccount(String organisation, String tenant, XeroAccount xeroAccount) {
        log.info("Account - " + xeroAccount.toString());
        xeroDataMapper.mapAccount(organisation, tenant, xeroAccount);
    }

    @Override
    public void loadPayment(String organisation, String tenant, XeroPayments.XeroPayment xeroPayment) {
        log.info("Payment - " + xeroPayment.toString());
        xeroDataMapper.mapPayment(organisation, tenant, xeroPayment);
    }

    @Override
    public void loadTransfer(String organisation, String tenant, XeroBankTransfers.XeroBankTransfer xeroTransfer) {
        log.info("Transfer - " + xeroTransfer.toString());
        xeroDataMapper.mapTransfer(organisation, tenant, xeroTransfer);
    }

    @Override
    public void loadJournal(String organisation, String tenant, Journal journal) {
        log.info("Journal - " + journal.toString());
        xeroDataMapper.mapJournal(organisation, tenant, journal);
    }

    @Override
    public XeroAuthentication getAppStoreAuthorizationUrl() {
        var scopes = config.getXeroOpenIdScopes();
        var res = oauth2Client.getAuthorizeUrl(UUID.randomUUID().toString(), scopes);
        return new XeroAuthentication(res.getUrl());
    }

    @Override
    public void syncContacts() {
        var integrations = integrationService.findConnectedIntegrations(XERO);

        log.info("Number of connected integrations - {}", integrations.size());

        integrations.forEach(integration -> {
            try {
                var tenant = integration.getTenantId();
                var token = integration.getAccessToken();
                var organisationId = integration.getOrganisationId();

                log.info("Organisation - {}", organisationId);

                var contacts =
                        xeroClient.listContactsWithRetry(tenant, token, Instant.parse(XeroClient.DEFAULT_XERO_START_TIMESTAMP));
                var filteredContacts = contacts.getContacts()
                        .stream().filter(contact -> Objects.isNull(integration.getAllowedContactGroups())
                                || integration.getAllowedContactGroups().isEmpty()
                                || integration.getAllowedContactGroups()
                                .stream()
                                .anyMatch(group -> contact.getContactGroups()
                                        .stream()
                                        .map(ContactGroup::getName)
                                        .anyMatch(xGroup -> xGroup.equals(group)))
                        ).collect(toUnmodifiableList());

                log.info("Contacts size - {}", filteredContacts.size());

                var organisation = organisationService.getOrganisation(organisationId);
                var users = userService.findOrganisationUsersWithAddresses(organisationId);

                log.info("Number of users - {}", users.size());

                var notSyncedContacts = users
                        .stream()
                        .filter(user -> new ArrayList<>(List.of(
                                "NEW",
                                "TENANT",
                                "LANDLORD",
                                "OWNER",
                                "SUPPLIER",
                                "GUARANTOR",
                                "SOLICITOR",
                                "VENDOR",
                                "CONSULTANT",
                                "OTHER",
                                "TEAM",
                                "MANAGER",
                                "FINANCE",
                                "INBOX",
                                "LEASEHOLDER",
                                "AGENT",
                                "ADMIN"
                        )).contains(user.getType()))
                        .filter(user -> {
                                    var name = Optional
                                            .ofNullable(user.getCompanyName())
                                            .map(StringUtils::trimLeadingWhitespace)
                                            .map(StringUtils::trimTrailingWhitespace)
                                            .filter(StringUtils::hasLength)
                                            .orElseGet(() -> {
                                                var fname = Optional.ofNullable(user.getFname())
                                                        .map(StringUtils::trimTrailingWhitespace)
                                                        .map(StringUtils::trimLeadingWhitespace)
                                                        .orElse("");
                                                var sname = Optional.ofNullable(user.getSname())
                                                        .map(StringUtils::trimTrailingWhitespace)
                                                        .map(StringUtils::trimLeadingWhitespace)
                                                        .orElse("");
                                                return trimLeadingWhitespace(trimTrailingWhitespace(join(" ", fname, sname)));
                                            });
                                    if (!StringUtils.hasLength(name)) {
                                        log.info("Contact name is empty - {}, skipping it", name);
                                        return false;
                                    }
                                    var contact = filteredContacts
                                            .stream()
                                            .filter(xeroContact -> xeroContact.getName().equals(name))
                                            .findAny()
                                            .orElse(null);
                                    if (contact != null) {
                                        userService.updateXeroDetails(user.getId(), contact.getContactID().toString());
                                        return false;
                                    }
                                    return true;
                                }
                        ).collect(toUnmodifiableList());

                if (!StringUtils.hasLength(organisation.getXeroId()) || isNull(findContact(filteredContacts, organisation.getXeroId(), organisation.getName()))) {
                    try {
                        var organisationContact = toOrganisationXeroContact(organisation);

                        log.info("Organisation contact to create - {}", wrappedToJsonString(organisationContact));
                        var result = xeroClient.createContactWithRetry(organisationContact, tenant, token);

                        organisationService.updateXeroDetails(organisationId, result.getContactID().toString());
                    } catch (Exception e) {
                        log.error("Failed to create organisation contact", e);
                    }
                }

                log.info("Number of not synced contacts - {}", notSyncedContacts.size());

                notSyncedContacts.forEach(user -> {
                    try {
                        var contact = toXeroContact(user);
                        log.info("Rentancy user - {}, Contact to create - {}", wrappedToJsonString(user), wrappedToJsonString(contact));
                        var result = xeroClient.createContactWithRetry(contact, tenant, token);
                        log.info("Result contact id - {}", result.getContactID().toString());

                        userService.updateXeroDetails(user.getId(), result.getContactID().toString());
                    } catch (Exception e) {
                        log.error("Failed to create contact", e);
                    }
                });
            } catch (Exception e) {
                log.error("Failed to sync contacts for - " + integration.getOrganisationId(), e);
            }
        });
    }

    @Override
    public void syncInvoices(String organisationId) {
        var integration = integrationService.findIntegrationByOrganisationId(organisationId, XERO);
        var dailyLimit = integration.getRemainingCallAmount();

        if (dailyLimit != null && dailyLimit >= 2000) {
            throw new IllegalStateException("Remaining api calls in more than 2000 or it's 0: " + organisationId);
        }

        var metadata = findMetadata(integration, ResourceType.INVOICE);
        var lastUpdatedDate = metadata.getLastUpdatedDate();

        if (lastUpdatedDate == null) {
            fetchInvoices(integration, Instant.now().minus(1, ChronoUnit.HOURS));
        } else {
            fetchInvoices(integration, lastUpdatedDate);
        }
    }

    @Override
    public XeroInvoicesUpdateResponse updateInvoicesStatus(String cognitoId, String statementId, XeroInvoicesUpdateInput apiInput) {
        var organisationId = apiInput.getOrganisationId();
        var invoiceIds = apiInput.getInvoiceIds();
        var status = apiInput.getStatus();
        var integration = integrationService.findIntegrationByOrganisationId(organisationId, XERO);
        var user = userService.findUserWithCognitoId(cognitoId);
        List<XeroInvoicesUpdateResponse.XeroInvoiceUpdateResponse> response;
        Statement statement;

        if (integration == null || NOT_CONNECTED == integration.getStatus()) {
            throw new IllegalStateException("Integration not exists or not connected - " + organisationId);
        }

        if (invoiceIds.isEmpty()) {
            if (statementId != null) {
                statement = reportService.getStatement(statementId);
                reportService.saveStatement(statement
                        .toBuilder()
                        .billsUpdatedDate(Instant.now())
                        .billsUpdatedBy(getUserInitials(formatUser(user)))
                        .billsUpdated(true)
                        .billsUpdateResult(List.of())
                        .build());

                if (statement.isApproved() && statement.isPayedOut() && statement.isSent()) {
                    var landlordBill = invoiceService.getLandlordBill(statement.getLandlordBillId());

                    landlordBill.setStatus(LandlordBill.LandlordBillStatus.COMPLETED);

                    invoiceService.updateLandlordBill(landlordBill);
                }
            }

            return new XeroInvoicesUpdateResponse(List.of());
        }

        var invoices = invoiceIds
                .stream()
                .map(id -> new Invoice().invoiceID(UUID.fromString(id)).status(Invoice.StatusEnum.valueOf(status)))
                .collect(toUnmodifiableList());
        var result = xeroClient.createInvoicesWithRetry(invoices, integration.getTenantId(), integration.getAccessToken());

        log.info("Response - {}", wrappedToJsonString(result));

        response = new ArrayList<>(result
                .stream()
                .map(invoice -> new XeroInvoicesUpdateResponse.
                        XeroInvoiceUpdateResponse(invoice.getInvoiceID().toString(),
                        invoice.getHasErrors() ? invoice
                                .getValidationErrors()
                                .stream()
                                .map(ValidationError::getMessage)
                                .collect(joining(", ")) : "OK",
                        userService.findUserWithXeroId(invoice.getContact().getContactID().toString()),
                        BigDecimal.valueOf(invoice.getAmountDue())))
                .collect(toUnmodifiableList()));

        if (statementId != null) {
            statement = reportService.getStatement(statementId);
            var billsUpdated = result.stream().noneMatch(Invoice::getHasErrors);

            reportService.saveStatement(statement
                    .toBuilder()
                    .billsUpdatedDate(Instant.now())
                    .billsUpdatedBy(getUserInitials(formatUser(user)))
                    .billsUpdated(billsUpdated)
                    .billsUpdateResult(response)
                    .build());

            if (billsUpdated && statement.isApproved() && statement.isPayedOut() && statement.isSent()) {
                var landlordBill = invoiceService.getLandlordBill(statement.getLandlordBillId());

                landlordBill.setStatus(LandlordBill.LandlordBillStatus.COMPLETED);

                invoiceService.updateLandlordBill(landlordBill);
            }
        }

        return new XeroInvoicesUpdateResponse(response);
    }

    private Contact toXeroContact(User user) {
        var fname = user.getFname();
        var sname = user.getSname();
        var companyName = user.getCompanyName();
        var cognitoEmail = user.getCognitoEmail();
        var email = Optional.ofNullable(cognitoEmail)
                .orElseGet(() -> Optional
                        .ofNullable(user.getEmails()).flatMap(emails -> emails.stream().findAny().map(User.Email::getEmail))
                        .orElse(null));
        var phones = Utils.getOrEmpty(user.getPhones());
        var xeroPhones = phones
                .stream()
                .map(phone -> new Phone().phoneType(DEFAULT).phoneNumber(phone.getPhone()))
                .collect(toUnmodifiableList());
        return new Contact()
                .contactID(UUID.randomUUID())
                .emailAddress(email)
                .firstName(fname)
                .lastName(sname)
                .name(Optional
                        .ofNullable(companyName)
                        .map(StringUtils::trimLeadingWhitespace)
                        .map(StringUtils::trimTrailingWhitespace)
                        .filter(StringUtils::hasLength)
                        .orElseGet(() -> {
                            var formattedFname = Optional.ofNullable(fname)
                                    .map(StringUtils::trimTrailingWhitespace)
                                    .map(StringUtils::trimLeadingWhitespace)
                                    .orElse("");
                            var formattedSname = Optional.ofNullable(sname)
                                    .map(StringUtils::trimTrailingWhitespace)
                                    .map(StringUtils::trimLeadingWhitespace)
                                    .orElse("");
                            return trimLeadingWhitespace(trimTrailingWhitespace(join(" ", formattedFname, formattedSname)));
                        }))
                .phones(xeroPhones);
    }

    private Contact toOrganisationXeroContact(Organisation organisation) {
        return new Contact()
                .contactID(UUID.randomUUID())
                .name(organisation.getName())
                .addresses(List.of(new Address()
                        .addressType(STREET)
                        .addressLine1(getDetail(organisation.getAddressLine1()))
                        .addressLine2(getDetail(organisation.getAddressLine2()))
                        .addressLine3(getDetail(organisation.getAddressLine3()))
                        .city(getDetail(organisation.getCity()))
                        .country(getDetail(organisation.getState()))
                        .postalCode(getDetail(organisation.getPostcode())))
                );
    }

    private IntegrationMetadata findMetadata(Integration integration, ResourceType type) {
        return integration
                .getMetadata()
                .stream()
                .filter(meta -> meta.getRecourseType() == type)
                .findAny()
                .orElseGet(() -> {
                    var integrationId = integration.getId();
                    var item = Integration.IntegrationMetadata
                            .builder()
                            .retryCount(0)
                            .recourseType(type)
                            .build();
                    integrationService.createMetadata(integrationId, item);
                    return item;
                });
    }

    private Contact findContact(List<Contact> contacts, String xeroId, String name) {
        return contacts
                .stream()
                .filter(xeroContact -> xeroContact.getContactID().toString().equals(xeroId))
                .filter(xeroContact -> xeroContact.getName().equals(name))
                .findAny()
                .orElse(null);
    }
}
