package com.rentancy.integrations.servicies;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.rentancy.integrations.exceptions.XeroConnectionAlreadyExistsException;
import com.rentancy.integrations.pojos.ExchangeInput;
import com.rentancy.integrations.pojos.Integration.ResourceType;
import com.rentancy.integrations.pojos.User;
import com.rentancy.integrations.pojos.XeroAccounts.XeroAccount;
import com.rentancy.integrations.pojos.XeroAuthentication;
import com.rentancy.integrations.pojos.XeroBankTransfers.XeroBankTransfer;
import com.rentancy.integrations.pojos.XeroExchangeResult;
import com.rentancy.integrations.pojos.XeroInvoices.XeroInvoice;
import com.rentancy.integrations.pojos.XeroInvoicesUpdateInput;
import com.rentancy.integrations.pojos.XeroInvoicesUpdateResponse;
import com.rentancy.integrations.pojos.XeroOverPayments.XeroOverPayment;
import com.rentancy.integrations.pojos.XeroPayments.XeroPayment;
import com.rentancy.integrations.pojos.XeroTransactions.XeroTransaction;
import com.rentancy.integrations.pojos.XeroWebHookHandlerPayload;
import com.rentancy.integrations.pojos.XeroWebHooksPayload;
import com.rentancy.integrations.servicies.oauth.OAuth;
import com.xero.models.accounting.Contact;
import com.xero.models.accounting.Journal;

import java.time.Instant;

public interface XeroService extends OAuth {

    byte[] getInvoicePdf(String cognitoId, String InvoiceId, boolean xeroId);

    byte[] getInvoicePdf(String organisationId, String invoiceId);

    void handleWebHookEvent(XeroWebHooksPayload payload);

    void handleWebHook(XeroWebHookHandlerPayload payload);

    void handleContactUpdate(User user) throws JsonProcessingException;

    void fetchData(ResourceType type);
    void fetchAccountData(String OrganisationId);
    void loadContact(String organisation, Contact contact);

    void loadInvoice(String organisation, String tenant, XeroInvoice xeroInvoice);

    void loadTransaction(String organisation, String tenant, XeroTransaction xeroTransaction);

    void loadAccount(String organisation, String tenant, XeroAccount xeroAccount);

    void loadPayment(String organisation, String tenant, XeroPayment xeroPayment);

    void loadTransfer(String organisation, String tenant, XeroBankTransfer xeroTransfer);

    void loadJournal(String organisation, String tenant, Journal journal);

    void syncContacts();

    void syncInvoices(String organisationId);

    XeroInvoicesUpdateResponse updateInvoicesStatus(String cognitoId, String statementId, XeroInvoicesUpdateInput apiInput);

    void loadOverPayment(String organisation, XeroOverPayment overPayment);
}
