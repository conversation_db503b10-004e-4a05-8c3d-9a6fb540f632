package com.rentancy.integrations.servicies;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.AttachmentMessage;
import com.rentancy.integrations.pojos.EmailSenderPayload;
import com.rentancy.integrations.pojos.MessageResponsePayload;
import com.rentancy.integrations.pojos.TwilioMessage;
import com.rentancy.integrations.servicies.persistence.ConversationService;
import com.rentancy.integrations.servicies.persistence.OrganisationService;
import com.rentancy.integrations.servicies.persistence.UserService;
import com.rentancy.integrations.util.JSONUtils;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import static com.rentancy.integrations.util.JSONUtils.deserializePayload;
import static com.rentancy.integrations.util.JSONUtils.wrappedToJsonString;
import static com.rentancy.integrations.util.Utils.downloadResource;

@RequiredArgsConstructor
class WhatsAppServiceImpl implements WhatsAppService {

    private static final Logger log = LogManager.getLogger(WhatsAppServiceImpl.class);

    private final Config config;
    private final TwilioClient twilioClient;
    private final ConversationService conversationService;
    private final OrganisationService organisationService;
    private final UserService userService;
    private final AppSyncServiceProvider appSyncServiceProvider;
    private final S3Client s3Client;
    private final SQSClient sqsClient;
    private final MainServiceClient mainServiceClient;


    @Override
    public void postMessage(Map<String, Object> payload) {
        var to = parseWhatsAppAddress(decodeParameter(payload, "To"));
        var decodedFrom = decodeParameter(payload, "From");
        var from = parseWhatsAppAddress(decodedFrom);
        var body = decodeParameter(payload, "Body");
        var mediaUrl = decodeParameter(payload, "MediaUrl0");
        var mediaType = parseMimeType(decodeParameter(payload, "MediaContentType0"));
        var name = decodeParameter(payload, "ProfileName");
        log.info("To - {}, From - {}, Body - {}", Arrays.toString(to), Arrays.toString(from), body);

        var organisations = organisationService.findOrganisationsWithWhatsAppAddress(to[1].trim());
        var customWhatsAppOrganisationNames = config.getCustomWhatsAppOrganisationNames();
        log.info("Organisations - {}", organisations);

        organisations.forEach(organisation -> {
            var organisationId = organisation.getId();
            var authorId = organisation.getBotUser();
            var adminUser = organisation.getAdminUser();
            var botUser = userService.findUser(authorId, false);

            var conversationId = organisationId + from[1].trim();
            var conversation = conversationService.findConversation(conversationId);
            var senderOrganisationName = customWhatsAppOrganisationNames
                    .stream()
                    .map(item -> item.split(":"))
                    .filter(item -> item[0].equals(organisationId))
                    .findAny()
                    .map(item -> item[1])
                    .orElse(organisation.getName());
            var messagePayload = getAutoReplyBody(senderOrganisationName, name);

            if (Objects.isNull(conversation)) {
                Set<String> members;
                var inboxMembers = new HashSet<>(userService.findOrganisationUserIdsWithRole(organisationId, "INBOX"));
                log.info("Inbox members - {}", inboxMembers.size());
                if (!inboxMembers.isEmpty()) {
                    inboxMembers.add(authorId);
                    members = new HashSet<>(inboxMembers);
                } else {
                   members = new HashSet<>(Set.of(authorId, adminUser));
                }
                appSyncServiceProvider.createConversation(organisationId, conversationId, botUser.getCognitoId(), String.join(" - ", from[1], name), members, "WHATSAPP_GROUP_CHAT");
                sendResponse(new MessageResponsePayload(decodedFrom, messagePayload, organisationId, null, null, "TEXT", null));
            }

            if (!mediaUrl.isEmpty()) {
                var fileName = UUID.randomUUID().toString() + "." + mediaType;
                var key = constructKey(conversationId, fileName);
                var content = downloadResource(mediaUrl);
                log.info("key - {}, Content - {}", key, content);
                s3Client.uploadPdf(config.getRentancyDocumentUploads(), String.join("/", "public", key), content, mediaType);
                var user = userService.findUserWithCognitoId(botUser.getCognitoId());
                var document = mainServiceClient.createConversationAttachment(conversationId, user.getId(), key, fileName, mediaType);
                var map = Map.of(
                        "fileName", fileName,
                        "fileType", mediaType,
                        "fileKey", key,
                        "conversationAttachmentId", document.getId()
                );
                appSyncServiceProvider.createMessage(organisationId, conversationId, authorId, wrappedToJsonString(map), "CONVERSATION_ATTACHMENTS");
            }

            if (!body.isEmpty()) {
                appSyncServiceProvider.createMessage(organisationId, conversationId, authorId, formatPayload(name, from[1], body), "TEXT");
            }
        });
    }

    private String parseMimeType(String fullMimeType) {
        var parts = fullMimeType.split("/");
        return parts.length > 1 ? parts[1] : fullMimeType;
    }

    private String getAutoReplyBody(String organisationName, @Nullable String sender) {
        return Optional.ofNullable(sender)
                .map(s -> "Hello, " + sender + " this is " + organisationName + ". Thanks for you message, a member of our team will get back to you shortly.")
                .orElse("Hello, this is " + organisationName + ". Reply with 'hi' to start chatting with the team.");
    }

    @Override
    public void sendWhatsAppIntegrationRequest(String organisationId, String userId){
        var emailQueue = config.getEmailNotificationQueue();
        var integrationQueue = config.getWhatsAppIntegrationQueue();
        var queueRegion = config.getEmailNotificationRegion();

        var organisation = organisationService.getOrganisation(organisationId);
        var organisationName = organisation.getName();
        var to = config.getRentancySupportEmail();
        var body = organisationName + " asks to add whatsApp integration";
        var organisationEmailAddress = String.join("+", "info", organisationId + "@email.loftyworks.com");

        var subject = "WhatApp integration";
        var user = userService.findUser(userId, false);
        if(user.getCognitoEmail() == null){
            throw new IllegalStateException("user does not have an email");
        }

        var emailPayload = EmailSenderPayload.builder()
                .organisationId(organisationId)
                .fromEmail(organisationEmailAddress)
                .email(to)
                .subject(subject)
                .body(body)
                .emails(List.of())
                .simple(false)
                .build();
        var emailPayloadForUser = EmailSenderPayload.builder()
                .organisationId(organisationId)
                .fromEmail(organisationEmailAddress)
                .email(user.getCognitoEmail())
                .subject(subject)
                .emails(List.of())
                .simple(false)
                .build();

        sqsClient.enqueue(emailQueue, queueRegion, JSONUtils.wrappedToJsonString(emailPayload));
        sqsClient.enqueue(integrationQueue, queueRegion, JSONUtils.wrappedToJsonString(emailPayloadForUser));
    }

    @Override
    public void sendResponse(MessageResponsePayload payload) {
        var organisationId = payload.getOrganisation();
        var messageId = payload.getMessageId();
        var to = payload.getTo();
        var sender = payload.getSender();
        var body = payload.getBody();
        var organisation = organisationService.getOrganisation(organisationId);
        var from = toWhatsAppAddress(organisation.getWhatsAppAddress());
        var type = payload.getType();
        var authorId = organisation.getBotUser();
        TwilioMessage twilioMessage = null;

        if (type.equals("CONVERSATION_ATTACHMENTS")) {
            try {
                var objectKey = deserializePayload(body, AttachmentMessage.class);
                var bucketName = config.getRentancyDocumentUploads();
                var fileKey = "public/" + objectKey.getFileKey();
                String presignedURL = s3Client.getPresignedUrl(bucketName, fileKey);
                twilioMessage = twilioClient.sendWhatsAppMessage(organisation.getTwilioAccountSID(), organisation.getTwilioAuthToken(), from, to, sender, null, presignedURL);

            } catch (IOException e) {
                throw new IllegalStateException(e.getMessage());
            }
        } else {
            twilioMessage = twilioClient.sendWhatsAppMessage(organisation.getTwilioAccountSID(), organisation.getTwilioAuthToken(), from, to, sender, body, null);
        }
        appSyncServiceProvider.updateMessage(messageId, twilioMessage.getStatus(), twilioMessage.getId());
    }

    private String toWhatsAppAddress(String phone) {
        return String.join(":", "whatsapp", phone);
    }

    @Override
    public void postStatus(Map<String, Object> payload) {
        var messageSid = decodeParameter(payload, "MessageSid");
        var status = decodeParameter(payload, "SmsStatus");
        var message = conversationService.findMessageWithWhatsAppId(messageSid);
        if (status.equals("failed") || status.equals("undelivered")) {
            var messages = conversationService.findMessageWithWhatsAppId(messageSid);
            var conversationId = messages.getConversationId();
            var organisationId = messages.getOrganisationId();
            var organisation = organisationService.getOrganisation(organisationId);
            var authorId = organisation.getBotUser();
            appSyncServiceProvider.createMessage(organisationId, conversationId, authorId, "Message could not be delivered to Whatsapp", "TEXT");
        }
        Optional.ofNullable(message).ifPresent(msg -> {
            appSyncServiceProvider.updateMessage(msg.getId(), status, null);
        });
    }

    private String formatPayload(String name, String from, String body) {
        return String.join("\n", name + " " + from, body);
    }

    private String decodeParameter(Map<String, Object> payload, String paramName) {
        return URLDecoder.decode(String.valueOf(payload.getOrDefault(paramName, "")), StandardCharsets.UTF_8);
    }

    private String[] parseWhatsAppAddress(String address) {
        return address.split(":");
    }

    private String constructKey(String conversationId, String fileName) {
        return String.join("/", "conversations", conversationId, fileName);
    }
}
