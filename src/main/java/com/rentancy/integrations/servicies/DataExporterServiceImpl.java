package com.rentancy.integrations.servicies;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.*;
import com.rentancy.integrations.servicies.persistence.OrganisationService;
import com.rentancy.integrations.servicies.persistence.PropertyService;
import com.rentancy.integrations.servicies.persistence.UserService;
import com.rentancy.integrations.servicies.rent.NextInvoiceDate;
import com.rentancy.integrations.servicies.rent.NextInvoiceDateCalculator;
import com.rentancy.integrations.util.Utils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.WordUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.annotation.Nullable;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.rentancy.integrations.servicies.PortfolioBaseProcessor.DEFAULT_VALUE;
import static com.rentancy.integrations.util.Utils.formatValue;
import static com.rentancy.integrations.util.Utils.getOrDefault;
import static java.time.ZoneOffset.UTC;
import static java.util.stream.Collectors.*;

@RequiredArgsConstructor
public class DataExporterServiceImpl implements DataExporterService {

    private static final Logger log = LogManager.getLogger(DataExporterServiceImpl.class);

    private final Config config;
    private final UserService userService;
    private final PropertyService propertyService;
    private final OrganisationService organisationService;
    private final NextInvoiceDateCalculator nextInvoiceDateCalculator;

    @Override
    public DataExporterResponse exportContacts(DataExporterInput input) {
        var organisationId = input.getOrganisationId();
        var format = input.getFormat();

        var organisation = organisationService.getOrganisation(organisationId);

        var users = userService.findOrganisationUsersWithAddresses(organisationId);

        var headers = List.of(
                "Contact_Ref",
                "Contract_Name",
                "Contact_Type",
                "Primary_Person_Title",
                "Primary_Person_Fname",
                "Primary_Person_Sname",
                "Primary_Person_Email",
                "Primary__Person_Phone1_Type",
                "Primary_Person_Phone1",
                "Primary__Person_Phone2_Type",
                "Primary_Person_Phone2",
                "internal_Notes",
                "Postal_Address_Line1",
                "Postal_Address_Line2",
                "Postal_Address_Line3",
                "Postal_Town",
                "Postal_PostCode",
                "Postal_Country",
                "VAT_Number",
                "Non_Resident_Flag",
                "Bank_Beneficiary",
                "Bank_Name",
                "Bank_Account",
                "Bank_Sort_Code",
                "Bank_IBAN",
                "Bank_SWIFT",
                "Home_Address_Line1",
                "Home_Address_Line2",
                "Home_Address_Line3",
                "Home_Town",
                "Home_PostCode",
                "Home_Country",
                "Tag1",
                "Tag2",
                "Tag3",
                "Tag4",
                "Tag5"
        );
        var fileName = "Contact-Export(" + organisation.getName() + ")." + format.name().toLowerCase();
        return doExport(format, headers, fileName, () -> convertUsersToRows(users));
    }

    @Override
    public DataExporterResponse exportOrganisationProperties(String organisationId) {
        var organisation = organisationService.getOrganisation(organisationId);
        var tenancies = propertyService.findTenancies(organisationId, null, true);
        var headers = List.of(
                "Property Ref",
                "Contract Ref",
                "Property Address 1",
                "Property Address 2",
                "Property Postcode",
                "Primary Landlord Company",
                "Primary Landlord TiTle",
                "Primary Landlord Name",
                "Primary Landlord Surname",
                "Primary Landlord Email",
                "Primary Landlord Telephone",
                "Primary Landlord Address1",
                "Primary Landlord Address 2",
                "Primary Landlord Postcode",
                "Contract Status",
                "Contact Type",
                "Contract Start Date",
                "Contract End Date",
                "Rent Amount",
                "Frequency",
                "Due Day",
                "Auto Invoice On/Off",
                "Email Invoice On/Off",
                "Management Fee",
                "Management Fee %",
                "VAT Type",
                "Deposit Amount",
                "Deposit Scheme",
                "Primary Tenant Email",
                "Primary Tenant Name",
                "Secondary_Tenant_Company_Name",
                "Secondary_Tenant_Company_Title",
                "Secondary_Tenant_Company_Fname",
                "Secondary_Tenant_Company_Sname",
                "Third_Tenant_Company_Name",
                "Third_Tenant_Company_Title",
                "Third_Tenant_Company_Fname",
                "Third_Tenant_Company_Sname"
        );
        var fileName = "Property-Export(" + organisation.getName() + ")." + "xlsx";
        Supplier<List<List<String>>> row = () -> convertOrganisationPropertiesToRow(organisation, tenancies);
        var file = generateExcel(headers, row.get());

        return new DataExporterResponse(file, fileName);
    }

    private List<List<String>> convertTenantLedgerToRows(PropertyLedgersSummary summary) {
        var dateFormatter = new SimpleDateFormat("dd/MM/yyyy");

        var headers = List.of(
                List.of("Tenant Ledger Report"),
                List.of("Property: " + summary.getPropertyAddress()),
                List.of("Dates: " + dateFormatter.format(Date.from(Instant.parse(summary.getStartDate()))) + " to " + dateFormatter.format(Date.from(Instant.parse(summary.getEndDate())))),
                List.of("Date", "Type", "Description", "Property", "Contract", "Due", "Debit", "Credit", "Held", "Invoice ID")
        );

        var tableRows = summary.getItems()
                .stream()
                .map(item -> List.<String>of(
                                getOrDefault(item.getDate(), DEFAULT_VALUE),
                                getOrDefault(item.getLineItemType().name(), DEFAULT_VALUE),
                                getOrDefault(item.getDescription(), DEFAULT_VALUE),
                                getOrDefault(item.getPropertyAddress(), DEFAULT_VALUE),
                                getOrDefault(item.getTenancyReference(), DEFAULT_VALUE),
                                getOrDefault(formatValue(item.getDueAmount()), DEFAULT_VALUE),
                                getOrDefault(formatValue(item.getDebitAmount()), DEFAULT_VALUE),
                                getOrDefault(formatValue(item.getCreditAmount()), DEFAULT_VALUE),
                                getOrDefault(formatValue(item.getHeldAmount()), DEFAULT_VALUE),
                                getOrDefault(item.getXeroNumber(), DEFAULT_VALUE)
                        )
                )
                .collect(Collectors.toUnmodifiableList());

        var table = new ArrayList<List<String>>();
        table.add(List.of("Opening Balance", "", "", "", "", "", "", "", formatValue(summary.getOpeningBalanceAmount())));
        table.addAll(tableRows);
        table.add(List.of("Closing Balance", "", "", "", "", "", "", "", formatValue(summary.getClosingBalanceAmount())));

        var footer = List.<List<String>>of(List.of(), List.of(), List.of(), List.of(), List.of("", "", "", "", "", "", formatValue(summary.getDebitTotalAmount()), formatValue(summary.getCreditTotalAmount()), "", ""));

        return Stream.of(headers, table, footer)
                .flatMap(Collection::stream)
                .collect(Collectors.toUnmodifiableList());
    }


    @Override
    public DataExporterResponse generateTenantLedgerReport(PropertyLedgersSummary summary,
                                                           ExportFormat format) {
        var headers = List.<String>of();
        var fileName = "Tenant Ledgers." + format.name().toLowerCase();

        return doExport(format, headers, fileName, () -> convertTenantLedgerToRows(summary));
    }

    private String getFirstWordLettersUpperCase(@Nullable String str) {
        return Optional.ofNullable(str)
                .map(t -> WordUtils.capitalizeFully(t, new char[]{' '}))
                .orElse(DEFAULT_VALUE);
    }

    private List<List<String>> convertIncomeArrearsRows(IncomeArrearsSummary summary) {
        var dateFormatter = new SimpleDateFormat("dd/MM/yyyy");

        return summary.getItems()
                .stream()
                .map(IncomeArrearsSummary.IncomeArrearsSummaryItem::getArrearsInvoices)
                .flatMap(Collection::stream)
                .map(item -> List.of(
                                getOrDefault(item.getInvoiceReference(), DEFAULT_VALUE),
                                getOrDefault(item.getTenancyReference(), DEFAULT_VALUE),
                                getOrDefault("" + item.getDaysInArrears(), DEFAULT_VALUE),
                                getOrDefault(dateFormatter.format(Date.from(Instant.parse(item.getInvoiceDueDate()))), DEFAULT_VALUE),
                                getOrDefault(formatValue(item.getArrearsAmount()), DEFAULT_VALUE),
                                getOrDefault(formatValue(item.getTotalInvoiceAmount()), DEFAULT_VALUE),
                                getOrDefault(item.getTenantName(), DEFAULT_VALUE),
                                getOrDefault(item.getLandlordName(), DEFAULT_VALUE),
                                getOrDefault(item.getPropertyReference(), DEFAULT_VALUE),
                                getOrDefault(item.getPropertyAddress(), DEFAULT_VALUE),
                                item.isHasGuarantors() ? "Yes" : "No",
                                getOrDefault(item.getInvoiceDescription(), DEFAULT_VALUE),
                                item.isFullyManagedTenancy() ? "Fully Managed" : "Rent Collect",
                                getFirstWordLettersUpperCase(item.getTenancyStatus()),
                                Optional.ofNullable(item.getTenancyType()).map(Enum::name).orElse(DEFAULT_VALUE),
                                Optional.ofNullable(item.getTenancyStartDate()).map(Instant::parse).map(Date::from).map(dateFormatter::format).orElse(DEFAULT_VALUE),
                                Optional.ofNullable(item.getTenancyEndDate()).map(Instant::parse).map(Date::from).map(dateFormatter::format).orElse(DEFAULT_VALUE)
                        )
                ).collect(Collectors.toUnmodifiableList());
    }

    @Override
    public DataExporterResponse generateIncomeArrearsReport(IncomeArrearsSummary summary, ExportFormat format) {
        var headers = List.<String>of("Invoice Reference", "Contract Reference", "Days in Arrears", "Due Date", "Arrears Amount",
                "Invoice Total", "Tenant", "Landlord", "Reference", "Address", "Guarantor", "Invoice Description", "Service", "Contract Status",
                "Contract Type", "Contract Start Date", "Contract End Date");
        var fileName = "Income Arrears." + format.name().toLowerCase();

        return doExport(format, headers, fileName, () -> convertIncomeArrearsRows(summary));
    }

    private List<List<String>> convertMonthlyJournalRows(List<JournalResult> journalResults) {
        return journalResults
                .stream()
                .map(result -> List.of(
                        getOrDefault(result.getPropertyAddress(), DEFAULT_VALUE),
                        getOrDefault(result.getTenancyReference(), DEFAULT_VALUE),
                        getOrDefault(result.getTenantName(), DEFAULT_VALUE),
                        getOrDefault(result.getLandlordName(), DEFAULT_VALUE),
                        getOrDefault(formatValue(result.getJournalAmount()), DEFAULT_VALUE),
                        getOrDefault(formatValue(result.getExpectedJournalAmount()), DEFAULT_VALUE),
                        getOrDefault(result.getBalanceTransferNumber(), DEFAULT_VALUE),
                        getOrDefault(result.getManagementFeeNumber(), DEFAULT_VALUE),
                        getOrDefault(formatValue(result.getArrears()), "No Arrears")
                        )
                )
                .collect(Collectors.toUnmodifiableList());
    }

    @Override
    public DataExporterResponse generateMonthlyJournalReport(List<JournalResult> journalResults, ExportFormat exportFormat) {
        var headers = List.<String>of("Property", "Contract Ref", "Tenant", "Landlord", "Amount Journaled", "Expected Journal Amount", "Balance Transfer Bill", "Management Fee Bill", "New Arrears");

        return doExport(exportFormat, headers, "Monthly Journal", () -> convertMonthlyJournalRows(journalResults));
    }

    private List<String> generateEmptyRow(int numberOfEmptyCells) {
        return IntStream.range(0, numberOfEmptyCells)
                .mapToObj(i -> DEFAULT_VALUE)
                .collect(Collectors.toUnmodifiableList());
    }

    @SafeVarargs
    private List<String> concatenateLists(List<String>... lists) {
        return Arrays.stream(lists).flatMap(Collection::stream).collect(Collectors.toUnmodifiableList());
    }

    private List<List<String>> generateJournalForecastReportRows(JournalForecastReport forecastReport) {
        var monthColumnDescriptions = forecastReport.getItems().stream()
                .map(JournalForecastReport.JournalForecastReportTenancyItem::getPeriodMonths)
                .findFirst()
                .stream()
                .flatMap(Collection::stream)
                .map(month -> List.of(DEFAULT_VALUE, month.getMonthName(), DEFAULT_VALUE))
                .flatMap(Collection::stream)
                .collect(Collectors.toUnmodifiableList());
        var monthsRow = Stream.of(generateEmptyRow(9), monthColumnDescriptions).flatMap(Collection::stream).collect(Collectors.toUnmodifiableList());
        var periodHeaders = forecastReport.getItems().stream()
                .map(JournalForecastReport.JournalForecastReportTenancyItem::getPeriodMonths)
                .findFirst()
                .stream()
                .flatMap(Collection::stream)
                .flatMap(month -> Stream.of("Collected", "Rent Per Month", "Forecast"));

        var headers = Stream.concat(
                Stream.of("Landlord", "Property Ref", "Contract Ref", "Tenant", "Rent Amount", "Status", "Period", "Contract Start", "Contract End", "Amount Held"),
                periodHeaders
        ).collect(Collectors.toUnmodifiableList());

        var data = forecastReport.getItems()
                .stream()
                .map(item -> {
                    var reportColumns = Stream.of(
                            getOrDefault(item.getLandlordName(), DEFAULT_VALUE),
                            getOrDefault(item.getPropertyReference(), DEFAULT_VALUE),
                            getOrDefault(item.getTenancyReference(), DEFAULT_VALUE),
                            getOrDefault(item.getTenantName(), DEFAULT_VALUE),
                            getOrDefault(formatValue(item.getMonthlyRentAmount()), DEFAULT_VALUE),
                            getOrDefault(item.getTenancyStatus(), DEFAULT_VALUE),
                            getOrDefault(item.getTenancyPeriod(), DEFAULT_VALUE),
                            getOrDefault(item.getTenancyStartDate(), DEFAULT_VALUE),
                            getOrDefault(item.getTenancyEndDate(), DEFAULT_VALUE),
                            getOrDefault(formatValue(item.getTenantBalanceAmountHeld()), DEFAULT_VALUE)
                    );
                    var monthColumns = item.getPeriodMonths().stream()
                            .flatMap(month -> Stream.of(getOrDefault(formatValue(month.getCollected()), DEFAULT_VALUE),
                                    getOrDefault(formatValue(month.getMonthlyRent()), DEFAULT_VALUE),
                                    getOrDefault(formatValue(month.getForecast()), DEFAULT_VALUE)));

                    return Stream.concat(reportColumns, monthColumns).collect(Collectors.toUnmodifiableList());
                })
                .collect(Collectors.toUnmodifiableList());

        return Stream.of(List.of(List.<String>of("Rent Payment Tracker Report")),
                        List.of(List.<String>of("Date Generated", DEFAULT_VALUE, forecastReport.getDate())),
                        List.of(List.<String>of("Period Selected", DEFAULT_VALUE, forecastReport.getStartDate(), String.valueOf(forecastReport.getPeriodLengthInMonths()))),
                        List.of(monthsRow),
                        List.of(headers),
                        data)
                .flatMap(Collection::stream)
                .collect(Collectors.toUnmodifiableList());
    }

    @Override
    public DataExporterResponse generateJournalForecastReport(Organisation organisation, JournalForecastReport forecastReport, ExportFormat exportFormat) {
        var fileName = "Forecast Report:" + organisation.getName() + " " + forecastReport.getDate();

        return doExport(exportFormat, null, fileName, () -> generateJournalForecastReportRows(forecastReport));
    }

    @Override
    public DataExporterResponse generateOrganisationReportAsExcel() {
        var headers = List.of("Name", "Type", "Country", "Properties", "Contracts", "Created", "Admin Name", "Admin Email", "Phone Number");
        var excludeTenancyStatuses = Set.of("ARCHIVED", "VACATED", "VACATING", "NOTICE_GIVEN", "CANCELLED", "DRAFT");
        var dateFormatter = new SimpleDateFormat("dd/MM/yyyy");
        //organisationService
        var allUnexpiredOrganisations = organisationService.findAllUnexpiredOrganisations();
        var allProperties = propertyService.findAllProperties();
        var allTenancies = propertyService.findAllTenancies()
                .stream()
                .filter(tenancy -> !excludeTenancyStatuses.contains(tenancy.getStatus()))
                .filter(tenancy -> Objects.isNull(tenancy.getArchived()) || tenancy.getArchived().equals(false))
                .collect(toUnmodifiableList());

        var allAdminUserIds = allUnexpiredOrganisations.stream().map(Organisation::getAdminUser).filter(Objects::nonNull).collect(toSet());

        var adminUsers = userService.findUsers(allAdminUserIds).stream().collect(groupingBy(User::getId));

        var groupedProperties = allProperties.stream()
                .filter(property -> Objects.nonNull(property.getOrganisation()))
                .filter(property -> !property.isArchived())
                .collect(groupingBy((Property::getOrganisation)));
        var groupedTenancies = allTenancies.stream()
                .filter(tenancy -> Objects.nonNull(tenancy.getOrganisation()))
                .collect(groupingBy((Tenancy::getOrganisation)));

        var res = allUnexpiredOrganisations.stream()
                .sorted((o1, o2) -> {
                    var date1 = Optional.ofNullable(o1.getCreatedAt()).orElse(DEFAULT_VALUE);
                    var date2 = Optional.ofNullable(o2.getCreatedAt()).orElse(DEFAULT_VALUE);

                    return -date1.compareTo(date2);
                })
                .map(organisation -> {
                    List<String> organisationList = new ArrayList<>();

                    var adminUser = Optional.ofNullable(organisation.getAdminUser()).map(adminUsers::get).map(users -> users.get(0));
                    var organisationCreationDate = Optional.ofNullable(organisation.getCreatedAt())
                            .map(Instant::parse)
                            .map(Date::from).map(dateFormatter::format).orElse(DEFAULT_VALUE);
                    var organisationType = Optional.ofNullable(organisation.getType())
                            .map(Enum::name)
                            .map(name -> "LANDLORD".equals(name) ? "Landlord" : "Agent")
                            .orElse("Agent");

                    organisationList.add(organisation.getName());
                    organisationList.add(organisationType);
                    organisationList.add(Optional.ofNullable(organisation.getCountry()).orElse(DEFAULT_VALUE));
                    organisationList.add("" + groupedProperties.getOrDefault(organisation.getId(), List.of()).size());
                    organisationList.add("" + groupedTenancies.getOrDefault(organisation.getId(), List.of()).size());
                    organisationList.add(organisationCreationDate);
                    organisationList.add(adminUser.map(Utils::formatUser).orElse(DEFAULT_VALUE));
                    organisationList.add(adminUser.flatMap(user -> user.getEmails().stream().map(User.Email::getEmail).findFirst()).orElse(DEFAULT_VALUE));
                    organisationList.add(adminUser.flatMap(user -> user.getPhones().stream().map(User.Phone::getPhone).findFirst()).orElse(DEFAULT_VALUE));
                    return organisationList;
                })
                .collect(Collectors.toList());
        return doExport(ExportFormat.XLSX, headers, "organisationReport", () -> res);
    }

    @Override
    public DataExporterResponse generateOrganisationChargesReport(List<OrganisationStripeCharge> organisationStripeCharges) {
        var headers = List.of("ContactName", "EmailAddress", "POAddressLine1", "POAddressLine2", "POAddressLine3", "POAddressLine4", "POCity", "PORegion", "POPostalCode", "POCountry", "InvoiceNumber", "Reference", "InvoiceDate", "DueDate",
                "Total", "InventoryItemCode", "Description", "Quantity", "UnitAmount", "Discount", "AccountCode", "TaxType", "TaxAmount", "TrackingName1", "TrackingOption1", "TrackingName2", "TrackingOption2", "Currency", "BrandingTheme");

        var dateFormatter = new SimpleDateFormat("yyyy-MM-dd");
        var res = organisationStripeCharges
                .stream()
                .filter(organisationStripeCharge -> !"CARD_TEST_CHARGE".equals(organisationStripeCharge.getChargeType()))
                .filter(organisationStripeCharge -> "payment_intent.succeeded".equals(organisationStripeCharge.getEventType()))
                .map(organisationStripeCharge -> {
                    var organisation = organisationService.getOrganisation(organisationStripeCharge.getOrganisationId());
                    var total = BigDecimal.valueOf(organisationStripeCharge.getAmount());
                    var unitAmount = BigDecimal.valueOf(150);
                    var quantity = BigDecimal.valueOf(organisationStripeCharge.getPropertyCount());
                    var tax = total.subtract(unitAmount.multiply(quantity));

                    if (organisationStripeCharge.getPropertyCount() < 17) {
                        quantity = BigDecimal.ONE;
                        unitAmount = BigDecimal.valueOf(2500);
                        total = BigDecimal.valueOf(3000);
                        tax = BigDecimal.valueOf(500);
                    }

                    List<String> row = new ArrayList<>();
                    row.add(organisation.getName());
                    row.add(DEFAULT_VALUE);
                    row.add(getOrDefault(organisation.getAddressLine1(), DEFAULT_VALUE));
                    row.add(getOrDefault(organisation.getAddressLine2(), DEFAULT_VALUE));
                    row.add(getOrDefault(organisation.getAddressLine3(), DEFAULT_VALUE));
                    row.add(DEFAULT_VALUE);
                    row.add(getOrDefault(organisation.getCity(), DEFAULT_VALUE));
                    row.add(getOrDefault(organisation.getState(), DEFAULT_VALUE));
                    row.add(getOrDefault(organisation.getPostcode(), DEFAULT_VALUE));
                    row.add(getOrDefault(organisation.getCountry(), DEFAULT_VALUE));
                    row.add(getOrDefault(organisationStripeCharge.getNumber(), DEFAULT_VALUE));
                    row.add(DEFAULT_VALUE);
                    row.add(Optional.ofNullable(organisationStripeCharge.getOccuredAt()).map(Instant::parse).map(Date::from).map(dateFormatter::format).orElse(DEFAULT_VALUE));
                    row.add(Optional.ofNullable(organisationStripeCharge.getOccuredAt()).map(Instant::parse).map(Date::from).map(dateFormatter::format).orElse(DEFAULT_VALUE));
                    row.add(total.movePointLeft(2).toPlainString());
                    row.add(DEFAULT_VALUE);
                    row.add("Monthly Subscription");
                    row.add(quantity.toString());
                    row.add(unitAmount.movePointLeft(2).toString());
                    row.add(DEFAULT_VALUE);
                    row.add("200");
                    row.add("20% (VAT on Income)");
                    row.add(tax.movePointLeft(2).toString());
                    row.add(DEFAULT_VALUE);
                    row.add(DEFAULT_VALUE);
                    row.add(DEFAULT_VALUE);
                    row.add(DEFAULT_VALUE);
                    row.add(DEFAULT_VALUE);
                    row.add(DEFAULT_VALUE);

                    return row;
                }).collect(toUnmodifiableList());

        return doExport(ExportFormat.XLSX, headers, "organisationChargesReport", () -> res);
    }

    @Override
    public DataExporterResponse generateOrganisationSubscriptionReport(List<OrganisationStripeCharge> organisationStripeCharges) {
        var headers = List.of("Date", "Workspace Name", "Properties", "Price (exc VAT)", "VAT", "Total", "Success/Fail");
        var dateFormatter = new SimpleDateFormat("yyyy-MM-dd");

        var res = organisationStripeCharges
                .stream()
                .filter(organisationStripeCharge -> !"CARD_TEST_CHARGE".equals(organisationStripeCharge.getChargeType()))
                .map(organisationStripeCharge -> {
                    var organisation = organisationService.getOrganisation(organisationStripeCharge.getOrganisationId());
                    List<String> row = new ArrayList<>();
                    var propertyRate = BigDecimal.valueOf(150);
                    var vatRate = BigDecimal.ONE.add(BigDecimal.valueOf(0.2));
                    var minimumCharge = BigDecimal.valueOf(2500);
                    var propertyCharge = propertyRate.multiply(BigDecimal.valueOf(organisationStripeCharge.getPropertyCount()));
                    var netAmount = propertyCharge.compareTo(minimumCharge) < 0 ? minimumCharge : propertyCharge;
                    row.add(Optional.ofNullable(organisationStripeCharge.getOccuredAt()).map(Instant::parse).map(Date::from).map(dateFormatter::format).orElse(DEFAULT_VALUE));
                    row.add(organisation.getName());
                    row.add(String.valueOf(organisationStripeCharge.getPropertyCount()));
                    row.add(netAmount.movePointLeft(2).toString());
                    row.add(vatRate.toString());
                    row.add(netAmount.multiply(vatRate).movePointLeft(2).setScale(2, RoundingMode.HALF_UP).toString());
                    row.add("payment_intent.succeeded".equals(organisationStripeCharge.getEventType()) ? "Success" : "Fail");

                    return row;
                }).collect(toUnmodifiableList());

        return doExport(ExportFormat.XLSX, headers, "organisationSubscriptionReport", () -> res);
    }

    @Override
    public DataExporterResponse generateOrganisationSignupReport(List<Organisation> organisations) {
        var headers = List.of("Workspace Name", "Primary Email Address", "First Name", "Last Name", "Phone Number", "Workspace Registration Date", "Credit Card Details Input (Y/N)", "Properties", "Contracts", "Attribution Source");
        var dateFormatter = new SimpleDateFormat("yyyy-MM-dd");

        var res = organisations
                .stream()
                .map(organisation -> {
                    var adminUser = userService.findUser(organisation.getAdminUser(), false);
                    var properties = propertyService.findPropertiesWithOrganisation(organisation.getId());
                    var tenancies = propertyService.findTenancies(organisation.getId(), null, false);
                    List<String> row = new ArrayList<String>();
                    row.add(organisation.getName());
                    row.add(adminUser.getCognitoEmail());
                    row.add(adminUser.getFname());
                    row.add(adminUser.getSname());
                    row.add(adminUser.getPhone());
                    row.add(dateFormatter.format(Date.from(Instant.parse(organisation.getCreatedAt()))));
                    row.add(organisation.isPayer() ? "Y" : "N");
                    row.add(String.valueOf(properties.size()));
                    row.add(String.valueOf(tenancies.size()));
                    row.add(organisation.getUtmCustomerAttributionSource());

                    return row;
                }).collect(toUnmodifiableList());

        return doExport(ExportFormat.XLSX, headers, "organisationDailySignupReport", () -> res);
    }

    private List<List<String>> convertOrganisationPropertiesToRow(Organisation organisation, List<Tenancy> tenancy) {
        var propertys = propertyService.findPropertiesWithOrganisation(organisation.getId());
        var allUsedUsersIds = new HashSet<String>();
        for (var eachTenanty : tenancy) {
            if (Objects.nonNull(eachTenanty.getLandlords()) && eachTenanty.getLandlords().size() > 0) {
                for (var eachLandlord : eachTenanty.getLandlords()) {
                    allUsedUsersIds.add(eachLandlord);
                }
            }
            if (Objects.nonNull(eachTenanty.getTenants()) && eachTenanty.getTenants().size() > 0) {
                for (var eachTenant : eachTenanty.getTenants()) {
                    allUsedUsersIds.add(eachTenant);
                }
            }
            if (Objects.nonNull(eachTenanty.getPrimaryTenant())) {
                allUsedUsersIds.add(eachTenanty.getPrimaryTenant());
            }
        }
        var usedUsers = userService.findUsers(allUsedUsersIds);
        return tenancy.stream().map(tenancy1 -> {
            var row = new ArrayList<String>();
            var property = propertys.stream().filter(property1 -> Objects.nonNull(tenancy1.getProperty()) && property1.getId().equals(tenancy1.getProperty())).findFirst().orElse(null);
            var primaryLandlordTitle = "";
            var primaryLandlordCompanyName = "";
            var primaryLandlordFname = "";
            var primaryLandlordSname = "";
            var primaryLandlordPhone = "";
            var primaryLandlordEmail = "";
            var primaryLandlordAddresLine1 = "";
            var primaryLandlordAddresLine2 = "";
            var primaryLandlordPostCode = "";
            var propertyReference = "";
            var propertyAddressLine1 = "";
            var propertyAddressLine2 = "";
            var propertyPostCode = "";
            var tenancyStatus = tenancy1.getStatus();
            var tenancyType = Objects.nonNull(tenancy1.getType()) && Objects.nonNull(tenancy1.getType().name()) ? tenancy1.getType().name() : null;
            var tenancyStartDate = tenancy1.getStartDate();
            var tenancyEndDate = tenancy1.getEndDate();
            var tenancyRent = Objects.nonNull(tenancy1.getRent()) ? String.valueOf(tenancy1.getRent()) : null;
            var tenancyperiodName = Objects.nonNull(tenancy1.getPeriod()) && Objects.nonNull(tenancy1.getPeriod().name()) ? tenancy1.getPeriod().name() : null;
            var tenancyDueDate = "";
            var tenancyAutoRent = tenancy1.getSettings().isAutoInvoiceRent() ? "on" : "off";
            var TenancyManagmentFee = "";
            var tenancyVATType = "";
            var tenanctDeposit = Objects.nonNull(tenancy1.getDeposit()) ? String.valueOf(tenancy1.getDeposit()) : null;
            var primarytenantEmail = "";
            var primarytenantName = "";
            var secondaryTenantCompanyName = "";
            var secondaryTenantTitle = "";
            var secondaryTenantFname = "";
            var secondaryTenantSname = "";
            var thirdTenantCompanyName = "";
            var thirdTenantCompanyTitle = "";
            var thirdTenantCompanyFname = "";
            var thirdTenantCompanySname = "";
            var sendInvoiceToTenant = "";
            var feeType = "";
            var depositScheme = Objects.nonNull(tenancy1.getDepositProtectionScheme()) ? tenancy1.getDepositProtectionScheme() : null;
            if (Objects.nonNull(tenancy1.getLandlords()) && tenancy1.getLandlords().size() != 0 && Objects.nonNull(property)) {
                var primaryLandlord = usedUsers.stream().filter(user1 -> Objects.nonNull(property.getPrimaryLandlordId()) && property.getPrimaryLandlordId().equals(user1.getId())).map(user1 -> user1).collect(Collectors.toList());
                var primaryLandlordObject = primaryLandlord.size() > 0 ? primaryLandlord.get(0) : null;
                primaryLandlordTitle = Objects.nonNull(primaryLandlordObject) ? primaryLandlordObject.getTitle() : null;
                primaryLandlordCompanyName = Optional.ofNullable(primaryLandlordObject).map(User::getCompanyName).orElse(null);
                primaryLandlordFname = Optional.ofNullable(primaryLandlordObject).map(User::getFname).orElse(null);
                primaryLandlordSname = Optional.ofNullable(primaryLandlordObject).map(User::getSname).orElse(null);
                primaryLandlordPhone = Optional.ofNullable(primaryLandlordObject).map(User::getPhone).orElse(null);
                primaryLandlordEmail = Objects.nonNull(primaryLandlordObject) ? (Objects.nonNull(primaryLandlordObject.getEmails()) && primaryLandlordObject.getEmails().size() > 0 ? primaryLandlordObject.getEmails().get(0).getEmail() : null) : null;
                primaryLandlordAddresLine1 = Objects.nonNull(primaryLandlordObject) ? (Objects.nonNull(primaryLandlordObject.getHomeAddress()) && Objects.nonNull(primaryLandlordObject.getHomeAddress().getAddressLine1()) ? primaryLandlordObject.getHomeAddress().getAddressLine1() : null) : null;
                primaryLandlordAddresLine2 = Objects.nonNull(primaryLandlordObject) ? (Objects.nonNull(primaryLandlordObject.getHomeAddress()) && Objects.nonNull(primaryLandlordObject.getHomeAddress().getAddressLine2()) ? primaryLandlordObject.getHomeAddress().getAddressLine2() : null) : null;
                primaryLandlordPostCode = Objects.nonNull(primaryLandlordObject) ? (Objects.nonNull(primaryLandlordObject.getPostalAddress()) && Objects.nonNull(primaryLandlordObject.getPostalAddress().getPostcode()) ? primaryLandlordObject.getPostalAddress().getPostcode() : null) : null;
            }
            if (Objects.nonNull(tenancy1.getProperty()) && Objects.nonNull(property)) {
                var property1 = property.getId().equals(tenancy1.getProperty()) ? property : null;
                propertyReference = Optional.ofNullable(property1).map(Property::getReference).orElse(null);
                propertyAddressLine1 = Optional.ofNullable(property1).map(Property::getAddressLine1).orElse(null);
                propertyAddressLine2 = Optional.ofNullable(property1).map(Property::getAddressLine2).orElse(null);
                propertyPostCode = Optional.ofNullable(property1).map(Property::getPostcode).orElse(null);
            }
            if (Objects.nonNull(tenancy1.getSettings())) {
                var dateFormatter = new SimpleDateFormat("dd MMM yyyy");
                tenancyDueDate = Optional.ofNullable(nextInvoiceDateCalculator.calculate(ZonedDateTime.now(UTC), tenancy1))
                        .map(NextInvoiceDate::getDue)
                        .map(dateFormatter::format)
                        .orElse(StringUtils.EMPTY);
                TenancyManagmentFee = String.valueOf(tenancy1.getSettings().getPercentageFee());
                sendInvoiceToTenant = Objects.nonNull(tenancy1.getSettings().isSendInvoiceToTenant()) ? "on" : "off";
                feeType = Objects.nonNull(tenancy1.getSettings().getFeeType()) ? tenancy1.getSettings().getFeeType() : null;
            }
            if (Objects.nonNull(tenancy1.getPrimaryTenant())) {
                var tenant = usedUsers.stream().filter(user1 -> user1.equals(tenancy1.getPrimaryTenant())).map(user1 -> user1).collect(Collectors.toList());
                var tenantObject = tenant.size() > 0 ? tenant.get(0) : null;
                primarytenantEmail = Optional.ofNullable(tenantObject).map(User::getCognitoEmail).orElse(null);
                primarytenantName = Optional.ofNullable(tenantObject).map(User::getFname).orElse(null);
            }
            if (Objects.nonNull(tenancy1.getTenants()) && tenancy1.getTenants().size() > 1 && Objects.nonNull(property)) {
                var secontdaryTenant = usedUsers.stream().filter(user1 -> Objects.nonNull(property.getLandlords()) && property.getLandlords().size() > 1 && property.getLandlords().get(1).equals(user1.getId())).map(user1 -> user1).collect(Collectors.toList());
                var secontdaryTenantObject = secontdaryTenant.size() > 0 ? secontdaryTenant.get(0) : null;
                secondaryTenantCompanyName = Optional.ofNullable(secontdaryTenantObject).map(User::getCompanyName).orElse(null);
                secondaryTenantTitle = Optional.ofNullable(secontdaryTenantObject).map(User::getTitle).orElse(null);
                secondaryTenantFname = Optional.ofNullable(secontdaryTenantObject).map(User::getFname).orElse(null);
                secondaryTenantSname = Optional.ofNullable(secontdaryTenantObject).map(User::getSname).orElse(null);
            }
            if (Objects.nonNull(tenancy1.getTenants()) && tenancy1.getTenants().size() > 2 && Objects.nonNull(property)) {
                var thirdTenant = usedUsers.stream().filter(user1 -> Objects.nonNull(property.getLandlords()) && property.getLandlords().size() > 2 && property.getLandlords().get(2).equals(user1.getId())).map(user1 -> user1).collect(Collectors.toList());
                var thirdTenantObject = thirdTenant.size() > 0 ? thirdTenant.get(0) : null;
                thirdTenantCompanyName = Optional.ofNullable(thirdTenantObject).map(User::getCompanyName).orElse(null);
                thirdTenantCompanyTitle = Optional.ofNullable(thirdTenantObject).map(User::getTitle).orElse(null);
                thirdTenantCompanyFname = Optional.ofNullable(thirdTenantObject).map(User::getFname).orElse(null);
                thirdTenantCompanySname = Optional.ofNullable(thirdTenantObject).map(User::getSname).orElse(null);
            }
            row.add(propertyReference);
            row.add(Optional.ofNullable(tenancy1.getReference()).orElse(null));
            row.add(propertyAddressLine1);
            row.add(propertyAddressLine2);
            row.add(propertyPostCode);
            row.add(primaryLandlordCompanyName);
            row.add(primaryLandlordTitle);
            row.add(primaryLandlordFname);
            row.add(primaryLandlordSname);
            row.add(primaryLandlordEmail);
            row.add(primaryLandlordPhone);
            row.add(primaryLandlordAddresLine1);
            row.add(primaryLandlordAddresLine2);
            row.add(primaryLandlordPostCode);
            row.add(tenancyStatus);
            row.add(tenancyType);
            row.add(tenancyStartDate);
            row.add(tenancyEndDate);
            row.add(tenancyRent);
            row.add(tenancyperiodName);
            row.add(tenancyDueDate);
            row.add(tenancyAutoRent);
            row.add(sendInvoiceToTenant);
            row.add(feeType);
            row.add(TenancyManagmentFee);
            row.add(tenancyVATType);
            row.add(tenanctDeposit);
            row.add(depositScheme);
            row.add(primarytenantEmail);
            row.add(primarytenantName);
            row.add(secondaryTenantCompanyName);
            row.add(secondaryTenantTitle);
            row.add(secondaryTenantFname);
            row.add(secondaryTenantSname);
            row.add(thirdTenantCompanyName);
            row.add(thirdTenantCompanyTitle);
            row.add(thirdTenantCompanyFname);
            row.add(thirdTenantCompanySname);
            return row;
        }).collect(Collectors.toList());

    }

    @Override
    public DataExporterResponse exportProperties(DataExporterInput input) {
        var organisationId = input.getOrganisationId();
        var format = input.getFormat();

        var organisation = organisationService.getOrganisation(organisationId);
        var properties = propertyService.findPropertiesWithOrganisation(organisationId);

        var headers = List.of(
                "Property_RefID",
                "Property_Address_Line1",
                "Property_Address_Line2",
                "Property_Address_Line3",
                "Property_Address_Town",
                "Property_Postcode",
                "Property_Country",
                "Property_Status",
                "Property_Type",
                "Property_Service",
                "Landlord_Count",
                "Primary_Landlord_Contact_Email",
                "Primary_Landlord_Contact_Phone",
                "Primary_Landlord_Company_Name",
                "Primary_Landlord_Title",
                "Primary_Landlord_Fname",
                "Primary_Landlord_Sname",
                "Primary_Landlord_Address_Line1",
                "Primary_Landlord_Address_Line2",
                "Primary_Landlord_Address_Line3",
                "Primary_Landlord_Town",
                "Primary_Landlord_Postcode",
                "Secondary_Landlord_Company_Name",
                "Secondary_Landlord_Title",
                "Secondary_Landlord_Fname",
                "Secondary_Landlord_Sname",
                "Third_Landlord_Company_Name",
                "Third_Landlord_Contact_Title",
                "Third_Landlord_Contact_Fname",
                "Third_Landlord_Contact_Sname",
                "Manager_Count",
                "Primary_Manager_Contact_Email",
                "Primary_Manager_Company_Name",
                "Primary_Manager_Title",
                "Primary_Manager_Fname",
                "Primary_Manager_Sname",
                "Secondary_Manager_Company_Name",
                "Secondary_Manager_Contact_Title",
                "Secondary_Manager_Contact_Fname",
                "Secondary_Manager_Contact_Sname",
                "Third_Manager_Company_Name",
                "Third_Manager_Contact_Title",
                "Third_Manager_Contact_Fname",
                "Third_Manager_Contact_Sname"
        );

        var fileName = "Property-Export(" + organisation.getName() + ")." + format.name().toLowerCase();

        return doExport(format, headers, fileName, () -> convertPropertiesToRows(properties, organisationId));
    }

    @Override
    public DataExporterResponse exportContracts(DataExporterInput input) {
        var organisationId = input.getOrganisationId();
        var format = input.getFormat();

        var organisation = organisationService.getOrganisation(organisationId);

        log.info("Fetching data required to export contracts...");
        var tenanciesFuture = CompletableFuture.supplyAsync(() -> propertyService.findTenancies(organisationId, null, true));
        var propertiesFuture = CompletableFuture.supplyAsync(() -> propertyService.findPropertiesWithOrganisation(organisationId));
        var usersFuture = CompletableFuture.supplyAsync(() -> userService.findUsersWithAddressesByOrganisation(organisationId));
        CompletableFuture.allOf(tenanciesFuture, propertiesFuture).join();
        var tenancies = tenanciesFuture.join();
        var properties = propertiesFuture.join();
        var users = usersFuture.join();
        log.info("Fetching data required to export contracts... Done");

        var headers = List.of(
                "Contract_RefID",
                "Contract_Title",
                "Contract_Status",
                "Contract_Type",
                "Contract_Start_Date",
                "Contract_End_Date",
                "Rent_Amount",
                "Frequency",
                "Due_Day",
                "Deposit_Amount",
                "Reservation_Amount",
                "Management_Fee_Type",
                "With_VAT",
                "Float_Amount",
                "Auto_Invoice_Status",
                "Email_Invoice",
                "Add_VAT",
                "Deposits_Scheme",
                "Deposits_Reference",
                "Deposits_Reference_Date",
                "Tenant_Count",
                "Primary_Tenant_Company_Email",
                "Primary_Tenant_Company_Name",
                "Primary_Tenant_Company_Title",
                "Primary_Tenant_Company_Fname",
                "Primary_Tenant_Company_Sname",
                "Secondary_Tenant_Company_Name",
                "Secondary_Tenant_Company_Title",
                "Secondary_Tenant_Company_Fname",
                "Secondary_Tenant_Company_Sname",
                "Third_Tenant_Company_Name",
                "Third_Tenant_Company_Title",
                "Third_Tenant_Company_Fname",
                "Third_Tenant_Company_Sname",
                "Property_Reference"
        );

        var fileName = "Contract-Export(" + organisation.getName() + ")." + format.name().toLowerCase();

        return doExport(format, headers, fileName, () -> convertTenanciesToRows(tenancies, properties, users));
    }

    public DataExporterResponse doExport(ExportFormat format,
                                         @Nullable List<String> headers,
                                         String fileName,
                                         List<List<String>> rows) {
        return doExport(format, headers, fileName, () -> rows);
    }

    private DataExporterResponse doExport(ExportFormat format,
                                          @Nullable List<String> headers,
                                          String fileName,
                                          Supplier<List<List<String>>> rows) {
        switch (format) {
            case XLSX:
                return new DataExporterResponse(generateExcel(headers, rows.get()), fileName);
            case CSV:
                return new DataExporterResponse(generateCSV(headers, rows.get()), fileName);
            default:
                throw new IllegalArgumentException("Invalid export type - " + format);
        }
    }

    private List<List<String>> convertUsersToRows(List<User> users) {
        return users
                .stream()
                .filter(user -> user.getRoles().stream().noneMatch(role -> role.equals("ADMIN_AGENT") || role.equals("AGENT")))
                .map(user -> {
                    var row = new ArrayList<String>();
                    row.add(user.getReference());
                    row.add(user.getCompanyName());
                    row.add(user.getType());
                    row.add(user.getTitle());
                    row.add(user.getFname());
                    row.add(user.getSname());
                    row.add(getContactEmail(user));

                    Optional.ofNullable(user.getPhones()).ifPresentOrElse(phones -> {
                        phones.stream().filter(Objects::nonNull).forEach(phone -> {
                            row.add(phone.getType());
                            row.add(phone.getPhone());
                        });

                        if (phones.size() == 0) {
                            addValues(row, 4, null);
                        } else if (phones.size() == 1) {
                            addValues(row, 2, null);
                        }
                    }, () -> {
                        addValues(row, 4, null);
                    });

                    row.add(user.getInternalNotes());

                    Optional.ofNullable(user.getPostalAddress()).ifPresentOrElse(userAddress -> {
                        row.add(userAddress.getAddressLine1());
                        row.add(userAddress.getAddressLine2());
                        row.add(userAddress.getAddressLine3());
                        row.add(userAddress.getCity());
                        row.add(userAddress.getPostcode());
                        row.add(userAddress.getCountry());
                    }, () -> {
                        addValues(row, 6, null);
                    });

                    row.add(user.getVat());

                    // null for Non_Resident_Flag we don't have such field
                    row.add(null);
                    row.add(user.getBeneficiaryName());
                    row.add(user.getBankName());
                    row.add(user.getBankAccountNumber());
                    row.add(user.getBankAccountSortCode());
                    row.add(user.getBankIBAN());
                    row.add(user.getBankSWIFT());

                    Optional.ofNullable(user.getHomeAddress()).ifPresentOrElse(userAddress -> {
                        row.add(userAddress.getAddressLine1());
                        row.add(userAddress.getAddressLine2());
                        row.add(userAddress.getAddressLine3());
                        row.add(userAddress.getCity());
                        row.add(userAddress.getPostcode());
                        row.add(userAddress.getCountry());
                    }, () -> {
                        addValues(row, 6, null);
                    });

                    Optional.ofNullable(user.getTags()).ifPresent(tags -> tags.stream().limit(5).forEach(row::add));

                    return row;
                }).collect(Collectors.toList());
    }

    private String getContactEmail(User user) {
        return Optional.ofNullable(user.getCognitoEmail())
                .orElse(Optional.ofNullable(user.getEmails())
                        .orElse(List.of())
                        .stream()
                        .findAny()
                        .map(User.Email::getEmail)
                        .orElse(null));
    }

    private List<List<String>> convertPropertiesToRows(List<Property> properties, String organisationId) {
        var users = userService.findUsersWithAddressesByOrganisation(organisationId).stream()
                .collect(Collectors.toMap(User::getId, Function.identity()));

        return properties
                .stream()
                .map(property -> {
                    var row = new ArrayList<String>();

                    var landlords = property.getLandlords();
                    var managers = property.getManagers();

                    row.add(property.getReference());
                    row.add(property.getAddressLine1());
                    row.add(property.getAddressLine2());
                    row.add(property.getAddressLine3());
                    row.add(property.getCity());
                    row.add(property.getPostcode());
                    row.add(property.getCountry());
                    row.add(property.getStatus());
                    row.add(property.getType());
                    row.add(property.getListingType());

                    Optional.ofNullable(landlords).ifPresentOrElse(lands -> {
                        User primary = !lands.isEmpty() ? users.get(lands.get(0)) : null;
                        User second = lands.size() > 1 ? users.get(lands.get(1)) : null;
                        User third = lands.size() > 2 ? users.get(lands.get(2)) : null;

                        row.add(String.valueOf(Stream.of(primary, second, third).filter(Objects::nonNull).count()));

                        if (primary != null) {
                            row.add(getContactEmail(primary));
                            row.add(Optional.ofNullable(primary.getPhones())
                                    .flatMap(phones -> phones.stream().findAny().map(User.Phone::getPhone))
                                    .orElse(null));
                            row.add(primary.getCompanyName());
                            row.add(primary.getTitle());
                            row.add(primary.getFname());
                            row.add(primary.getSname());

                            if (primary.getPostalAddress() != null) {
                                var userAddress = primary.getPostalAddress();
                                row.add(userAddress.getAddressLine1());
                                row.add(userAddress.getAddressLine2());
                                row.add(userAddress.getAddressLine3());
                                row.add(userAddress.getCity());
                                row.add(userAddress.getPostcode());
                            } else if (primary.getHomeAddress() != null) {
                                var userAddress = primary.getHomeAddress();
                                row.add(userAddress.getAddressLine1());
                                row.add(userAddress.getAddressLine2());
                                row.add(userAddress.getAddressLine3());
                                row.add(userAddress.getCity());
                                row.add(userAddress.getPostcode());
                            } else {
                                addValues(row, 5, null);
                            }
                        } else {
                            addValues(row, 11, null);
                        }

                        if (second != null) {
                            row.add(second.getCompanyName());
                            row.add(second.getTitle());
                            row.add(second.getFname());
                            row.add(second.getSname());
                        } else {
                            addValues(row, 4, null);
                        }

                        if (third != null) {
                            row.add(third.getCompanyName());
                            row.add(third.getTitle());
                            row.add(third.getFname());
                            row.add(third.getSname());
                        } else {
                            addValues(row, 4, null);
                        }
                    }, () -> {
                        row.add(String.valueOf(0));

                        addValues(row, 19, null);
                    });

                    row.addAll(createUsersRowBasedOnRank(managers, users));

                    return row;
                }).collect(Collectors.toList());
    }

    private List<String> createUsersRowBasedOnRank(List<String> userIds, Map<String, User> usersLookup) {
        var row = new ArrayList<String>();
        Optional.ofNullable(userIds).ifPresentOrElse(it -> {
            User primary = !it.isEmpty() ? usersLookup.get(it.get(0)) : null;
            User second = it.size() > 1 ? usersLookup.get(it.get(1)) : null;
            User third = it.size() > 2 ? usersLookup.get(it.get(2)) : null;

            row.add(String.valueOf(Stream.of(primary, second, third).filter(Objects::nonNull).count()));

            if (primary != null) {
                row.add(getContactEmail(primary));
                row.add(primary.getCompanyName());
                row.add(primary.getTitle());
                row.add(primary.getFname());
                row.add(primary.getSname());
            } else {
                addValues(row, 5, null);
            }

            if (second != null) {
                row.add(second.getCompanyName());
                row.add(second.getTitle());
                row.add(second.getFname());
                row.add(second.getSname());
            } else {
                addValues(row, 4, null);
            }

            if (third != null) {
                row.add(third.getCompanyName());
                row.add(third.getTitle());
                row.add(third.getFname());
                row.add(third.getSname());
            } else {
                addValues(row, 4, null);
            }
        }, () -> {
            row.add(String.valueOf(0));
            addValues(row, 13, null);
        });

        return row;
    }

    private List<List<String>> convertTenanciesToRows(List<Tenancy> tenancies, List<Property> properties, List<User> users) {
        var propertiesLookup = properties.stream().collect(Collectors.toMap(Property::getId, Function.identity()));
        var usersLookup = users.stream().collect(Collectors.toMap(User::getId, Function.identity()));

        return tenancies
                .stream()
                .filter(tenancy -> tenancy.getSettings() != null)
                .map(tenancy -> {
                    var row = new ArrayList<String>();
                    var property = propertiesLookup.get(tenancy.getProperty());

                    var tenants = tenancy.getTenants();

                    row.add(tenancy.getReference());
                    row.add(tenancy.getTitle());
                    row.add(tenancy.getStatus());
                    row.add(getOrDefault(tenancy.getType(), Tenancy.TenancyType.NA).name());
                    row.add(Optional
                            .ofNullable(tenancy.getStartDate())
                            .map(Utils::formatDate)
                            .orElse(null));
                    row.add(Optional
                            .ofNullable(tenancy.getEndDate())
                            .map(Utils::formatDate)
                            .orElse(null));
                    row.add(BigDecimal.valueOf(getOrDefault(tenancy.getRent(), 0)).movePointLeft(2).toString());
                    row.add(Optional.ofNullable(tenancy.getPeriod()).map(Enum::name).orElse(null));
                    row.add(Optional.ofNullable(tenancy.getPaymentDay()).map(it -> Integer.toString(it)).filter(day -> !"0".equals(day)).orElse(null));
                    row.add(BigDecimal.valueOf(getOrDefault(tenancy.getDeposit(), 0)).movePointLeft(2).toString());
                    row.add(BigDecimal.valueOf(getOrDefault(tenancy.getReservation(), 0)).movePointLeft(2).toString());
                    row.add(tenancy.getSettings().getFeeType());
                    row.add(Optional.ofNullable(tenancy.getLandlordVat()).map(vat -> "Yes").orElse("No"));
                    row.add(BigDecimal.valueOf(getOrDefault(tenancy.getOpeningBalance(), 0)).movePointLeft(2).toString());
                    row.add(Optional.of(tenancy.getSettings().isAutoInvoiceRent())
                            .filter(autoInvoice -> autoInvoice)
                            .map(autoInvoice -> "Yes")
                            .orElse("No"));
                    row.add(Optional.of(tenancy.getSettings().isSendInvoiceToTenant())
                            .filter(autoInvoice -> autoInvoice)
                            .map(autoInvoice -> "Yes")
                            .orElse("No"));
                    row.add(Optional.of(tenancy.getSettings().isAddCommissionBillVat())
                            .filter(autoInvoice -> autoInvoice)
                            .map(autoInvoice -> "Yes")
                            .orElse("No"));
                    row.add(tenancy.getDepositProtectionScheme());
                    row.add(tenancy.getDepositReference());
                    row.add(Optional
                            .ofNullable(tenancy.getDateDepositRegistered())
                            .map(Utils::formatDate)
                            .orElse(null));

                    row.addAll(createUsersRowBasedOnRank(tenants, usersLookup));

                    row.add(Optional.ofNullable(property).map(Property::getReference).orElse(null));

                    return row;
                }).collect(toList());

    }

    private byte[] generateExcel(@Nullable List<String> headers, List<List<String>> rows) {
        var workbook = new XSSFWorkbook();
        var sheet = workbook.createSheet("ORGANISATION_EXPORT");

        var numberOfColumns = Optional.ofNullable(headers)
                .map(List::size)
                .orElse(rows.stream()
                        .mapToInt(List::size)
                        .max()
                        .orElse(0)
                );

        for (var i = 0; i < numberOfColumns; i++) {
            sheet.setColumnWidth(i, 4000);
        }

        Optional.ofNullable(headers).ifPresent(columnNames -> {
            var header = sheet.createRow(0);
            for (var i = 0; i < columnNames.size(); i++) {
                var headerCell = header.createCell(i);
                headerCell.setCellValue(columnNames.get(i));
            }
        });

        var numberOfHeaderRows = Objects.isNull(headers) ? 0 : 1;

        for (var i = 0; i < rows.size(); i++) {
            var row = sheet.createRow(numberOfHeaderRows + i);
            var cells = rows.get(i);
            for (var i1 = 0; i1 < cells.size(); i1++) {
                var cell = row.createCell(i1);
                var cellValue = cells.get(i1);
                setCellValue(cell, cellValue);
            }
        }
        return toByteArray(workbook);
    }

    private void setCellValue(XSSFCell cell, String cellValue) {
        if (cellValue == null) {
            cell.setCellValue(cellValue);
            return;
        }

        var preprocessedCellValue = cellValue.trim()
                .replaceAll("£", "")
                .replaceAll(",", "");

        if (!NumberUtils.isParsable(preprocessedCellValue)) {
            cell.setCellValue(preprocessedCellValue);
            return;
        }

        try {
            var parsedNumber = Double.parseDouble(preprocessedCellValue);
            cell.setCellValue(parsedNumber);
        } catch (Exception ignored) {
            cell.setCellValue(preprocessedCellValue);
        }
    }

    private byte[] generateCSV(@Nullable List<String> headers, List<List<String>> rows) {

        StringBuilder csv = new StringBuilder();
        Optional.ofNullable(headers).ifPresent(columnNames -> {
            csv.append(String.join(",", headers)).append("\n");
        });

        csv.append(rows.stream().map(row -> String.join(",", row)).collect(joining("\n")));

        return csv.toString().getBytes();
    }

    private byte[] toByteArray(Workbook workbook) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            workbook.write(outputStream);

            return outputStream.toByteArray();
        } catch (Exception e) {
            throw new IllegalStateException("Failed to write to output stream", e);
        }
    }

    private void addValues(List<String> list, int count, String value) {
        for (int i = 0; i < count; i++) {
            list.add(value);
        }
    }
}
