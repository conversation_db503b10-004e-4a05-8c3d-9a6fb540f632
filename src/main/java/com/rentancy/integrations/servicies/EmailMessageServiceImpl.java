package com.rentancy.integrations.servicies;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.Document;
import com.rentancy.integrations.pojos.EmailMessage;
import com.rentancy.integrations.pojos.EmailSenderPayload;
import com.rentancy.integrations.pojos.MessageResponsePayload;
import com.rentancy.integrations.servicies.persistence.ConversationService;
import com.rentancy.integrations.servicies.persistence.DocumentService;
import com.rentancy.integrations.servicies.persistence.OrganisationService;
import com.rentancy.integrations.servicies.persistence.UserService;
import com.rentancy.integrations.util.JSONUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.apache.james.mime4j.dom.BinaryBody;
import org.apache.james.mime4j.dom.Entity;
import org.apache.james.mime4j.dom.Message;
import org.apache.james.mime4j.dom.Multipart;
import org.apache.james.mime4j.dom.TextBody;
import org.apache.james.mime4j.dom.address.Address;
import org.apache.james.mime4j.message.BodyPart;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static com.rentancy.integrations.servicies.EmailDownloaderImpl.DELIMITER;
import static java.util.stream.Collectors.toUnmodifiableList;

@RequiredArgsConstructor
class EmailMessageServiceImpl implements EmailMessageService {

    private static final Logger log = LogManager.getLogger(EmailMessageServiceImpl.class);

    private static final String PLAIN_MIMETYPE = "text/plain";
    private static final String ATTACHMENTS_KEY_PREFIX = "public/email_attachments";

    private final Config config;
    private final OrganisationService organisationService;
    private final UserService userService;
    private final DocumentService documentService;
    private final EmailDownloader emailDownloader;
    private final AppSyncServiceProvider appSyncServiceProvider;
    private final ConversationService conversationService;
    private final SQSClient sqsClient;

    @Override
    public void handleMessage(Message message) {
        var to = message.getTo();

        var content = new ArrayList<String>();
        var attachments = new ArrayList<BodyPart>();

        parseMessageBody(message, content, attachments);

        var attachmentContent = handleAttachments(attachments);

        var emailAttachments = getEmailAttachments(attachmentContent);

        to.forEach(address -> handleAddress(message, content, emailAttachments, address));
    }

    @Override
    public void sendResponse(MessageResponsePayload payload) {
        var emailQueue = config.getEmailNotificationQueue();
        var queueRegion = config.getEmailNotificationRegion();
        var organisationId = payload.getOrganisation();
        var to = payload.getTo();
        var subject = payload.getSubject();
        var body = payload.getBody();
        var organisationEmailAddress = String.join("+", "info", organisationId + "@email.loftyworks.com");

        var emailPayload = EmailSenderPayload.builder()
                .organisationId(organisationId)
                .fromEmail(organisationEmailAddress)
                .email(to)
                .subject(subject)
                .body(body)
                .emails(List.of())
                .simple(false)
                .build();

        log.info("Sending payload to email queue - {}", JSONUtils.wrappedToJsonString(emailPayload));
        sqsClient.enqueue(emailQueue, queueRegion, JSONUtils.wrappedToJsonString(emailPayload));
    }

    private void handleAddress(Message message,
                               List<String> content,
                               List<EmailMessage.EmailAttachment> emailAttachments,
                               Address address) {
        try {
            var subject = message.getSubject();
            var from = message.getFrom().get(0).getAddress();
            var to = address.toString();

            log.info("From - {}", from);
            log.info("To - {}", to);
            log.info("Subject - {}", subject);
            log.info("Body - {}", content);
            log.info("Attachments - {}", emailAttachments);

            var addressContent = parseToAddress(to);
            var organisationId = addressContent.getOrganisationId();
            var conversationId = addressContent.getConversationId();
            log.info("Content - {}", addressContent);

            if (Objects.nonNull(organisationId)) {
                var organisation = organisationService.getOrganisation(organisationId);
                var botUserId = organisation.getBotUser();
                var adminUser = organisation.getAdminUser();
                var botUser = userService.findUser(botUserId, false);

                if (Objects.nonNull(conversationId)) {
                    var user = userService.findUserWithEmail(from);
                    log.info("Organisation - {}, User - {}", organisation, user);

                    appSyncServiceProvider.createMessage(organisationId, conversationId, user.getId(), content.get(0), "TEXT");
                } else {
                    var newConversationId = String.join("+", organisationId, from);
                    var conversation = conversationService.findConversation(newConversationId);

                    if (Objects.isNull(conversation)) {
                        Set<String> members;
                        var name = String.join(" | ", subject, from);
                        var inboxMembers = new HashSet<>(userService.findOrganisationUserIdsWithRole(organisationId, "INBOX"));
                        log.info("Inbox members - {}", inboxMembers.size());
                        if (!inboxMembers.isEmpty()) {
                            inboxMembers.add(botUserId);
                            members = new HashSet<>(inboxMembers);
                        } else {
                            members = new HashSet<>(Set.of(botUserId, adminUser));
                        }

                        appSyncServiceProvider.createConversation(organisationId, newConversationId, botUser.getCognitoId(), name, members, "EMAIL_GROUP_CHAT");
                    }

                    appSyncServiceProvider.createMessage(organisationId, newConversationId, botUser.getId(), content.get(0), "TEXT");
                }
            }
        } catch (Exception e) {
            log.error("Failed to handle address - " + address, e);
        }
    }

    private AddressContent parseToAddress(String to) {
        var plusIndexOf = to.indexOf('+');
        var atIndexOf = to.indexOf('@');
        if (plusIndexOf == -1) {
            throw new IllegalArgumentException("Failed to parse address - " + to);
        }

        var parts = to.substring(plusIndexOf + 1, atIndexOf).split("_");
        log.info("Parts - {}", Arrays.toString(parts));

        if (parts.length < 2) {
            return new AddressContent(parts[0], null);
        }

        return new AddressContent(parts[0], parts[1]);
    }

    private List<EmailMessage.EmailAttachment> getEmailAttachments(List<Attachment> attachmentContent) {
        return attachmentContent
                .stream()
                .map(attachment -> {
                    var key = emailDownloader.uploadDocument(constructKey(attachment), attachment.getContent());

                    // TODO organisationId
                    return documentService.create(Document
                            .builder()
                            .key(key)
                            .mimeType(attachment.getMimeType())
                            .name(attachment.getFileName())
                            .build());
                })
                .map(EmailMessage.EmailAttachment::new)
                .collect(toUnmodifiableList());
    }

    private String constructKey(Attachment attachment) {
        return ATTACHMENTS_KEY_PREFIX + DELIMITER + attachment.getFileName();
    }

    private List<Attachment> handleAttachments(List<BodyPart> attachments) {
        return attachments
                .stream()
                .map(attachment -> {
                    try {
                        var result = new Attachment();
                        var body = attachment.getBody();
                        var fileName = attachment.getFilename();
                        var mimeType = attachment.getMimeType();
                        result.setFileName(fileName);
                        result.setMimeType(mimeType);
                        log.info(fileName);
                        if (body instanceof BinaryBody) {
                            result.setContent(((BinaryBody) body).getInputStream());
                        } else if (body instanceof TextBody) {
                            result.setContent(((TextBody) body).getInputStream());
                        }

                        return result;
                    } catch (Exception e) {
                        log.error(e);
                    }

                    return null;
                })
                .filter(Objects::nonNull)
                .collect(toUnmodifiableList());
    }

    private void parseMessageBody(Message message, List<String> content, List<BodyPart> attachments) {
        try {
            if (message.isMultipart()) {
                var multipart = (Multipart) message.getBody();
                recursiveMultipartProcessing(multipart, content, false, attachments);
            } else {
                content.add(getTextPart(message));
            }
        } catch (Exception e) {
            log.error(e);
        }
    }

    private void recursiveMultipartProcessing(Multipart multipart, List<String> content, Boolean hasBody, List<BodyPart> attachments)
            throws IOException {
        for (Entity entity : multipart.getBodyParts()) {
            BodyPart part = (BodyPart) entity;
            if (part.getDispositionType() != null && !part.getDispositionType().equals("")) {
                attachments.add(part);
            } else {
                if (part.getMimeType().equals(PLAIN_MIMETYPE) && !hasBody) {
                    content.add(getTextPart(part));
                    hasBody = true;
                } else if (part.isMultipart()) {
                    recursiveMultipartProcessing((Multipart) part.getBody(), content, hasBody, attachments);
                }
            }
        }
    }

    private String getTextPart(Entity part) throws IOException {
        TextBody tb = (TextBody) part.getBody();
        ByteArrayOutputStream content = new ByteArrayOutputStream();
        tb.writeTo(content);
        return content.toString();
    }

    @Data
    private static class Attachment {
        private String fileName;
        private String mimeType;
        private InputStream content;
    }

    @Data
    @AllArgsConstructor
    private static class AddressContent {
        private String organisationId;
        private String conversationId;
    }
}
