package com.rentancy.integrations.servicies.autojournal;

import com.rentancy.integrations.pojos.*;
import com.rentancy.integrations.servicies.XeroClient;
import com.rentancy.integrations.util.Utils;
import com.xero.models.accounting.TrackingCategories;
import com.xero.models.accounting.TrackingCategory;
import com.xero.models.accounting.TrackingOption;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

@Slf4j
@Getter
public class TrackingCategoryService {
    private String propertyTrackingName;
    private String contactsTrackingName;

    public TrackingCategories createTrackingCategories(Organisation organisation, Integration integration, Map<String, Property> propertyIdMap, List<Tenancy> journalTenancies, Map<String, User> userIdMap, XeroClient xeroClient) {
        var trackingCategories = xeroClient.getTrackingCategoriesWithRetry(integration.getTenantId(), integration.getAccessToken());

        propertyTrackingName = Utils.findMappedTrackingCategory(organisation, "Property");
//        contactsTrackingName = Utils.findMappedTrackingCategory(organisation, "Contacts");

        List<TrackingCategory> newTrackingCategories = new ArrayList<>(trackingCategories.getTrackingCategories());
        if (Objects.isNull(findTrackingCategory(trackingCategories, propertyTrackingName))) {
            newTrackingCategories.addAll(xeroClient.createTrackingCategoryWithRetry(integration.getTenantId(), integration.getAccessToken(), propertyTrackingName).getTrackingCategories());
        }
//        if (Objects.isNull(findTrackingCategory(trackingCategories, contactsTrackingName))) {
//            newTrackingCategories.addAll(xeroClient.createTrackingCategoryWithRetry(integration.getTenantId(), integration.getAccessToken(), contactsTrackingName).getTrackingCategories());
//        }
        trackingCategories.setTrackingCategories(newTrackingCategories);
        var propertyTrackingCategoryId = findTrackingCategory(trackingCategories, propertyTrackingName).getTrackingCategoryID();
//        var contactsTrackingCategoryId = findTrackingCategory(trackingCategories, contactsTrackingName).getTrackingCategoryID();

        var allPropertyReferences = propertyIdMap.values().stream().map(Property::getReference).collect(toList());
//        var allContactsNames = Stream.concat(
//                        journalTenancies.stream().map(tenancy -> {
//                            var property = propertyIdMap.get(tenancy.getProperty());
//                            return Utils.getPrimaryLandlordId(property);
//                        }),
//                        journalTenancies.stream().map(Utils::getPrimaryTenantId)
//                )
//                .filter(Optional::isPresent)
//                .map(Optional::get)
//                .map(userIdMap::get)
//                .map(Utils::formatUser)
//                .collect(toList());

        var optionsReferencesSet = findTrackingCategory(trackingCategories, propertyTrackingName)
                .getOptions()
                .stream()
                .map(TrackingOption::getName)
                .collect(Collectors.toSet());
//        var optionsContactsSet = findTrackingCategory(trackingCategories, contactsTrackingName)
//                .getOptions()
//                .stream()
//                .map(TrackingOption::getName)
//                .collect(Collectors.toSet());

        var nonExistentReferences = allPropertyReferences
                .stream()
                .filter(Predicate.not(optionsReferencesSet::contains))
                .distinct()
                .collect(toList());
//        var nonExistentContacts = allContactsNames
//                .stream()
//                .filter(Predicate.not(optionsContactsSet::contains))
//                .distinct()
//                .collect(toList());
        {
            var groupsOf10 = ListUtils.partition(nonExistentReferences, 10);
            List<List<List<String>>> groupedBatchesOfReferences = new ArrayList<>();
            var size = groupsOf10.size();
            for (int i = 0; i < size; i += 5) {
                groupedBatchesOfReferences.add(Stream.of(
                        i < size ? groupsOf10.get(i) : null,
                        i + 1 < size ? groupsOf10.get(i + 1) : null,
                        i + 2 < size ? groupsOf10.get(i + 2) : null,
                        i + 3 < size ? groupsOf10.get(i + 3) : null,
                        i + 4 < size ? groupsOf10.get(i + 4) : null
                ).filter(Objects::nonNull).collect(toList()));
            }


            groupedBatchesOfReferences.forEach(propertyPartitions -> {
                log.info("Creating property with partitions [{}]", propertyPartitions.stream().map(list -> String.join(",", list)).collect(toList()));
                propertyPartitions.parallelStream().forEach(
                        partition -> {
                            var now1 = Instant.now();
                            xeroClient.createBulkTrackingCategoryOptionsWithRetry(integration.getTenantId(), integration.getAccessToken(), propertyTrackingCategoryId, partition);
                            var duration = Duration.between(now1, Instant.now());
                            log.info("Took: {} ms, each {} ms", duration.toMillis(), duration.toMillis() / partition.size());
                        }
                );
            });
        }
//        {
//            var groupsOf10 = ListUtils.partition(nonExistentContacts, 10);
//            List<List<List<String>>> groupedBatchesOfReferences = new ArrayList<>();
//            var size = groupsOf10.size();
//            for (int i = 0; i < size; i += 5) {
//                groupedBatchesOfReferences.add(Stream.of(
//                        i < size ? groupsOf10.get(i) : null,
//                        i + 1 < size ? groupsOf10.get(i + 1) : null,
//                        i + 2 < size ? groupsOf10.get(i + 2) : null,
//                        i + 3 < size ? groupsOf10.get(i + 3) : null,
//                        i + 4 < size ? groupsOf10.get(i + 4) : null
//                ).filter(Objects::nonNull).collect(toList()));
//            }
//
//            groupedBatchesOfReferences.forEach(contactsPartition -> {
//                log.info("Creating contacts with partitions [{}]", contactsPartition.stream().map(list -> String.join(",", list)).collect(toList()));
//                contactsPartition.parallelStream().forEach(
//                        partition -> {
//                            try {
//                                var now1 = Instant.now();
//                                xeroClient.createBulkTrackingCategoryOptionsWithRetry(integration.getTenantId(), integration.getAccessToken(), contactsTrackingCategoryId, partition);
//                                var duration = Duration.between(now1, Instant.now());
//                                log.info("Took: {} ms, each {} ms", duration.toMillis(), duration.toMillis() / partition.size());
//                            } catch (Exception e) {
//                            }
//                        }
//                );
//            });
//        }

        return xeroClient.getTrackingCategoriesWithRetry(integration.getTenantId(), integration.getAccessToken());
    }

    public TrackingCategory findTrackingCategory(TrackingCategories categories, String name) {
        return categories
                .getTrackingCategories()
                .stream()
                .filter(trackingCategory -> trackingCategory.getName().equals(name))
                .findAny()
                .orElse(null);
    }

    public TrackingCategoryOption createTrackingCategory(TrackingCategories trackingCategories,
                                                         String tenantId,
                                                         String token,
                                                         String optionName,
                                                         String categoryName,
                                                         XeroClient xeroClient) {
        var trackingCategory = findTrackingCategory(trackingCategories, categoryName);
        var categoryId = Optional
                .ofNullable(trackingCategory)
                .map(TrackingCategory::getTrackingCategoryID)
                .orElseGet(() -> {
                    // FIXME: This fails when there are already TrackingCategories present
                    var result = xeroClient.createTrackingCategoryWithRetry(tenantId, token, categoryName);
                    var propertyTrackingCategory = findTrackingCategory(result, categoryName);
                    return propertyTrackingCategory.getTrackingCategoryID();
                });


        var opt = Optional.ofNullable(trackingCategory)
                .map(TrackingCategory::getOptions)
                .map(a -> findTrackingOption(a, optionName))
                .map(TrackingOption::getTrackingOptionID);

        opt.ifPresent(a -> log.info("Found tracking option for [{},{}]", categoryName, optionName));

        UUID optionId = opt.orElseGet(() -> {
            log.info("Creating tracking option for [{},{}]", categoryName, optionName);
            return xeroClient.createTrackingCategoryOptionWithRetry(tenantId, token, categoryId.toString(), optionName);
        });

        return new TrackingCategoryOption(categoryId, optionId);
    }

    public TrackingOption findTrackingOption(List<TrackingOption> trackingOptions, String optionName) {
        return trackingOptions.stream()
                .filter(option -> optionName.equals(option.getName()))
                .findFirst()
                .orElse(null);
    }

    @Getter
    @AllArgsConstructor
    public static class TrackingCategoryOption {
        private UUID categoryId;
        private UUID optionId;
    }
}
