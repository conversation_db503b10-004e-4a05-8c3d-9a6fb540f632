package com.rentancy.integrations.servicies.autojournal;

import com.rentancy.integrations.pojos.*;
import com.rentancy.integrations.pojos.Account;
import com.rentancy.integrations.pojos.Organisation;
import com.rentancy.integrations.pojos.User;
import com.rentancy.integrations.servicies.*;
import com.rentancy.integrations.servicies.persistence.InvoiceService;
import com.rentancy.integrations.servicies.persistence.OrganisationService;
import com.rentancy.integrations.servicies.persistence.PropertyService;
import com.rentancy.integrations.servicies.persistence.UserService;
import com.rentancy.integrations.servicies.rent.NextInvoiceDateCalculator;
import com.rentancy.integrations.servicies.xero_integration.callback.AfterCreationOfBillData;
import com.rentancy.integrations.servicies.xero_integration.queue.InvoiceOutboundSender;
import com.rentancy.integrations.servicies.xero_integration.queue.dto.CallbackOutbound;
import com.rentancy.integrations.servicies.xero_integration.queue.dto.SqsInvoiceMessagePattern;
import com.rentancy.integrations.servicies.xero_integration.queue.dto.SqsInvoiceType;
import com.rentancy.integrations.util.Utils;
import com.xero.models.accounting.*;
import com.xero.models.accounting.Invoice;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.rentancy.integrations.servicies.TenancyService.ACTIVE_TENANCY_STATUSES;
import static com.rentancy.integrations.util.JSONUtils.wrappedToJsonString;
import static com.rentancy.integrations.util.Utils.getDetail;
import static java.time.ZoneOffset.UTC;
import static java.util.stream.Collectors.*;

@Slf4j
@RequiredArgsConstructor
public class AutoJournalService {
    public static int DEFAULT_SLEEP = 1000;

    private final OrganisationService organisationService;
    private final PropertyService propertyService;
    private final ReportService reportService;
    private final IntegrationService integrationService;
    private final UserService userService;
    private final InvoiceService invoiceService;
    private final TenancyService tenancyService;
    private final XeroClient xeroClient;
    private final PortfolioXeroProcessor portfolioXeroProcessor;
    private final TrackingCategoryService trackingCategoryService;
    private final InvoiceOutboundSender invoiceOutboundSender;
    private final String xeroInvoicesCallbackQueue;

    private static final String DELIMITER = ", ";
    private static final String DEFAULT_VALUE = "";

    public void sendMonthlyJournalBills(String organisationId, List<String> landlordIds) {
        var organisation = organisationService.getOrganisation(organisationId);
        var journalMonth = Instant.now().atZone(UTC);

        if (organisation.getJournalPeriod().equals(Organisation.JournalPeriod.PRIOR_MONTH)) {
            journalMonth = journalMonth.minusMonths(1);
        }
        var journalPeriodStartTime = journalMonth.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        /*
          DEV NOTE:
          journalPeriodEndTime should be last day of the month.
          To achieve that we are adding 1 month to journalPeriodStartTime to get 1st day of the next month
          and then subtracting 1 day to get the last day of the current month.
         */
        var journalPeriodEndTime = journalPeriodStartTime.plusMonths(1).minusDays(1);

        log.info("Getting Tenancies to AutoJournal for organisation {}", organisationId);
        var tenancies = propertyService.findTenancies(organisationId, null, true)
                .stream()
                .filter(tenancy -> ACTIVE_TENANCY_STATUSES.contains(tenancy.getStatus()))
                .filter(tenancy -> {
                    if (!tenancy.isEnableJournal()) {
                        return false;
                    }
                    if (Objects.isNull(tenancy.getStartDate())) {
                        return false;
                    }
                    var startDate = Instant.parse(tenancy.getStartDate()).atZone(UTC);

                    if (startDate.isAfter(journalPeriodEndTime.minusNanos(1))) {
                        return false;
                    }

                    if (Objects.isNull(tenancy.getEndDate()) || "PERIODIC".equals(tenancy.getStatus())) {
                        return true;
                    }
                    var endDate = Instant.parse(tenancy.getEndDate()).atZone(UTC);

                    return endDate.equals(journalPeriodStartTime) || endDate.isAfter(journalPeriodStartTime);
                }).collect(toUnmodifiableList());

        if (tenancies.isEmpty()) {
            log.info("No tenancies to journal in:{}", organisationId);
            return;
        } else {
            log.info("Found {} Tenancies to AutoJournal", tenancies.size());
        }


        if (Integration.IntegrationService.XERO == Objects.requireNonNull(organisation.getConnectedFinanceIntegration())) {
            var journalPeriod = new NextInvoiceDateCalculator.RentPeriod(journalPeriodStartTime, journalPeriodEndTime);
            List<JournalResult> journalResults = this.sendMonthlyJournalBills(organisation, tenancies, journalPeriod, landlordIds);
            reportService.generateMonthlyJournalReport(organisation, journalResults);
        } else {
            throw new IllegalStateException("Unknown integration type:" + organisation.getConnectedFinanceIntegration());
        }
    }


    List<JournalResult> sendMonthlyJournalBills(
            Organisation organisation,
            List<Tenancy> tenancies,
            NextInvoiceDateCalculator.RentPeriod journalPeriod,
            List<String> landlordIds) {
        var now = Instant.now();
        var integration = integrationService.findIntegrationByOrganisationId(organisation.getId(), Integration.IntegrationService.XERO);

        log.info("Collecting information for AutoJournal for organisation {}", organisation.getId());

        var propertyIds = tenancies.stream().map(Tenancy::getProperty).collect(Collectors.toSet());
        var propertyIdMap = propertyService.getProperties(propertyIds).collect(toUnmodifiableMap(Property::getId, Function.identity()));
        var userIds = Stream.of(Utils.getPrimaryLandlordIds(propertyIdMap.values()), Utils.getPrimaryTenantIds(tenancies), List.of(organisation.getBalanceTransferContactUserId()))
                .flatMap(Collection::stream)
                .collect(toUnmodifiableSet());
        var userIdMap = userService.findUsers(userIds).stream().collect(toUnmodifiableMap(User::getId, Function.identity()));
        var balanceTransferUser = userIdMap.get(organisation.getBalanceTransferContactUserId());
        var lineItems = invoiceService.findOrganisationLineItems(organisation.getId(), null, null);
        var accounts = invoiceService.findAccountsWithOrganisationId(organisation.getId(), integration.getTenantId());

        var xeroIdBalance = Utils.getPrimaryTenantIds(tenancies)
                .parallelStream()
                .map(tenantUserId -> {
                    var user = userIdMap.get(tenantUserId);
                    if (user == null) {
                        log.info("User with id {} was not found in DB but was assigned to Tenancy", tenantUserId);
                        return Pair.of(tenantUserId, BigDecimal.ZERO);
                    }
                    var balance = tenancyService.calculateTenantBalance(organisation, user, propertyIdMap, tenancies, lineItems);

                    log.info("Tenant balance: {} {} {}", user.getId(), user.getXeroId(), balance);

                    return Pair.of(user.getId(), balance);
                })
                .collect(toUnmodifiableMap(Pair::getKey, Pair::getValue));


        var tenantBalanceLedgerCode = getLedgerCodes(organisation.getLedgerCodes(),
                code -> code.getName().equals("Tenant Balances"))
                .stream()
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Organisation " + organisation.getId() + " doesnt has tenant balances ledger code configured"));
        var landlordBalanceLedgerCode = getLedgerCodes(organisation.getLedgerCodes(),
                code -> code.getName().equals("Landlord Balances"))
                .stream()
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Organisation " + organisation.getId() + " doesnt has landlord balances ledger code configured"));

        var dateFormatter = new SimpleDateFormat("dd MMM yyyy");

        var lineItemDescription = dateFormatter.format(Date.from(journalPeriod.startTime().toInstant())) + " to " + dateFormatter.format(Date.from(journalPeriod.endTime().toInstant()));


        var invalidTenancies = new ArrayList<Pair<String, IGNORE_REASON>>();
        var journalTenancies = tenancies.stream()
                .filter(tenancy -> invalidTenancy(Objects.nonNull(tenancy.getProperty()), tenancy, IGNORE_REASON.MISSING_PROPERTY, invalidTenancies))
                .filter(tenancy -> invalidTenancy(Utils.getPrimaryTenantId(tenancy).isPresent(), tenancy, IGNORE_REASON.MISSING_PRIMARY_TENANT, invalidTenancies))
                .filter(tenancy -> {
                    var property = propertyIdMap.get(tenancy.getProperty());
                    return invalidTenancy(Utils.getPrimaryLandlordId(property).isPresent(), tenancy, IGNORE_REASON.MISSING_PRIMARY_LANDLORD, invalidTenancies);
                })
                .filter(tenancy -> invalidTenancy(Objects.nonNull(tenancy.getPeriod()), tenancy, IGNORE_REASON.MISSING_PERIOD, invalidTenancies))
                .filter(tenancy -> invalidTenancy(Objects.nonNull(tenancy.getRent()), tenancy, IGNORE_REASON.MISSING_RENT, invalidTenancies))
                .filter(tenancy -> {
                    var property = propertyIdMap.get(tenancy.getProperty());
                    var primaryLandlordId = Utils.getPrimaryLandlordId(property).get();
                    return invalidTenancy(Objects.nonNull(userIdMap.get(primaryLandlordId)), tenancy, IGNORE_REASON.INVALID_PRIMARY_LANDLORD, invalidTenancies);
                })
                .filter(tenancy -> {
                    var primaryTenantId = Utils.getPrimaryTenantId(tenancy).get();
                    return invalidTenancy(Objects.nonNull(userIdMap.get(primaryTenantId)), tenancy, IGNORE_REASON.INVALID_PRIMARY_TENANT, invalidTenancies);
                })
                .filter(tenancy -> {
                    if (landlordIds.isEmpty()) return true;
                    var property = propertyIdMap.get(tenancy.getProperty());
                    var primaryLandlordId = Utils.getPrimaryLandlordId(property).get();
                    return invalidTenancy(landlordIds.contains(primaryLandlordId), tenancy, IGNORE_REASON.LANDLORD_NOT_IN_BATCH, invalidTenancies);
                })
                .collect(toUnmodifiableList());

        log.info("Journal tenancies constructed:" + wrappedToJsonString(journalTenancies));
        log.info("Journal tenancies with missing properties/fields:" + wrappedToJsonString(invalidTenancies));

        var saveDateFormatter = new SimpleDateFormat("yyyy-MM-dd");
        var currentDate = saveDateFormatter.format(java.sql.Date.from(now));
        var updatedSettings = new ArrayList<Tenancy.Settings>();
        var journalingTenants = new HashMap<String, BigDecimal>();
        var tenancyJournalAmounts = new HashMap<String, BigDecimal>();

        Contact balanceTransferContact = getBalanceTransferContactFromXero(integration, balanceTransferUser);
        var trackingCategories = trackingCategoryService.createTrackingCategories(organisation, integration, propertyIdMap, journalTenancies, userIdMap, xeroClient);
        var propertyTrackingName = trackingCategoryService.getPropertyTrackingName();
//        var contactsTrackingName = trackingCategoryService.getContactsTrackingName();

        List<SqsInvoiceMessagePattern<Invoice>> messagesToSend = new ArrayList<>();
        var tenancyBalanceTransferMap = new HashMap<String, String>();
        var tenancyArrearsMap = new HashMap<String, Tenancy.Settings>();

        journalTenancies.stream()
                .forEach(tenancy -> {
                    var skipTenancyWrapper = new AtomicBoolean(false);
                    List<LineItem> fundDistributionLineItems = null;
                    var rentDistributionAmount = new AtomicReference<BigDecimal>();
                    var property = propertyIdMap.get(tenancy.getProperty());
                    var fundDistribution = tenancy.getSettings().getFundDistribution();

                    // TODO: Guaranteed to be present because of filtering above
                    var primaryTenant = Utils.getPrimaryTenantIds(List.of(tenancy)).stream().findFirst().map(userIdMap::get).orElseThrow();
                    var primaryLandlord = Utils.getPrimaryLandlordIds(List.of(property)).stream().findFirst().map(userIdMap::get).orElseThrow();

                    var tenantBalance = xeroIdBalance.getOrDefault(primaryTenant.getId(), BigDecimal.ZERO);

                    var rentAmount = tenancyService.getTenancyMonthlyRent(tenancy);
                    var arrearsSum = Optional.ofNullable(tenancy
                                    .getSettings()
                                    .getAutoJournalArrears()).orElse(Collections.emptyList())
                            .stream()
                            .map(Tenancy.AutoJournalArrears::getAmount)
                            .map(BigDecimal::new)
                            .map(amount -> amount.movePointLeft(2))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    var journalAmount = new AtomicReference<>(rentAmount.add(arrearsSum).min(tenantBalance).max(BigDecimal.ZERO));
                    tenancyJournalAmounts.put(tenancy.getId(), journalAmount.get());

                    if (journalAmount.get().compareTo(BigDecimal.ZERO) == 0) {
                        log.info("Zero tenant balance:" + tenancy.getId() + " " + property.getAddressLine1() + " " + organisation.getId() + " " + primaryTenant.getId());
                        if (Objects.nonNull(fundDistribution) && !fundDistribution.isEmpty()) {
                            updatedSettings.add(tenancy.getSettings());
                            var autoJournalArrears = tenancy.getSettings().getAutoJournalArrears();

                            fundDistribution
                                    .stream()
                                    .filter(fund -> Objects.nonNull(fund.getLedger()))
                                    .forEach(fund -> {
                                        var ledgerCode = getLedgerCodes(organisation.getLedgerCodes(),
                                                code -> fund.getLedger().equals(code.getCode()))
                                                .stream()
                                                .findFirst()
                                                .orElseGet(() -> {
                                                    log.info("Organisation {} doesn't have ledger code configured, setting it to {}", organisation.getId(), fund.getLedger());
                                                    return fund.getLedger();
                                                });
                                        var amount = BigDecimal.valueOf(fund.getAmount()).movePointLeft(2).movePointRight(2).intValue();
                                        autoJournalArrears.stream().filter(aja -> aja.getLedgerCode().equals(ledgerCode)).findAny()
                                                .ifPresentOrElse(aja -> {
                                                    // modified in-place
                                                    aja.setAmount(aja.getAmount() + amount);
                                                }, () -> {
                                                    autoJournalArrears.add(Tenancy.AutoJournalArrears
                                                            .builder()
                                                            .amount(amount)
                                                            .ledgerCode(ledgerCode)
                                                            .ledgerName(accounts
                                                                    .stream()
                                                                    .filter(acc -> Objects.nonNull(acc.getCode()))
                                                                    .filter(acc -> acc.getCode().equals(ledgerCode))
                                                                    .findAny()
                                                                    .map(Account::getName)
                                                                    .orElse(DEFAULT_VALUE))
                                                            .build());
                                                });
                                    });
                        }
                        tenancyArrearsMap.put(tenancy.getReference(), tenancy.getSettings());
                        return;
                    }

                    log.info("Journal amount - {}", journalAmount.get());

                    var dueDate = Utils.toXeroDate(now.toEpochMilli());
                    var currency = Optional.of(organisation).map(Organisation::getCurrency).orElse(tenancy.getSettings().getCurrency());

                    // Call 1 for property Tracking option
                    var propertyTracking = createTrackingItem(trackingCategories, integration.getTenantId(), integration.getAccessToken(), propertyTrackingName, property.getReference());

                    // Call 2 for Contact tracking option
                    var tenantTracking = Stream.of(
                                    propertyTracking
//                                    createTrackingItem(trackingCategories, integration.getTenantId(), integration.getAccessToken(), contactsTrackingName, Utils.formatUser(primaryTenant))
                            )
                            .flatMap(Collection::stream)
                            .collect(Collectors.toList());

                    var tenantLineItem = new LineItem()
                            .quantity(BigDecimal.ONE.doubleValue())
                            .unitAmount(journalAmount.get().doubleValue())
                            .taxType("NONE")
                            .description(lineItemDescription)
                            .tracking(tenantTracking)
                            .accountCode(tenantBalanceLedgerCode);

                    // Call 3 time for landlord
                    var landlordTracking = Stream.of(
                                    propertyTracking
//                                    createTrackingItem(trackingCategories, integration.getTenantId(), integration.getAccessToken(), contactsTrackingName, Utils.formatUser(primaryLandlord))
                            )
                            .flatMap(Collection::stream)
                            .collect(Collectors.toList());

                    var landlordLineItem = new LineItem()
                            .quantity(BigDecimal.ONE.doubleValue())
                            .unitAmount(-journalAmount.get().doubleValue())
                            .taxType("NONE")
                            .description(lineItemDescription)
                            .tracking(landlordTracking)
                            .accountCode(landlordBalanceLedgerCode);

                    tenancy.getSettings().setLastJournalRunDate(currentDate);
                    if (tenancy.getSettings().getFirstJournalRunDate() == null) {
                        tenancy.getSettings().setFirstJournalRunDate(currentDate);
                    }
                    updatedSettings.add(tenancy.getSettings());

                    log.info("Fund distribution - {}, tenancy id - {}", fundDistribution, tenancy.getId());
                    if (Objects.nonNull(fundDistribution) && !fundDistribution.isEmpty()) {
                        var autoJournalArrears = tenancy.getSettings().getAutoJournalArrears();

                        fundDistributionLineItems = fundDistribution
                                .stream()
                                .filter(fund -> Objects.nonNull(fund.getLedger()))
                                .map(fund -> {
                                    var ledgerCode = getLedgerCodes(organisation.getLedgerCodes(),
                                            code -> fund.getLedger().equals(code.getCode()))
                                            .stream()
                                            .findFirst()
                                            .orElseGet(() -> {
                                                log.info("Organisation {} doesn't have ledger code configured, setting it to {}", organisation.getId(), fund.getLedger());
                                                skipTenancyWrapper.set(true);
                                                return fund.getLedger();
                                            });

                                    var unitAmount = new AtomicReference<>(BigDecimal.ZERO);

                                    // if journal amount >= fund amount
                                    var journalAmountGreaterThanFundAmount = journalAmount.get().compareTo(BigDecimal.valueOf(fund.getAmount()).movePointLeft(2)) >= 0;
                                    if (journalAmountGreaterThanFundAmount) {
                                        // if the corresponding AJA exists:
                                        autoJournalArrears.stream().filter(aja -> aja.getLedgerCode().equals(ledgerCode)).findAny().ifPresentOrElse(aja -> {
                                            // unit amount set to remaining journalAmount
                                            unitAmount.set(BigDecimal.valueOf(fund.getAmount()).movePointLeft(2).add(BigDecimal.valueOf(aja.getAmount()).movePointLeft(2)).min(journalAmount.get()));
                                            // subtract the unit amount from journalAmount.
                                            // so unitAmount is the distribution of funds for Arrears.
                                            journalAmount.set(journalAmount.get().subtract(unitAmount.get()));
                                            // this sets the AJA amount to subtract unitAmount. (modified in-place)
                                            aja.setAmount(BigDecimal.valueOf(fund.getAmount()).movePointLeft(2).add(BigDecimal.valueOf(aja.getAmount()).movePointLeft(2)).subtract(unitAmount.get()).movePointRight(2).intValue());
                                        }, () -> {
                                            // if the AJA ledgerCode does not exist:
                                            // unitAmount = fundAmount
                                            unitAmount.set(BigDecimal.valueOf(fund.getAmount()).movePointLeft(2));
                                            // journalAmount = journalAmount - fundAmount
                                            journalAmount.set(journalAmount.get().subtract(BigDecimal.valueOf(fund.getAmount()).movePointLeft(2)));
                                        });
                                        // if journal amount < fund amount:
                                    } else {
                                        // unitAmount = journalAmount
                                        unitAmount.set(journalAmount.get());
                                        // amount = fundAmount - journalAmount
                                        var amount = BigDecimal.valueOf(fund.getAmount()).movePointLeft(2).subtract(journalAmount.get()).movePointRight(2).intValue();
                                        autoJournalArrears.stream().filter(aja -> aja.getLedgerCode().equals(ledgerCode)).findAny()
                                                .ifPresentOrElse(aja -> {
                                                    // if aja exists
                                                    // aja = ajaAmount + amount (modified in-place)
                                                    aja.setAmount(aja.getAmount() + amount);
                                                }, () -> {
                                                    // if aja does not exist:
                                                    autoJournalArrears.add(Tenancy.AutoJournalArrears
                                                            .builder()
                                                            // set AJA amount = amount
                                                            .amount(amount)
                                                            .ledgerCode(ledgerCode)
                                                            .ledgerName(accounts
                                                                    .stream()
                                                                    .filter(acc -> Objects.nonNull(acc.getCode()))
                                                                    .filter(acc -> acc.getCode().equals(ledgerCode))
                                                                    .findAny()
                                                                    .map(Account::getName)
                                                                    .orElse(DEFAULT_VALUE))
                                                            .build());
                                                });
                                        // this is still in the journalAmount < fundAmount
                                        // so we want to zero out the journalAmount because it is fully used up
                                        // journalAmount = 0
                                        journalAmount.set(BigDecimal.ZERO);
                                    }

                                    // ????
                                    // it is outside the loop so the last one is used here ???
                                    // fixme: should it be that way?
                                    rentDistributionAmount.set(unitAmount.get());

                                    return new LineItem()
                                            .quantity(BigDecimal.ONE.doubleValue())
                                            .unitAmount(-unitAmount.get().doubleValue())
                                            .taxType("NONE")
                                            .description(lineItemDescription)
                                            .tracking(landlordTracking)
                                            .accountCode(ledgerCode);
                                }).collect(toList());
                        // prepend the tenantLineItem
                        fundDistributionLineItems.add(0, tenantLineItem);
                    }

                    log.info("Rent distribution amount - {}", rentDistributionAmount.get());
                    journalingTenants.put(tenancy.getId(), Optional.ofNullable(rentDistributionAmount.get()).orElse(journalAmount.get()));

                    if (!skipTenancyWrapper.get()) {
                        updatedSettings.remove(tenancy.getSettings());
                        Invoice journalBill = new Invoice()
                                .type(Invoice.TypeEnum.ACCPAY)
                                .status(Invoice.StatusEnum.AUTHORISED)
                                .date(dueDate)
                                .dueDate(dueDate)
                                .currencyCode(CurrencyCode.fromValue(currency))
                                .invoiceNumber(String.join("/", property.getReference(), tenancy.getReference(), Utils.generateRandomString()))
                                .contact(balanceTransferContact.isSupplier(true).isCustomer(false))
                                .lineAmountTypes(LineAmountTypes.NOTAX)
                                // if the fund distribution is defined then use the fundDistributionLineItems (contains tenantLineItem), else use just tenantLineItem and landlordLineItem
                                .lineItems(Optional.ofNullable(fundDistributionLineItems).orElse(new ArrayList<>(List.of(tenantLineItem, landlordLineItem))));

                        reorderAutoJournalArrears(tenancy.getSettings());
                        BigDecimal precalculatedAmount = portfolioXeroProcessor.calculateLandlordBillAmount(property, lineItems);
                        CallbackOutbound callback = CallbackOutbound.afterCreationOfBill(
                                xeroInvoicesCallbackQueue,
                                tenancy.getId(),
                                tenancy.getSettings().getLastJournalRunDate(),
                                tenancy.getSettings().getFirstJournalRunDate(),
                                tenancy.getSettings().getAutoJournalArrears(),
                                precalculatedAmount
                        );
                        SqsInvoiceMessagePattern<Invoice> message = SqsInvoiceMessagePattern.<Invoice>builder()
                                .organisationId(organisation.getId())
                                .tenantId(integration.getTenantId())
                                .entityId(journalBill.getInvoiceNumber())
                                .body(journalBill)
                                .type(SqsInvoiceType.CREATE_INVOICE)
                                .callback(callback)
                                .build();
                        messagesToSend.add(message);
                        tenancyBalanceTransferMap.put(tenancy.getReference(), journalBill.getInvoiceNumber());
                    }
                    tenancyArrearsMap.put(tenancy.getReference(), tenancy.getSettings());
                });

        log.info("Journal bills constructed: " + wrappedToJsonString(messagesToSend.stream().map(SqsInvoiceMessagePattern::getBody).collect(toList())));
        log.info("Bills - {}", messagesToSend.size());

        for (SqsInvoiceMessagePattern<Invoice> message : messagesToSend) {
            log.info("Sending bill to sqs - {}", message);
            invoiceOutboundSender.sendMessageToTopic(message);
        }

        var tenancyManagementFeeMap = portfolioXeroProcessor.raiseCommissionBillWithSqs(TenancyInvoiceSenderPayload
                .builder()
                .tenant(integration.getTenantId())
                .token(integration.getAccessToken())
                .organisationId(organisation.getId())
                .journalingTenantAmount(journalingTenants)
                .tenancies(journalingTenants.keySet().stream().collect(toUnmodifiableList()))
                .date(now.toString())
                .journalPeriod(journalPeriod)
                .build(), organisation);


        /*
            update settings for tenancies that dont have any journal bills created
         */
        fixAJAOrderingForSettings(updatedSettings);
        propertyService.updateTenancySettings(updatedSettings);
        tenancyArrearsMap.forEach((ref, settings) -> reorderAutoJournalArrears(settings));
        return journalTenancies.stream()
                .filter(tenancy -> Objects.nonNull(tenancy.getPeriod()) && Objects.nonNull(tenancy.getRent()))
                .map(tenancy -> {
                    var property = propertyIdMap.get(tenancy.getProperty());
                    var primaryTenant = Utils.getPrimaryTenantIds(List.of(tenancy)).stream().findFirst().map(userIdMap::get).orElseThrow();
                    var primaryLandlord = Utils.getPrimaryLandlordIds(List.of(property)).stream().findFirst().map(userIdMap::get).orElseThrow();

                    var rentAmount = tenancyService.getTenancyMonthlyRent(tenancy);
                    var journalAmount = tenancyJournalAmounts.get(tenancy.getId());

                    return JournalResult.builder()
                            .propertyAddress(getPropertyDescription(property, DELIMITER))
                            .tenancyReference(tenancy.getReference())
                            .tenantName(Utils.formatUser(primaryTenant))
                            .landlordName(Utils.formatUser(primaryLandlord))
                            .journalAmount(journalAmount)
                            .expectedJournalAmount(rentAmount)
                            .balanceTransferNumber(tenancyBalanceTransferMap.get(tenancy.getReference()))
                            .managementFeeNumber(tenancyManagementFeeMap.get(tenancy.getReference()))
                            .arrears(tenancyArrearsMap.getOrDefault(tenancy.getReference(), Tenancy.Settings.builder().build()).getAutoJournalArrears())
                            .build();
                })
                .collect(toUnmodifiableList());
    }

    public void handleAfterNormalBillCreationCallback(String organisationId,
                                                      List<AfterCreationOfBillData> billsCallbackData) {
        Organisation organisation = organisationService.getOrganisation(organisationId);
        var rentIncomeCode = organisation.getLedgerCodes().stream()
                .filter(code -> code.getName().equals("Rent Income"))
                .map(Organisation.LedgerCode::getCode)
                .findFirst().orElse("200");
        var rentIncomeLineItem = billsCallbackData.stream()
                .filter(i -> !i.getInvoice().getHasErrors())
                .filter(i -> i.getInvoice().getValidationErrors().isEmpty())
                .map(billData -> {
                            var li = billData.getInvoice().getLineItems()
                                    .stream()
                                    // TODO: create test for this
                                    .filter(li2 -> rentIncomeCode.equals(li2.getAccountCode()))
                                    .findFirst();
                            return (InvoiceLineItemData) li.map(lineItem -> new InvoiceLineItemData(lineItem, billData.getInvoice(), billData.getTenancyId(), billData.getPreCalculatedOriginalAmount())).orElse(null);
                        }
                )
                .filter(Objects::nonNull)
                .filter(this::lineItemAmountGreaterThanZero)
                .collect(toList());

        List<String> tenancyIds = billsCallbackData.stream().map(AfterCreationOfBillData::getTenancyId).distinct().collect(Collectors.toList());
        Map<String, Tenancy> tenanciesById = propertyService.findTenancies(tenancyIds, true)
                .stream()
                .collect(toMap(Tenancy::getId, Function.identity()));
        log.info("found {} rent Income line items", rentIncomeLineItem.size());
        for (InvoiceLineItemData invoiceLineItemData : rentIncomeLineItem) {
            Tenancy tenancy = tenanciesById.get(invoiceLineItemData.getTenancyId());
            if (tenancy != null) {
                log.info("Creating landlord bill for line item {}", invoiceLineItemData.getLineItem().getLineItemID());
                var amount = getLineItemAmount(invoiceLineItemData.getLineItem());
                portfolioXeroProcessor.saveLandlordBill(tenancy, amount.toString(), invoiceLineItemData.getInvoice().getInvoiceID().toString(), invoiceLineItemData.getPreCalculatedOriginalAmount());
            }
        }
        for (AfterCreationOfBillData billCallback : billsCallbackData) {
            Tenancy tenancy = tenanciesById.get(billCallback.getTenancyId());
            if (tenancy == null) {
                continue;
            }

            // updating settings
            log.info("Updating settings of tenancy {}", tenancy.getReference());
            tenancy.getSettings().setAutoJournalArrears(billCallback.getAutoJournalArrears());
            tenancy.getSettings().setLastJournalRunDate(billCallback.getLastJournalRunDate());
            tenancy.getSettings().setFirstJournalRunDate(billCallback.getFirstJournalRunDate());
            propertyService.updateTenancySettings(List.of(tenancy.getSettings()));
        }
    }

    private void fixAJAOrderingForSettings(ArrayList<Tenancy.Settings> updatedSettings) {
        for (var settings : updatedSettings) {
            reorderAutoJournalArrears(settings);
        }
    }

    private void reorderAutoJournalArrears(Tenancy.Settings settings) {
        var oldArrears = settings.getAutoJournalArrears().stream()
                .collect(Collectors.toMap(Tenancy.AutoJournalArrears::getLedgerCode, Function.identity()));
        var newArrears = settings.getFundDistribution().stream()
                .map(fund -> oldArrears.get(fund.getLedger()))
                .filter(Objects::nonNull)
                .collect(toList());

        settings.setAutoJournalArrears(newArrears);
    }

    private Contact getBalanceTransferContactFromXero(Integration integration, User balanceTransferUser) {
        // this can be just Contacts/Id for balanceTransferUser
        return xeroClient.getContactWithRetry(balanceTransferUser.getXeroId(), integration.getTenantId(), integration.getAccessToken());
    }

    private List<String> getLedgerCodes(List<Organisation.LedgerCode> ledgerCodes,
                                        Predicate<Organisation.LedgerCode> predicate) {
        return ledgerCodes
                .stream()
                .filter(predicate)
                .map(Organisation.LedgerCode::getCode)
                .filter(Objects::nonNull)
                .collect(toUnmodifiableList());
    }

    private boolean invoiceBelongsToTenancy(Tenancy tenancy, Invoice invoice) {
        var reference = Optional.ofNullable(invoice.getInvoiceNumber())
                .map(ref -> ref.split("/"))
                .filter(ref -> ref.length >= 2)
                .map(ref -> ref[1])
                .filter(StringUtils::hasText);
        return reference.filter(s -> tenancy.getReference().equals(s)).isPresent();
    }

    private BigDecimal getLineItemAmount(LineItem lineItem) {
        return Optional.ofNullable(lineItem.getLineAmount()).map(BigDecimal::new).map(BigDecimal::abs).orElse(BigDecimal.ZERO);
    }

    private boolean lineItemAmountGreaterThanZero(InvoiceLineItemData pair) {
        return getLineItemAmount(pair.getLineItem()).compareTo(BigDecimal.ZERO) > 0;
    }

    private List<LineItemTracking> createTrackingItem(TrackingCategories trackingCategories,
                                                      String tenant,
                                                      String token,
                                                      String categoryName,
                                                      String optionName) {
        try {
            var categoryOption
                    = trackingCategoryService.createTrackingCategory(
                    trackingCategories, tenant, token, optionName, categoryName, xeroClient);

            return List.of(new LineItemTracking()
                    .name(categoryName)
                    .option(optionName)
                    .trackingCategoryID(categoryOption.getCategoryId())
                    .trackingOptionID(categoryOption.getOptionId()));
        } catch (Exception e) {
            log.error("Failed to get Tracking item - " + categoryName, e.getMessage());
        }

        return List.of();
    }


    public String getPropertyDescription(Property property, String DELIMITER) {
        return Stream.of(
                        getDetail(property.getAddressLine1()),
                        getDetail(property.getAddressLine2()),
                        getDetail(property.getAddressLine3()),
                        getDetail(property.getPostcode()),
                        getDetail(property.getCity()))
                .filter(StringUtils::hasText)
                .collect(joining(DELIMITER));
    }


    @Getter
    @AllArgsConstructor
    private static class InvoiceLineItemData {
        private final LineItem lineItem;
        private final Invoice invoice;
        private final String tenancyId;
        private final BigDecimal preCalculatedOriginalAmount;
    }

    private boolean invalidTenancy(boolean result, Tenancy tenancy, IGNORE_REASON reason, List<Pair<String, IGNORE_REASON>> invalidTenancies) {
        if (!result) {
            invalidTenancies.add(Pair.of(tenancy.getId(), reason));
        }
        return result;
    }

    private enum IGNORE_REASON {
        MISSING_PROPERTY,
        MISSING_PRIMARY_TENANT,
        MISSING_PRIMARY_LANDLORD,
        INVALID_PRIMARY_TENANT,
        INVALID_PRIMARY_LANDLORD,
        MISSING_RENT,
        LANDLORD_NOT_IN_BATCH,
        MISSING_PERIOD
    }
}