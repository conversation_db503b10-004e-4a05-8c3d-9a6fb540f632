package com.rentancy.integrations.servicies;

import com.rentancy.integrations.pojos.User;
import com.rentancy.integrations.pojos.XeroAccounts.XeroAccount;
import com.rentancy.integrations.pojos.XeroBankTransfers.XeroBankTransfer;
import com.rentancy.integrations.pojos.XeroInvoices.XeroInvoice;
import com.rentancy.integrations.pojos.XeroOverPayments.XeroOverPayment;
import com.rentancy.integrations.pojos.XeroPayments.XeroPayment;
import com.rentancy.integrations.pojos.XeroTransactions.XeroTransaction;
import com.xero.models.accounting.Contact;
import com.xero.models.accounting.Journal;

public interface XeroDataMapper {
    void mapInvoice(String organisation, String tenant, XeroInvoice xeroInvoice, User contactUser);

    void mapAccount(String organisation, String tenant, XeroAccount xeroAccount);

    void mapTransaction(String organisation, String tenant, XeroTransaction xeroTransaction, User contactUser);

    User mapContact(String organisation, Contact xeroContact);

    void mapPayment(String organisation, String tenant, XeroPayment xeroPayment);

    void mapTransfer(String organisation, String tenant, XeroBankTransfer xeroTransfer);

    void mapJournal(String organisation, String tenant, Journal xeroJournal);

    void mapOverPayment(String organisation, XeroOverPayment overPayment);
}
