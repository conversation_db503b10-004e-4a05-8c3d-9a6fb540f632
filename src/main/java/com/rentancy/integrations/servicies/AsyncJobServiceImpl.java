package com.rentancy.integrations.servicies;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.handlers.AsyncLambdaInvoker;
import com.rentancy.integrations.pojos.ClientStatementCommand;
import com.rentancy.integrations.pojos.LandlordBillBulkPayoutRequest;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.rentancy.integrations.util.JSONUtils.wrappedDeserializePayload;
import static com.rentancy.integrations.util.JSONUtils.wrappedToJsonString;


/**
 * This handler accepts API Gateway request for starting various async jobs.
 * The jobs themselves are handled by their respective handlers after receiving the SQS message.
 * <br>
 * The flow is as such:
 * <br>
 * <b>User clicks button -> AsyncJobService receives request -> SQS Message is sent out -> <PERSON><PERSON> receives SQS message and does it's job asynchronously</b>
 *
 * <table style="border: 1px solid black">
 *     <tr><th>Case</th><th>Queue Name</th><th>Message Object</th><th>Handler</th></tr>
 *     <tr>
 *         <td>cashBalanceReport</td>
 *         <td>cash-balance-report-queue-{stage}</td>
 *         <td> {@link com.rentancy.integrations.pojos.CashBalanceReportCommand }</td>
 *         <td> {@link com.rentancy.integrations.handlers.CashBalanceSenderHandler }</td>
 *     </tr>
 *     <tr>
 *         <td>propertyTenancyReport</td>
 *         <td>organisation-property-data-exporter-queue-{stage}</td>
 *         <td> {@link com.rentancy.integrations.pojos.OrganisationPropertyReportCommand }</td>
 *         <td> {@link com.rentancy.integrations.handlers.PropertyDataExporterHandler }</td>
 *     </tr>
 *     <tr>
 *         <td>tenancyScheduleReport</td>
 *         <td>tenancy-schedule-queue-{stage}</td>
 *         <td> {@link com.rentancy.integrations.pojos.TenancyScheduleCreationCommand }</td>
 *         <td> {@link com.rentancy.integrations.handlers.TenancyScheduleSenderHandler }</td>
 *     </tr>
 *     <tr>
 *         <td>clientBalanceReport</td>
 *         <td>client-balance-report-queue-{stage}</td>
 *         <td> {@link com.rentancy.integrations.pojos.PropertyReportCommand }</td>
 *         <td> {@link com.rentancy.integrations.handlers.ClientBalanceReportHandler }</td>
 *     </tr>
 *     <tr>
 *         <td>clientStatementReport</td>
 *         <td>client-statement-report-queue-{stage}</td>
 *         <td> {@link com.rentancy.integrations.pojos.ClientStatementCommand }</td>
 *         <td> {@link com.rentancy.integrations.handlers.ClientStatementReportHandler }</td>
 *     </tr>
 *     <tr>
 *         <td>overseasResidentReport</td>
 *         <td>overseas-resident-report-queue-{stage}</td>
 *         <td> {@link com.rentancy.integrations.pojos.TenancyInvoiceSenderPayload }</td>
 *         <td> {@link com.rentancy.integrations.handlers.OverseasResidentBillSender }</td>
 *     </tr>
 *     <tr>
 *         <td>clientGeneralReport</td>
 *         <td>client-general-report-queue-{stage}</td>
 *         <td> {@link com.rentancy.integrations.pojos.ClientStatementCommand }</td>
 *         <td> {@link com.rentancy.integrations.handlers.ClientGeneralReportHandler }</td>
 *     </tr>
 *     <tr>
 *         <td>supplierLandlordStatement</td>
 *         <td>supplier-landlord-statement-report-queue-{stage}</td>
 *         <td> {@link com.rentancy.integrations.pojos.SupplierLandlordStatementReportCommand}</td>
 *         <td> {@link com.rentancy.integrations.handlers.SupplierLandlordStatementReportHandler }</td>
 *     </tr>
 *     <tr>
 *         <td>bulkPayout</td>
 *         <td>bulk-payout-queue-{stage}</td>
 *         <td> {@link com.rentancy.integrations.pojos.LandlordBillBulkPayoutRequest}</td>
 *         <td> {@link com.rentancy.integrations.handlers.LandlordBillBulkPayoutHandler}</td>
 *     </tr>
 *     <tr>
 *         <td>bacsReport</td>
 *         <td>bacs-report-queue-{stage}</td>
 *         <td> {@link com.rentancy.integrations.pojos.BacsReportPayoutRequest}</td>
 *         <td> {@link com.rentancy.integrations.handlers.BacsReportSenderHandler}</td>
 *     </tr>
 * </table>
 *
 */
@RequiredArgsConstructor
public class AsyncJobServiceImpl implements AsyncJobService {

    private final Config config;
    private final SQSClient sqsClient;

    private static final Logger log = LogManager.getLogger(AsyncJobServiceImpl.class);


    @Override
    public void runJob(String jobName, String data, String cognitoId) {

        log.info("Running job {} with data {}", jobName, data);

        switch (jobName) {
            case "cashBalanceReport":
                sqsClient.enqueue(config.getCashBalanceReportQueueName(), data);
                break;
            case "propertyTenancyReport":
                sqsClient.enqueue(config.getOrganisationPropertyReportQueueName(), data);
                break;
            case "tenancyScheduleReport":
                sqsClient.enqueue(config.getTenancyScheduleReportQueueName(), data);
                break;
            case "propertyBalanceReport":
                sqsClient.enqueue(config.getPropertyBalanceReportQueueName(), data);
                break;
            case "clientBalanceReport":
                sqsClient.enqueue(config.getClientBalanceReportQueueName(), data);
                break;
            case "clientStatementReport":
                sqsClient.enqueue(config.getClientStatementReportQueueName(), data);
                break;
            case "overseasResidentReport":
                sqsClient.enqueue(config.getOverseasResidentReportQueueName(), data);
                break;
            case "clientGeneralReport":
                var command = wrappedDeserializePayload(data, ClientStatementCommand.class);
                command.setCognitoId(cognitoId);
                sqsClient.enqueue(config.getClientGeneralReportQueueName(), wrappedToJsonString(command));
                break;
            case "supplierLandlordStatement":
                sqsClient.enqueue(config.getSupplierLandlordStatementReportQueueName(), data);
                break;
            case "bulkPayout":
                var input = wrappedDeserializePayload(data, LandlordBillBulkPayoutRequest.class);
                input.setCognitoId(cognitoId);
                sqsClient.enqueue(config.getBulkPayoutQueueName(), wrappedToJsonString(input));
                break;
            case "bacsReport":
                sqsClient.enqueue(config.getBacsReportQueueName(), data);
                break;
            default:
                throw new IllegalArgumentException(jobName + " doesn't exists");
        }
    }
}
