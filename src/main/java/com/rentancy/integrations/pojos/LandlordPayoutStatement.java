package com.rentancy.integrations.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LandlordPayoutStatement {

    private String organisationLogo;
    private String organisationName;
    private String date;
    private String period;
    private String landlordsDescription;
    private String parentPropertyDescriptions;
    private String propertyDescriptions;

    private IncomeExpensePercentageSection incomePercentageSection;
    private IncomeExpensePercentageSection expensePercentageSection;
    // For 'payoutSplitOwnershipReport' we need to exclude management fee
    private IncomeExpensePercentageSection splitOwnershipExpenseSection;


    private BigDecimal payoutAmount;

    private List<LandlordPayoutStatementPercentageItem> landlordPayoutPercentageSection;

    private List<PropertyParentPropertySection> groupedPropertySummarySection;

    private BigDecimal managementFeeAmount;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IncomeExpensePercentageSection {
        private List<LandlordPayoutStatementPercentageItem> items;
        private BigDecimal total;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PropertyParentPropertySection {
        private boolean parentPropertySection;
        private String address;
        private List<LandlordPayoutStatementItem> items;
        private BigDecimal total;
        private BigDecimal minimumBalance;
        private String currency;
    }

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LandlordPayoutStatementItem {
        private String description;
        private String ledgerCodeName;
        private String currency;
        private BigDecimal amount;
        private boolean income;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LandlordPayoutStatementPercentageItem {
        // Can be landlord or ledger code
        private String itemName;
        private String percentage;
        private String currency;
        private BigDecimal amount;
    }
}
