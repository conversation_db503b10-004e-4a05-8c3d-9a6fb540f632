package com.rentancy.integrations.pojos;

import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyRequestEvent;
import lombok.Data;

import java.util.Map;

@Data
public class ApiGatewayProxyRequestInput {
    private String path;
    private String httpMethod;
    private String body;
    private String source;

    private Map<String, String> headers;
    private Map<String, String> queryStringParameters;
    private Map<String, String> pathParameters;
    private APIGatewayV2ProxyRequestEvent.RequestContext requestContext;
}
