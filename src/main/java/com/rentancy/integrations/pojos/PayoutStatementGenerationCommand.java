package com.rentancy.integrations.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Data
@NoArgsConstructor
public class PayoutStatementGenerationCommand {
    private String landlordBillId;
    private String organisationId;
    private String periodStartDate;
    private String periodEndDate;

    // This is mainly for YPP
    @Nullable boolean payoutSplitOwnershipReport;

    private Set<String> landlordIds;
    private Set<String> lineItemIds;

    private List<LandlordPercentageItem> landlordPercentages;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LandlordPercentageItem {
        private String landlordId;
        private String landlordName;
        private BigDecimal percentage;
        private BigDecimal amount;
    }
}
