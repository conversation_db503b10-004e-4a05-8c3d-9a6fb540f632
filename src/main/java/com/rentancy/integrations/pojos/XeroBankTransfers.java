package com.rentancy.integrations.pojos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class XeroBankTransfers {

    @JsonProperty("BankTransfers")
    private List<XeroBankTransfer> bankTransfers;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class XeroBankTransfer {
        @JsonProperty("BankTransferID")
        private String bankTransferId;

        @JsonProperty("CreatedDateUTC")
        private String createdDate;

        @JsonProperty("Date")
        private String date;

        @JsonProperty("FromBankAccount")
        private XeroAccounts.XeroAccount fromBankAccount;

        @JsonProperty("ToBankAccount")
        private XeroAccounts.XeroAccount toBankAccount;

        @JsonProperty("Amount")
        private String amount;

        @JsonProperty("FromBankTransactionID")
        private String fromBankTransactionID;

        @JsonProperty("ToBankTransactionID")
        private String toBankTransactionId;
    }
}
