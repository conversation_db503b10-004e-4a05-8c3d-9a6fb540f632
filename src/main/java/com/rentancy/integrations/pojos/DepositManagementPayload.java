package com.rentancy.integrations.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DepositManagementPayload {
    private String organisationId;
    private Boolean registered;
    private Boolean transferred;
    private DepositStatus depositStatus;
    private String contractEndDate;
    private Boolean released;
    private int page;
    private int limit;
    private String propertyReference;
    private String contractReference;
    private String dueDate;
    /*
    DEV NOTE: LET-301
     */
    private String tenancyStatus;
    private String primaryTenantId;
    private Integer depositFrom;
    private Integer depositTo;

    public enum DepositStatus {
        RECEIVING,
        NOT_RECEIVING,
        REGISTERING,
        NOT_REGISTERING,
        REFUNDING,
        NOT_REFUNDING,
        REFUNDED,
        NOT_REFUNDED,
        ALL
    }
}
