package com.rentancy.integrations.pojos;

import lombok.Builder;
import lombok.Data;

@Data
@Builder(toBuilder = true)
public class LandlordBill {
    private final String id;
    private final String eventId;
    private final String propertyId;
    private final String datePaid;
    private final int originalAmount;
    private final String propertyAddress;
    private final String tenancyReference;
    private int billAmount;
    private final boolean approved;
    private final String approvedBy;
    private String dateRaised;
    private LandlordBillStatus status;
    private final String landlordBillOrganisationId;
    private final String landlordId;
    private String invoiceId;
    private String originalInvoiceId;
    private String landlordName;

    public enum LandlordBillStatus {
        NEW,
        COMPLETED,
        SKIPPED
    }
}
