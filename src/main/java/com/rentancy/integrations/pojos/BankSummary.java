package com.rentancy.integrations.pojos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BankSummary {

    @JsonProperty("Reports")
    private List<Summary> summaries;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Summary {
        @JsonProperty("Rows")
        private List<Row> rows;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Row {
        @JsonProperty("RowType")
        private String type;

        @JsonProperty("Rows")
        private List<Row> rows;

        @JsonProperty("Cells")
        private List<Cell> cells;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Cell {
        @JsonProperty("Value")
        private String value;

        @JsonProperty("Attributes")
        private List<Attribute> attributes;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Attribute {
        @JsonProperty("Id")
        private String id;

        @JsonProperty("Value")
        private String value;
    }
}
