package com.rentancy.integrations.pojos;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class DocumentDTO {
    private String id;
    private String key;
    private String name;
    private String mimeType;
    private String documentOrganisationId;
    private String expiry;
    private String createdAt;
    private String type;
    private String description;

    private String documentPropertyId;
    private String documentTenancyId;

    public Document mapToDocument() {
        return Document.builder()
            .id(getId())
            .key(getKey())
            .name(getName())
            .mimeType(getMimeType())
            .organisationId(getDocumentOrganisationId())
            .expiry(getExpiry())
            .createdAt(getCreatedAt())
            .type(getType())
            .description(getDescription())
            .propertyId(getDocumentPropertyId())
            .tenancyId(getDocumentTenancyId())
            .build();
    }
}
