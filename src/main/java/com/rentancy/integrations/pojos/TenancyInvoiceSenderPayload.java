package com.rentancy.integrations.pojos;

import com.rentancy.integrations.servicies.rent.NextInvoiceDateCalculator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TenancyInvoiceSenderPayload {
    private String tenant;
    private String token;
    private String organisationId;
    private List<String> tenancies;
    private Map<String, BigDecimal> journalingTenantAmount;

    private BigDecimal paidAmount;
    private String xeroInvoiceId;
    private String date;
    private NextInvoiceDateCalculator.RentPeriod journalPeriod;

    @Builder.Default
    private Instant issueDate = Instant.now();
    @Builder.Default
    private Instant sendDate = Instant.now();
}
