package com.rentancy.integrations.pojos;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class CognitoAuthorizer {

    private Claims claims;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Claims {
        private String sub;

        @JsonProperty("cognito:groups")
        private String cognitoGroups;

        @JsonProperty("event_id")
        private String eventId;

        @JsonProperty("token_use")
        private String tokenUse;

        private String scope;

        @JsonProperty("auth_time")
        private String authTime;

        private String iss;
        private String exp;
        private String iat;
        private String jti;
        private String aud;

        @JsonProperty("client_id")
        private String clientId;

        @JsonProperty("username")
        private String usernameOld;

        private String email;

        @JsonProperty("cognito:username")
        private String username;

        @JsonProperty("custom:organisation")
        private String organisationId;

        @JsonProperty("custom:organisationId")
        private String organisationIdOld;

        @JsonProperty("custom:eligibleWorkspaces")
        private String eligibleWorkspaces;

        @JsonProperty("email_verified")
        private boolean emailVerified;

        @JsonProperty("cognito:preferred_role")
        private String preferredRole;

        @JsonProperty("cognito:roles")
        private String roles;
    }
}
