package com.rentancy.integrations.pojos;

import com.rentancy.integrations.pojos.Invoice.LineItem;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class Transaction extends XeroModel {
    private String transactionId;
    private String prePaymentId;
    private String overPaymentId;
    private String currencyRate;
    private String url;
    private String accountId;
    private String tenant;

    private boolean reconciled;
    private boolean balanceTransfer;

    private List<LineItem> lineItems;

    private String batchPaymentId;
    private boolean overPaymentTransaction;

    @Builder(toBuilder = true)
    public Transaction(String id, String type, String status, String contactId, boolean hasAttachments, Instant date, String reference, String currency, String lineAmountTypes, String subTotal, String totalTax, String total, String organisation, Instant updatedAt, String transactionId, String prePaymentId, String overPaymentId, boolean reconciled, String currencyRate, String url, String accountId, String tenant, List<LineItem> lineItems, boolean balanceTransfer, String batchPaymentId, boolean overPaymentTransaction) {
        super(id, type, status, contactId, hasAttachments, date, reference, currency, lineAmountTypes, subTotal, totalTax, total, organisation, updatedAt);
        this.transactionId = transactionId;
        this.prePaymentId = prePaymentId;
        this.overPaymentId = overPaymentId;
        this.reconciled = reconciled;
        this.currencyRate = currencyRate;
        this.url = url;
        this.accountId = accountId;
        this.tenant = tenant;
        this.lineItems = lineItems;
        this.balanceTransfer = balanceTransfer;
        this.batchPaymentId = batchPaymentId;
        this.overPaymentTransaction = overPaymentTransaction;
    }
}
