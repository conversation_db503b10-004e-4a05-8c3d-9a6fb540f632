package com.rentancy.integrations.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JournalResult {

    private String propertyAddress;
    private String tenancyReference;
    private String tenantName;
    private String landlordName;
    private BigDecimal journalAmount;
    private BigDecimal expectedJournalAmount;
    private String balanceTransferNumber;
    private String managementFeeNumber;
    private List<Tenancy.AutoJournalArrears> arrears;
}
