package com.rentancy.integrations.pojos;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xero.models.accounting.Contact;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class XeroOverPayments {
    @JsonProperty("Overpayments")
    private List<XeroOverPayment> overPayments;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class XeroOverPayment {
        @JsonProperty("OverpaymentID")
        private String overPaymentId;

        @JsonProperty("Type")
        private String type;

        @JsonProperty("Reference")
        private String reference;

        @JsonProperty("RemainingCredit")
        private String remainingCredit;

        @JsonProperty("Contact")
        private Contact contact;

        @JsonProperty("Date")
        private String date;

        @JsonProperty("Status")
        private String status;

        @JsonProperty("LineAmountTypes")
        private String lineAmountTypes;

        @JsonProperty("SubTotal")
        private String subTotal;

        @JsonProperty("TotalTax")
        private String totalTax;

        @JsonProperty("Total")
        private String total;

        @JsonProperty("CurrencyCode")
        private String currency;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class XeroOverPaymentLoaderPayload {
        private String organisation;
        private String tenant;
        private XeroOverPayment overPayment;
    }

}
