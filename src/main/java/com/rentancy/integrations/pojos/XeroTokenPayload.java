package com.rentancy.integrations.pojos;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class XeroTokenPayload {
    private String iss;
    private String aud;
    private String sub;
    private String sid;
    private String jti;
    private String iat;
    @JsonProperty("at_hash")
    private String hash;
    @JsonProperty("authentication_event_id")
    private String authEventId;
    @JsonProperty("client_id")
    private String clientId;
    @JsonProperty("xero_userid")
    private String xeroUserId;
    @JsonProperty("global_session_id")
    private String globalSessionId;

    @JsonProperty("preferred_username")
    private String username;
    private String email;
    @JsonProperty("given_name")
    private String fname;
    @JsonProperty("family_name")
    private String sname;

    @JsonProperty("auth_time")
    private long auth_time;
    private long nbf;
    private long exp;

    private List<String> scope;
    private List<String> amr;
}
