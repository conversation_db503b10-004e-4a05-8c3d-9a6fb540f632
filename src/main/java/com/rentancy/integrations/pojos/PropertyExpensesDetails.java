package com.rentancy.integrations.pojos;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
public class PropertyExpensesDetails {
    private int limit;
    private int total;
    private int pageCount;

    private List<PropertyExpensesDetailItem> items;

    @Data
    @Builder
    public static class PropertyExpensesDetailItem {
        private String id;
        private String reference;
        private String fromUser;
        private String date;
        private String dueDate;
        private BigDecimal paidAmount;
        private BigDecimal dueAmount;
        private boolean taxExclusive;
        private String status;
    }
}
