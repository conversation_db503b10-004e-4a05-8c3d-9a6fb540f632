package com.rentancy.integrations.pojos;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xero.models.accounting.Contact;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class XeroInvoices extends XeroBaseResponse {

    @JsonProperty("Invoices")
    private List<XeroInvoice> invoices;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class XeroInvoice {
        @JsonProperty("InvoiceID")
        private String invoiceId;

        @JsonProperty("InvoiceNumber")
        private String number;

        @JsonProperty("Reference")
        private String reference;

        @JsonProperty("Type")
        private String type;

        @JsonProperty("Status")
        private String status;

        @JsonProperty("Contact")
        private Contact contact;

        @JsonProperty("LineAmountTypes")
        private String lineAmountTypes;

        @JsonProperty("SubTotal")
        private String subTotal;

        @JsonProperty("TotalTax")
        private String totalTax;

        @JsonProperty("Total")
        private String total;

        @JsonProperty("AmountDue")
        private String amountDue;

        @JsonProperty("AmountPaid")
        private String amountPaid;

        @JsonProperty("TotalDiscount")
        private String totalDiscount;

        @JsonProperty("AmountCredited")
        private String amountCredited;

        @JsonProperty("CurrencyCode")
        private String currency;

        @JsonProperty("SentToContact")
        private boolean sentToContact;

        @JsonProperty("HasAttachments")
        private boolean hasAttachments;

        @JsonProperty("ExpectedPaymentDate")
//        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
//        @JsonSerialize(using = InstantSerializer.class)
//        @JsonDeserialize(using = InstantDeserializer.class)
        private String expectedPaymentDate;

        @JsonProperty("PlannedPaymentDate")
//        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
//        @JsonSerialize(using = InstantSerializer.class)
//        @JsonDeserialize(using = InstantDeserializer.class)
        private String plannedPaymentDate;

        @JsonProperty("FullyPaidOnDate")
//        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
//        @JsonSerialize(using = InstantSerializer.class)
//        @JsonDeserialize(using = InstantDeserializer.class)
        private String fullyPaidOnDate;

        @JsonProperty("Date")
//        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
//        @JsonSerialize(using = InstantSerializer.class)
//        @JsonDeserialize(using = InstantDeserializer.class)
        private String date;

        @JsonProperty("DueDate")
//        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
//        @JsonSerialize(using = InstantSerializer.class)
//        @JsonDeserialize(using = InstantDeserializer.class)
        private String dueDate;

        @JsonProperty("LineItems")
        private List<XeroInvoiceLineItem> lineItems;

        @JsonProperty("Payments")
        private List<XeroPayments> payments;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class XeroInvoiceLineItem {
        @JsonProperty("LineItemID")
        private String lineItemId;

        @JsonProperty("Description")
        private String description;

        @JsonProperty("Quantity")
        private String quantity;

        @JsonProperty("UnitAmount")
        private String unitAmount;

        @JsonProperty("ItemCode")
        private String itemCode;

        @JsonProperty("AccountCode")
        private String accountCode;

        @JsonProperty("TaxType")
        private String taxType;

        @JsonProperty("TaxAmount")
        private String taxAmount;

        @JsonProperty("LineAmount")
        private String lineAmount;

        @JsonProperty("DiscountRate")
        private String discountRate;

        @JsonProperty("DiscountAmount")
        private String discountAmount;

        @JsonProperty("Tracking")
        private List<TrackingCategory> tracking;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class XeroPayments {
        @JsonProperty("Date")
        private String date;

        @JsonProperty("Amount")
        private String amount;

        @JsonProperty("PaymentID")
        private String paymentId;

        @JsonProperty("BatchPaymentID")
        private String batchPaymentId;

        @JsonProperty("Reference")
        private String reference;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrackingCategory {

        @JsonProperty("Name")
        private String trackingName;

        @JsonProperty("Option")
        private String trackingOption;
    }
}
