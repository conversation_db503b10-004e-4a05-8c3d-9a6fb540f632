package com.rentancy.integrations.pojos;

import lombok.Builder;
import lombok.Data;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;

import static com.rentancy.integrations.pojos.Integration.IntegrationStatus.*;

@Data
@Builder(toBuilder = true)
public class Integration {
    private String id;
    private String state;
    private String url;
    private String refreshToken;
    private String accessToken;
    private String organisationId;
    private String userId;
    private String tenantId;
    private String tenantName;
    private String connectionId;

    private Instant expirationDate;
    private Instant startDate;
    private Instant updatedAt;

    private Integer remainingCallAmount;

    private IntegrationStatus status;
    private IntegrationService type;
    private List<IntegrationMetadata> metadata;
    private List<String> allowedContactGroups;

    public enum IntegrationService {
        XERO, QUICK_BOOKS, CHIME, GOOGLE, OUTLOOK, GOOGLE_GMAIL, OUTLOOK_EMAIL, TWILIO;

        public static IntegrationService fromValue(String name) {
            for (IntegrationService e : IntegrationService.values()) {
                if (e.name().equals(name)) {
                    return e;
                }
            }
            return null;
        }
    }

    public enum IntegrationStatus {
        NOT_CONNECTED, // After auth flow is started
        CONNECTED, // After auth flow is successfully completed
        FAILED // After some error happens during auth flow or getting data from Xero
    }

    public enum ResourceType {
        CONTACT,
        INVOICE,
        TRANSACTION,
        ACCOUNT,
        PAYMENT,
        BANK_TRANSFER,
        JOURNAL,
        OVERPAYMENT
    }

    @Data
    @Builder(toBuilder = true)
    public static class IntegrationMetadata {
        private int retryCount;
        private String errorMessage;
        private Instant lastUpdatedDate;
        private ResourceType recourseType;
    }


    public Integration connected(Oauth2ExchangeResponse response, XeroConnection connection) {
        return this
                .toBuilder()
                .accessToken(response.getAccessToken())
                .refreshToken(response.getRefreshToken())
                .tenantId(connection.getTenantId())
                .tenantName(connection.getTenantName())
                .connectionId(connection.getId())
                .status(CONNECTED)
                .expirationDate(Instant.now().plus(response.getExpiration(), ChronoUnit.SECONDS))
                .build();
    }

    public Integration failed() {
        return this.toBuilder().status(FAILED).build();
    }

    public static Integration create(String organisationId, String userId, String url, String state, Instant startDate, IntegrationService type) {
       return Integration
                .builder()
                .url(url)
                .state(state)
                .userId(userId)
                .organisationId(organisationId)
                .status(NOT_CONNECTED)
                .type(type)
                .startDate(startDate)
                .metadata(List.of())
                .build();
    }

    public Integration update(String url, String state, Instant startDate, IntegrationService type) {
        return this
                .toBuilder()
                .id(this.getId())
                .state(state)
                .url(url)
                .type(type)
                .startDate(this.getStatus() == CONNECTED ? this.getStartDate() : startDate)
                .metadata(this.getMetadata().stream().anyMatch(metadata -> metadata.getRetryCount() > 2) ? List.of() : this.getMetadata())
                .build();
    }
}
