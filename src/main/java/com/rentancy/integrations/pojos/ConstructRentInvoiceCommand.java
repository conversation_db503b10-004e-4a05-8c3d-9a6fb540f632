package com.rentancy.integrations.pojos;

import com.rentancy.integrations.servicies.rent.NextInvoiceDateCalculator;
import com.xero.models.accounting.BrandingThemes;
import com.xero.models.accounting.Contacts;
import com.xero.models.accounting.Invoice;
import com.xero.models.accounting.TrackingCategories;
import lombok.Builder;
import lombok.Value;
import lombok.experimental.Accessors;

import java.time.Instant;

@Value
@Accessors(fluent = true)
@Builder
public class ConstructRentInvoiceCommand {
    Tenancy tenancy;
    String tenant;
    String token;
    NextInvoiceDateCalculator.RentPeriod rentPeriod;
    Invoice.StatusEnum status;
    Contacts contacts;
    TrackingCategories trackingCategories;
    BrandingThemes brandingThemes;
    Instant dueDate;
    Instant issueDate;
}
