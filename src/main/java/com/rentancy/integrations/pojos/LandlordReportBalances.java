package com.rentancy.integrations.pojos;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class LandlordReportBalances {

    private final LandlordReportBalance incomes;
    private final LandlordReportBalance expenses;
    private final LandlordReportBalance disbursements;

    @Data
    @AllArgsConstructor
    public static class LandlordReportBalance {
        private BigDecimal debit;
        private BigDecimal credit;
    }
}
