package com.rentancy.integrations.pojos;

import lombok.Builder;
import lombok.Data;

import java.time.Instant;
import java.util.List;
import java.util.Set;

@Data
@Builder(toBuilder = true)
public class Statement {
    private String id;
    private String organisationId;
    private String propertyId;
    private String clientId;
    private String landlordBillId;
    private String reference;
    private String fileKey;
    private String finalisedBy;
    private String type;
    private String sentBy;
    private String payedOutBy;
    private String billsUpdatedBy;

    private boolean sent;
    private boolean approved;
    private boolean payedOut;
    private boolean billsUpdated;

    private Instant createdAt;
    private Instant from;
    private Instant to;
    private Instant sentDate;
    private Instant payedOutDate;
    private Instant billsUpdatedDate;

    private Set<String> relatedProperties;
    private Set<String> relatedParentProperties;
    private Set<String> relatedLandlords;
    private List<XeroInvoicesUpdateResponse.XeroInvoiceUpdateResponse> billsUpdateResult;
}
