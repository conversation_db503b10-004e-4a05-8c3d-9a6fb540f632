package com.rentancy.integrations.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DepositItemsResponse {
    private long depositsOwed;
    private long depositsToRegister;
    private long depositsToTransfer;
    private int receivedDepositManagementItemsCount;
    private int registeredDepositManagementItemsCount;
    private int refundDepositManagementItemsCount;
    private long depositsToProcess;
    private List<DepositManagementBody> receivedDepositManagementItems;
    private List<DepositManagementBody> registeredDepositManagementItems;
    private List<DepositManagementBody> refundDepositManagementItems;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DepositManagementBody {
        private String tenancyId;
        private String propertyReference;
        private String propertyDisplayAddress;
        private String contractReference;
        private String tenantName;
        private String tenantEmail;
        private String tenancyReference;
        private String tenancyProtectionScheme;
        private String tenancyStatus;
        private String tenancyStartDate;
        private Boolean depositReturned;
        private String tenantId;
        private String depositValue;
        private String dueDate;
        private Instant dueDateInstant;
        private String dateRegistered;
        private String contractEndDate;
        private Instant contractEndDateInstant;
        private String lineItemId;
        private String propertyId;
        private boolean inDispute;
        private boolean released;
        private boolean transferred;
        private boolean registered;
    }

}
