package com.rentancy.integrations.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
public class EmailSenderPayload {
    private final String organisationId;
    private final String fromEmail;
    private final String email;
    private final String type;
    private final String subject;
    private final String body;
    private Map<String, String> templateParameters;
    private final List<EmailAttachment> attachments;
    private List<String> emails;
    private boolean simple;

    @Data
    @Builder
    @AllArgsConstructor
    public static class EmailAttachment {
        private final String bucketName;
        private final String key;
        private final String attachmentName;
        private final boolean deleteAfterSending;
    }
}
