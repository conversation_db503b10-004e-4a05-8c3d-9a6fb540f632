package com.rentancy.integrations.pojos;

import lombok.Builder;
import lombok.Data;

import java.time.Instant;

@Data
@Builder
public class Transfer {
    private String id;
    private String transferId;
    private String amount;
    private String fromAccount;
    private String fromTransaction;
    private String organisationId;
    private String accountId;
    private String transactionId;
    private String tenant;

    private boolean hasAttachments;

    private Instant date;
}
