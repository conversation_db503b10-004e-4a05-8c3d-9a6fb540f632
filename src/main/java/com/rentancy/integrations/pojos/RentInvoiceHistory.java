package com.rentancy.integrations.pojos;

import lombok.Builder;
import lombok.Data;

@Data
@Builder(toBuilder = true)
public class RentInvoiceHistory {
    private String id;
    private String xeroId;
    private String periodFromDate;
    private String periodEndDate;
    private String createdAt;
    private String tenancyId;
    private String propertyId;
    private String againstUser;
    private String organisationId;
    private String tenantId;
    private String message;

    private RentHistoryType type;

    private boolean successful;
    private boolean manual;

    public enum RentHistoryType {
        TENANCY_INVOICE,
        LANDLORD_COMMISSION,
        COMMISSION,
        LANDLORD_BILL,
        RECORD,
        FLOAT_ADJUSTMENT_LANDLORD_BILL
    }
}
