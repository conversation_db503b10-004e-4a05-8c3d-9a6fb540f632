package com.rentancy.integrations.pojos;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Property {
    private String id;
    private String addressLine1;
    private String addressLine2;
    private String addressLine3;
    private String postcode;
    private String city;
    private String country;
    private String type;
    private String listingType;
    private String state;
    private String status;
    @JsonProperty("propertyOrganisationId")
    private String organisation;
    @JsonProperty("propertyParentPropertyEntityId")
    private String parentPropertyId;
    private String reference;
    private String propertyOwnerId;
    private String coverImage;
    private String primaryLandlordId;
    private String summary;
    private String description;
    private String startDate;
    private String rentPeriod;
    private String leaseTerm;
    private String furnished;
    private String councilTax;
    private String taxEPCRating;
    private String certificateNumber;
    private String certificateExpirationDate;
    private BigDecimal minimumBalance = BigDecimal.ZERO;
    private BigDecimal openingBalance = BigDecimal.ZERO;
    private int monthlyRent;
    private int securityDeposit;
    private int bedRooms;
    private int bathRooms;
    private int sqmt;
    private boolean archived;
    private boolean splitOwnershipEnabled;

    private List<String> landlords;
    private List<String> managers;
    private List<String> images;
    private List<String> floorPlans;
    private List<Utility> utilities;
    private List<String> amenities;

    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @Data
    public static class Utility {
        private String name;
        private Float metric;
        private String supplierId;
        private boolean checked;
        public static Utility fromDynamoMap(Map<String, AttributeValue> map) {
            return Utility.builder()
                    .name(Optional.ofNullable(map.get("name")).map(AttributeValue::getS).orElse(null))
                    .metric(Optional.ofNullable(map.get("metric"))
                            .map(AttributeValue::getN)
                            .map(Float::parseFloat)
                            .orElse(null))
                    .supplierId(Optional.ofNullable(map.get("supplierId")).map(AttributeValue::getS).orElse(null))
                    .checked(Boolean.parseBoolean(
                            Optional.ofNullable(map.get("checked"))
                                    .map(AttributeValue::getBOOL)
                                    .map(Object::toString)
                                    .orElse("false")))
                    .build();
        }
    }

    public List<String> getLandlordList() {
        if(landlords != null && !landlords.isEmpty()) {
            return landlords;
        }
        if (primaryLandlordId != null) {
            return List.of(primaryLandlordId);
        }
        return List.of();
    }
}
