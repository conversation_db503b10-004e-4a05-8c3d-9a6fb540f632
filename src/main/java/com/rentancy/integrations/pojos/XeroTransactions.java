package com.rentancy.integrations.pojos;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xero.models.accounting.Contact;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import com.rentancy.integrations.pojos.XeroAccounts.XeroAccount;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class XeroTransactions extends XeroBaseResponse {

    @JsonProperty("BankTransactions")
    private List<XeroTransaction> transactions;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class XeroTransaction {
        @JsonProperty("BankTransactionID")
        private String transactionId;

        @JsonProperty("PrepaymentID")
        private String prePaymentId;

        @JsonProperty("OverpaymentID")
        private String overPaymentId;

        @JsonProperty("Type")
        private String type;

        @JsonProperty("Reference")
        private String reference;

        @JsonProperty("Date")
        private String date;

        @JsonProperty("UpdatedDateUTC")
        private String updatedDate;

        @JsonProperty("CurrencyCode")
        private String currency;

        @JsonProperty("CurrencyRate")
        private String currencyRate;

        @JsonProperty("Url")
        private String url;

        @JsonProperty("Status")
        private String status;

        @JsonProperty("LineAmountTypes")
        private String lineAmountTypes;

        @JsonProperty("SubTotal")
        private String subTotal;

        @JsonProperty("TotalTax")
        private String totalTax;

        @JsonProperty("Total")
        private String total;

        @JsonProperty("IsReconciled")
        private boolean reconciled;

        @JsonProperty("HasAttachments")
        private boolean hasAttachments;

        @JsonProperty("Contact")
        private Contact contact;

        @JsonProperty("BankAccount")
        private XeroAccount account;

        @JsonProperty("LineItems")
        private List<XeroInvoices.XeroInvoiceLineItem> lineItems;

        @JsonProperty("BatchPayment")
        private BatchPayment batchPayment;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BatchPayment {
        @JsonProperty("BatchPaymentID")
        private String batchPaymentId;
    }

    public XeroTransactions append(XeroTransactions other) {
        if(this.getTransactions() == null) this.setTransactions(new ArrayList<>());
        if(this.getWarnings() == null) this.setWarnings(new ArrayList<>());

        Optional.ofNullable(other.getTransactions()).ifPresent(transactions -> this.getTransactions().addAll(transactions));
        Optional.ofNullable(other.getWarnings()).ifPresent(warnings -> this.getWarnings().addAll(warnings));
        return this;
    }
}
