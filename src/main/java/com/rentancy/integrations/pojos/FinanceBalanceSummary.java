package com.rentancy.integrations.pojos;

import java.math.BigDecimal;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class FinanceBalanceSummary {
    private final FinanceSummary income;
    private final FinanceSummary expenses;

    private final FinanceSummary depositIncome;
    private final FinanceSummary depositExpense;

    private final BigDecimal balance;
    private final BigDecimal depositBalance;
    private final BigDecimal minimumBalance;
}
