package com.rentancy.integrations.pojos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LandlordBillBulkPayoutRequest {
    private String cognitoId;
    private String endDate;
    private boolean sendStatement;

    private Set<LandlordBillBulkPayoutItem> landlordBillIds;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LandlordBillBulkPayoutItem {
        private String startDate;
        private String landlordBillId;
    }
}
