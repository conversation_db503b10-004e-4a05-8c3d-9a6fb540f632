package com.rentancy.integrations.pojos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

@Data
public abstract class XeroBaseResponse {
    @JsonProperty("Id")
    private String id;

    @JsonProperty("Status")
    private String status;

    @JsonProperty("DateTimeUTC")
    private String dateTimeUTC;

    @JsonProperty("ProviderName")
    private String providerName;

    @JsonProperty("Warnings")
    private List<Warning> warnings;

    @Data
    public static class Warning {
        @JsonProperty("Message")
        private String message;
    }
}