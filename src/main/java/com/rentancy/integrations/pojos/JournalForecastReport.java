package com.rentancy.integrations.pojos;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
public class JournalForecastReport {
    private String date;
    private String startDate;
    private int periodLengthInMonths;

    private List<JournalForecastReportTenancyItem> items;

    @Data
    @Builder
    public static class JournalForecastReportTenancyItem {
        private String landlordName;
        private String propertyReference;
        private String tenancyReference;
        private String tenantName;
        private String tenancyStatus;
        private String tenancyPeriod;
        private String tenancyStartDate;
        private String tenancyEndDate;
        private BigDecimal tenantBalanceAmountHeld;
        private BigDecimal monthlyRentAmount;

        private List<JournalForecastReportTenancyItemMonth> periodMonths;
    }

    @Data
    @Builder
    public static class JournalForecastReportTenancyItemMonth {
        private String monthName;

        private BigDecimal collected;
        private BigDecimal monthlyRent;
        private BigDecimal forecast;
    }
}
