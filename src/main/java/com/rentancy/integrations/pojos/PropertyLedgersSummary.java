package com.rentancy.integrations.pojos;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class PropertyLedgersSummary {

    private BigDecimal openingBalanceAmount;
    private BigDecimal closingBalanceAmount;
    private BigDecimal debitTotalAmount;
    private BigDecimal creditTotalAmount;
    private BigDecimal arrearsTotalAmount;
    private BigDecimal overPayment;

    private List<PropertyLedgersLineItem> items;

    private PropertyLedgersTableType tableType;
    private String startDate;
    private String endDate;
    private String propertyAddress;

    @Data
    @Builder
    public static class PropertyLedgersLineItem {
        private String date;
        private Instant timeInstant;
        private PropertyLedgersLineItemType lineItemType;
        private String description;
        private String propertyAddress;
        private String tenancyReference;
        private BigDecimal dueAmount;
        private BigDecimal debitAmount;
        private BigDecimal creditAmount;
        private BigDecimal heldAmount;
        private String xeroNumber;
    }

    public enum PropertyLedgersTableType {
        LANDLORD,
        TENANT,
        DEPOSIT,
    }

    public enum PropertyLedgersLineItemType {
        INVOICE,
        PAYMENT,
        TRANSFER,
        EXPENSE,
    }
}
