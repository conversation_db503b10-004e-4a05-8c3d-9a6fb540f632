package com.rentancy.integrations.pojos;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
public abstract class XeroModel {
    protected String id;
    protected String type;
    protected String status;
    protected String contactId;
    protected boolean hasAttachments;
    protected Instant date;
    protected String reference;
    protected String currency;
    protected String lineAmountTypes;
    protected String subTotal;
    protected String totalTax;
    protected String total;
    protected String organisation;
    protected Instant updatedAt;
}
