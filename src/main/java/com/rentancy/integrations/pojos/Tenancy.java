package com.rentancy.integrations.pojos;

import com.rentancy.integrations.servicies.rent.NextInvoiceDate;
import com.rentancy.integrations.servicies.rent.NextInvoiceDateCalculator.RentPeriod;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Set;

import static java.time.ZoneOffset.UTC;
import static java.time.temporal.ChronoUnit.DAYS;

@Builder(toBuilder = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Tenancy {
    private String id;
    private String number;
    private String address;
    private String title;
    private TenancyPeriod period;
    private String reference;
    private DepositStatus depositStatus;
    private String rentNote;
    private TenancyType type;
    private String currency;
    private String agent;
    private String mutator;
    private String primaryTenant;
    private String status;
    private String lastAction;
    private String property;
    private String organisation;
    private String invoiceStartDate;
    private String startDate;
    private String endDate;
    private String tenancySettingsId;
    private String rentReview;
    private String primaryTenantName;

    private String depositReference;
    private boolean depositReturned;
    private boolean depositReleased;
    private boolean depositRegistered;
    private boolean depositTransferred;
    private boolean depositDisputed;
    private String dateDepositRegistered;
    private String certificateNumber;
    private String depositProtectionScheme;

    private Integer paymentDay;
    private Integer deposit;
    private Integer rent;
    private Integer reservation;
    private Integer openingBalance;
    private Integer area;

    private Boolean archived;
    private Boolean accounting;
    private Boolean landlordVat;
    private Boolean addLandlordDetails;
    private Boolean attachInvoicesToContract;
    private Boolean shareInvoicesToContract;
    private Boolean shareInvoicesWithLandlord;
    private boolean dontCollectRent;
    private boolean enableJournal;
    private boolean enableProRataJournal;

    private List<String> tenants;
    private List<String> teamMembers;
    private List<String> landlords;
    private List<String> guarantors;
    private List<String> tags;
    private List<BreakClauseItem> breakClauseItems;

    private Settings settings;

    public boolean shouldAutoInvoiceForDay(ZonedDateTime now, NextInvoiceDate nextInvoiceDate) {
        if (!"ACTIVE".equals(status) && !"PERIODIC".equals(status)) {
            return false;
        }

        if (!settings.isAutoInvoiceRent() || nextInvoiceDate == null || nextInvoiceDate.getSend() == null)  {
            return false;
        }

        if (invoiceStartDate == null) {
            return false;
        }

        if ("ACTIVE".equals(status) && paymentWouldOccurAfterEndDate(nextInvoiceDate)) {
            return false;
        }

        var expected = now.toInstant().truncatedTo(DAYS);
        return expected.equals(nextInvoiceDate.getSend().toInstant().truncatedTo(DAYS));
    }

    /**
     * to be consistent with current collectAutoInvoiceTenancies implementation
     * for PERCENTAGE_OF_AMOUNT_RECEIVED tenancies commission should not be auto-invoiced
     * hence the same condition is replicated here
     * such commission bills are created only when we synchronize rent invoices back from Xero to our system
     * @see com.rentancy.integrations.servicies.XeroDataMapperImpl#mapInvoice mapInvoice
     */
    public boolean shouldRaiseCommission(ZonedDateTime now, NextInvoiceDate nextInvoiceDate) {
        return shouldAutoInvoiceForDay(now, nextInvoiceDate) && !"NONE".equals(settings.feeType) &&
                !"PERCENTAGE_OF_AMOUNT_RECEIVED".equals(settings.feeType);
    }

    public boolean paymentWouldFallOutsideTenancyDuration(NextInvoiceDate nextInvoiceDate) {
        if(nextInvoiceDate == null) return true;
        return paymentWouldOccurAfterEndDate(nextInvoiceDate) || invoiceStartDate == null;
    }

    private boolean paymentWouldOccurAfterEndDate(NextInvoiceDate nextInvoiceDate) {
        var nextDueDate = nextInvoiceDate.getDue().toInstant();
        return endDate != null && nextDueDate.atZone(UTC).isAfter(Instant.parse(endDate).atZone(UTC).truncatedTo(DAYS));
    }

    public Settings settingsWithRentPaymentRecorded(ZonedDateTime paymentDate, RentPeriod currentRentPeriod) {
        return this.settings.toBuilder()
                .lastPayment(paymentDate.toInstant())
                .lastPaymentCoveredTo(currentRentPeriod.endTime().toInstant())
                .build();
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BreakClauseItem {
        private String who;
        private String date;
        private String noticeDate;
    }

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Settings {
        private String id;
        private BigDecimal rentCommission;
        private Integer fixedFee;
        private List<AutoJournalArrears> autoJournalArrears;
        private String feeType;
        private Integer percentageFee;
        private String currency;
        private String tenancyId;
        private String firstJournalRunDate;
        private String organisationId;
        private String lastJournalRunDate;
        private Integer invoiceRentInAdvanceDays;
        private boolean autoInvoiceRent;
        private boolean sendInvoiceToTenant;
        private boolean addCommissionBillVat;
        private Organisation.LedgerCode tenancyLedgerCode;
        private Organisation.LedgerCode feeLedgerCode;
        private List<FundDistribution> fundDistribution;
        private Instant lastPaymentCoveredTo;
        private Instant lastPayment;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AutoJournalArrears {
        private Integer amount;
        private String ledgerCode;
        private String ledgerName;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TenancyLedgerCode {
        private String code;
        private String name;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FundDistribution {
        private String ledger;
        private int amount;
    }

    public enum TenancyPeriod {
        DAILY,
        WEEKLY,
        TWO_WEEKLY,
        MONTHLY,
        QUARTERLY,
        UK_QUARTERLY,
        SIX_MONTHLY,
        ANNUALLY,
        BI_ANNUALLY,
        FIVE_YEAR,
        TEN_YEAR,
        FIFTEEN_YEAR,
        TWENTY_YEAR,
        TWENTY_FIVE_YEAR
    }

    public enum DepositStatus {
        RECEIVING,
        REGISTERING,
        REFUNDING,
        REFUNDED
    }

    public enum TenancyType {
        NA,
        FIRM_TERM,
        DPS_CUSTODIAL,
        DPS_INSURANCE,
        MY_DEPOSITS,
        TDS_CUSTODIAL,
        TDS_INSURANCE,
        REPOSIT,
        DISPUDE_SERVICE_CUSTODIAL,
        INSURANCE,
        HELD_BY_AGENT,
        HELD_BY_LANDLORD,
        AST,
        ASSURED,
        CONTRACTUAL,
        COMMON_LAW,
        LETTING,
        LICENSE,
        COMMERCIAL,
        SERVICE_CHARGE,
        HOLIDAYLET,
        NONHOUSINGACT,
        SALE,
        LEASE,
        GROUND_RENT,
        PROJECT,
        RESIDENTIAL,
        MONTH_TO_MONTH,
        SUBLET,
        VACATION_STAY,
        RENEWAL,
        JOINT,
        RENT_TO_OWN,
        APARTMENT,
        RESIDENTIAL_CONTRACT,
        SECTION_8,
        DISPUTE_SERVICE_CUSTODIAL,
    }
}
