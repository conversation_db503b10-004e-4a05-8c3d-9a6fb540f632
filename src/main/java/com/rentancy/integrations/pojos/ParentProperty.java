package com.rentancy.integrations.pojos;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class ParentProperty {
    private String id;
    private String name;
    private String type;
    private String reference;
    private String addressLine1;
    private String city;
    private String postcode;
    private String country;
    private String notes;
    private String primaryLandlordId;
    private String organisationId;

    private boolean archived;
    private boolean splitOwnershipEnabled;

    private List<String> landlords;
    private List<String> managers;
}
