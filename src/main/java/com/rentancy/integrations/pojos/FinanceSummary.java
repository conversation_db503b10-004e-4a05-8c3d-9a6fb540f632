package com.rentancy.integrations.pojos;

import com.rentancy.integrations.pojos.Invoice.LineItem;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class FinanceSummary {
    private final BigDecimal paidAmount;
    private final BigDecimal raisedNotPaidAmount;
    private final BigDecimal inArrearsAmount;
    private final BigDecimal totalAmount;

    private final List<LineItem> paidLineItems;
    private final List<LineItem> raisedNotPaidLineItems;
    private final List<LineItem> inArrearsLineItems;
    private final List<LineItem> allLineItems;

    /**
     * Tenancy reference -> paid line items
     */
    private Map<String, List<LineItem>> tenancyPaidLineItems;

    /**
     * Tenancy reference -> in arrears line items
     */
    private Map<String, List<Invoice.LineItem>> tenancyInArrearsLineItems;
}
