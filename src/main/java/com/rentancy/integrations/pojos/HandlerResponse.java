package com.rentancy.integrations.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.checkerframework.checker.units.qual.A;

@Data
@Builder
@AllArgsConstructor
public class HandlerResponse {
    public static final HandlerResponse OK = new HandlerResponse(200);
    public static final HandlerResponse ERROR = new HandlerResponse(500);
    public static final HandlerResponse NOT_FOUND = new HandlerResponse(404);
    public static final HandlerResponse FORBIDDEN = new HandlerResponse(403);
    public static final HandlerResponse UNAUTHORIZED = new HandlerResponse(401);
    public static final HandlerResponse PARTIAL_SUCCESS = new HandlerResponse(207);

    private final int status;
}
