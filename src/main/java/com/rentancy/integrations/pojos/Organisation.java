package com.rentancy.integrations.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Organisation {
    private String id;
    private String name;
    private String logo;
    private String adminUser;
    private String botUser;
    private String invoiceConversation;
    private String reportConversation;
    private String currency;
    private String addressLine1;
    private String addressLine2;
    private Integration.IntegrationService connectedFinanceIntegration;
    private String addressLine3;
    private String state;
    private String city;
    private String postcode;
    private String country;
    private String phone;
    private String customEmail;
    private String website;
    private String whatsAppAddress;
    private String twilioAccountSID;
    private String twilioAuthToken;
    private String xeroId;
    private String tenancySettingsId;
    private String defaultCountryTaxBotId;
    private String landlordStatementTemplateType;
    private String balanceTransferContactUserId;
    private String createdAt;
    private Type type;
    private boolean clientBatchPaymentsOnDemand;
    private String commissionBillVatNumber;
    private boolean addCommissionBillVat;
    private boolean addCommissionBillVatInclusive;
    private boolean overseasResidentBillVAT;
    private String payoutStatementVersion;
    private boolean payer;
    private String utmCustomerAttributionSource;
    private String depositSchemeLedgerContactUserId;
    private String openingBalanceContactUserId;

    private boolean enableJournal;
    // 0 means journal is not enabled
    private int journalDate;
    private JournalPeriod journalPeriod;

    private List<Integer> clientBatchPayments;
    private List<LedgerCode> ledgerCodes;
    private List<String> incomeLedgerCodeNames;
    private List<InvoiceTemplate> invoiceTemplates;
    private List<TrackingCategory> trackingCategories;
    private String expiredFlag;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LedgerCode {
        private String code;
        private String displayName;
        private String name;
    }

    @Data
    @AllArgsConstructor
    public static class InvoiceTemplate {
        private String type;
        private String templateName;
        private String rentDescription;
    }

    @Data
    @AllArgsConstructor
    public static class TrackingCategory {
        private String name;
        private String xeroName;
    }

    public enum Type {
        LANDLORD,
        AGENT,
        OCCUPIER
    }

    public enum JournalPeriod {
        PRIOR_MONTH,
        CURRENT_MONTH
    }
}
