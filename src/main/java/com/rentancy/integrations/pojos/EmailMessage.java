package com.rentancy.integrations.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder(toBuilder = true)
public class EmailMessage {
    private String organisationId;
    private String parentId;
    private String parentType;
    private String subject;
    private String body;
    private String to;
    private String from;

    private boolean reply;
    private int status;

    private List<EmailAttachment> attachments;

    @Data
    @AllArgsConstructor
    public static class EmailAttachment {
        private String documentId;
    }
}
