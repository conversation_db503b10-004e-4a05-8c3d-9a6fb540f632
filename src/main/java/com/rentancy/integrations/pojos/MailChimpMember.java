package com.rentancy.integrations.pojos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

/**
 * integrations
 *
 * <AUTHOR>
 * @since 28/03/2021
 */
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MailChimpMember {

    @JsonProperty("email_address")
    private String emailAddress;
    @JsonProperty("status")
    private String status;
    @JsonProperty("status_if_new")
    private String statusIfNew;
    @JsonProperty("merge_fields")
    private Map<String, Object> mergeFields;
    @JsonProperty("tags")
    private List<String> tags;
    @JsonProperty(value = "email_type", defaultValue = "html")
    private String emailType;
}
