package com.rentancy.integrations.pojos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.rentancy.integrations.pojos.XeroAccounts.XeroAccount;
import com.rentancy.integrations.pojos.XeroInvoices.XeroInvoice;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class XeroPayments extends XeroBaseResponse {

    @JsonProperty("Payments")
    private List<XeroPayment> payments;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class XeroPayment {

        @JsonProperty("PaymentID")
        private String paymentId;

        @JsonProperty("BatchPaymentID")
        private String batchPaymentId;

        @JsonProperty("Date")
        private String date;

        @JsonProperty("BankAmount")
        private String bankAmount;

        @JsonProperty("Amount")
        private String amount;

        @JsonProperty("Reference")
        private String reference;

        @JsonProperty("CurrencyRate")
        private String currencyRate;

        @JsonProperty("PaymentType")
        private String paymentType;

        @JsonProperty("Status")
        private String status;

        @JsonProperty("UpdatedDateUTC")
        private String updatedDateUTC;

        @JsonProperty("HasAccount")
        private boolean hasAccount;

        @JsonProperty("IsReconciled")
        private boolean isReconciled;

        @JsonProperty("Account")
        private XeroAccount account;

        @JsonProperty("Invoice")
        private XeroInvoice invoice;
    }
}
