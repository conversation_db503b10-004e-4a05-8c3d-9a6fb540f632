package com.rentancy.integrations.pojos;

import java.time.Instant;
import lombok.Builder;
import lombok.Data;

@Data
@Builder(toBuilder = true)
public class OverPayment {
    private String id;
    private String overPaymentId;
    private String type;
    private String reference;
    private String remainingCredit;
    private String contactId;
    private OverPaymentStatuses status;
    private String lineAmountTypes;
    private String subTotal;
    private String totalTax;
    private String total;
    private String currency;
    private String organisationId;

    private Instant date;


    public enum OverPaymentStatuses {
        AUTHORISED,
        PAID,
        // We don't store this type in dynamo, but delete it instead
        VOIDED,
    }
}
