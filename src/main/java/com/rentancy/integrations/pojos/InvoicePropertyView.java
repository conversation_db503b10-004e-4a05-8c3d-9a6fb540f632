package com.rentancy.integrations.pojos;


import com.rentancy.integrations.servicies.enhanced.entity.InvoiceProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InvoicePropertyView {

    private String id;
    private String invoicePropertyInvoiceId;
    private String invoicePropertyOrganisationId;
    private String invoicePropertyPropertyId;
    private Property property;

    public InvoicePropertyView(InvoiceProperty parent, Property property) {
        this.id = parent.getId();
        this.invoicePropertyInvoiceId = parent.getInvoicePropertyInvoiceId();
        this.invoicePropertyOrganisationId = parent.getInvoicePropertyOrganisationId();
        this.invoicePropertyPropertyId = parent.getInvoicePropertyPropertyId();
        this.property = property;
    }

}
