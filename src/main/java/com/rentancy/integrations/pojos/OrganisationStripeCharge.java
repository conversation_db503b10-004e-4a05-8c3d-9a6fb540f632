package com.rentancy.integrations.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrganisationStripeCharge {
    private String id;
    private String organisationId;
    private String stripePaymentIntentId;
    private String number;
    private String createdAt;
    private String updatedAt;
    private String occuredAt;
    private String currency;
    private String eventType;
    private String chargeType;
    private String data;
    private String error;
    private String errorMessage;
    private int amount;
    private int propertyCount;
    private boolean success;

}
