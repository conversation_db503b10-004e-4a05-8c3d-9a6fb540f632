package com.rentancy.integrations.pojos;

import com.rentancy.integrations.pojos.Tenancy.TenancyType;
import java.math.BigDecimal;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder(toBuilder = true)
public class IncomeArrearsSummary {

    private final List<IncomeArrearsSummaryItem> items;

    private final BigDecimal invoiceTotalAmount;
    private final BigDecimal arrearsTotalAmount;
    private final long totalArrearsInvoiceCount;
    private final int page;
    private final int limit;
    private final int incomeArrearsSummaryItemCount;

    private final String organisationName;

    private final String appliedFilter;
    private final ArrearsSortBy sortedBy;

    public enum ArrearsSortBy {
        INVOICE_REFERENCE,
        TENANCY_REFERENCE,
        TENANT_NAME,
        LANDLORD_NAME,
        PROPERTY_REFERENCE,
        PROPERTY_ADDRESS,
        INVOICE_DUE_DATE,
        DAYS_IN_ARREARS,
        TOTAL_INVOICE_AMOUNT,
        ARREARS_AMOUNT,
        GUARANTOR,
    }

    public enum ArrearsSortOrder {
        ASC,
        DESC
    }

    @Data
    @Builder
    public static class IncomeArrearsSummaryItem {
        private final String invoiceReference;
        private final String tenancyReference;
        private final String tenantName;
        private final String tenantEmail;
        private final List<User.Phone> tenantPhoneNumbers;
        private final String tenantImageId;
        private final String landlordImageId;
        private final String propertyImageId;
        private final String landlordName;
        private final boolean hasGuarantors;
        private final String propertyReference;
        private final String propertyAddress;
        private final String invoiceDueDate;
        private final String invoiceDescription;
        private final long daysInArrears;
        private final BigDecimal totalInvoiceAmount;
        private final BigDecimal arrearsAmount;
        private final List<User> guarantors;
        private final long arrearsInvoiceCount;
        private final boolean fullyManagedTenancy;
        private final String tenancyStatus;
        private final TenancyType tenancyType;
        private final String tenancyStartDate;
        private final String tenancyEndDate;
        private final String propertyId;
        private final String tenancyId;

        private final List<IncomeArrearsSummaryItem> arrearsInvoices;
    }
}
