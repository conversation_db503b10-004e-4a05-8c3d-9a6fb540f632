package com.rentancy.integrations.pojos;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
@Builder(toBuilder = true)
public class TenancySummary {
    private final BigDecimal paidIncome;
    private final BigDecimal totalIncome;
    private final BigDecimal inArrears;
    private final BigDecimal paidDeposit;
    private final BigDecimal raisedNotPaid;

    /**
     * Tenancy reference -> paid line items
     */
    private Map<String, List<Invoice.LineItem>> tenancyPaidLineItems;

    /**
     * Tenancy reference -> in arrears line items
     */
    private Map<String, List<Invoice.LineItem>> inArrearsLineItems;
}
