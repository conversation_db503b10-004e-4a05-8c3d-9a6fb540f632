package com.rentancy.integrations.pojos;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode
public class BacsReportPayoutRequest {
    private String bank;
    private String startDate;
    private String endDate;
    private String organisationId;
    private String senderId;

    public BacsReportPayoutRequest(String bank, String startDate, String endDate, String organisationId, String senderId) {
        this.bank = bank;
        this.startDate = startDate;
        this.endDate = endDate;
        this.organisationId = organisationId;
        this.senderId = senderId;
    }

    public BacsReportPayoutRequest() {
    }


}
