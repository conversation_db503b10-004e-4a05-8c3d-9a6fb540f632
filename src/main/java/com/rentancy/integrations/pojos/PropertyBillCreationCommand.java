package com.rentancy.integrations.pojos;

import lombok.Data;

import java.util.List;

@Data
public class PropertyBillCreationCommand {
    private String fromUserId;
    private String propertyId;
    private String date;
    private String dueDate;
    private String taxType;
    private String status;
    private boolean parentProperty;
    private List<LineItem> lineItems;

    @Data
    public static class LineItem {
        private String description;
        private double quantity;
        private double unitPrice;
        private String ledgerCode;
        private String taxType;
    }
}
