package com.rentancy.integrations.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
public class XeroInvoicesUpdateResponse {
    private List<XeroInvoiceUpdateResponse> invoices;

    @Data
    @AllArgsConstructor
    public static class XeroInvoiceUpdateResponse {
        private String invoiceId;
        private String status;
        private User user;
        private BigDecimal amountDue;
    }
}
