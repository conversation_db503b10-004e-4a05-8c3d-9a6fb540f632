package com.rentancy.integrations.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LandlordBillsRequestV3 {
    private String id;
    private List<LandlordBillsRequestV3Item> items;
    private List<String> lineItemIds;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class LandlordBillsRequestV3Item {
        private String landlordId;
        private List<LandlordBillsRequestV3PropertyItem> propertyItems;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class LandlordBillsRequestV3PropertyItem {
        private String propertyId;
        private BigDecimal amount;
        private BigDecimal floatAdjustmentAmount;
    }
}
