package com.rentancy.integrations.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ClientSummary {
    private String landlordId;
    private String landlordName;

    private BigDecimal openingBalance;
    private BigDecimal income;
    private BigDecimal expenses;
    private BigDecimal closingBalance;
    private BigDecimal minimumBalance;
    private BigDecimal dueToClient;

    private int propertyCount;
}
