package com.rentancy.integrations.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
public class PropertySummary {
    private final BigDecimal inArrears;
    private final BigDecimal income;
    private final BigDecimal paidDeposit;
    private final BigDecimal expenses;
    private final BigDecimal minimumBalance;
    private final BigDecimal balance;
    private final BigDecimal incomeRaisedNotPaid;
    private final BigDecimal billsOutstanding;
    private final BigDecimal openingBalance;
    private final BigDecimal clientPayable;
    private final long activeContracts;
    private final int totalArea;
    private final long monthsRemaining;

    public static PropertySummary zero() {
        return PropertySummary.builder()
                .income(BigDecimal.ZERO)
                .expenses(BigDecimal.ZERO)
                .openingBalance(BigDecimal.ZERO)
                .clientPayable(BigDecimal.ZERO)
                .balance(BigDecimal.ZERO)
                .minimumBalance(BigDecimal.ZERO)
                .build();
    }
}
