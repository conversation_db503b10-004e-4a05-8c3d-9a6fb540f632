package com.rentancy.integrations.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AllClientSummary {
    private List<ClientSummary> clientSummaries;

    private BigDecimal totalOpeningBalance;
    private BigDecimal totalIncome;
    private BigDecimal totalExpenses;
    private BigDecimal totalClosing;
    private BigDecimal totalMinimumBalance;
    private BigDecimal totalDueToClient;

}
