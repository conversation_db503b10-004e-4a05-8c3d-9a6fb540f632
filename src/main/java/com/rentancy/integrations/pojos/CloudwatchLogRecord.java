package com.rentancy.integrations.pojos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CloudwatchLogRecord {
    private String messageType;
    private String owner;
    private String logGroup;
    private String logStream;

    private List<String> subscriptionFilters;
    private List<LogEvent> logEvents;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LogEvent {
        private String id;
        private long timestamp;
        private String message;
    }
}
