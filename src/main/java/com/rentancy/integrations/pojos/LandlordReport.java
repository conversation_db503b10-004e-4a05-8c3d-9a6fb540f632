package com.rentancy.integrations.pojos;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder(toBuilder = true)
public class LandlordReport {
    private final String date;
    private final String landlordName;
    private final String landlordAddress;
    private final String propertyAddress;
    private final String propertyAddress1;
    private final String propertyAddress2;
    private final String propertyAddress3;
    private final String postCode;
    private final String organisationName;
    private final String logoUrl;
    private final String startDate;
    private final String endDate;
    private final String organisationVat;

    private final String landlordBankName;
    private final String landlordAccountName;
    private final String landlordAccountNumber;
    private final String landlordSortCode;

    private final String reportNumber;

    private final String landlordStatementTemplateType;

    private final Property property;
    private final BigDecimal openingBalance;
    private final BigDecimal closingBalance;
    private final BigDecimal minimumBalance;
    private final BigDecimal clientPayable;
    private final BigDecimal periodTotal;
    private final BigDecimal periodVat;
    private final BigDecimal periodSubtotal;

    private final BigDecimal totalIncomeSubtotal;
    private final BigDecimal totalIncomeVat;
    private final BigDecimal totalIncomeTotal;

    private final BigDecimal totalExpenditureSubtotal;
    private final BigDecimal totalExpenditureVat;
    private final BigDecimal totalExpenditureTotal;

    private final BigDecimal subTotalSubTotal;
    private final BigDecimal subTotalVat;
    private final BigDecimal subTotalTotal;

    private final BigDecimal totalRetentionSubtotal;
    private final BigDecimal totalRetentionVat;
    private final BigDecimal totalRetentionTotal;

    private final List<LandlordReportItem> rentalIncomeInvoices;
    private final List<LandlordReportItem> managementInvoices;
    private final List<LandlordReportItem> allExpendatureInvoices;
    private final List<LandlordReportItem> propertyFloats;
    private final List<LandlordReportTenancy> tenancies;
    private final List<LandlordReportExpenses> propertyExpenses;
    private final List<LandlordReportItem> inArrears;
}
