package com.rentancy.integrations.pojos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

// Could be both property and parent property finance summary
// Could probably be turned into a DynamoDB object for easier mapping and serialization https://aws.amazon.com/blogs/developer/storing-java-objects-in-amazon-dynamodb-tables/
@Data
@Builder
public class PropertyFinanceSummary {
    private final String name;
    @Deprecated
    private final String budgetId;
    private final String reference;
    private final BigDecimal inArrears;
    private final BigDecimal income;
    private final BigDecimal paidDeposit;
    private final BigDecimal expenses;

    @JsonProperty("float") // float is a reserved word in java
    private final BigDecimal minimumBalance;
    private final BigDecimal dueToClient;
    private final BigDecimal balance;
    private final BigDecimal reservers;
    private final BigDecimal suspense;
    private final BigDecimal incomeRaisedNotPaid;
    private final BigDecimal billsOutstanding;
    private final long activeContracts;
    private final int totalArea;
    private final long monthsRemaining;
    private final String currency;
    private final BigDecimal openingBalance;
}
