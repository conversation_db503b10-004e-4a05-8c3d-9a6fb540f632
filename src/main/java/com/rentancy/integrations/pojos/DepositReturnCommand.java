package com.rentancy.integrations.pojos;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class DepositReturnCommand {
    private String organisationId;
    private String lineItemId;
    private BigDecimal depositTotal;
    private BigDecimal netTotal;
    private List<DepositDeduction> deductions;

    @Data
    public static class DepositDeduction {
        private String supplierId;
        private String description;
        private BigDecimal amount;
    }
}
