package com.rentancy.integrations.pojos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class XeroAccounts {

    @JsonProperty("Accounts")
    private List<XeroAccount> accounts;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class XeroAccount {
        @JsonProperty("AccountID")
        private String accountId;

        @JsonProperty("Code")
        private String code;

        @JsonProperty("Name")
        private String name;

        @JsonProperty("Type")
        private String type;

        @JsonProperty("Status")
        private String status;
    }
}
