package com.rentancy.integrations.pojos;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
public class LandlordReportSummary {
    private final BigDecimal arrears;
    private final BigDecimal landlordFunds;
    private final BigDecimal netIncomeInPeriod;
    private final BigDecimal expensesRaised;
    private final BigDecimal expensesOutstanding;
    private final BigDecimal minimumBalance;
    private final BigDecimal owedOrDueToLandlord;
    private final BigDecimal openingBalance;
    private final BigDecimal landlordPaidPayments;
}
