package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.SQSEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.HandlerResponse;
import com.rentancy.integrations.pojos.XeroUpdateMessage;
import com.rentancy.integrations.servicies.XeroFactory;
import com.rentancy.integrations.servicies.XeroService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.rentancy.integrations.util.JSONUtils.deserializePayload;

public class XeroUpdateHandler implements RequestHandler<SQSEvent, HandlerResponse> {

    private static final Logger log = LogManager.getLogger(XeroUpdateHandler.class);

    private final XeroService xeroService;

    public XeroUpdateHandler() {
        this.xeroService = XeroFactory.getXeroService(new ConfigImpl());
    }

    @Override
    public HandlerResponse handleRequest(SQSEvent input, Context context) {
        try {
            var records = input.getRecords();
            for (var record : records) {
                log.info(record.getBody());

                var message = deserializePayload(record.getBody(), XeroUpdateMessage.class);
                var category = message.getCategory();

                switch (category) {
                    case CONTACT:
                        xeroService.handleContactUpdate(message.getUser());
                        break;
                    default:
                        log.info("Invalid category: " + category);
                }
            }

            return HandlerResponse
                    .builder()
                    .status(200)
                    .build();
        } catch (Exception e) {
            log.error("Error", e);
            return HandlerResponse
                    .builder()
                    .status(500)
                    .build();
        }
    }
}
