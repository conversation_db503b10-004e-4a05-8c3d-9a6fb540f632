package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.servicies.ReportFactory;
import com.rentancy.integrations.servicies.ReportService;
import com.rentancy.integrations.util.CognitoUtils;
import com.rentancy.integrations.util.ResponseUtils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Objects;
import java.util.Optional;

import static com.rentancy.integrations.util.JSONUtils.toApiResponse;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;
import static org.springframework.http.HttpHeaders.ORIGIN;

public class TaskReportPdfHandler implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {

    private static final Logger log = LogManager.getLogger(TaskReportPdfHandler.class);

    private final ReportService reportService;

    public TaskReportPdfHandler() {
        this.reportService = ReportFactory.getReportService(new ConfigImpl());
    }

    @Override
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        var response = new APIGatewayV2ProxyResponseEvent();
        var source = input.getSource();

        if (Objects.nonNull(source) && source.equals("WARMUP")) {
            log.info("Warming lambda");
            return response;
        }

        var currentOrganisation = CognitoUtils.getCurrentOrganisation(input);
        var params = input.getQueryStringParameters();
        var organisationId = params.get("organisationId");
        var startDate = params.get("startDate");
        var endDate = params.get("endDate");
        var format = params.get("format");
        var sortBy = params.get("sortBy");
        var userId = CognitoUtils.getCurrentUsername(input);

        log.info("query params {}", params);
        var requestHeaders = input.getHeaders();
        var origin = Optional.ofNullable(requestHeaders.get(ORIGIN)).orElse(requestHeaders.get(ORIGIN.toLowerCase()));
        var headers = ResponseUtils.buildHeader(origin);
        headers.add(CONTENT_TYPE, "application/pdf;");

        if (Objects.nonNull(currentOrganisation) && !currentOrganisation.equals(organisationId)) {
            response.setHeaders(headers.toSingleValueMap());
            response.setStatusCode(403);
            return response;
        }

        try {
            var data = reportService.generateTaskReportPdf(organisationId, startDate, endDate, format, sortBy, userId);

            response.setStatusCode(200);
            response.setBody(toApiResponse(data));
            response.setIsBase64Encoded(true);
        } catch (Exception e) {
            log.error("task report error",  e);
            response.setStatusCode(500);
        }

        response.setHeaders(headers.toSingleValueMap());
        return response;
    }
}

