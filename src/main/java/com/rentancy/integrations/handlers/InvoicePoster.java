package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.CloudWatchLogsEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.servicies.PortfolioFactory;
import com.rentancy.integrations.servicies.PortfolioService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class InvoicePoster implements RequestHandler<CloudWatchLogsEvent, Void> {

    private static final Logger log = LogManager.getLogger(InvoicePoster.class);

    private final PortfolioService service;

    public InvoicePoster() {
        this.service = PortfolioFactory.getPortfolioService(new ConfigImpl());
    }

    @Override
    public Void handleRequest(CloudWatchLogsEvent input, Context context) {
        try {
            this.service.postInvoiceHistory();
        } catch (Exception e) {
            log.error("Failed to handle event", e);
        }

        return null;
    }
}
