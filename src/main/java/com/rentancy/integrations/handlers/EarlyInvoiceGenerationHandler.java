package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.exceptions.EntityNotFoundException;
import com.rentancy.integrations.exceptions.EntityNotInOrganizationScopeException;
import com.rentancy.integrations.exceptions.InvalidRequestException;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.pojos.HandlerErrorResponseBody;
import com.rentancy.integrations.servicies.IntegrationServiceImpl;
import com.rentancy.integrations.servicies.LambdaClient;
import com.rentancy.integrations.servicies.SQSClient;
import com.rentancy.integrations.servicies.XeroOauth2Client;
import com.rentancy.integrations.servicies.persistence.DDBClient;
import com.rentancy.integrations.servicies.persistence.IntegrationRepository;
import com.rentancy.integrations.servicies.persistence.OrganisationRepository;
import com.rentancy.integrations.servicies.persistence.TenancyRepository;
import com.rentancy.integrations.servicies.persistence.config.TenancyRepositoryConfig;
import com.rentancy.integrations.servicies.rent.EarlyInvoiceGenerationPayload;
import com.rentancy.integrations.servicies.rent.EarlyInvoiceGenerator;
import com.rentancy.integrations.servicies.rent.NextInvoiceDateCalculator;
import com.rentancy.integrations.util.CognitoUtils;
import com.rentancy.integrations.util.ResponseUtils;
import com.xero.api.ApiClient;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.Clock;
import java.util.Map;
import java.util.Optional;

import static com.rentancy.integrations.util.JSONUtils.wrappedDeserializePayload;
import static com.rentancy.integrations.util.JSONUtils.wrappedToJsonString;
import static org.springframework.http.HttpHeaders.*;

public class EarlyInvoiceGenerationHandler implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {

    private static final Logger log = LogManager.getLogger(EarlyInvoiceGenerationHandler.class);
    private final EarlyInvoiceGenerator generator;

    public EarlyInvoiceGenerationHandler() {
        var config = new ConfigImpl();
        var ddbClient = new DDBClient(config);
        var integrationService = new IntegrationServiceImpl(new XeroOauth2Client(config, new ApiClient()),
                new IntegrationRepository(config, ddbClient), new LambdaClient(config), config);

        generator = new EarlyInvoiceGenerator(
                new NextInvoiceDateCalculator(),
                integrationService,
                new OrganisationRepository(config, ddbClient),
                new TenancyRepository(new TenancyRepositoryConfig(), ddbClient),
                new SQSClient(),
                config,
                Clock.systemUTC()
        );
    }

    @Override
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        var response = new APIGatewayV2ProxyResponseEvent();

        try {
            var params = input.getQueryStringParameters();
            var tenancyId = params.get("tenancyId");
            EarlyInvoiceGenerationPayload payload = new EarlyInvoiceGenerationPayload(tenancyId);
            payload.validate();
            var result = generator.generate(payload, CognitoUtils.getCurrentOrganisation(input));
            response.setBody(wrappedToJsonString(result));
            response.setStatusCode(201);
        } catch (EntityNotFoundException e) {
            response.setStatusCode(404);
        } catch (EntityNotInOrganizationScopeException e) {
            response.setStatusCode(403);
        } catch (InvalidRequestException e) {
            response.setStatusCode(400);
        } catch (Exception e) {
            log.error("Failed to generate invoices early", e);
            response.setStatusCode(500);
            response.setBody(wrappedToJsonString(new HandlerErrorResponseBody("Unable to generate invoice early")));
        }

        response.setHeaders(prepareResponseHeaders(input));
        return response;
    }

    private Map<String, String> prepareResponseHeaders(ApiGatewayProxyRequestInput input) {
        var requestHeaders = input.getHeaders();
        var origin = Optional.ofNullable(requestHeaders.get(ORIGIN)).orElse(requestHeaders.get(ORIGIN.toLowerCase()));
        var headers = ResponseUtils.buildHeader(origin);
        headers.add("Content-Type", "application/json");
        return headers.toSingleValueMap();
    }

}