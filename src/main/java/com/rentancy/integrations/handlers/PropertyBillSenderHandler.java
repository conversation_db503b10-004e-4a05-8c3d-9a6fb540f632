package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.pojos.PropertyBillCreationCommand;
import com.rentancy.integrations.servicies.PortfolioFactory;
import com.rentancy.integrations.servicies.PortfolioService;
import com.rentancy.integrations.util.CognitoUtils;
import com.rentancy.integrations.util.ResponseUtils;

import lombok.SneakyThrows;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.web.client.HttpClientErrorException;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.rentancy.integrations.util.JSONUtils.wrappedDeserializePayload;
import static com.rentancy.integrations.util.JSONUtils.wrappedToJsonString;
import static org.springframework.http.HttpHeaders.*;

public class PropertyBillSenderHandler implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {

    private static final Logger log = LogManager.getLogger(ManualInvoiceHandler.class);

    private final PortfolioService portfolioService;

    public PropertyBillSenderHandler() {
        this.portfolioService = PortfolioFactory.getPortfolioService(new ConfigImpl());
    }

    @SneakyThrows
    @Override
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        var response = new APIGatewayV2ProxyResponseEvent();
        var source = input.getSource();

        if (Objects.nonNull(source) && source.equals("WARMUP")) {
            log.info("Warming lambda");
            return response;
        }

        var currentOrganisation = CognitoUtils.getCurrentOrganisation(input);
        var data = input.getBody();
        var body = wrappedDeserializePayload(data, PropertyBillCreationCommand.class);

        var requestHeaders = input.getHeaders();
        var origin = Optional.ofNullable(requestHeaders.get(ORIGIN)).orElse(requestHeaders.get(ORIGIN.toLowerCase()));
        var headers = ResponseUtils.buildHeader(origin);
        headers.add("Content-Type", "application/json");

        var params = input.getQueryStringParameters();
        var organisationId = params.get("organisationId");

        if (Objects.nonNull(currentOrganisation) && !currentOrganisation.equals(organisationId)) {
            response.setHeaders(headers.toSingleValueMap());
            response.setStatusCode(403);
            return response;
        }

        try {
            log.info("Request body - {}", body);

            portfolioService.createPropertyBill(organisationId, body);

            response.setStatusCode(201);
        } catch (HttpClientErrorException e) {
            log.error("Failed to generate manual invoice", e);
            response.setStatusCode(e.getRawStatusCode());
            response.setBody(e.getResponseBodyAsString());
        } catch (Exception e) {
            log.error("Failed to generate manual invoice", e);
            response.setStatusCode(500);
            response.setBody(wrappedToJsonString(Map.of("Message", e.getMessage())));
        }

        response.setHeaders(headers.toSingleValueMap());
        return response;
    }
}
