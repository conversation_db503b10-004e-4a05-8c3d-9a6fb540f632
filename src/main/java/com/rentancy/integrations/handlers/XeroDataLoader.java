package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.SQSEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.HandlerResponse;
import com.rentancy.integrations.pojos.XeroAccountLoaderPayload;
import com.rentancy.integrations.pojos.XeroBankTransferLoaderPayload;
import com.rentancy.integrations.pojos.XeroContactLoaderPayload;
import com.rentancy.integrations.pojos.XeroInvoiceLoaderPayload;
import com.rentancy.integrations.pojos.XeroJournalLoaderPayload;
import com.rentancy.integrations.pojos.XeroOverPayments.XeroOverPaymentLoaderPayload;
import com.rentancy.integrations.pojos.XeroPaymentLoaderPayload;
import com.rentancy.integrations.pojos.XeroTransactionLoaderPayload;
import com.rentancy.integrations.servicies.XeroFactory;
import com.rentancy.integrations.servicies.XeroService;
import com.rentancy.integrations.util.SentryErrors;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.function.Consumer;

import static com.rentancy.integrations.util.JSONUtils.wrappedDeserializePayload;

public class XeroDataLoader implements RequestHandler<SQSEvent, HandlerResponse> {

    private static final Logger log = LogManager.getLogger(XeroDataLoader.class);

    private final XeroService xeroService;

    public XeroDataLoader() {
        this.xeroService = XeroFactory.getXeroService(new ConfigImpl());
    }

    public HandlerResponse handle(SQSEvent input, Context context, Consumer<SQSEvent.SQSMessage> consumer) {
        try {
            SentryErrors.initSentry();
            log.info(input);
            var records = input.getRecords();

            for (var record : records) {
                consumer.accept(record);
            }
            SentryErrors.destroySentry();
            return HandlerResponse
                    .builder()
                    .status(200)
                    .build();
        } catch (Exception e) {
            SentryErrors.catchException(e);
            SentryErrors.destroySentry();
            log.error("Error", e);
            throw e;
        }
    }

    public HandlerResponse loadContact(SQSEvent input, Context context) {
        return handle(input, context, record -> {
            var payload = wrappedDeserializePayload(record.getBody(), XeroContactLoaderPayload.class);
            log.info(payload.toString());

            xeroService.loadContact(payload.getOrganisation(), payload.getContact());
        });
    }

    public HandlerResponse loadOverPayment(SQSEvent input, Context context) {
        return handle(input, context, record -> {
            var payload = wrappedDeserializePayload(record.getBody(), XeroOverPaymentLoaderPayload.class);
            log.info(payload.toString());

            xeroService.loadOverPayment(payload.getOrganisation(), payload.getOverPayment());
        });
    }

    public HandlerResponse loadInvoice(SQSEvent input, Context context) {
        return handle(input, context, record -> {
            var payload = wrappedDeserializePayload(record.getBody(), XeroInvoiceLoaderPayload.class);
            log.info(payload.toString());

            xeroService.loadInvoice(payload.getOrganisation(), payload.getTenant(), payload.getInvoice());
        });
    }

    public HandlerResponse loadTransaction(SQSEvent input, Context context) {
        return handle(input, context, record -> {
            var payload = wrappedDeserializePayload(record.getBody(), XeroTransactionLoaderPayload.class);
            log.info(payload.toString());

            xeroService.loadTransaction(payload.getOrganisation(), payload.getTenant(), payload.getTransaction());
        });
    }

    public HandlerResponse loadAccount(SQSEvent input, Context context) {
        return handle(input, context, record -> {
            var payload = wrappedDeserializePayload(record.getBody(), XeroAccountLoaderPayload.class);
            log.info(payload.toString());

            xeroService.loadAccount(payload.getOrganisation(), payload.getTenant(), payload.getAccount());
        });
    }

    public HandlerResponse loadPayment(SQSEvent input, Context context) {
        return handle(input, context, record -> {
            var payload = wrappedDeserializePayload(record.getBody(), XeroPaymentLoaderPayload.class);
            log.info(payload.toString());

            xeroService.loadPayment(payload.getOrganisation(), payload.getTenant(), payload.getPayment());
        });
    }

    public HandlerResponse loadTransfer(SQSEvent input, Context context) {
        return handle(input, context, record -> {
            var payload = wrappedDeserializePayload(record.getBody(), XeroBankTransferLoaderPayload.class);
            log.info(payload.toString());

            xeroService.loadTransfer(payload.getOrganisation(), payload.getTenant(), payload.getBankTransfer());
        });
    }

    public HandlerResponse loadJournal(SQSEvent input, Context context) {
        return handle(input, context, record -> {
            var payload = wrappedDeserializePayload(record.getBody(), XeroJournalLoaderPayload.class);
            log.info(payload.toString());

//            xeroService.loadJournal(payload.getOrganisation(), payload.getTenant(), payload.getJournal());
        });
    }

    @Override
    public HandlerResponse handleRequest(SQSEvent input, Context context) {
        return null;
    }
}
