package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.SQSEvent;
import com.fasterxml.jackson.core.type.TypeReference;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.HandlerResponse;
import com.rentancy.integrations.pojos.XeroInvoiceLoaderPayload;
import com.rentancy.integrations.pojos.XeroInvoices;
import com.rentancy.integrations.servicies.SQSClient;
import com.rentancy.integrations.servicies.xero_integration.queue.dto.CallbackInbound;
import com.rentancy.integrations.servicies.xero_integration.queue.dto.SqsInvoiceMessagePattern;
import com.xero.models.accounting.Invoice;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.rentancy.integrations.util.JSONUtils.wrappedDeserializePayload;
import static com.rentancy.integrations.util.JSONUtils.wrappedToJsonString;

public class XeroIntegrationServiceReceiver implements RequestHandler<SQSEvent, HandlerResponse> {
    private static final Logger log = LogManager.getLogger(XeroIntegrationServiceReceiver.class);

    RestTemplate restTemplate;
    SQSClient sqsClient;
    String loadInvoiceQueueName;

    public XeroIntegrationServiceReceiver() {
        var config = new ConfigImpl();
        restTemplate = new RestTemplateBuilder()
                .defaultHeader("Content-Type", "application/json")
                .defaultHeader("Accept", "application/json")
                .defaultHeader("Encoding-Type", "UTF-8")
                .build();
        sqsClient = new SQSClient();
        loadInvoiceQueueName = config.getInvoicesQueue();
        if (loadInvoiceQueueName == null || loadInvoiceQueueName.isEmpty()) {
            log.error("Load invoices queue name is mandatory, but is {}", loadInvoiceQueueName);
        }
    }

    @Override
    public HandlerResponse handleRequest(SQSEvent sqsEvent, Context context) {
        log.info("Received SQS event {}", sqsEvent.getRecords().toString().replace("\n", ""));
        List<RecordHandlingResult> recordHandlingResults = new ArrayList<>();

        for (SQSEvent.SQSMessage record : sqsEvent.getRecords()) {
            log.info("Handling record {}", record.toString().replace("\n", ""));
            var recordHandlingResult = new RecordHandlingResult();
            recordHandlingResults.add(recordHandlingResult);

            SqsInvoiceMessagePattern<XeroInvoices.XeroInvoice> payload;
            try {
                var typeReference = new TypeReference<SqsInvoiceMessagePattern<XeroInvoices.XeroInvoice>>() {
                };
                payload = wrappedDeserializePayload(record.getBody(), typeReference);
                recordHandlingResult.parsing = true;
            } catch (IllegalArgumentException e) {
                log.error("Failed to deserialize", e);
                // no point in processing the record further
                continue;
            }
            recordHandlingResult.entityId = payload.getEntityId();

            try {
                log.info("Sending {}", payload.getBody());
                sqsClient.enqueue(
                        loadInvoiceQueueName,
                        wrappedToJsonString(
                                new XeroInvoiceLoaderPayload(payload.getOrganisationId(), payload.getTenantId(), payload.getBody())
                        ));
                recordHandlingResult.loadInvoice = true;
            } catch (Exception e) {
                log.error("Failed to send to loadInvoice", e);
            }

            var callback = payload.getCallback();
            try {
                if (callback != null) {
                    var typeReference = new TypeReference<SqsInvoiceMessagePattern<Invoice>>() {
                    };
                    var payloadWithLegacyInvoiceObject = wrappedDeserializePayload(record.getBody(), typeReference);
                    sqsClient.enqueue(callback.getQueue(), wrappedToJsonString(new CallbackInbound(
                            payload.getOrganisationId(),
                            payloadWithLegacyInvoiceObject.getBody(),
                            callback.getType(),
                            callback.getTenancyId(),
                            callback.getTenantId(),
                            callback.getDueDate(),
                            callback.getLastJournalRunDate(),
                            callback.getFirstJournalRunDate(),
                            callback.getAutoJournalArrears(),
                            callback.getPreCalculatedOriginalAmount()
                    )));
                    recordHandlingResult.callback = true;
                }
            } catch (Exception e) {
                log.error("Failed to call callback", e);
            }
        }
        log.info("Result: {}", recordHandlingResults.stream().map(Object::toString).collect(Collectors.joining(",")));
        return HandlerResponse.OK;
    }
}

class RecordHandlingResult {
    public String entityId;
    public boolean parsing = false;
    public boolean loadInvoice = false;
    public boolean callback = false;

    @Override
    public String toString() {
        return String.format("[id:%s,parsed:%s,invoiceLoaded:%s,callbackCalled:%s]", entityId, parsing, loadInvoice, callback);
    }
}