package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.SQSEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.PropertyFinanceSummary;
import com.rentancy.integrations.pojos.sqs.ReadModelParentPropertySummaryFillCommand;
import com.rentancy.integrations.servicies.TenancyService;
import com.rentancy.integrations.servicies.TenancyServiceFactory;
import com.rentancy.integrations.servicies.persistence.ReadModelRepository;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.DateTimeException;
import java.time.Instant;

import static com.rentancy.integrations.util.JSONUtils.wrappedDeserializePayload;

public class ReadModelParentPropertySummaryFillerHandler implements RequestHandler<SQSEvent, Void> {
    private static final Logger log = LogManager.getLogger(ReadModelParentPropertySummaryFillerHandler.class);
    private final TenancyService tenancyService;
    private final ReadModelRepository readModelRepository;

    public ReadModelParentPropertySummaryFillerHandler() {
        tenancyService = TenancyServiceFactory.getTenancyService(new ConfigImpl());
        readModelRepository = ReadModelRepository.create(new ConfigImpl());
    }

    @Override
    public Void handleRequest(SQSEvent input, Context context) {
        var records = input.getRecords();

        for (var record : records) {
            try {
                log.info("Record - {}", record);

                var payload = wrappedDeserializePayload(record.getBody(), ReadModelParentPropertySummaryFillCommand.class);

                var existing = readModelRepository.findByPropertyId(payload.parentPropertyId);
                if (updateIsNewerThanExisting(existing, payload)) continue;

                var parentPropertyFinanceSummary = tenancyService.calculateParentPropertyFinanceSummary(payload.parentPropertyId);
                readModelRepository.saveParentPropertySummary(payload, parentPropertyFinanceSummary);
            } catch (Exception e) {
                log.error("Failed to handle record", e);
            }
        }

        return null;
    }

    private static boolean updateIsNewerThanExisting(Pair<PropertyFinanceSummary, Instant> existing, ReadModelParentPropertySummaryFillCommand payload) {
        if (existing.getRight() == null) return false;
        try {
            var incomingUpdate = Instant.parse(payload.itemUpdatedAt);
            if (incomingUpdate.compareTo(existing.getRight()) < 0) {
                log.info("Incoming update is older that existing");
                return true;
            }
        } catch (DateTimeException e) {
            log.info("Cannot parse incoming update");
        }
        return false;
    }
}
