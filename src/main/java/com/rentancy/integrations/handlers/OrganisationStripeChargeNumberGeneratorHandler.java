package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.CloudWatchLogsEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.servicies.IntegrationFactory;
import com.rentancy.integrations.servicies.persistence.OrganisationService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class OrganisationStripeChargeNumberGeneratorHandler implements RequestHandler<CloudWatchLogsEvent, Void> {

    private static final Logger log = LogManager.getLogger(OrganisationStripeChargeNumberGeneratorHandler.class);

    private final OrganisationService organisationService;

    public OrganisationStripeChargeNumberGeneratorHandler() {
        this.organisationService = IntegrationFactory.getOrganisationService(new ConfigImpl());
    }

    @Override
    public Void handleRequest(CloudWatchLogsEvent cloudWatchLogsEvent, Context context) {
        try {
            organisationService.numberStripeChargeItems();
        } catch (Exception e) {
            log.error("Failed to handle event", e);
        }

        return null;
    }
}
