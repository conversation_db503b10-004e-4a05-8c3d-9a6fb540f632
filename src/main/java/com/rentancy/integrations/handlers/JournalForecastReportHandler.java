package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.servicies.ReportFactory;
import com.rentancy.integrations.servicies.ReportService;
import com.rentancy.integrations.util.CognitoUtils;
import com.rentancy.integrations.util.ResponseUtils;

import lombok.SneakyThrows;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.rentancy.integrations.util.JSONUtils.toApiResponse;
import static com.rentancy.integrations.util.JSONUtils.toJsonString;
import static org.springframework.http.HttpHeaders.*;

public class JournalForecastReportHandler implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {

    private static final Logger log = LogManager.getLogger(DataExporterHandler.class);

    private final ReportService reportService;

    public JournalForecastReportHandler() {
        this.reportService = ReportFactory.getReportService(new ConfigImpl());
    }

    @Override
    @SneakyThrows
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        var response = new APIGatewayV2ProxyResponseEvent();
        var requestHeaders = input.getHeaders();
        var origin = Optional.ofNullable(requestHeaders)
                .map(headers -> Optional.ofNullable(headers.get(ORIGIN)).orElse(headers.get(ORIGIN.toLowerCase())))
                .orElse("https://" + System.getenv("RENTANCY_DOMAIN"));
        var headers = ResponseUtils.buildHeader(origin);
        var source = input.getSource();

        if (Objects.nonNull(source) && source.equals("WARMUP")) {
            log.info("Warming lambda");
            return response;
        }

        var currentOrganisation = CognitoUtils.getCurrentOrganisation(input);

        try {
            log.info(input);
            var params = input.getQueryStringParameters();

            headers.add(CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

            var organisationId = currentOrganisation;
            var startDate = params.get("startDate");
            var periodLengthInMonths = Integer.parseInt(params.get("periodLength"));

            if (Objects.nonNull(currentOrganisation) && !currentOrganisation.equals(organisationId)) {
                response.setHeaders(headers.toSingleValueMap());
                response.setStatusCode(403);
                return response;
            }

            var data = reportService.generateForecastReport(organisationId, startDate, periodLengthInMonths);

            response.setStatusCode(200);
            response.setBody(toApiResponse(data));
            response.setIsBase64Encoded(true);
        } catch (Exception e) {
            log.error("Failed to generate forecast report", e);
            response.setStatusCode(500);
            var message = Objects.isNull(e.getMessage()) ? "Something went wrong" : e.getMessage();
            response.setBody(toJsonString(Map.of("message", message)));
        }

        response.setHeaders(headers.toSingleValueMap());
        return response;
    }
}
