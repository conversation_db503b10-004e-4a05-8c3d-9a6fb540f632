package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.servicies.IntegrationFactory;
import com.rentancy.integrations.servicies.WhatsAppService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;

public class WhatsAppHandler implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {

    private static final Logger log = LogManager.getLogger(WhatsAppHandler.class);

    private final WhatsAppService whatsAppService;

    public WhatsAppHandler() {
        this.whatsAppService = IntegrationFactory.getWhatsAppService(new ConfigImpl());
    }

    @Override
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        log.info("Received - {}", input);
        var response = new APIGatewayV2ProxyResponseEvent();
        var path = input.getPath();
        response.setStatusCode(200);
        response.setHeaders(Map.of("Content-Type", "text/plain"));

        try {
            var body = input.getBody().split("&");
            var payload = new HashMap<String, Object>();
            for (String param : body) {
                var parts = param.split("=");
                if (parts.length < 2) {
                    payload.put(parts[0], "");
                    continue;
                }
                payload.put(parts[0], parts[1]);
            }

            log.info("Payload - {}", payload);

            switch (path) {
                case "/whatsapp/message":
                    whatsAppService.postMessage(payload);
                    break;
                case "/whatsapp/status":
                    whatsAppService.postStatus(payload);
                    break;
                default:
                    log.error("Invalid path - {}", path);
            }
        } catch (Exception e) {
            log.error("Failed to handle WhatsApp message", e);
        }

        return response;
    }
}
