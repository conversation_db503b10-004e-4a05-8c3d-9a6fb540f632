package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.servicies.XeroFactory;
import com.rentancy.integrations.servicies.XeroService;
import com.rentancy.integrations.util.CognitoUtils;
import com.rentancy.integrations.util.ResponseUtils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.rentancy.integrations.util.JSONUtils.wrappedToJsonString;
import static org.springframework.http.HttpHeaders.ORIGIN;


public class XeroAccountDataFetcher implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {
    private static final Logger log = LogManager.getLogger(XeroAccountDataFetcher.class);

    private final XeroService xeroService;

    public XeroAccountDataFetcher() {
        this.xeroService = XeroFactory.getXeroService(new ConfigImpl());
    }
    @Override
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        var response = new APIGatewayV2ProxyResponseEvent();
        var currentOrganisation = CognitoUtils.getCurrentOrganisation(input);

        var requestHeaders = input.getHeaders();
        var origin = Optional.ofNullable(requestHeaders.get(ORIGIN)).orElse(requestHeaders.get(ORIGIN.toLowerCase()));
        var headers = ResponseUtils.buildHeader(origin);
        headers.add("Content-Type", "application/json");
        response.setHeaders(headers.toSingleValueMap());

        try {
            log.info(input);
            var params = input.getQueryStringParameters();
            var organisationId = params.get("organisationId");

            if (Objects.nonNull(currentOrganisation) && !currentOrganisation.equals(organisationId)) {
                response.setHeaders(headers.toSingleValueMap());
                response.setStatusCode(403);
                return response;
            }

            xeroService.fetchAccountData(organisationId);
            response.setStatusCode(200);

            return response;
        } catch (Exception e) {
            log.error("Error", e);
            response.setBody(wrappedToJsonString(Map.of("Message", e.getMessage())));
            response.setStatusCode(500);
            return response;
        }
    }
}
