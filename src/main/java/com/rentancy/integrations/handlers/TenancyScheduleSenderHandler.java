package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.SQSEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.TenancyScheduleCreationCommand;
import com.rentancy.integrations.servicies.ReportFactory;
import com.rentancy.integrations.servicies.ReportService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.rentancy.integrations.util.JSONUtils.wrappedDeserializePayload;
import static com.rentancy.integrations.util.JSONUtils.wrappedToJsonString;

public class TenancyScheduleSenderHandler implements RequestHandler<SQSEvent, Void> {
    private static final Logger log = LogManager.getLogger(TenancyScheduleSenderHandler.class);

    private final ReportService reportService;

    public TenancyScheduleSenderHandler() {
        this.reportService = ReportFactory.getReportService(new ConfigImpl());
    }

    @Override
    public Void handleRequest(SQSEvent input, Context context) {
        var records = input.getRecords();

        for (var record : records) {
            try {
                log.info("Record - {}", wrappedToJsonString(record));
                var payload = wrappedDeserializePayload(record.getBody(), TenancyScheduleCreationCommand.class);
                reportService.sendTenancySchedule(payload);
            } catch (Exception e) {
                log.error("Failed to handle record", e);
            }
        }

        return null;
    }
}

