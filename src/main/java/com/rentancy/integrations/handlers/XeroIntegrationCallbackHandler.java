package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.SQSEvent;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.servicies.*;
import com.rentancy.integrations.servicies.autojournal.AutoJournalService;
import com.rentancy.integrations.servicies.autojournal.TrackingCategoryService;
import com.rentancy.integrations.servicies.enhanced.InvoicePropertyRepositoryImpl;
import com.rentancy.integrations.servicies.persistence.*;
import com.rentancy.integrations.servicies.persistence.config.TenancyRepositoryConfig;
import com.rentancy.integrations.servicies.rent.NextInvoiceDateCalculator;
import com.rentancy.integrations.servicies.xero_integration.callback.AfterCreationOfBillData;
import com.rentancy.integrations.servicies.xero_integration.callback.AfterCreationOfRaiseCommissionBillData;
import com.rentancy.integrations.servicies.xero_integration.queue.InvoiceOutboundSender;
import com.rentancy.integrations.servicies.xero_integration.queue.dto.CallbackInbound;
import com.xero.api.ApiClient;
import lombok.AllArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;

import java.time.Clock;
import java.util.List;

import static com.rentancy.integrations.util.JSONUtils.wrappedDeserializePayload;

@AllArgsConstructor
public class XeroIntegrationCallbackHandler implements RequestHandler<SQSEvent, Void> {

    private final AutoJournalService autoJournalService;
    private final PortfolioXeroProcessor portfolioXeroProcessor;

    private static final Logger log = LogManager.getLogger(XeroIntegrationCallbackHandler.class);

    public XeroIntegrationCallbackHandler() {
        Config config = new ConfigImpl();
        var lambdaClient = new LambdaClient(config);
        var ddbClient = new DDBClient(config);
        var integrationServiceImpl = new IntegrationServiceImpl(new XeroOauth2Client(config, new ApiClient()),
                new IntegrationRepository(config, ddbClient), lambdaClient, config);

        var dynamoDbEnhancedClient = DynamoDbEnhancedClient.builder()
                .dynamoDbClient(DynamoDbClient.builder()
                        .region(Region.of(config.getDDBRegion()))
                        .build())
                .build();
        var trackingCategoryService = new TrackingCategoryService();

        var organisationService = new OrganisationServiceImpl(new OrganisationRepository(config, ddbClient));
        var sqsClient = new SQSClient();
        var userService = new UserServiceImpl(new UserRepository(config, ddbClient));
        var paymentRepository = new PaymentRepository(config, ddbClient);
        var landlordBillRepository = new LandlordBillRepository(config, ddbClient);
        var tenancyService = TenancyServiceFactory.getTenancyService(config);
        var appSyncServiceProvider = new AppSyncServiceProvider(lambdaClient, config);
        var invoiceService = new InvoiceServiceImpl(new InvoiceRepository(config, ddbClient),
                new AccountRepository(config, ddbClient),
                landlordBillRepository,
                paymentRepository,
                new TransactionRepository(config, ddbClient),
                new TransferRepository(config, ddbClient),
                new JournalRepository(config, ddbClient),
                new InvoiceWebhookEventsRepository(config, ddbClient),
                new PropertyRepository(config, ddbClient),
                new OverPaymentRepository(config, ddbClient),
                new TenancyRepository(new TenancyRepositoryConfig(), ddbClient),
                new EventBridgeClient(),
                config);
        var reportService = ReportFactory.getReportService(config);
        var xeroService = XeroFactory.getXeroService(config);
        var propertyService = new PropertyServiceImpl(
                new PropertyRepository(config, ddbClient),
                new TenancyRepository(new TenancyRepositoryConfig(), ddbClient),
                new InvoicePropertyRepositoryImpl(dynamoDbEnhancedClient, config));
        Clock clock = Clock.systemUTC();
        var portfolioXeroService = new PortfolioXeroProcessor(config, integrationServiceImpl, userService, invoiceService,
                organisationService, propertyService, sqsClient, tenancyService, reportService, appSyncServiceProvider,
                XeroFactory.getXeroClient(config, integrationServiceImpl), xeroService, new NextInvoiceDateCalculator(), clock,
                trackingCategoryService, new InvoiceOutboundSender(config, sqsClient));
        var xeroClient = XeroFactory.getXeroClient(config, integrationServiceImpl);
        this.autoJournalService = new AutoJournalService(
                organisationService,
                propertyService,
                reportService,
                integrationServiceImpl,
                userService,
                invoiceService,
                tenancyService,
                xeroClient,
                portfolioXeroService,
                trackingCategoryService,
                new InvoiceOutboundSender(config, sqsClient),
                config.getXeroInvoicesCallbackQueue()
        );

        this.portfolioXeroProcessor = new PortfolioXeroProcessor(
                config,
                integrationServiceImpl,
                userService,
                invoiceService,
                organisationService,
                propertyService,
                sqsClient,
                tenancyService,
                reportService,
                appSyncServiceProvider,
                xeroClient,
                xeroService,
                new NextInvoiceDateCalculator(),
                clock,
                trackingCategoryService,
                new InvoiceOutboundSender(config, sqsClient)
        );
    }

    @Override
    public Void handleRequest(SQSEvent sqsEvent, Context context) {
        var records = sqsEvent.getRecords();

        for (var record : records) {
            try {
                log.info("Record - {}", record);

                CallbackInbound callback = wrappedDeserializePayload(record.getBody(), CallbackInbound.class);
                switch (callback.getType()) {
                    case AFTER_CREATION_OF_BILL: {
                        AfterCreationOfBillData data = new AfterCreationOfBillData(callback.getOrganisationId(), callback.getInvoice(), callback.getTenancyId(), callback.getLastJournalRunDate(), callback.getFirstJournalRunDate(), callback.getAutoJournalArrears(), callback.getPreCalculatedOriginalAmount());
                        autoJournalService.handleAfterNormalBillCreationCallback(callback.getOrganisationId(), List.of(data));
                        break;
                    }
                    case AFTER_CREATION_OF_RAISE_COMMISSION_BILL: {
                        AfterCreationOfRaiseCommissionBillData data = new AfterCreationOfRaiseCommissionBillData(callback.getInvoice(), callback.getTenancyId(), callback.getTenantId(), callback.getDueDate());
                        portfolioXeroProcessor.handleAfterRaiseCommissionBillCallback(callback.getOrganisationId(), callback.getTenantId(), List.of(data));
                        break;
                    }
                    default:
                        throw new RuntimeException("Unexpected callback type: " + callback.getType());
                }
            } catch (Exception e) {
                log.error("Failed to handle record", e);
                throw e;
            }
        }
        return null;
    }
}
