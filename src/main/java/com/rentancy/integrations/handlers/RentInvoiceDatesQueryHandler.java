package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.pojos.HandlerErrorResponseBody;
import com.rentancy.integrations.pojos.Tenancy;
import com.rentancy.integrations.servicies.persistence.DDBClient;
import com.rentancy.integrations.servicies.persistence.TenancyRepository;
import com.rentancy.integrations.servicies.persistence.config.TenancyRepositoryConfig;
import com.rentancy.integrations.servicies.rent.NextInvoiceDateCalculator;
import com.rentancy.integrations.servicies.rent.RentInvoiceDatesQueryResponse;
import com.rentancy.integrations.util.CognitoUtils;
import com.rentancy.integrations.util.ResponseUtils;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiFunction;

import static com.rentancy.integrations.util.JSONUtils.wrappedToJsonString;
import static org.springframework.http.HttpHeaders.ORIGIN;

@Slf4j
public class RentInvoiceDatesQueryHandler implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {

    private final NextInvoiceDateCalculator nextInvoiceDateCalculator;
    private final TenancyRepository tenancyRepository;
    private final BiFunction<Tenancy, ApiGatewayProxyRequestInput, Boolean> authorizationStrategy;

    public RentInvoiceDatesQueryHandler() {
        this.nextInvoiceDateCalculator = new NextInvoiceDateCalculator();
        var config = new ConfigImpl();
        this.tenancyRepository = new TenancyRepository(new TenancyRepositoryConfig(), new DDBClient(config));
        this.authorizationStrategy = (Tenancy tenancy, ApiGatewayProxyRequestInput input) -> {
            var currentOrganisationId = CognitoUtils.getCurrentOrganisation(input);
            return tenancy.getOrganisation().equals(currentOrganisationId);
        };
    }

    public RentInvoiceDatesQueryHandler(BiFunction<Tenancy, ApiGatewayProxyRequestInput, Boolean> authorizationStrategy) {
        this.nextInvoiceDateCalculator = new NextInvoiceDateCalculator();
        var config = new ConfigImpl();
        this.tenancyRepository = new TenancyRepository(new TenancyRepositoryConfig(), new DDBClient(config));
        this.authorizationStrategy = authorizationStrategy;
    }

    @Override
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        var response = populateResponse(input);
        response.setHeaders(prepareResponseHeaders(input));
        return response;
    }

    private APIGatewayV2ProxyResponseEvent populateResponse(ApiGatewayProxyRequestInput input) {
        var response = new APIGatewayV2ProxyResponseEvent();

        try {
            var tenancyId = input.getQueryStringParameters().get("tenancyId");
            var referenceDate = Optional.ofNullable(input.getQueryStringParameters().get("referenceDate"))
                    .map(it -> LocalDate.parse(it).atStartOfDay(ZoneId.of("UTC")))
                    .orElse(ZonedDateTime.now());

            var tenancy = tenancyRepository.getTenancyWithSettings(tenancyId);
            if (tenancy == null) {
                response.setStatusCode(404);
                response.setBody(wrappedToJsonString(new HandlerErrorResponseBody("Tenancy with provided id does not exist")));
                return response;
            }

            if (!authorizationStrategy.apply(tenancy, input)) {
                response.setStatusCode(403);
                response.setBody(wrappedToJsonString(new HandlerErrorResponseBody("This tenancy does not belong to your organisation")));
                return response;
            }

            log.info("Generating response for tenancy: {} and reference date: {}",
                    tenancyId, referenceDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
            var responseBody = generateSuccessfulResponseBody(tenancy, referenceDate);
            response.setStatusCode(200);
            response.setBody(wrappedToJsonString(responseBody));
            return response;
        } catch(DateTimeParseException e) {
            var message = "Unable to parse referenceDate param";
            response.setStatusCode(400);
            response.setBody(wrappedToJsonString(new HandlerErrorResponseBody(message)));
            return response;
        } catch (Exception e) {
            var message = "Unable to return response";
            log.error(message, e);
            response.setStatusCode(500);
            response.setBody(wrappedToJsonString(new HandlerErrorResponseBody(message)));
            return response;
        }
    }

    private RentInvoiceDatesQueryResponse generateSuccessfulResponseBody(Tenancy tenancy, ZonedDateTime referenceDate) {
        var nextInvoiceDate = nextInvoiceDateCalculator.calculate(referenceDate, tenancy);
        if (tenancy.paymentWouldFallOutsideTenancyDuration(nextInvoiceDate)) {
            return new RentInvoiceDatesQueryResponse(null, tenancy.getSettings());
        }
        return new RentInvoiceDatesQueryResponse(nextInvoiceDate, tenancy.getSettings());
    }

    private Map<String, String> prepareResponseHeaders(ApiGatewayProxyRequestInput input) {
        var requestHeaders = input.getHeaders();
        var origin = Optional.ofNullable(requestHeaders.get(ORIGIN)).orElse(requestHeaders.get(ORIGIN.toLowerCase()));
        var headers = ResponseUtils.buildHeader(origin);
        headers.add("Content-Type", "application/json");
        return headers.toSingleValueMap();
    }

}
