package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.HandlerResponse;
import com.rentancy.integrations.pojos.XeroDataFetcherEvent;
import com.rentancy.integrations.servicies.XeroFactory;
import com.rentancy.integrations.servicies.XeroService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class XeroDataFetcher implements RequestHandler<XeroDataFetcherEvent, HandlerResponse> {

    private static final Logger log = LogManager.getLogger(XeroDataFetcher.class);

    private final XeroService xeroService;

    public XeroDataFetcher() {
        this.xeroService = XeroFactory.getXeroService(new ConfigImpl());
    }

    @Override
    public HandlerResponse handleRequest(XeroDataFetcherEvent input, Context context) {
        try {
            log.info(input);
            var type = input.getType();

            xeroService.fetchData(type);

            return HandlerResponse
                    .builder()
                    .status(200)
                    .build();
        } catch (Exception e) {
            log.error("Error", e);
            throw e;
        }
    }
}
