package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.S3Event;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.servicies.EmailDownloader;
import com.rentancy.integrations.servicies.EmailServiceFactory;
import com.rentancy.integrations.servicies.EmailMessageService;
import org.apache.james.mime4j.dom.Message;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class EmailParser implements RequestHandler<S3Event, Void> {

    private static final Logger log = LogManager.getLogger(EmailParser.class);

    private final EmailDownloader emailDownloader;
    private final EmailMessageService parserService;

    public EmailParser() {
        var config = new ConfigImpl();
        this.emailDownloader = EmailServiceFactory.getDownloader(config);
        this.parserService = EmailServiceFactory.getEmailService(config);
    }

    @Override
    public Void handleRequest(S3Event input, Context context) {
        try {
            for (var record : input.getRecords()) {
                var key = record.getS3().getObject().getKey();

                log.info(key);

                var result = emailDownloader.download(key);

                var message = Message
                        .Builder
                        .of(result.getInputStream())
                        .build();

                parserService.handleMessage(message);
            }
        } catch (Exception e) {
            log.error("Error handling event", e);
        }

        return null;
    }
}
