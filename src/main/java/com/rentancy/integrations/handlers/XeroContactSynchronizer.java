package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.CloudWatchLogsEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.servicies.XeroFactory;
import com.rentancy.integrations.servicies.XeroService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class XeroContactSynchronizer implements RequestHandler<CloudWatchLogsEvent, Void> {

    private static final Logger log = LogManager.getLogger(XeroContactSynchronizer.class);

    private final XeroService service;

    public XeroContactSynchronizer() {
        this.service = XeroFactory.getXeroService(new ConfigImpl());
    }

    @Override
    public Void handleRequest(CloudWatchLogsEvent input, Context context) {
        try {
            this.service.syncContacts();
        } catch (Exception e) {
            log.error("Failed to handle event", e);
        }

        return null;
    }
}
