package com.rentancy.integrations.handlers;


import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.servicies.IntegrationFactory;
import com.rentancy.integrations.servicies.WhatsAppService;
import com.rentancy.integrations.util.CognitoUtils;
import com.rentancy.integrations.util.ResponseUtils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Objects;
import java.util.Optional;

import static org.springframework.http.HttpHeaders.ORIGIN;

public class WhatsAppRequestHandler implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {
    private static final Logger log = LogManager.getLogger(WhatsAppRequestHandler.class);

    private final WhatsAppService whatsAppService;

    public WhatsAppRequestHandler() {
        this.whatsAppService = IntegrationFactory.getWhatsAppService(new ConfigImpl());
    }

    @Override
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        var response = new APIGatewayV2ProxyResponseEvent();
        log.info("Received - {}", input);
        var params = input.getQueryStringParameters();
        var currentOrganisation = CognitoUtils.getCurrentOrganisation(input);

        var organisationId = params.get("organisationId");
        var userId = params.get("userId");
        var requestHeaders = input.getHeaders();
        var origin = Optional.ofNullable(requestHeaders.get(ORIGIN)).orElse(requestHeaders.get(ORIGIN.toLowerCase()));
        var headers = ResponseUtils.buildHeader(origin);
        response.setHeaders(headers.toSingleValueMap());
        try {

            if (Objects.nonNull(currentOrganisation) && !currentOrganisation.equals(organisationId)) {
                response.setHeaders(headers.toSingleValueMap());
                response.setStatusCode(403);
                return response;
            }

            whatsAppService.sendWhatsAppIntegrationRequest(organisationId, userId);

            response.setStatusCode(200);
            return response;
        } catch (Exception e) {
            response.setStatusCode(500);
            log.error("Failed to handle WhatsApp message", e);
            return response;
        }
    }
}
