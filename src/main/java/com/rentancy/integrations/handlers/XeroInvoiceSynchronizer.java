package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.servicies.XeroFactory;
import com.rentancy.integrations.servicies.XeroService;
import com.rentancy.integrations.util.CognitoUtils;
import com.rentancy.integrations.util.ResponseUtils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Objects;
import java.util.Optional;

import static org.springframework.http.HttpHeaders.CONTENT_TYPE;
import static org.springframework.http.HttpHeaders.ORIGIN;

public class XeroInvoiceSynchronizer implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {

    private static final Logger log = LogManager.getLogger(XeroInvoiceSynchronizer.class);

    private final XeroService service;

    public XeroInvoiceSynchronizer() {
        this.service = XeroFactory.getXeroService(new ConfigImpl());
    }

    @Override
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        var response = new APIGatewayV2ProxyResponseEvent();
        var source = input.getSource();

        if (Objects.nonNull(source) && source.equals("WARMUP")) {
            log.info("Warming lambda");
            return response;
        }

        var currentOrganisation = CognitoUtils.getCurrentOrganisation(input);
        var params = input.getQueryStringParameters();
        var organisationId = params.get("organisationId");

        var requestHeaders = input.getHeaders();
        var origin = Optional.ofNullable(requestHeaders.get(ORIGIN)).orElse(requestHeaders.get(ORIGIN.toLowerCase()));
        var headers = ResponseUtils.buildHeader(origin);
        headers.add(CONTENT_TYPE, "application/json;");

        if (Objects.nonNull(currentOrganisation) && !currentOrganisation.equals(organisationId)) {
            response.setHeaders(headers.toSingleValueMap());
            response.setStatusCode(403);
            return response;
        }

        try {
            log.info("Syncing invoices for - {}", organisationId);
            service.syncInvoices(organisationId);

            response.setStatusCode(200);
        } catch (Exception e) {
            log.error("Failed to sync invoices", e);
            response.setStatusCode(500);
            response.setBody(e.getMessage());
        }

        response.setHeaders(headers.toSingleValueMap());
        return response;
    }
}
