package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.AutoInvoiceEvent;
import com.rentancy.integrations.pojos.HandlerResponse;
import com.rentancy.integrations.servicies.PortfolioFactory;
import com.rentancy.integrations.servicies.PortfolioService;
import com.rentancy.integrations.util.SentryErrors;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class TenancyJournalCollector implements RequestHandler<AutoInvoiceEvent, HandlerResponse> {

    private static final Logger log = LogManager.getLogger(TenancyInvoiceCollector.class);

    private final PortfolioService portfolioService;

    public TenancyJournalCollector() {
        this.portfolioService = PortfolioFactory.getPortfolioService(new ConfigImpl());
    }

    @Override
    public HandlerResponse handleRequest(AutoInvoiceEvent input, Context context) {
        SentryErrors.initSentry();

        try {
            portfolioService.collectAutoJournalTenancies();
            SentryErrors.destroySentry();
            return HandlerResponse
                .builder()
                .status(200)
                .build();
        } catch (Exception e) {
            SentryErrors.catchException(e);
            SentryErrors.destroySentry();
            log.error("Error", e);
            return HandlerResponse
                .builder()
                .status(500)
                .build();
        }
    }
}