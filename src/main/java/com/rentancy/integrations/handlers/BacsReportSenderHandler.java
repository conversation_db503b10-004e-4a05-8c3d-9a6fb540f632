package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.SQSEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.BacsReportPayoutRequest;
import com.rentancy.integrations.pojos.CashBalanceReportCommand;
import com.rentancy.integrations.servicies.EventBridgeClient;
import com.rentancy.integrations.servicies.ReportFactory;
import com.rentancy.integrations.servicies.ReportService;
import com.rentancy.integrations.servicies.factory.BacsReportFactory;
import com.rentancy.integrations.servicies.payments.BacsReportService;
import com.rentancy.integrations.util.SentryErrors;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.rentancy.integrations.util.JSONUtils.wrappedDeserializePayload;


/**
 * <h1>BACS Report handler</h1>
 * <h2><a href="https://rentancy.atlassian.net/browse/HEL-7874">JIRA Story</a></h2>
 * <br>
 * BACS (Banker Automated Clearing Services) file helps automate creating multiple payments that need to take place
 * from the clients bank account. Instead of sending each payment one by one, the BACS file is sent to the client's bank
 * and payments are automatically sent out after being approved.
 * <br><br>
 *
 * <h3>Inputs:</h3>
 * SQS queue <b>bacs-report-queue-{stage}</b> with message {@link BacsReportPayoutRequest}
 * <h3>Outputs:</h3>
 * <p>SQS queue <b>email-notification-queue-{stage}</b> with message {@link com.rentancy.integrations.pojos.EmailSenderPayload}</p>
 * <p>S3 bucket <b>rentancy-email-attachments-{stage}</b> with report file</p>
 *
 * @see BacsReportPayoutRequest
 * @see com.rentancy.integrations.servicies.AsyncJobServiceImpl
 */
public class BacsReportSenderHandler implements RequestHandler<SQSEvent, Void> {

    private static final Logger log = LogManager.getLogger(BacsReportSenderHandler.class);

    private final BacsReportService bacsFileService;
    private final ReportService reportService;

    public BacsReportSenderHandler() {
        reportService = ReportFactory.getReportService(new ConfigImpl());
        bacsFileService = BacsReportFactory.build(new ConfigImpl(), new EventBridgeClient());
    }
    public BacsReportSenderHandler(BacsReportService bacsFileService,  ReportService reportService) {
        this.reportService = reportService;
        this.bacsFileService = bacsFileService;
    }


    @Override
    public Void handleRequest(SQSEvent input, Context context) {

        var records = input.getRecords();
        SentryErrors.initSentry();

        if (records == null || records.isEmpty()) {
            log.debug("BacsReportSenderHandler records is empty");
            return null;
        }

        for (var record : records) {
            try {
                var payload = wrappedDeserializePayload(record.getBody(), BacsReportPayoutRequest.class);

                log.info("Record - {} parsed to {}", record, payload);
                var report = bacsFileService.prepareBacsFile(payload);
                log.info("Report generated {}" , report);
                reportService.publishReportViaEmail(report.getContent(),payload.getOrganisationId(), "BACS report", payload.getSenderId(), report.getFileName());
            } catch (Exception e) {
                SentryErrors.catchException(e);
                log.error(String.format("BacsReportSenderHandler failed to handle record: %s", e.getMessage()), e);
            }
        }
        SentryErrors.destroySentry();

        log.info("Bacs sender finished");
        return null;
    }
}
