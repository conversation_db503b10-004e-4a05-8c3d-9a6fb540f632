package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.SQSEvent;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.XeroWebHookHandlerPayload;
import com.rentancy.integrations.servicies.XeroFactory;
import com.rentancy.integrations.servicies.XeroService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.rentancy.integrations.util.JSONUtils.deserializePayload;

public class XeroWebHooksHandler implements RequestHandler<SQSEvent, Void> {

    private static final Logger log = LogManager.getLogger(XeroWebHooksHandler.class);

    private final XeroService xeroService;
    private final Config config;

    public XeroWebHooksHandler() {
        this.config = new ConfigImpl();
        this.xeroService = XeroFactory.getXeroService(config);
    }

    @Override
    public Void handleRequest(SQSEvent input, Context context) {
        var records = input.getRecords();
        for (var record : records) {
            try {
                log.info(record.getBody());

                var message = deserializePayload(record.getBody(), XeroWebHookHandlerPayload.class);

                xeroService.handleWebHook(message);
            } catch (Exception e) {
                log.error("Failed to handle record - " + record.getBody(), e);
            }
        }

        return null;
    }
}
