package com.rentancy.integrations.handlers;

import static com.rentancy.integrations.util.JSONUtils.toApiResponse;
import static com.rentancy.integrations.util.JSONUtils.toJsonString;
import static org.springframework.http.HttpHeaders.ORIGIN;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.servicies.PortfolioFactory;
import com.rentancy.integrations.servicies.PortfolioService;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import com.rentancy.integrations.util.CognitoUtils;
import com.rentancy.integrations.util.ResponseUtils;

import lombok.SneakyThrows;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class IncomeArrearsSummarySender implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {

    private static final Logger log = LogManager.getLogger(DataExporterHandler.class);

    private final PortfolioService portfolioService;

    public IncomeArrearsSummarySender() {
        this.portfolioService = PortfolioFactory.getPortfolioService(new ConfigImpl());
    }

    @Override
    @SneakyThrows
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        var response = new APIGatewayV2ProxyResponseEvent();
        var requestHeaders = input.getHeaders();
        var origin = Optional.ofNullable(requestHeaders.get(ORIGIN)).orElse(requestHeaders.get(ORIGIN.toLowerCase()));
        var headers = ResponseUtils.buildHeader(origin);
        var source = input.getSource();

        if (Objects.nonNull(source) && source.equals("WARMUP")) {
            log.info("Warming lambda");
            return response;
        }

        var currentOrganisation = CognitoUtils.getCurrentOrganisation(input);

        try {
            log.info(input);
            var params = input.getQueryStringParameters();

            var organisationId = params.get("organisationId");
            var receiverId = params.get("receiverId");
            var filter = params.get("filter");

            if (Objects.nonNull(currentOrganisation) && !currentOrganisation.equals(organisationId)) {
                response.setHeaders(headers.toSingleValueMap());
                response.setStatusCode(403);
                return response;
            }

            switch (input.getPath()) {
                case "/arrears/send": {
                    portfolioService.sendIncomeArrearsSummary(organisationId, receiverId, filter);
                    break;
                }
                case "/arrears/report": {
                    var reportUrl = portfolioService.getIncomeArrearsSummaryReport(organisationId, filter);
                    response.setBody(toApiResponse(reportUrl));
                    response.setIsBase64Encoded(true);
                    break;
                }
                default:
                    throw new IllegalArgumentException(String.format("Invalid path %s", input.getPath()));
            }


            response.setStatusCode(200);
        } catch (Exception e) {
            log.error("Failed to export data", e);
            response.setStatusCode(500);
            var message = Objects.isNull(e.getMessage()) ? "Something went wrong" : e.getMessage();
            response.setBody(toJsonString(Map.of("message", message)));
        }

        response.setHeaders(headers.toSingleValueMap());
        return response;
    }
}