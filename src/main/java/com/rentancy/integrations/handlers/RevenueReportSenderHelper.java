package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.CloudWatchLogsEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.servicies.ReportFactory;
import com.rentancy.integrations.servicies.ReportService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class RevenueReportSenderHelper implements RequestHandler<CloudWatchLogsEvent, Void> {

    private static final Logger log = LogManager.getLogger(InvoicePoster.class);

    private final ReportService reportService;

    public RevenueReportSenderHelper() {
        this.reportService = ReportFactory.getReportService(new ConfigImpl());
    }

    @Override
    public Void handleRequest(CloudWatchLogsEvent input, Context context) {
        try {
            reportService.sendRevenueReport();
        } catch (Exception e) {
            log.error("Failed to handle event", e);
        }

        return null;
    }
}
