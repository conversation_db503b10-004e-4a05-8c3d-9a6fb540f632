package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.util.JSONUtils;
import com.rentancy.integrations.servicies.ReportFactory;
import com.rentancy.integrations.servicies.ReportService;
import com.rentancy.integrations.util.ResponseUtils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Objects;
import java.util.Optional;

import static org.springframework.http.HttpHeaders.ORIGIN;

public class LandlordReportHandler implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {

    private static final Logger log = LogManager.getLogger(LandlordReportHandler.class);

    private final ReportService reportService;

    public LandlordReportHandler() {
        this.reportService = ReportFactory.getReportService(new ConfigImpl());
    }

    @Override
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        var response = new APIGatewayV2ProxyResponseEvent();
        var source = input.getSource();

        if (Objects.nonNull(source) && source.equals("WARMUP")) {
            log.info("Warming lambda");
            return response;
        }

        var params = input.getQueryStringParameters();
        var propertyId = params.get("propertyId");
        var startDate = params.get("startDate");
        var endDate = params.get("endDate");

        log.info("query params {}", params);
        var requestHeaders = input.getHeaders();
        var origin = Optional.ofNullable(requestHeaders.get(ORIGIN)).orElse(requestHeaders.get(ORIGIN.toLowerCase()));
        var headers = ResponseUtils.buildHeader(origin);
        headers.add("Content-Type", "application.json");

        try {
            var report = reportService.generateLandlordReport(propertyId, startDate, endDate);

            response.setStatusCode(200);
            response.setBody(JSONUtils.toJsonString(report));
        } catch (Exception e) {
            log.error(e);
            response.setStatusCode(500);
        }

        response.setHeaders(headers.toSingleValueMap());
        return response;
    }
}
