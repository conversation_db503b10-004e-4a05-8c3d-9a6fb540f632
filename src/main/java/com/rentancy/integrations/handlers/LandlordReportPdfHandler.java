package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.pojos.StatementType;
import com.rentancy.integrations.servicies.ReportFactory;
import com.rentancy.integrations.servicies.ReportService;
import com.rentancy.integrations.util.ResponseUtils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Objects;
import java.util.Optional;

import static com.rentancy.integrations.util.JSONUtils.toApiResponse;
import static com.rentancy.integrations.util.CognitoUtils.getCurrentUsername;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;
import static org.springframework.http.HttpHeaders.ORIGIN;

public class LandlordReportPdfHandler implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {

    private static final Logger log = LogManager.getLogger(LandlordReportPdfHandler.class);

    private final ReportService reportService;

    public LandlordReportPdfHandler() {
        this.reportService = ReportFactory.getReportService(new ConfigImpl());
    }

    @Override
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        var response = new APIGatewayV2ProxyResponseEvent();
        var source = input.getSource();

        if (Objects.nonNull(source) && source.equals("WARMUP")) {
            log.info("Warming lambda");
            return response;
        }

        var params = input.getQueryStringParameters();
        var propertyId = params.get("propertyId");
        var startDate = params.get("startDate");
        var endDate = params.get("endDate");
        var format = params.get("format");
        var type = params.get("type");
        var landlordBillId = params.get("landlordBillId");
        var approved = Boolean.parseBoolean(params.get("approved"));

        log.info("query params {}", params);
        var requestHeaders = input.getHeaders();
        var origin = Optional.ofNullable(requestHeaders.get(ORIGIN)).orElse(requestHeaders.get(ORIGIN.toLowerCase()));
        var headers = ResponseUtils.buildHeader(origin);
        headers.add(CONTENT_TYPE, "application/pdf;");

        try {
            var enumType = Optional.ofNullable(type).map(StatementType::valueOf).orElse(null);
            var data = reportService.generateLandlordReportPdf(propertyId, startDate, endDate, format, enumType);

            if (type != null && StatementType.FINALISED == StatementType.valueOf(type)) {
                var currentUser = getCurrentUsername(input);
                reportService.saveStatement(currentUser, propertyId, startDate, endDate, "PROPERTY", landlordBillId, data, approved);
            }

            response.setStatusCode(200);
            response.setBody(toApiResponse(data));
            response.setIsBase64Encoded(true);
        } catch (Exception e) {
            log.error("error", e);
            response.setStatusCode(500);
        }

        response.setHeaders(headers.toSingleValueMap());
        return response;
    }
}
