package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.pojos.PayoutStatementGenerationCommand;
import com.rentancy.integrations.servicies.ReportFactory;
import com.rentancy.integrations.servicies.ReportService;
import com.rentancy.integrations.util.CognitoUtils;
import com.rentancy.integrations.util.ResponseUtils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Objects;
import java.util.Optional;

import static com.rentancy.integrations.util.JSONUtils.toApiResponse;
import static com.rentancy.integrations.util.JSONUtils.wrappedDeserializePayload;
import static org.springframework.http.HttpHeaders.ORIGIN;

public class StatementReportSender implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {

    private static final Logger log = LogManager.getLogger(StatementReportSender.class);

    private final ReportService reportService;

    public StatementReportSender() {
        this.reportService = ReportFactory.getReportService(new ConfigImpl());
    }

    @Override
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        var response = new APIGatewayV2ProxyResponseEvent();
        var source = input.getSource();

        if (Objects.nonNull(source) && source.equals("WARMUP")) {
            log.info("Warming lambda");
            return response;
        }

        var currentOrganisation = CognitoUtils.getCurrentOrganisation(input);
        var requestHeaders = input.getHeaders();
        var origin = Optional.ofNullable(requestHeaders.get(ORIGIN)).orElse(requestHeaders.get(ORIGIN.toLowerCase()));
        var headers = ResponseUtils.buildHeader(origin);

        try {
            var path = input.getPath();
            switch (path) {
                case "/generate-statement-report":
                    log.info("input body:{}", input.getBody());
                    var payload = wrappedDeserializePayload(input.getBody(), PayoutStatementGenerationCommand.class);

                    if (Objects.nonNull(currentOrganisation) && !currentOrganisation.equals(payload.getOrganisationId())) {
                        response.setHeaders(headers.toSingleValueMap());
                        response.setStatusCode(403);
                        return response;
                    }

                    var data = reportService.sendStatementReport(payload);
                    response.setBody(toApiResponse(data));
                    response.setStatusCode(200);
                    response.setIsBase64Encoded(true);
                    break;
                case "/send-statement-report":
                    var landlordBillId = input.getQueryStringParameters().get("landlordBillId");
                    reportService.sendStatementReport(landlordBillId);
                    response.setStatusCode(200);
                    break;
                case "/statement-report":
                    var statementId = input.getQueryStringParameters().get("statementId");
                    var skip = Optional.ofNullable(input.getQueryStringParameters().get("skip")).map(Boolean::parseBoolean).orElse(false);
                    reportService.sendStatementReport(CognitoUtils.getCurrentUsername(input), statementId, skip);
                    response.setStatusCode(200);
                    break;
            }

        } catch (Exception e) {
            log.error("error", e);
            response.setStatusCode(500);
            response.setBody(e.getMessage());
        }

        response.setHeaders(headers.toSingleValueMap());
        return response;
    }
}
