package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.SQSEvent;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.HandlerResponse;
import com.rentancy.integrations.pojos.MailChimpMemberRequest;
import com.rentancy.integrations.util.JSONUtils;
import com.rentancy.integrations.servicies.MailchimpService;
import org.apache.http.HttpStatus;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;

/**
 * integrations
 *
 * <AUTHOR>
 * @since 27/03/2021
 */
public class MailChimpHandler implements RequestHandler<SQSEvent, HandlerResponse> {

    private static final Logger log = LogManager.getLogger(MailChimpHandler.class);

    private final MailchimpService mailchimpService;

    public MailChimpHandler() {
        Config config = new ConfigImpl();
        mailchimpService = new MailchimpService(config, HttpClientBuilder.create().build());
    }

    /**
     * Handles a Lambda Function request
     *
     * @param event   The Lambda Function input
     * @param context The Lambda execution environment context object.
     * @return The Lambda Function output
     */
    @Override
    public HandlerResponse handleRequest(SQSEvent event, Context context) {
        int errors = 0;
        for (SQSEvent.SQSMessage msg : event.getRecords()) {
            try {
                final MailChimpMemberRequest member = JSONUtils.deserializePayload(msg.getBody(), MailChimpMemberRequest.class);
                if (!mailchimpService.createOrUpdateMember(member.getListId(), member)) {
                    errors++;
                }
            } catch (IOException e) {
                log.error("Cannot serialise the MailChimp data", e);
            }
            System.out.println(msg.getBody());
        }
        if (errors > 0) {
            return HandlerResponse.builder().status(HttpStatus.SC_EXPECTATION_FAILED).build();
        } else {
            return HandlerResponse.builder().status(HttpStatus.SC_OK).build();
        }
    }
}
