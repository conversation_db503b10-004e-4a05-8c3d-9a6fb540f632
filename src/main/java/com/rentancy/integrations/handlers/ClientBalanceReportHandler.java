package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.SQSEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.PropertyReportCommand;
import com.rentancy.integrations.servicies.ReportFactory;
import com.rentancy.integrations.servicies.ReportService;
import com.rentancy.integrations.util.SentryErrors;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.rentancy.integrations.util.JSONUtils.wrappedDeserializePayload;

public class ClientBalanceReportHandler implements RequestHandler<SQSEvent, Void> {

    private static final Logger log = LogManager.getLogger(ClientBalanceReportHandler.class);

    private final ReportService reportService;

    public ClientBalanceReportHandler() {
        this.reportService = ReportFactory.getReportService(new ConfigImpl());
    }

    @Override
    public Void handleRequest(SQSEvent input, Context context) {
        var records = input.getRecords();
        SentryErrors.initSentry();

        for (var record : records) {
            try {
                log.info("Record - {}", record);
                var payload = wrappedDeserializePayload(record.getBody(), PropertyReportCommand.class);
                reportService.generateClientBalanceReport(payload);
            } catch (Exception e) {
                SentryErrors.catchException(e);
                log.error("Failed to handle record", e);
            }
        }
        SentryErrors.destroySentry();
        return null;
    }
}
