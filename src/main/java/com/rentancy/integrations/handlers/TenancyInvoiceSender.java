package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.SQSEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.TenancyInvoiceSenderPayload;
import com.rentancy.integrations.servicies.PortfolioFactory;
import com.rentancy.integrations.servicies.PortfolioService;
import com.rentancy.integrations.util.SentryErrors;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.web.client.HttpClientErrorException;

import static com.rentancy.integrations.util.JSONUtils.wrappedDeserializePayload;

public class TenancyInvoiceSender implements RequestHandler<SQSEvent, Void> {

    private static final Logger log = LogManager.getLogger(TenancyInvoiceSender.class);

    private final PortfolioService portfolioService;

    public TenancyInvoiceSender() {
        this.portfolioService = PortfolioFactory.getPortfolioService(new ConfigImpl());
    }

    @Override
    public Void handleRequest(SQSEvent input, Context context) {
        var records = input.getRecords();
        SentryErrors.initSentry();

        for (var record : records) {
            try {
                log.info("Record - {}", record);

                if (portfolioService.invoiceRecordProcessed(record.getMessageId())) {
                    log.info("Record was already processed");
                    continue;
                }

                var payload = wrappedDeserializePayload(record.getBody(), TenancyInvoiceSenderPayload.class);

                portfolioService.saveInvoiceRecord(record.getMessageId());
                portfolioService.raiseTenancyInvoice(payload);
            } catch (HttpClientErrorException e) {
                SentryErrors.catchException(e);
                log.error("Failed to generate invoices - " + e.getMessage() + " " + e.getResponseBodyAsString(), e);
            } catch (Exception e) {
                SentryErrors.catchException(e);
                log.error("Failed to handle record", e);
            }
        }
        SentryErrors.destroySentry();
        return null;
    }
}
