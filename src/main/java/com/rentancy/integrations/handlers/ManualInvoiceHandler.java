package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.pojos.CognitoAuthorizer;
import com.rentancy.integrations.servicies.PortfolioFactory;
import com.rentancy.integrations.servicies.PortfolioService;
import com.rentancy.integrations.util.CognitoUtils;
import com.rentancy.integrations.util.ResponseUtils;

import lombok.SneakyThrows;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Objects;
import java.util.Optional;

import static com.rentancy.integrations.util.JSONUtils.deserializePayload;
import static com.rentancy.integrations.util.JSONUtils.toJsonString;
import static org.springframework.http.HttpHeaders.ORIGIN;

public class ManualInvoiceHandler implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {

    private static final Logger log = LogManager.getLogger(ManualInvoiceHandler.class);

    private final PortfolioService portfolioService;

    public ManualInvoiceHandler() {
        this.portfolioService = PortfolioFactory.getPortfolioService(new ConfigImpl());
    }

    @SneakyThrows
    @Override
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        var response = new APIGatewayV2ProxyResponseEvent();
        var source = input.getSource();

        if (Objects.nonNull(source) && source.equals("WARMUP")) {
            log.info("Warming lambda");
            return response;
        }

        var cognitoId = CognitoUtils.getCurrentUsername(input);
        var tenancyId = input.getPathParameters().get("tenancyId");
        var requestHeaders = input.getHeaders();
        var origin = Optional.ofNullable(requestHeaders.get(ORIGIN)).orElse(requestHeaders.get(ORIGIN.toLowerCase()));
        var headers = ResponseUtils.buildHeader(origin);
        headers.add("Content-Type", "application/json");

        try {
            log.info("Creating manual invoice for {}", tenancyId);

            var url = portfolioService.createManualInvoice(cognitoId, tenancyId);

            headers.add("Location", url);
            response.setStatusCode(201);
        } catch (Exception e) {
            log.error("Failed to generate manual invoice", e);
            response.setStatusCode(500);
            response.setBody(e.getMessage());
        }

        response.setHeaders(headers.toSingleValueMap());
        return response;
    }
}
