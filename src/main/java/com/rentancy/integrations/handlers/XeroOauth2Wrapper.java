package com.rentancy.integrations.handlers;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.servicies.XeroFactory;
import com.rentancy.integrations.servicies.XeroService;

/**
 * integrations
 *
 * <AUTHOR>
 * @since 30/03/2021
 */
public class XeroOauth2Wrapper extends XeroOauth2 {
    private static final Config CONFIG = new ConfigImpl();

    public XeroOauth2Wrapper() {
        super(CONFIG, XeroFactory.getXeroService(CONFIG));
    }
}
