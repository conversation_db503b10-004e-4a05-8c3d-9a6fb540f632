package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyRequestEvent;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.*;
import com.rentancy.integrations.servicies.oauth.OAuth;
import com.rentancy.integrations.util.CognitoUtils;
import com.rentancy.integrations.util.ResponseUtils;

import lombok.SneakyThrows;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpHeaders;

import java.io.IOException;
import java.time.Instant;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.rentancy.integrations.util.JSONUtils.deserializePayload;
import static com.rentancy.integrations.util.JSONUtils.toJsonString;
import static org.springframework.http.HttpHeaders.*;
import static org.springframework.http.MediaType.TEXT_HTML_VALUE;

public class XeroOauth2 implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {

    private static final Logger log = LogManager.getLogger(XeroOauth2.class);

    private static final String SUCCESS_TEMPLATE = "xero-connected.html";
    private static final String ERROR_TEMPLATE = "xero-failed.html";

    private final OAuth xeroService;
    private final Config config;

    public XeroOauth2(Config config, OAuth xeroService) {
        this.xeroService = xeroService;
        this.config = config;
    }

    @Override
    @SneakyThrows
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        var response = new APIGatewayV2ProxyResponseEvent();
        HttpHeaders headers;
        var source = input.getSource();

        if (Objects.nonNull(source) && source.equals("WARMUP")) {
            log.info("Warming lambda");
            return response;
        }

        try {
            log.info(input);
            var currentOrganisation = CognitoUtils.getCurrentOrganisation(input);
            var path = input.getPath();
            var body = input.getBody();
            var params = input.getQueryStringParameters();
            var requestContext = input.getRequestContext();
            log.info(path);
            log.info(body);
            log.info(params);

            var requestHeaders = input.getHeaders();
            var origin = Optional.ofNullable(requestHeaders.get(ORIGIN)).orElse(requestHeaders.get(ORIGIN.toLowerCase()));
            headers = ResponseUtils.buildHeader(origin);

            switch (path) {
                case "/xero/auth":
                    var authInput = deserializePayload(toJsonString(params), AuthInput.class);
                    if (Objects.nonNull(currentOrganisation) && !currentOrganisation.equals(authInput.getOrganisationId())) {
                        response.setHeaders(headers.toSingleValueMap());
                        response.setStatusCode(403);
                        return response;
                    }
                    var result = toJsonString(xeroService.getAuthorizationUrl(authInput.getOrganisationId(), authInput.getStartDate()));
                    response.setBody(result);
                    response.setStatusCode(200);
                    log.info("Body - {}", result);
                    break;
                case "/xero/appstore/auth":
                    var res = toJsonString(xeroService.getAppStoreAuthorizationUrl());
                    response.setBody(res);
                    response.setStatusCode(200);
                    log.info("Body - {}", res);
                    break;
                case "/xero/disconnect":
                    xeroService.disconnect(getCurrentUserName(requestContext));
                    response.setStatusCode(200);
                    break;
                case "/xero/callback":
                    var xeroExchangeInput = deserializePayload(toJsonString(params), ExchangeInput.class);

                    var exchangeResult = xeroService.exchangeCode(xeroExchangeInput);

                    log.info("Exchange result - {}", exchangeResult);
                    if (exchangeResult.getFlowType() == XeroFlowType.ORGANIZATION) {
                        headers.add(CONTENT_TYPE, TEXT_HTML_VALUE);
                        response.setBody(new String(config.getResource(SUCCESS_TEMPLATE)));
                        response.setStatusCode(200);
                    } else if (exchangeResult.getFlowType() == XeroFlowType.USER) {
                        headers.add(LOCATION, exchangeResult.getRedirectUrl());
                        response.setStatusCode(301);
                    }
                    break;
                default:
                    response.setBody(toJsonString(Map.of("message", "Invalid path " + path)));
                    response.setStatusCode(400);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            headers = new HttpHeaders();
            headers.add(CONTENT_TYPE, TEXT_HTML_VALUE);
            response.setStatusCode(500);
            response.setBody(new String(config.getResource(ERROR_TEMPLATE))
                    .replace("{request-id}", context.getAwsRequestId())
                    .replace("{error}", e.getMessage())
                    .replace("{timestamp}", Instant.now().toString())
            );
        }

        response.setHeaders(headers.toSingleValueMap());
        return response;
    }

    private String getCurrentUserName(APIGatewayV2ProxyRequestEvent.RequestContext context) throws IOException {
        var authorizer = deserializePayload(toJsonString(context.getAuthorizer()), CognitoAuthorizer.class);
        log.info(authorizer.toString());

        return Optional.ofNullable(authorizer.getClaims().getUsername()).orElse(authorizer.getClaims().getUsernameOld());
    }
}
