package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.CloudWatchLogsEvent;
import com.amazonaws.util.Base64;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.CloudwatchLogRecord;
import com.rentancy.integrations.servicies.CloudwatchLogsService;
import com.rentancy.integrations.servicies.IntegrationFactory;
import com.rentancy.integrations.util.JSONUtils;
import com.rentancy.integrations.util.Utils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class CloudwatchLogsHandler implements RequestHandler<CloudWatchLogsEvent, Void> {

    private static final Logger log = LogManager.getLogger(CloudwatchLogsHandler.class);

    private final CloudwatchLogsService cloudwatchLogsService;

    public CloudwatchLogsHandler() {
        this.cloudwatchLogsService = IntegrationFactory.getCloudwatchLogsService(new ConfigImpl());
    }

    @Override
    public Void handleRequest(CloudWatchLogsEvent input, Context context) {
        try {
            var data = input.getAwsLogs().getData();
            var decompressed = decodeEvent(data);
            log.info("Decompressed - " + decompressed);
            var payload = JSONUtils.wrappedDeserializePayload(decompressed, CloudwatchLogRecord.class);
            cloudwatchLogsService.handleRecord(payload);
        } catch (Exception e) {
            log.error("Failed to handle cloud watch logs event", e);
        }

        return null;
    }

    private String decodeEvent(String data) {
        var base64Decoded = Base64.decode(data);

        return Utils.decompressGzip(base64Decoded);
    }
}
