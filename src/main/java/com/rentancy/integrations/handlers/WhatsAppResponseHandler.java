package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.SQSEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.MessageResponsePayload;
import com.rentancy.integrations.servicies.IntegrationFactory;
import com.rentancy.integrations.servicies.WhatsAppService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.rentancy.integrations.util.JSONUtils.wrappedDeserializePayload;

public class WhatsAppResponseHandler implements RequestHandler<SQSEvent, Void> {

    private static final Logger log = LogManager.getLogger(WhatsAppResponseHandler.class);

    private final WhatsAppService whatsAppService;

    public WhatsAppResponseHandler() {
        this.whatsAppService = IntegrationFactory.getWhatsAppService(new ConfigImpl());
    }

    @Override
    public Void handleRequest(SQSEvent input, Context context) {
        log.info(input);
        var records = input.getRecords();

        for (var record : records) {
            try {
                log.info("Record - {}", record);
                var payload = wrappedDeserializePayload(record.getBody(), MessageResponsePayload.class);
                whatsAppService.sendResponse(payload);
            } catch (Exception e) {
                log.error("Failed to handle record", e);
            }
        }

        return null;
    }
}
