package com.rentancy.integrations.handlers;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.amazonaws.services.dynamodbv2.model.Record;
import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.DynamodbEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.Invoice;
import com.rentancy.integrations.servicies.readmodel.FindParentPropertyFromLineItemUseCase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class InvoiceLineItemTriggerHandler implements RequestHandler<DynamodbEvent, String> {

    private static final Logger logger = LoggerFactory.getLogger(InvoiceLineItemTriggerHandler.class);
    private final FindParentPropertyFromLineItemUseCase usecase;

    public InvoiceLineItemTriggerHandler(FindParentPropertyFromLineItemUseCase usecase) {
        this.usecase = usecase;
    }

    public InvoiceLineItemTriggerHandler() {
        this.usecase = FindParentPropertyFromLineItemUseCase.create(new ConfigImpl());
    }

    @Override
    public String handleRequest(DynamodbEvent event, Context context) {

        List<Invoice.LineItem> lineItems = event.getRecords().stream().filter(record -> !record.getEventName().equals("REMOVE")).map(this::getLineItemNewRecord).collect(Collectors.toList());
        logger.info("Invoice lineItems size: {}", lineItems.size());
        try {
            this.usecase.findParentPropertyFromLineItemAndSendOutSqsMessages(lineItems);
        }
        catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return "Processed " + event.getRecords().size() + " records.";
    }

    private Invoice.LineItem getLineItemNewRecord(Record record) {
        var newImage = record.getDynamodb().getNewImage();
        return Invoice.LineItem.builder()
                .id(Optional.ofNullable(newImage.get("id")).map(AttributeValue::getS).orElse(""))
                .updatedAt(record.getDynamodb().getApproximateCreationDateTime().toInstant())
                .invoiceId(Optional.ofNullable(newImage.get("invoiceLineItemInvoiceId")).map(AttributeValue::getS).orElse(""))
                .parentReference(Optional.ofNullable(newImage.get("invoiceLineItemOrganisationId")).map(AttributeValue::getS).orElse(""))
                .trackingName(Optional.ofNullable(newImage.get("trackingName")).map(AttributeValue::getS).orElse(""))
                .build();
    }
}

