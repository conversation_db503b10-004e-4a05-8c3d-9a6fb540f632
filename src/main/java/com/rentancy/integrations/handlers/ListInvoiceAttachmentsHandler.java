package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.servicies.PortfolioFactory;
import com.rentancy.integrations.servicies.PortfolioService;
import com.rentancy.integrations.util.CognitoUtils;
import com.rentancy.integrations.util.ResponseUtils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Objects;
import java.util.Optional;

import static com.rentancy.integrations.util.JSONUtils.toJsonString;
import static org.springframework.http.HttpHeaders.ORIGIN;

public class ListInvoiceAttachmentsHandler implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent>  {

  private static final Logger log = LogManager.getLogger(ListInvoiceAttachmentsHandler.class);

  private final PortfolioService portfolioService;

  public ListInvoiceAttachmentsHandler() {
    this.portfolioService = PortfolioFactory.getPortfolioService(new ConfigImpl());
  }

  @Override
  public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
    var response = new APIGatewayV2ProxyResponseEvent();
    var source = input.getSource();

    if (Objects.nonNull(source) && source.equals("WARMUP")) {
      log.info("Warming lambda");
      return response;
    }

    var currentOrganisation = CognitoUtils.getCurrentOrganisation(input);
    var requestHeaders = input.getHeaders();
    var origin = Optional.ofNullable(requestHeaders.get(ORIGIN)).orElse(requestHeaders.get(ORIGIN.toLowerCase()));
    var headers = ResponseUtils.buildHeader(origin);

    try {
      var params = input.getQueryStringParameters();
      var invoiceId = params.get("invoiceId");
      var organisationId = params.get("organisationId");

      if (Objects.nonNull(currentOrganisation) && !currentOrganisation.equals(organisationId)) {
        response.setHeaders(headers.toSingleValueMap());
        response.setStatusCode(403);
        return response;
      }

      var data = portfolioService.getInvoiceAttachments(invoiceId, organisationId);

      response.setStatusCode(200);
      response.setBody(toJsonString(data));
    } catch (Exception e) {
      log.error(e);
      response.setStatusCode(500);
    }

    response.setHeaders(headers.toSingleValueMap());
    return response;
  }
}
