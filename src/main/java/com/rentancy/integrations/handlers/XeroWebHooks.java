package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyRequestEvent;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.XeroWebHooksPayload;
import com.rentancy.integrations.servicies.XeroFactory;
import com.rentancy.integrations.servicies.XeroService;
import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Base64;
import java.util.Optional;

import static com.rentancy.integrations.util.JSONUtils.deserializePayload;

public class XeroWebHooks implements RequestHandler<APIGatewayV2ProxyRequestEvent, APIGatewayV2ProxyResponseEvent> {

    private static final Logger log = LogManager.getLogger(XeroWebHooks.class);

    private final XeroService xeroService;
    private final Config config;

    public XeroWebHooks() {
        this.config = new ConfigImpl();
        this.xeroService = XeroFactory.getXeroService(config);
    }

    @Override
    public APIGatewayV2ProxyResponseEvent handleRequest(APIGatewayV2ProxyRequestEvent input, Context context) {
        var response = new APIGatewayV2ProxyResponseEvent();
        try {
            var headers = input.getHeaders();
            var hash = headers.get("x-xero-signature");
            var body = input.getBody();

            log.info("Hash - " + hash);
            log.info("Body - " + body);
            if (!checkHash(hash, body)) {
                log.info("Invalid signature");
                response.setStatusCode(401);
                return response;
            }

            var payload = deserializePayload(body, XeroWebHooksPayload.class);
            xeroService.handleWebHookEvent(payload);
            response.setStatusCode(200);
            return response;
        } catch (Exception e) {
            log.error(e);
            response.setStatusCode(200);
        }

        return response;
    }

    private String calculateHmac(String data) {
        var secret = config.getXeroWebhooksSecret();
        var utils = new HmacUtils(HmacAlgorithms.HMAC_SHA_256, secret.getBytes());
        var digest = utils.hmac(data.getBytes());
        var base64 = Base64.getEncoder().encode(digest);
        return new String(base64);
    }

    private boolean checkHash(String hmac, String data) {
        return Optional.ofNullable(hmac)
                .map(h -> calculateHmac(data).equals(h))
                .orElse(false);
    }
}
