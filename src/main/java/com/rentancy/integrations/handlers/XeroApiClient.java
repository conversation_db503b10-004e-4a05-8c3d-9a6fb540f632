package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.pojos.CognitoAuthorizer;
import com.rentancy.integrations.pojos.XeroInvoiceApiInput;
import com.rentancy.integrations.pojos.XeroInvoicesUpdateInput;
import com.rentancy.integrations.servicies.XeroService;
import com.rentancy.integrations.util.CognitoUtils;
import com.rentancy.integrations.util.ResponseUtils;

import lombok.SneakyThrows;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.rentancy.integrations.util.JSONUtils.deserializePayload;
import static com.rentancy.integrations.util.JSONUtils.toApiResponse;
import static com.rentancy.integrations.util.JSONUtils.toJsonString;
import static com.rentancy.integrations.util.JSONUtils.wrappedToJsonString;
import static org.springframework.http.HttpHeaders.CONTENT_DISPOSITION;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;
import static org.springframework.http.HttpHeaders.ORIGIN;
import static org.springframework.http.MediaType.APPLICATION_OCTET_STREAM_VALUE;

public class XeroApiClient implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {

    private static final Logger log = LogManager.getLogger(XeroApiClient.class);

    private XeroService xeroService;

    public XeroApiClient(Config config, XeroService xeroService) {
        if (config.isTestEnv()) {
            return;
        }
        this.xeroService = xeroService;
    }

    @Override
    @SneakyThrows
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        var response = new APIGatewayV2ProxyResponseEvent();
        var source = input.getSource();

        if (Objects.nonNull(source) && source.equals("WARMUP")) {
            log.info("Warming lambda");
            return response;
        }

        log.info("Input - {}", wrappedToJsonString(input));

        var requestHeaders = input.getHeaders();
        var origin = Optional.ofNullable(requestHeaders.get(ORIGIN)).orElse(requestHeaders.get(ORIGIN.toLowerCase()));
        var headers = ResponseUtils.buildHeader(origin);

        try {
            var path = input.getPath();
            var body = input.getBody();
            var params = input.getQueryStringParameters();

            switch (path) {
                case "/xero/api/invoice": {
                    String cognitoId = CognitoUtils.getCurrentUsername(input);
                    var apiInput = deserializePayload(body, XeroInvoiceApiInput.class);
                    var content = xeroService.getInvoicePdf(cognitoId, apiInput.getInvoiceId(), apiInput.isXeroId());
                    var fileName = apiInput.getInvoiceId() + ".pdf";

                    headers.add(CONTENT_TYPE, APPLICATION_OCTET_STREAM_VALUE);
                    headers.add(CONTENT_DISPOSITION, "attachment; filename=" + fileName);
                    response.setBody(toApiResponse(content));
                    response.setIsBase64Encoded(true);
                    break;
                }
                case "/public/xero/api/invoice": {
                    var organisationId = params.get("organisationId");
                    var invoiceId = params.get("invoiceId");

                    var content = xeroService.getInvoicePdf(organisationId, invoiceId);
                    var fileName = invoiceId + ".pdf";

                    headers.add(CONTENT_TYPE, APPLICATION_OCTET_STREAM_VALUE);
                    headers.add(CONTENT_DISPOSITION, "attachment; filename=" + fileName);
                    response.setBody(toApiResponse(content));
                    response.setIsBase64Encoded(true);
                    break;
                }
                case "/xero/api/invoices/update": {
                    String cognitoId = CognitoUtils.getCurrentUsername(input);
                    var apiInput = deserializePayload(body, XeroInvoicesUpdateInput.class);
                    var statementId = Optional
                            .ofNullable(params)
                            .map(p -> p.get("statementId"))
                            .orElse(null);
                    var xeroInvoicesUpdateResponse
                            = xeroService.updateInvoicesStatus(cognitoId, statementId, apiInput);
                    response.setBody(wrappedToJsonString(xeroInvoicesUpdateResponse));
                    break;
                }
            }

            response.setStatusCode(200);
        } catch (Exception e) {
            log.error(e);
            response.setStatusCode(500);
            var message = Objects.isNull(e.getMessage()) ? "Something went wrong" : e.getMessage();
            response.setBody(toJsonString(Map.of("message", message)));
        }

        response.setHeaders(headers.toSingleValueMap());

        return response;
    }
}
