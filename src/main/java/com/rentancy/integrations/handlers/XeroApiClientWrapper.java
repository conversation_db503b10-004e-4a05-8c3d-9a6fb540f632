package com.rentancy.integrations.handlers;

import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.servicies.XeroFactory;

/**
 * integrations
 *
 * <AUTHOR>
 * @since 30/03/2021
 */
public class XeroApiClientWrapper extends XeroApiClient {
    private static final Config CONFIG = new ConfigImpl();

    public XeroApiClientWrapper() {
        super(CONFIG, XeroFactory.getXeroService(CONFIG));
    }

}
