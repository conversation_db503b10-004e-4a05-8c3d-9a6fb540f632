package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.SQSEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.LandlordBillUpdateCommand;
import com.rentancy.integrations.servicies.PortfolioFactory;
import com.rentancy.integrations.servicies.PortfolioService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.rentancy.integrations.util.JSONUtils.wrappedDeserializePayload;

public class LandlordBillUpdater implements RequestHandler<SQSEvent, Void> {

    private static final Logger log = LogManager.getLogger(LandlordBillUpdater.class);

    private final PortfolioService portfolioService;

    public LandlordBillUpdater() {
        this.portfolioService = PortfolioFactory.getPortfolioService(new ConfigImpl());
    }

    @Override
    public Void handleRequest(SQSEvent input, Context context) {
        var records = input.getRecords();

        for (var record : records) {
            try {
                log.info("Record - {}", record);

                var payload = wrappedDeserializePayload(record.getBody(), LandlordBillUpdateCommand.class);
                portfolioService.updateLandlordBill(payload.getPropertyId());
            } catch (Exception e) {
                log.error("Failed to handle record", e);
            }
        }

        return null;
    }
}
