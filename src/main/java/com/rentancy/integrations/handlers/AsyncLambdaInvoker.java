package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.servicies.AsyncJobService;
import com.rentancy.integrations.servicies.IntegrationFactory;
import com.rentancy.integrations.util.CognitoUtils;
import com.rentancy.integrations.util.ResponseUtils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Objects;
import java.util.Optional;

import static org.springframework.http.HttpHeaders.CONTENT_TYPE;
import static org.springframework.http.HttpHeaders.ORIGIN;

public class AsyncLambdaInvoker implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {

  private static final Logger log = LogManager.getLogger(AsyncLambdaInvoker.class);

  private final AsyncJobService asyncJobService;

  public AsyncLambdaInvoker() {
    this.asyncJobService = IntegrationFactory.getAsyncJobService(new ConfigImpl());
  }

  @Override
  public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
    var response = new APIGatewayV2ProxyResponseEvent();
    var source = input.getSource();

    if (Objects.nonNull(source) && source.equals("WARMUP")) {
      log.info("Warming lambda");
      return response;
    }
    var params = input.getQueryStringParameters();
    var jobName = params.get("jobName");
    var data = input.getBody();

    log.info("query params {}", params);
    var requestHeaders = input.getHeaders();
    var origin = Optional.ofNullable(requestHeaders.get(ORIGIN)).orElse(requestHeaders.get(ORIGIN.toLowerCase()));
    var headers = ResponseUtils.buildHeader(origin);
    headers.add(CONTENT_TYPE, "application/pdf;");

    try {
      var currentUser = CognitoUtils.getCurrentUsername(input);
      asyncJobService.runJob(jobName, data, currentUser);

      response.setStatusCode(200);
    } catch (Exception e) {
      log.error("Failed to handle event", e);
      response.setStatusCode(500);
    }

    response.setHeaders(headers.toSingleValueMap());
    return response;
  }
}
