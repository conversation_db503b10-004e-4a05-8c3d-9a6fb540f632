package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.pojos.DepositManagementPayload;
import com.rentancy.integrations.servicies.DepositManagementExportServiceImpl;
import com.rentancy.integrations.servicies.DepositManagementExportServiceImpl.Report;
import com.rentancy.integrations.servicies.DepositManagementFactory;
import com.rentancy.integrations.util.CognitoUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpHeaders;

import java.util.Objects;

import static com.rentancy.integrations.util.JSONUtils.toApiResponse;
import static com.rentancy.integrations.util.JSONUtils.wrappedDeserializePayload;
import static org.apache.james.mime4j.dom.field.FieldName.CONTENT_TRANSFER_ENCODING;
import static org.springframework.http.HttpHeaders.*;
import static org.springframework.http.MediaType.APPLICATION_OCTET_STREAM_VALUE;

@SuppressWarnings("unused")
public class DepositManagementExportHandler implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {

    private static final Logger log = LogManager.getLogger(DepositManagementExportHandler.class);

    private final DepositManagementExportServiceImpl depositManagementExportService;

    public DepositManagementExportHandler() {
        var depositManagementService = DepositManagementFactory.getDepositManagement(new ConfigImpl());
        this.depositManagementExportService = DepositManagementFactory.getDepositManagementExport(depositManagementService);
    }

    @Override
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        var headers = new HttpHeaders();
        var response = new APIGatewayV2ProxyResponseEvent();
        var source = input.getSource();

        if (Objects.nonNull(source) && source.equals("WARMUP")) {
            log.info("Warming lambda");
            return response;
        }

        var currentOrganisation = CognitoUtils.getCurrentOrganisation(input);
        var author = CognitoUtils.getCurrentUsername(input);

        var inputData = input.getBody();
        var body = wrappedDeserializePayload(inputData, DepositManagementPayload.class);
        headers.add(ACCESS_CONTROL_ALLOW_ORIGIN, "*");
        headers.add(ACCESS_CONTROL_ALLOW_METHODS, "OPTIONS, POST");
        headers.add(ACCESS_CONTROL_ALLOW_HEADERS, "*");
        headers.add(ACCESS_CONTROL_EXPOSE_HEADERS, "*");

        if (Objects.nonNull(currentOrganisation) && !currentOrganisation.equals(body.getOrganisationId())) {
            response.setHeaders(headers.toSingleValueMap());
            response.setStatusCode(403);
            return response;
        }

        try {
            log.info("query body {}", body);
            Report excelReport = depositManagementExportService.exportDepositsToExcelFile(body, author);
            response.setStatusCode(200);
            headers.add(CONTENT_TYPE, APPLICATION_OCTET_STREAM_VALUE);
            headers.add(CONTENT_DISPOSITION, "attachment; filename=" + excelReport.getName() + "_export.xlsx");
            headers.add(CONTENT_TRANSFER_ENCODING, "base64");
            response.setBody(toApiResponse(excelReport.getContents()));
            response.setIsBase64Encoded(true);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setStatusCode(500);
        }

        response.setHeaders(headers.toSingleValueMap());
        return response;
    }
}
