package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.Config;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.pojos.PropertyFinanceSummary;
import com.rentancy.integrations.pojos.sqs.ReadModelParentPropertySummaryFillCommand;
import com.rentancy.integrations.servicies.SQSClient;
import com.rentancy.integrations.servicies.TenancyService;
import com.rentancy.integrations.servicies.TenancyServiceFactory;
import com.rentancy.integrations.servicies.persistence.ReadModelRepository;
import com.rentancy.integrations.util.CognitoUtils;
import com.rentancy.integrations.util.ResponseUtils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.Instant;
import java.util.Objects;
import java.util.Optional;

import static com.rentancy.integrations.util.JSONUtils.toJsonString;
import static com.rentancy.integrations.util.JSONUtils.wrappedToJsonString;
import static org.springframework.http.HttpHeaders.*;

public class ParentPropertySummary implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {

    private static final Logger log = LogManager.getLogger(ParentPropertySummary.class);

    private static final String READ_MODEL_PARENT_PROPERTY_SUMMARY_QUEUE = "READ_MODEL_PARENT_PROPERTY_SUMMARY_QUEUE";

    private final TenancyService tenancyService;
    private final ReadModelRepository readModelRepository;
    private final SQSClient sqsClient;
    private final Config config;

    public ParentPropertySummary() {
        this.config = new ConfigImpl();
        this.tenancyService = TenancyServiceFactory.getTenancyService(config);
        this.readModelRepository = ReadModelRepository.create(config);
        this.sqsClient = new SQSClient();
    }

    @Override
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        var response = new APIGatewayV2ProxyResponseEvent();
        var source = input.getSource();

        if (Objects.nonNull(source) && source.equals("WARMUP")) {
            log.info("Warming lambda");
            return response;
        }

        var requestHeaders = input.getHeaders();
        var origin = Optional.ofNullable(requestHeaders.get(ORIGIN)).orElse(requestHeaders.get(ORIGIN.toLowerCase()));
        var headers = ResponseUtils.buildHeader(origin);

        try {
            var parentPropertyId = input.getPathParameters().get("parentPropertyId");
            var organisationId = CognitoUtils.getCurrentOrganisation(input);

            PropertyFinanceSummary output;
            var readModel = readModelRepository.findByPropertyId(parentPropertyId);
            if (readModel.getLeft() != null) {
                output = readModel.getLeft();
            } else {
                ReadModelParentPropertySummaryFillCommand message = new ReadModelParentPropertySummaryFillCommand(organisationId, parentPropertyId, Instant.now().toString());
                log.info("Message for SQS: {}", wrappedToJsonString(message));
                var queueName = config.getVariable(READ_MODEL_PARENT_PROPERTY_SUMMARY_QUEUE);
                sqsClient.enqueue(queueName, wrappedToJsonString(message));
                output = tenancyService.calculateParentPropertyFinanceSummary(parentPropertyId);
            }
            response.setStatusCode(200);
            response.setBody(toJsonString(output));
        } catch (Exception e) {
            log.error("error", e);
            response.setStatusCode(500);
        }

        response.setHeaders(headers.toSingleValueMap());
        return response;
    }
}
