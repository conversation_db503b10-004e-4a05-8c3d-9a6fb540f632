package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.pojos.LandlordBillsRequest;
import com.rentancy.integrations.pojos.LandlordBillsRequestV2;
import com.rentancy.integrations.pojos.LandlordBillsRequestV3;
import com.rentancy.integrations.servicies.payout.PayoutCalculationRequestDto;
import com.rentancy.integrations.servicies.payout.PayoutCommandV1;
import com.rentancy.integrations.servicies.payout.PayoutService;
import com.rentancy.integrations.util.JSONUtils;
import com.rentancy.integrations.servicies.PortfolioFactory;
import com.rentancy.integrations.servicies.PortfolioService;
import com.rentancy.integrations.util.CognitoUtils;
import com.rentancy.integrations.util.ResponseUtils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpHeaders;

import java.util.Objects;
import java.util.Optional;

import static org.springframework.http.HttpHeaders.ORIGIN;

public class LandlordBillSender implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {

    private static final Logger log = LogManager.getLogger(LandlordBillSender.class);

    private final PortfolioService portfolioService;
    private final PayoutService payoutService;

    public LandlordBillSender() {
        var config = new ConfigImpl();
        this.portfolioService = PortfolioFactory.getPortfolioService(config);
        this.payoutService = PayoutService.create(config);
    }

    @Override
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        var response = new APIGatewayV2ProxyResponseEvent();
        var source = input.getSource();

        if (Objects.nonNull(source) && source.equals("WARMUP")) {
            log.info("Warming lambda");
            return response;
        }

        if (Objects.nonNull(source) && source.equals("DAILY_RUN")) {
            return response;
        }
        var requestHeaders = input.getHeaders();
        var origin = Optional.ofNullable(requestHeaders.get(ORIGIN)).orElse(requestHeaders.get(ORIGIN.toLowerCase()));
        var headers = ResponseUtils.buildHeader(origin);
        headers.add("Content-Type", "application.json");

        var currentOrganisation = CognitoUtils.getCurrentOrganisation(input);

        try {
            var path = input.getPath();
            var params = input.getQueryStringParameters();
            var statementId = params.get("statementId");
            var cognitoId = CognitoUtils.getCurrentUsername(input);

            if (path.startsWith("/landlordbills")) {
                var body = JSONUtils.wrappedDeserializePayload(input.getBody(), LandlordBillsRequest.class);
                portfolioService.sendLandlordBills(cognitoId, statementId, body);
            } else if (path.equals("/v2/landlordbill")) {
                var organisationId = params.get("organisationId");
                if (Objects.nonNull(currentOrganisation) && !currentOrganisation.equals(organisationId)) {
                    response.setHeaders(headers.toSingleValueMap());
                    response.setStatusCode(403);
                    return response;
                }
                var body = JSONUtils.wrappedDeserializePayload(input.getBody(), LandlordBillsRequestV2.class);
                portfolioService.sendLandlordBillV2(organisationId, body);
            } else if (path.equals("/v3/landlordbill")) {
                var organisationId = params.get("organisationId");
                if (Objects.nonNull(currentOrganisation) && !currentOrganisation.equals(organisationId)) {
                    response.setHeaders(headers.toSingleValueMap());
                    response.setStatusCode(403);
                    return response;
                }
                var body = JSONUtils.wrappedDeserializePayload(input.getBody(), LandlordBillsRequestV3.class);
                portfolioService.sendLandlordBillV3(organisationId, body);
            } else if (path.equals("/payout-process/v1/payout")) {
                var organisationId = params.get("organisationId");
                if (Objects.nonNull(currentOrganisation) && !currentOrganisation.equals(organisationId)) {
                    return forbidden(response, headers, currentOrganisation + " does not equal " + organisationId);
                }
                var body = JSONUtils.wrappedDeserializePayload(input.getBody(), PayoutCommandV1.class);
                var result = payoutService.payout(organisationId, body);
                response.setStatusCode(result.success ? 200 : 400);
                response.setBody(JSONUtils.wrappedToJsonString(result));
                return response;
            } else if (path.equals("/payout-process/v1/calculate")) {
                log.info("Calculating payout");
                var body = JSONUtils.wrappedDeserializePayload(input.getBody(), PayoutCalculationRequestDto.class);
                if (Objects.isNull(currentOrganisation) || !currentOrganisation.equals(body.organisationId)) {
                    return forbidden(response, headers, currentOrganisation + " does not equal " + body.organisationId);
                }

                response.setBody(JSONUtils.wrappedToJsonString(payoutService.calculatePayout(currentOrganisation, body)));
                return response;
            } else {
                var id = input.getPathParameters().get("id");
                portfolioService.sendLandlordBill(cognitoId, id, statementId);
            }

            response.setStatusCode(200);
        } catch (Exception e) {
            log.error("Error handling event", e);
            response.setBody(e.getMessage());
            response.setStatusCode(500);
        }

        response.setHeaders(headers.toSingleValueMap());
        return response;
    }

    private APIGatewayV2ProxyResponseEvent forbidden(APIGatewayV2ProxyResponseEvent response, HttpHeaders headers, String body) {
        response.setHeaders(headers.toSingleValueMap());
        response.setStatusCode(403);
        response.setBody(body);
        return response;
    }
}
