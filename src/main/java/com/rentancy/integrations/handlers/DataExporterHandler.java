package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.pojos.DataExporterInput;
import com.rentancy.integrations.pojos.DataExporterResponse;
import com.rentancy.integrations.servicies.DataExporterService;
import com.rentancy.integrations.servicies.IntegrationFactory;
import com.rentancy.integrations.util.CognitoUtils;
import com.rentancy.integrations.util.ResponseUtils;

import lombok.SneakyThrows;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.rentancy.integrations.util.JSONUtils.deserializePayload;
import static com.rentancy.integrations.util.JSONUtils.toApiResponse;
import static com.rentancy.integrations.util.JSONUtils.toJsonString;
import static org.springframework.http.HttpHeaders.CONTENT_DISPOSITION;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;
import static org.springframework.http.HttpHeaders.ORIGIN;
import static org.springframework.http.MediaType.APPLICATION_OCTET_STREAM_VALUE;

public class DataExporterHandler implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {

    private static final Logger log = LogManager.getLogger(DataExporterHandler.class);

    private final DataExporterService dataExporterService;

    public DataExporterHandler() {
        this.dataExporterService = IntegrationFactory.getDataExporterService(new ConfigImpl());
    }

    @Override
    @SneakyThrows
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        var response = new APIGatewayV2ProxyResponseEvent();
        var requestHeaders = input.getHeaders();
        var origin = Optional.ofNullable(requestHeaders.get(ORIGIN)).orElse(requestHeaders.get(ORIGIN.toLowerCase()));
        var headers = ResponseUtils.buildHeader(origin);
        var source = input.getSource();

        if (Objects.nonNull(source) && source.equals("WARMUP")) {
            log.info("Warming lambda");
            return response;
        }

        var currentOrganisation = CognitoUtils.getCurrentOrganisation(input);

        try {
            log.info(input);
            var path = input.getPath();
            var body = input.getBody();
            var params = input.getQueryStringParameters();
            var exporterInput = deserializePayload(toJsonString(params), DataExporterInput.class);
            DataExporterResponse dataExporterResponse = null;
            log.info(path);
            log.info(body);
            log.info(params);

            if (Objects.nonNull(currentOrganisation) && !currentOrganisation.equals(exporterInput.getOrganisationId())) {
                response.setHeaders(headers.toSingleValueMap());
                response.setStatusCode(403);
                return response;
            }

            switch (path) {
                case "/contacts/export":
                    dataExporterResponse = dataExporterService.exportContacts(exporterInput);
                    break;
                case "/properties/export":
                    dataExporterResponse = dataExporterService.exportProperties(exporterInput);
                    break;
                case "/contracts/export":
                    dataExporterResponse = dataExporterService.exportContracts(exporterInput);
                    break;
                default:
                    response.setBody(toJsonString(Map.of("message", "Invalid path " + path)));
                    response.setStatusCode(400);
            }

            if (dataExporterResponse != null) {
                headers.add(CONTENT_TYPE, APPLICATION_OCTET_STREAM_VALUE);
                headers.add(CONTENT_DISPOSITION, "attachment; filename=" + dataExporterResponse.getFileName());

                response.setStatusCode(200);
                response.setBody(toApiResponse(dataExporterResponse.getContent()));
                response.setIsBase64Encoded(true);
            }
        } catch (Exception e) {
            log.error("Failed to export data", e);
            response.setStatusCode(500);
            var message = Objects.isNull(e.getMessage()) ? "Something went wrong" : e.getMessage();
            response.setBody(toJsonString(Map.of("message", message)));
        }

        response.setHeaders(headers.toSingleValueMap());
        return response;
    }
}
