package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.pojos.OrganisationCommand;
import com.rentancy.integrations.servicies.PortfolioFactory;
import com.rentancy.integrations.servicies.PortfolioService;
import com.rentancy.integrations.util.CognitoUtils;
import com.rentancy.integrations.util.ResponseUtils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Objects;
import java.util.Optional;

import static com.rentancy.integrations.util.JSONUtils.wrappedDeserializePayload;
import static org.springframework.http.HttpHeaders.*;

public class OrgTenancyJournalCollectingHandler implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {

    private static final Logger log = LogManager.getLogger(PropertySummary.class);

    private final PortfolioService portfolioService;

    public OrgTenancyJournalCollectingHandler() {
        this.portfolioService = PortfolioFactory.getPortfolioService(new ConfigImpl());
    }

    @Override
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        var payload = wrappedDeserializePayload(input.getBody(), OrganisationCommand.class);
        var organisationId = payload.getOrganisationId();
        var currentOrganisation = CognitoUtils.getCurrentOrganisation(input);

        if (Objects.nonNull(currentOrganisation) && !currentOrganisation.equals(organisationId)) {
            return getHttpResponse(input, 403);
        }
        try {
            portfolioService.triggerAutoJournalNow(currentOrganisation);
        } catch (Exception e) {
            log.error("Error", e);
            return getHttpResponse(input, 500);
        }
        return getHttpResponse(input, 200);
    }

    private APIGatewayV2ProxyResponseEvent getHttpResponse(ApiGatewayProxyRequestInput input,
                                                           int statusCode) {
        var requestHeaders = input.getHeaders();
        var origin = Optional.ofNullable(requestHeaders.get(ORIGIN)).orElse(requestHeaders.get(ORIGIN.toLowerCase()));
        var headers = ResponseUtils.buildHeader(origin);
        var response = new APIGatewayV2ProxyResponseEvent();
        response.setHeaders(headers.toSingleValueMap());
        response.setStatusCode(statusCode);
        return response;
    }
}