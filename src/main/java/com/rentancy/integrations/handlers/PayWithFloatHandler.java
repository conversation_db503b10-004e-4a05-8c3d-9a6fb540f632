package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.pojos.PayWithFloatPayload;
import com.rentancy.integrations.servicies.PortfolioFactory;
import com.rentancy.integrations.servicies.PortfolioService;
import com.rentancy.integrations.util.CognitoUtils;
import com.rentancy.integrations.util.ResponseUtils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Optional;

import static com.rentancy.integrations.util.JSONUtils.wrappedDeserializePayload;
import static org.springframework.http.HttpHeaders.ORIGIN;

public class PayWithFloatHandler implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {

    private static final Logger log = LogManager.getLogger(PayWithFloatHandler.class);

    private final PortfolioService portfolioService;

    public PayWithFloatHandler() {
        this.portfolioService = PortfolioFactory.getPortfolioService(new ConfigImpl());
    }

    @Override
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        var response = new APIGatewayV2ProxyResponseEvent();

        var requestHeaders = input.getHeaders();
        var origin = Optional.ofNullable(requestHeaders.get(ORIGIN)).orElse(requestHeaders.get(ORIGIN.toLowerCase()));
        var headers = ResponseUtils.buildHeader(origin);
        headers.add("Content-Type", "application/json");

        try {
            var payload = wrappedDeserializePayload(input.getBody(), PayWithFloatPayload.class);
            payload.setOrganisationId(CognitoUtils.getCurrentOrganisation(input));

            portfolioService.payBillWithFloat(payload);

            response.setStatusCode(201);
        } catch (Exception e) {
            log.error("Failed to pay with float", e);
            response.setStatusCode(500);
            response.setBody(e.getMessage());
        }

        response.setHeaders(headers.toSingleValueMap());
        return response;
    }
}
