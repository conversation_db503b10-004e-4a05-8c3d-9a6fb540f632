package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.SQSEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.LandlordInvoicePaidCommand;
import com.rentancy.integrations.servicies.PortfolioFactory;
import com.rentancy.integrations.servicies.PortfolioService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.web.client.HttpClientErrorException;

import static com.rentancy.integrations.util.JSONUtils.wrappedDeserializePayload;

public class LandlordBillHandler implements RequestHandler<SQSEvent, Void> {

    private static final Logger log = LogManager.getLogger(LandlordBillHandler.class);

    private final PortfolioService portfolioService;

    public LandlordBillHandler() {
        this.portfolioService = PortfolioFactory.getPortfolioService(new ConfigImpl());
    }

    @Override
    public Void handleRequest(SQSEvent input, Context context) {
        var records = input.getRecords();

        for (var record : records) {
            try {
                log.info("Record - {}", record);

                if (portfolioService.invoiceRecordProcessed(record.getMessageId())) {
                    log.info("Record was already processed");
                    continue;
                }

                var payload = wrappedDeserializePayload(record.getBody(), LandlordInvoicePaidCommand.class);
                portfolioService.saveInvoiceRecord(record.getMessageId());
                portfolioService.saveLandlordBill(payload.getTenancy(), payload.getAmountPaid(), payload.getInvoiceId());
            } catch (HttpClientErrorException e) {
                log.error("Failed to send landlord bill - " + e.getMessage() + " " + e.getResponseBodyAsString(), e);
            } catch (Exception e) {
                log.error("Failed to handle record", e);
            }
        }

        return null;
    }
}
