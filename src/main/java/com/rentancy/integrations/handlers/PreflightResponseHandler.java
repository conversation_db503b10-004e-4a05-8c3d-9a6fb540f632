package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyRequestEvent;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.springframework.http.HttpHeaders.*;
import static org.springframework.http.HttpMethod.*;

public class PreflightResponseHandler implements RequestHandler<APIGatewayV2ProxyRequestEvent, APIGatewayV2ProxyResponseEvent> {

    public final static String DEV_ENV = "dev";
    public final static String DEV_US_ENV = "devus";
    private final static String LOCALHOST_ORIGIN = "http://localhost";
    private final static List<String> ALLOWED_ORIGIN_REGEXES = List.of(
            "^https://.*\\.amplifyapp.com/?$",
            "^https://.*\\.loftyworks.com/?$",
            "^https://.*\\.loftyworks.io/?$"
    );

    @Getter
    @Setter
    private String currentEnv = System.getenv("ENV");

    @Override
    public APIGatewayV2ProxyResponseEvent handleRequest(APIGatewayV2ProxyRequestEvent input, Context context) {
        var headers = input.getHeaders();
        var origin = Optional.ofNullable(headers.get(ORIGIN)).orElse(headers.get(ORIGIN.toLowerCase()));
        var response = new APIGatewayV2ProxyResponseEvent();
        response.setStatusCode(HttpStatus.OK.value());
        var allowedOrigin = isAllowedOrigin(origin) ? origin : null;
        response.setHeaders(createResponseHeaders(allowedOrigin).toSingleValueMap());
        return response;
    }

    private boolean isAllowedOrigin(String origin) {
        return ALLOWED_ORIGIN_REGEXES.stream().anyMatch(allowedOrigin -> {
            Pattern pattern = Pattern.compile(allowedOrigin);
            Matcher matcher = pattern.matcher(origin);
            return matcher.matches();
        }) || (isDev() && origin.startsWith(LOCALHOST_ORIGIN));
    }

    private boolean isDev() {
        return DEV_ENV.equals(currentEnv) || DEV_US_ENV.equals(currentEnv);
    }

    private HttpHeaders createResponseHeaders(String allowedOrigin) {
        var responseHeaders = new HttpHeaders();
        var corsAllowedHeaders = String.join(",",
                ACCEPT,
                AUTHORIZATION,
                CACHE_CONTROL,
                CONTENT_TYPE,
                PRAGMA,
                REFERER,
                USER_AGENT,
                "X-Amz-Date",
                "X-Api-Key",
                "X-Amz-Security-Token"
        );
        var corsAllowedMethods = String.join(",",
                GET.name(),
                POST.name(),
                PATCH.name(),
                PUT.name(),
                DELETE.name(),
                OPTIONS.name()
        );

        if (allowedOrigin != null) {
            responseHeaders.add(ACCESS_CONTROL_ALLOW_ORIGIN, allowedOrigin);
            responseHeaders.add(ACCESS_CONTROL_ALLOW_CREDENTIALS, "true");
        }

        responseHeaders.add(ACCESS_CONTROL_ALLOW_METHODS, corsAllowedMethods);
        responseHeaders.add(ACCESS_CONTROL_ALLOW_HEADERS, corsAllowedHeaders);
        return responseHeaders;
    }
}
