package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.pojos.Tenancy;
import lombok.extern.slf4j.Slf4j;

import java.util.function.BiFunction;

@Slf4j
public class RentInvoiceDatesQueryHandlerInternal implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {

    private final RentInvoiceDatesQueryHandler rentInvoiceDatesQueryHandler;

    public RentInvoiceDatesQueryHandlerInternal() {
        BiFunction<Tenancy, ApiGatewayProxyRequestInput, Boolean> authorizationStrategy =
                (Tenancy tenancy, ApiGatewayProxyRequestInput input) -> true;
        this.rentInvoiceDatesQueryHandler = new RentInvoiceDatesQueryHandler(authorizationStrategy);
    }

    @Override
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        return rentInvoiceDatesQueryHandler.handleRequest(input, context);
    }
}
