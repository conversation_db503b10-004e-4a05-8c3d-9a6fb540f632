package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.pojos.PropertySummaryRequest;
import com.rentancy.integrations.servicies.ReportFactory;
import com.rentancy.integrations.servicies.ReportService;
import com.rentancy.integrations.util.CognitoUtils;
import com.rentancy.integrations.util.ResponseUtils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Objects;
import java.util.Optional;

import static com.rentancy.integrations.util.JSONUtils.deserializePayload;
import static com.rentancy.integrations.util.JSONUtils.toJsonString;
import static org.springframework.http.HttpHeaders.*;

public class ClientBalanceSummaryHandler implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {

    private static final Logger log = LogManager.getLogger(PropertySummary.class);

    private final ReportService reportService;

    public ClientBalanceSummaryHandler() {
        this.reportService = ReportFactory.getReportService(new ConfigImpl());
    }

    @Override
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        var response = new APIGatewayV2ProxyResponseEvent();
        var source = input.getSource();

        if (Objects.nonNull(source) && source.equals("WARMUP")) {
            log.info("Warming lambda");
            return response;
        }


        var params = input.getQueryStringParameters();
        var path = input.getPath();
        var body = input.getBody();
        var organisationId = params.get("organisationId");
        var currentOrganisation = CognitoUtils.getCurrentOrganisation(input);
        var requestHeaders = input.getHeaders();
        var origin = Optional.ofNullable(requestHeaders.get(ORIGIN)).orElse(requestHeaders.get(ORIGIN.toLowerCase()));
        var headers = ResponseUtils.buildHeader(origin);

        if (Objects.nonNull(currentOrganisation) && !currentOrganisation.equals(organisationId)) {
            response.setHeaders(headers.toSingleValueMap());
            response.setStatusCode(403);
            return response;
        }

        try {
            String result = null;
            switch (path) {
                case "/client/summary":
                    var clientSummary = reportService.generateAllClientSummary(organisationId);
                    result = toJsonString(clientSummary.get());
                    break;
                case "/property/summary":
                    var propertySummaryRequest = deserializePayload(body, PropertySummaryRequest.class);
                    result = toJsonString(reportService.generatePropertySummary(organisationId, propertySummaryRequest));
                    break;
                default:
                    log.error("Invalid path - " + path);
            }

            response.setStatusCode(200);
            response.setBody(result);
        } catch (Exception e) {
            log.error("error", e);
            response.setStatusCode(500);
        }

        response.setHeaders(headers.toSingleValueMap());
        return response;
    }
}