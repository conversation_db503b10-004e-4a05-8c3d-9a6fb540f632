package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.AutoInvoiceEvent;
import com.rentancy.integrations.pojos.HandlerResponse;
import com.rentancy.integrations.servicies.PortfolioFactory;
import com.rentancy.integrations.servicies.PortfolioService;
import com.rentancy.integrations.util.SentryErrors;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.Clock;
import java.time.Instant;
import java.util.Optional;

import static java.time.ZoneOffset.UTC;

public class TenancyInvoiceCollector implements RequestHandler<AutoInvoiceEvent, HandlerResponse> {

    private static final Logger log = LogManager.getLogger(TenancyInvoiceCollector.class);

    private final PortfolioService portfolioService;

    public TenancyInvoiceCollector() {
        this.portfolioService = PortfolioFactory.getPortfolioService(new ConfigImpl());
    }

    @Override
    public HandlerResponse handleRequest(AutoInvoiceEvent input, Context context) {
        SentryErrors.initSentry();

        try {
            portfolioService.collectAutoInvoiceTenancies(input.getSourceType(), resolveClock(input));
            SentryErrors.destroySentry();
            return HandlerResponse
                    .builder()
                    .status(200)
                    .build();
        } catch (Exception e) {
            SentryErrors.catchException(e);
            SentryErrors.destroySentry();
            log.error("Error", e);
            return HandlerResponse
                    .builder()
                    .status(500)
                    .build();
        }
    }

    private Clock resolveClock(AutoInvoiceEvent input) {
        return Optional.ofNullable(input.getNow())
                .map(Instant::parse)
                .map(it -> {
                    log.info("Manual rent invoice collection triggered with date: {}", it);
                    return Clock.fixed(it, UTC);
                })
                .orElse(Clock.systemUTC());
    }
}
