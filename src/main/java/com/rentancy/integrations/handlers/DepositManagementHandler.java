package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.*;
import com.rentancy.integrations.servicies.DepositManagementFactory;
import com.rentancy.integrations.servicies.persistence.DepositManagement;
import com.rentancy.integrations.util.CognitoUtils;
import com.rentancy.integrations.util.JSONUtils;
import com.rentancy.integrations.util.ResponseUtils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Objects;
import java.util.Optional;

import static com.rentancy.integrations.util.JSONUtils.wrappedDeserializePayload;
import static org.springframework.http.HttpHeaders.ORIGIN;

public class DepositManagementHandler implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {

    private static final Logger log = LogManager.getLogger(DepositManagementHandler.class);

    private final DepositManagement depositManagementService;


    public DepositManagementHandler() {
        this.depositManagementService = DepositManagementFactory.getDepositManagement(new ConfigImpl());
    }

    @Override
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        var response = new APIGatewayV2ProxyResponseEvent();
        var source = input.getSource();

        if (Objects.nonNull(source) && source.equals("WARMUP")) {
            log.info("Warming lambda");
            return response;
        }

        var currentOrganisation = CognitoUtils.getCurrentOrganisation(input);

        var inputData = input.getBody();
        var body = wrappedDeserializePayload(inputData, DepositManagementPayload.class);
        var requestHeaders = input.getHeaders();
        var origin = Optional.ofNullable(requestHeaders.get(ORIGIN)).orElse(requestHeaders.get(ORIGIN.toLowerCase()));
        var headers = ResponseUtils.buildHeader(origin);
        headers.add("Content-Type", "application/json");

        if (Objects.nonNull(currentOrganisation) && !currentOrganisation.equals(body.getOrganisationId())) {
            response.setHeaders(headers.toSingleValueMap());
            response.setStatusCode(403);
            return response;
        }

        try {
            log.info("query body {}", body);
            var data = depositManagementService.getAllTypeDeposits(body);
            response.setStatusCode(200);
            response.setBody(JSONUtils.toJsonString(data));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setStatusCode(500);
        }

        response.setHeaders(headers.toSingleValueMap());
        return response;
    }
}
