package com.rentancy.integrations.handlers;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2ProxyResponseEvent;
import com.rentancy.integrations.config.ConfigImpl;
import com.rentancy.integrations.pojos.ApiGatewayProxyRequestInput;
import com.rentancy.integrations.pojos.DepositBillCreationCommand;
import com.rentancy.integrations.pojos.DepositReturnCommand;
import com.rentancy.integrations.servicies.PortfolioFactory;
import com.rentancy.integrations.servicies.PortfolioService;
import com.rentancy.integrations.util.CognitoUtils;
import com.rentancy.integrations.util.ResponseUtils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.web.client.HttpClientErrorException;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.rentancy.integrations.util.JSONUtils.wrappedDeserializePayload;
import static com.rentancy.integrations.util.JSONUtils.wrappedToJsonString;
import static org.springframework.http.HttpHeaders.ORIGIN;

public class DepositBillSenderHandler implements RequestHandler<ApiGatewayProxyRequestInput, APIGatewayV2ProxyResponseEvent> {

    private static final Logger log = LogManager.getLogger(DepositBillSenderHandler.class);

    private final PortfolioService portfolioService;

    public DepositBillSenderHandler() {
        this.portfolioService = PortfolioFactory.getPortfolioService(new ConfigImpl());
    }

    @Override
    public APIGatewayV2ProxyResponseEvent handleRequest(ApiGatewayProxyRequestInput input, Context context) {
        var response = new APIGatewayV2ProxyResponseEvent();
        var source = input.getSource();

        if (Objects.nonNull(source) && source.equals("WARMUP")) {
            log.info("Warming lambda");
            return response;
        }

        var currentOrganisation = CognitoUtils.getCurrentOrganisation(input);

        var data = input.getBody();
        var requestHeaders = input.getHeaders();
        var origin = Optional.ofNullable(requestHeaders.get(ORIGIN)).orElse(requestHeaders.get(ORIGIN.toLowerCase()));
        var headers = ResponseUtils.buildHeader(origin);
        headers.add("Content-Type", "application/json");

        log.info("Request body - {}", data);

        try {
            switch (input.getPath()) {
                case "/deposit/transferbill":
                    var depositBillCreationCommand = wrappedDeserializePayload(data, DepositBillCreationCommand.class);
                    if (Objects.nonNull(currentOrganisation) && !currentOrganisation.equals(depositBillCreationCommand.getOrganisationId())) {
                        response.setHeaders(headers.toSingleValueMap());
                        response.setStatusCode(403);
                        return response;
                    }
                    portfolioService.createDepositBill(depositBillCreationCommand);
                    break;
                case "/deposit/return":
                    var depositReturnCommand = wrappedDeserializePayload(data, DepositReturnCommand.class);
                    if (Objects.nonNull(currentOrganisation) && !currentOrganisation.equals(depositReturnCommand.getOrganisationId())) {
                        response.setHeaders(headers.toSingleValueMap());
                        response.setStatusCode(403);
                        return response;
                    }
                    portfolioService.returnDeposit(depositReturnCommand);
                    break;
                default:
                    log.error("Invalid path - {}", input.getPath());
            }

            response.setStatusCode(201);
        } catch (HttpClientErrorException e) {
            log.error("Failed to generate manual invoice", e);
            response.setStatusCode(e.getRawStatusCode());
            response.setBody(e.getResponseBodyAsString());
        } catch (Exception e) {
            log.error("Failed to generate manual invoice", e);
            response.setStatusCode(500);
            response.setBody(wrappedToJsonString(Map.of("Message", e.getMessage())));
        }

        response.setHeaders(headers.toSingleValueMap());
        return response;
    }
}
