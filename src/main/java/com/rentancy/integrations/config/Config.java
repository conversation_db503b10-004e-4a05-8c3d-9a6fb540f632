package com.rentancy.integrations.config;

import java.net.URI;
import java.util.List;

/**
 * integrations
 *
 * <AUTHOR>
 * @since 29/03/2021
 */
public interface Config extends GetConfigVariable {

    String REGION = "REGION";
    String DDB_REGION = "DDB_REGION";
    String UPLOADS_REGION = "UPLOADS_REGION";
    String MAILCHIMP_BASE_URI = "MA<PERSON>CHIMP_BASE_URI";
    String MAILCHIMP_API_KEY = "MAILCHIMP_API_KEY";
    String ENV = "ENV";
    String RENTANCY_DOMAIN = "RENTANCY_DOMAIN";
    String LAMBDA_REGION = "LAMBDA_REGION";
    String APPSYNC_LAMBDA_NAME = "APPSYNC_LAMBDA_NAME";
    String XERO_APP_CLIENT_ID = "XERO_APP_CLIENT_ID";
    String XERO_APP_CLIENT_SECRET = "XERO_APP_CLIENT_SECRET";
    String XERO_AUTHORIZATION_URL = "XERO_AUTHORIZATION_URL";
    String XERO_TOKEN_URL = "XERO_TOKEN_URL";
    String XERO_SCOPES = "XERO_SCOPES";
    String XERO_OPENID_SCOPES = "XERO_OPENID_SCOPES";
    String XERO_WEBHOOKS_SECRET = "XERO_WEBHOOKS_SECRET";
    String XERO_API_ROOT_PATH = "XERO_API_ROOT_PATH";
    String REDIRECT_URL = "REDIRECT_URL";
    String INTEGRATIONS_QUEUE = "INTEGRATIONS_QUEUE";
    String XERO_CONTACTS_QUEUE = "XERO_CONTACTS_QUEUE";
    String XERO_INVOICES_QUEUE = "XERO_INVOICES_QUEUE";
    String XERO_INVOICES_CALLBACK_QUEUE = "XERO_INVOICES_CALLBACK_QUEUE";
    String XERO_INVOICES_QUEUE_OUTBOUND = "XERO_INVOICES_QUEUE_OUTBOUND";
    String XERO_TRANSACTIONS_QUEUE = "XERO_TRANSACTIONS_QUEUE";
    String XERO_ACCOUNTS_QUEUE = "XERO_ACCOUNTS_QUEUE";
    String XERO_PAYMENTS_QUEUE = "XERO_PAYMENTS_QUEUE";
    String XERO_BANK_TRANSFER_QUEUE = "XERO_BANK_TRANSFER_QUEUE";
    String XERO_JOURNAL_QUEUE = "XERO_JOURNAL_QUEUE";
    String XERO_WEBHOOK_QUEUE = "XERO_WEBHOOK_QUEUE";
    String TENANCY_INVOICE_QUEUE = "TENANCY_INVOICE_QUEUE";
    String COMMISSION_INVOICE_QUEUE = "COMMISSION_INVOICE_QUEUE";
    String OVERSEAS_RESIDENT_BILL_QUEUE = "OVERSEAS_RESIDENT_BILL_QUEUE";
    String EMAIL_ATTACHMENT_BUCKET = "EMAIL_ATTACHMENT_BUCKET";
    String EMAIL_NOTIFICATION_QUEUE = "EMAIL_NOTIFICATION_QUEUE";
    String EMAIL_TEMPLATE_NOTIFICATION_QUEUE = "EMAIL_TEMPLATE_NOTIFICATION_QUEUE";
    String WHATS_APP_INTEGRATION_QUEUE = "WHATS_APP_INTEGRATION_QUEUE";
    String EMAIL_INVOICE_QUEUE = "EMAIL_INVOICE_QUEUE";
    String EMAIL_NOTIFICATION_QUEUE_REGION = "EMAIL_NOTIFICATION_QUEUE_REGION";
    String XERO_UI_ROOT_PATH = "XERO_UI_ROOT_PATH";
    String RENT_INVOICE_HISTORY_TABLE = "RENT_INVOICE_HISTORY_TABLE";
    String RENT_INVOICE_HISTORY_INDEX = "RENT_INVOICE_HISTORY_INDEX";
    String RENT_INVOICE_HISTORY_XERO_INDEX = "RENT_INVOICE_HISTORY_XERO_INDEX";
    String JASPER_SERVER_URL = "JASPER_SERVER_URL";
    String JASPER_SERVER_PORT = "JASPER_SERVER_PORT";
    String TWILIO_ACCOUNT_SID = "TWILIO_ACCOUNT_SID";
    String TWILIO_AUTH_TOKEN = "TWILIO_AUTH_TOKEN";
    String TWILIO_ADDRESS = "TWILIO_ADDRESS";
    String WHATS_APP_CONVERSATION_IDS = "WHATS_APP_CONVERSATION_IDS";
    String WHATS_APP_ORGANISATION_IDS = "WHATS_APP_ORGANISATION_IDS";
    String WHATS_APP_HELPDESK_IDS = "WHATS_APP_HELPDESK_IDS";
    String RENTANCY_DOCUMENT_UPLOADS = "RENTANCY_DOCUMENT_UPLOADS";
    String LANDLORD_BILL_QUEUE = "LANDLORD_BILL_QUEUE";
    String PROPERTY_BALANCE_REPORT_QUEUE_NAME = "PROPERTY_BALANCE_REPORT_QUEUE_NAME";
    String CLIENT_BALANCE_REPORT_QUEUE_NAME = "CLIENT_BALANCE_REPORT_QUEUE_NAME";
    String CLIENT_STATEMENT_REPORT_QUEUE_NAME = "CLIENT_STATEMENT_REPORT_QUEUE_NAME";
    String CLIENT_GENERAL_REPORT_QUEUE_NAME = "CLIENT_GENERAL_REPORT_QUEUE_NAME";
    String LANDLORD_COMMISSION_QUEUE = "LANDLORD_COMMISSION_QUEUE";
    String TENANCY_SCHEDULE_REPORT_QUEUE_NAME = "TENANCY_SCHEDULE_REPORT_QUEUE_NAME";
    String CASH_BALANCE_REPORT_QUEUE_NAME = "CASH_BALANCE_REPORT_QUEUE_NAME";
    String ORGANISATION_PROPERTY_DATA_EXPORTER_QUEUE_NAME = "ORGANISATION_PROPERTY_DATA_EXPORTER_QUEUE_NAME";
    String ERROR_CHANNEL_CONVERSATION_ID = "ERROR_CHANNEL_CONVERSATION_ID";
    String ACCOUNT_INDEX = "ACCOUNT_INDEX";

    String OVERSEAS_RESIDENT_REPORT_QUEUE_NAME = "OVERSEAS_RESIDENT_REPORT_QUEUE_NAME";

    String BACS_REPORT_QUEUE_NAME = "BACS_REPORT_QUEUE_NAME";

    String SUPPLIER_LANDLORD_REPORT_QUEUE_NAME = "SUPPLIER_LANDLORD_REPORT_QUEUE_NAME";

    String LANDLORD_BILL_UPDATE_QUEUE = "LANDLORD_BILL_UPDATE_QUEUE";

    String PRIMARY_ORGANISATION_ID = "PRIMARY_ORGANISATION_ID";

    String REVENUE_REPORT_SENDER_EMAILS = "REVENUE_REPORT_SENDER_EMAILS";

    String ORGANISATION_REPORT_SENDER_EMAILS = "ORGANISATION_REPORT_SENDER_EMAILS";

    String BULK_PAYOUT_QUEUE_NAME = "BULK_PAYOUT_QUEUE_NAME";

    String RENTANCY_SUPPORT_EMAIL = "RENTANCY_SUPPORT_EMAIL";

    String CUSTOM_WHATSAPP_ORGANISATION_NAMES = "CUSTOM_WHATSAPP_ORGANISATION_NAMES";

    String XERO_OVER_PAYMENTS_QUEUE = "XERO_OVER_PAYMENTS_QUEUE";
    String JOURNAL_BILL_SENDER_QUEUE = "JOURNAL_BILL_SENDER_QUEUE";
    String MONTHLY_JOURNAL_REPORT_SENDER_EMAILS = "MONTHLY_JOURNAL_REPORT_SENDER_EMAILS";
    String ORGANISATION_STRIPE_CHARGE_REPORT_EMAILS = "ORGANISATION_STRIPE_CHARGE_REPORT_EMAILS";
    String ORGANISATION_DAILY_SIGNUP_REPORT_EMAILS = "ORGANISATION_DAILY_SIGNUP_REPORT_EMAILS";

    String STRIPE_CHARGE_CURRENCY = "STRIPE_CHARGE_CURRENCY";
    String STRIPE_PROPERTY_CHARGE_RATE = "STRIPE_PROPERTY_CHARGE_RATE";
    String STRIPE_VAT_CHARGE_RATE = "STRIPE_VAT_CHARGE_RATE";
    String STRIPE_MINIMUM_CHARGE_RATE = "STRIPE_MINIMUM_CHARGE_RATE";

    String getAWSRegion();

    String getDDBRegion();

    String getUploadsRegion();

    String getEmailAttachmentBucket();

    URI getMailChimpBaseUri();

    String getMailChimpApiKey();

    String getEnv();

    boolean isTestEnv();

    String getLambdaRegion();

    String getAppsyncLambdaName();

    String getXeroAppClientId();

    String getXeroAppClientSecret();

    String getXeroAuthorizationUrl();

    String getXeroTokenUrl();

    String getRedirectUrl();

    List<String> getXeroScopes();

    List<String> getXeroOpenIdScopes();

    String getXeroWebhooksSecret();

    String getXeroApiRootPath();

    String getContactsQueue();

    String getInvoicesQueue();

    String getXeroInvoicesCallbackQueue();

    String getXeroInvoicesQueueOutbound();

    String getTransactionsQueue();

    String getAccountsQueue();

    String getPaymentsQueue();

    String getBankTransferQueue();

    String getJournalQueue();

    String getTenancyInvoiceQueue();

    String getCommissionInvoiceQueue();

    String getOverseasResidentBillQueue();

    byte[] getResource(String resourceName);

    String getEmailNotificationQueue();

    String getEmailTemplateNotificationQueue();

    String getEmailNotificationRegion();

    String getWhatsAppIntegrationQueue();

    String getXeroUIRootPath();

    String getRentInvoiceHistory();

    String getRentancyDomain();

    String getTwilioAccountSID();

    String getTwilioAuthToken();

    String getTwilioAddress();

    List<String> getWhatsAppConversationIds();

    List<String> getWhatsAppOrganisationIds();

    List<String> getWhatsAppHelpDeskIds();

    String getRentancyDocumentUploads();

    String getLandlordBillQueue();

    String getPropertyBalanceReportQueueName();

    String getClientBalanceReportQueueName();

    String getBacsReportQueueName();

    String getSupplierLandlordStatementReportQueueName();

    String getClientStatementReportQueueName();

    String getOverseasResidentReportQueueName();

    String getLandlordCommissionQueue();

    String getXeroWebHookQueue();

    String getClientGeneralReportQueueName();

    String getTenancyScheduleReportQueueName();

    String getOrganisationPropertyReportQueueName();

    String getCashBalanceReportQueueName();

    String getErrorChannelConversationId();

    String getTableIndexName();

    String getLandlordBillUpdateQueue();

    String getEmailInvoiceQueue();

    String getPrimaryOrganisationId();

    String getRevenueReportSenderEmails();

    String getOrganisationReportSenderEmails();

    String getBulkPayoutQueueName();

    List<String> getCustomWhatsAppOrganisationNames();

    String getRentancySupportEmail();

    String getOverPaymentQueue();

    String getJournalBillSenderQueue();

    String getMonthlyJournalReportSenderEmails();

    List<String> getOrganisationStripeChargeReportEmails();

    String getStripeChargeCurrency();

    String getStripePropertyChargeRate();

    String getStripeVatChargeRate();

    String getStripeMinimumCharge();

    List<String> getOrganisationDailySignupReportEmails();
}
