package com.rentancy.integrations.config;

import java.net.URI;
import java.util.Arrays;
import java.util.List;

/**
 * integrations
 *
 * <AUTHOR>
 * @since 29/03/2021
 */
public class ConfigImpl implements Config {

    @Override
    public String getAWSRegion() {
        return getVariable(REGION);
    }

    @Override
    public String getDDBRegion() {
        return getVariable(DDB_REGION);
    }

    @Override
    public String getUploadsRegion() {
        return getVariable(UPLOADS_REGION);
    }

    @Override
    public String getEmailAttachmentBucket() {
        return getVariable(EMAIL_ATTACHMENT_BUCKET);
    }

    @Override
    public URI getMailChimpBaseUri() {
        return URI.create(getVariable(MAILCHIMP_BASE_URI));
    }

    @Override
    public String getMailChimpApiKey() {
        return getVariable(MAILCHIMP_API_KEY).trim();
    }

    @Override
    public String getEnv() {
        return getVariable(ENV);
    }
    @Override
    public String getTableIndexName() {
        return getVariable(ACCOUNT_INDEX);
    }

    @Override
    public String getLandlordBillUpdateQueue() {
        return getVariable(LANDLORD_BILL_UPDATE_QUEUE);
    }

    public String getBacsReportQueueName() {
        return getVariable(BACS_REPORT_QUEUE_NAME);
    }

    @Override
    public String getEmailInvoiceQueue() {
        return getVariable(EMAIL_INVOICE_QUEUE);
    }

    @Override
    public String getPrimaryOrganisationId() {
        return getVariable(PRIMARY_ORGANISATION_ID);
    }

    @Override
    public String getRevenueReportSenderEmails() {
        return getVariable(REVENUE_REPORT_SENDER_EMAILS);
    }

    @Override
    public String getOrganisationReportSenderEmails() {
        return getVariable(ORGANISATION_REPORT_SENDER_EMAILS);
    }

    @Override
    public String getBulkPayoutQueueName() {
        return getVariable(BULK_PAYOUT_QUEUE_NAME);
    }

    @Override
    public String getRentancySupportEmail() {
        return getVariable(RENTANCY_SUPPORT_EMAIL);
    }

    @Override
    public List<String> getCustomWhatsAppOrganisationNames() {
        return Arrays.asList(getVariable(CUSTOM_WHATSAPP_ORGANISATION_NAMES).split(","));
    }

    @Override
    public boolean isTestEnv() {
        return getEnv().equals("test");
    }

    @Override
    public String getLambdaRegion() {
        return getVariable(LAMBDA_REGION);
    }

    @Override
    public String getAppsyncLambdaName() {
        return getVariable(APPSYNC_LAMBDA_NAME);
    }

    @Override
    public String getXeroAppClientId() {
        return getVariable(XERO_APP_CLIENT_ID);
    }

    @Override
    public String getXeroAppClientSecret() {
        return getVariable(XERO_APP_CLIENT_SECRET);
    }

    @Override
    public String getXeroAuthorizationUrl() {
        return getVariable(XERO_AUTHORIZATION_URL);
    }

    @Override
    public String getXeroTokenUrl() {
        return getVariable(XERO_TOKEN_URL);
    }

    @Override
    public String getRedirectUrl() {
        return getVariable(REDIRECT_URL);
    }

    @Override
    public List<String> getXeroScopes() {
        return Arrays.asList(getVariable(XERO_SCOPES).split(","));
    }

    @Override
    public List<String> getXeroOpenIdScopes() {
        return Arrays.asList(getVariable(XERO_OPENID_SCOPES).split(","));
    }

    @Override
    public String getXeroWebhooksSecret() {
        return getVariable(XERO_WEBHOOKS_SECRET);
    }

    @Override
    public String getXeroApiRootPath() {
        return getVariable(XERO_API_ROOT_PATH);
    }

    @Override
    public String getContactsQueue() {
        return getVariable(XERO_CONTACTS_QUEUE);
    }

    @Override
    public String getInvoicesQueue() {
        return getVariable(XERO_INVOICES_QUEUE);
    }

    @Override
    public String getXeroInvoicesCallbackQueue() {
        return getVariable(XERO_INVOICES_CALLBACK_QUEUE);
    }

    @Override
    public String getXeroInvoicesQueueOutbound() {
        return getVariable(XERO_INVOICES_QUEUE_OUTBOUND);
    }

    @Override
    public String getTransactionsQueue() {
        return getVariable(XERO_TRANSACTIONS_QUEUE);
    }

    @Override
    public String getAccountsQueue() {
        return getVariable(XERO_ACCOUNTS_QUEUE);
    }

    @Override
    public String getPaymentsQueue() {
        return getVariable(XERO_PAYMENTS_QUEUE);
    }

    @Override
    public String getBankTransferQueue() {
        return getVariable(XERO_BANK_TRANSFER_QUEUE);
    }

    @Override
    public String getJournalQueue() {
        return getVariable(XERO_JOURNAL_QUEUE);
    }

    @Override
    public String getTenancyInvoiceQueue() {
        return getVariable(TENANCY_INVOICE_QUEUE);
    }

    @Override
    public String getCommissionInvoiceQueue() {
        return getVariable(COMMISSION_INVOICE_QUEUE);
    }

    @Override
    public String getOverseasResidentBillQueue() {
        return getVariable(OVERSEAS_RESIDENT_BILL_QUEUE);
    }

    @Override
    public byte[] getResource(String resourceName) {
        try {
            return getClass().getClassLoader().getResourceAsStream(resourceName).readAllBytes();
        } catch (Exception e) {
            throw new IllegalStateException("Failed to get resource", e);
        }
    }

    @Override
    public String getEmailNotificationQueue() {
        return getVariable(EMAIL_NOTIFICATION_QUEUE);
    }

    @Override
    public String getEmailTemplateNotificationQueue() {
        return getVariable(EMAIL_TEMPLATE_NOTIFICATION_QUEUE);
    }

    @Override
    public String getWhatsAppIntegrationQueue() {
        return getVariable(WHATS_APP_INTEGRATION_QUEUE);
    }

    @Override
    public String getEmailNotificationRegion() {
        return getVariable(EMAIL_NOTIFICATION_QUEUE_REGION);
    }

    @Override
    public String getXeroUIRootPath() {
        return getVariable(XERO_UI_ROOT_PATH);
    }

    @Override
    public String getRentInvoiceHistory() {
        return getVariable(RENT_INVOICE_HISTORY_TABLE);
    }

    @Override
    public String getRentancyDomain() {
        return getVariable(RENTANCY_DOMAIN);
    }

    @Override
    @Deprecated
    public String getTwilioAccountSID() {
        return getVariable(TWILIO_ACCOUNT_SID);
    }

    @Override
    @Deprecated
    public String getTwilioAuthToken() {
        return getVariable(TWILIO_AUTH_TOKEN);
    }

    @Override
    @Deprecated
    public String getTwilioAddress() {
        return getVariable(TWILIO_ADDRESS);
    }

    @Override
    public List<String> getWhatsAppConversationIds() {
        return Arrays.asList(getVariable(WHATS_APP_CONVERSATION_IDS).split(",").clone());
    }

    @Override
    public List<String> getWhatsAppOrganisationIds() {
        return Arrays.asList(getVariable(WHATS_APP_ORGANISATION_IDS).split(",").clone());
    }

    @Override
    public List<String> getWhatsAppHelpDeskIds() {
        return Arrays.asList(getVariable(WHATS_APP_HELPDESK_IDS).split(",").clone());
    }

    @Override
    public String getRentancyDocumentUploads() {
        return getVariable(RENTANCY_DOCUMENT_UPLOADS);
    }

    @Override
    public String getLandlordBillQueue() {
        return getVariable(LANDLORD_BILL_QUEUE);
    }

    @Override
    public String getPropertyBalanceReportQueueName() {
        return getVariable(PROPERTY_BALANCE_REPORT_QUEUE_NAME);
    }

    @Override
    public String getClientBalanceReportQueueName() {
        return getVariable(CLIENT_BALANCE_REPORT_QUEUE_NAME);
    }

    @Override
    public String getSupplierLandlordStatementReportQueueName() {
        return getVariable(SUPPLIER_LANDLORD_REPORT_QUEUE_NAME);
    }

    @Override
    public String getClientStatementReportQueueName() {
        return getVariable(CLIENT_STATEMENT_REPORT_QUEUE_NAME);
    }

    @Override
    public String getOverseasResidentReportQueueName() {
        return getVariable(OVERSEAS_RESIDENT_REPORT_QUEUE_NAME);
    }

    @Override
    public String getLandlordCommissionQueue() {
        return getVariable(LANDLORD_COMMISSION_QUEUE);
    }

    @Override
    public String getXeroWebHookQueue() {
        return getVariable(XERO_WEBHOOK_QUEUE);
    }

    @Override
    public String getClientGeneralReportQueueName() {
        return getVariable(CLIENT_GENERAL_REPORT_QUEUE_NAME);
    }

    @Override
    public String getTenancyScheduleReportQueueName() {
        return getVariable(TENANCY_SCHEDULE_REPORT_QUEUE_NAME);
    }

    @Override
    public String getCashBalanceReportQueueName() {
        return getVariable(CASH_BALANCE_REPORT_QUEUE_NAME);
    }

    @Override
    public String getOrganisationPropertyReportQueueName() {
        return getVariable(ORGANISATION_PROPERTY_DATA_EXPORTER_QUEUE_NAME);
    }

    @Override
    public String getErrorChannelConversationId() {
        return getVariable(ERROR_CHANNEL_CONVERSATION_ID);
    }

    @Override
    public String getOverPaymentQueue() {
        return getVariable(XERO_OVER_PAYMENTS_QUEUE);
    }

    @Override
    public String getJournalBillSenderQueue() {
        return getVariable(JOURNAL_BILL_SENDER_QUEUE);
    }

    @Override
    public String getMonthlyJournalReportSenderEmails() {
        return getVariable(MONTHLY_JOURNAL_REPORT_SENDER_EMAILS);
    }

    @Override
    public List<String> getOrganisationStripeChargeReportEmails() {
        return Arrays.asList(getVariable(ORGANISATION_STRIPE_CHARGE_REPORT_EMAILS).split(",").clone());
    }

    @Override
    public String getStripeChargeCurrency() {
        return getVariable(STRIPE_CHARGE_CURRENCY);
    }

    @Override
    public String getStripePropertyChargeRate() {
        return getVariable(STRIPE_PROPERTY_CHARGE_RATE);
    }

    @Override
    public String getStripeVatChargeRate() {
        return getVariable((STRIPE_VAT_CHARGE_RATE));
    }

    @Override
    public String getStripeMinimumCharge() {
        return getVariable(STRIPE_MINIMUM_CHARGE_RATE);
    }

    @Override
    public List<String> getOrganisationDailySignupReportEmails() {
        return Arrays.asList(getVariable(ORGANISATION_DAILY_SIGNUP_REPORT_EMAILS).split(",").clone());
    }

}
