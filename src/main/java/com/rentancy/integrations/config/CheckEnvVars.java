package com.rentancy.integrations.config;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public interface CheckEnvVars extends GetConfigVariable {

    default List<String> getEmptyVars(List<String> varNames) {
        return varNames.stream()
                .map(this::getVariable)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    List<String> checkVars(List<String> varNames);

}
