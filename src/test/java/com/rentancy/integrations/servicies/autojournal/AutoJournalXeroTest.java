package com.rentancy.integrations.servicies.autojournal;

import com.xero.models.accounting.TrackingCategories;
import com.xero.models.accounting.TrackingCategory;
import com.xero.models.accounting.TrackingOption;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class AutoJournalXeroTest extends BaseAutoJournalTest {
    @BeforeEach
    void setup() {
        super.setup();
        // Make 2 tenancies to test multiple calls to Xero
        tenancies = List.of(
                tenancies.get(0).toBuilder().build(),
                tenancies.get(0).toBuilder().id("id").build());

        when(xeroClient.getTrackingCategoriesWithRetry(any(), any())).thenReturn(new TrackingCategories()
                .addTrackingCategoriesItem(new TrackingCategory()
                        .name("Property")
                        .trackingCategoryID(UUID.randomUUID()))
                .addTrackingCategoriesItem(new TrackingCategory()
                        .name("Contacts")
                        .trackingCategoryID(UUID.randomUUID()))
        );
    }

    @Test
    @DisplayName("When there are no Tracking categories defined in Xero, create Contacts and Properties TrackingCategories only once")
    void whenThereAreNoTrackingCategoriesDefinedInXero() {
        when(xeroClient.getTrackingCategoriesWithRetry(any(), any())).thenReturn(new TrackingCategories());

        when(xeroClient.createTrackingCategoryWithRetry(TENANT_ID, TOKEN, "Property")).thenReturn(new TrackingCategories()
                .trackingCategories(List.of(new TrackingCategory()
                        .trackingCategoryID(UUID.randomUUID())
                        .name("Property")
                )));
        when(xeroClient.createTrackingCategoryWithRetry(TENANT_ID, TOKEN, "Contacts")).thenReturn(new TrackingCategories()
                .trackingCategories(List.of(new TrackingCategory()
                        .trackingCategoryID(UUID.randomUUID())
                        .name("Contacts")
                )));

        var result = autoJournalService.sendMonthlyJournalBills(organisation, tenancies, period, List.of());

        assertThat(result).hasSize(2);

        verify(xeroClient, times(1)).createTrackingCategoryWithRetry(TENANT_ID, TOKEN, "Property");
        verify(xeroClient, times(0)).createTrackingCategoryWithRetry(TENANT_ID, TOKEN, "Contacts");
    }

    @Test
    @DisplayName("When there are Tracking categories defined in Xero but Options are empty," +
            "create Contacts and Property TrackingCategory options for each Tenancy" +
            "1 Call for Property, 1 Call for Tenant Contact, 1 Call for Landlord Contact")
    void whenThereAreTrackingCategoriesDefinedInXeroButNoOptions() {
        var result = autoJournalService.sendMonthlyJournalBills(organisation, tenancies, period, List.of());

        assertThat(result).hasSize(tenancies.size());

        // TrackingCategories are already created so don't call them
        verify(xeroClient, times(0)).createTrackingCategoryWithRetry(any(), any(), any());

        verify(xeroClient, times(tenancies.size())).createTrackingCategoryOptionWithRetry(any(), any(), any(), any());
    }

    @Test
    @DisplayName("When there are Tracking categories defined in Xero and TrackingOptions for Tenancy already exist" +
            " don't create new Options for Tenancy")
    void whenThereAreTrackingCategoriesAndOptionsDefinedInXero() {
        UUID propertyTrackingOptionId = UUID.randomUUID();
        UUID tenantTrackingOptionId = UUID.randomUUID();
        UUID landlordTrackingOptionId = UUID.randomUUID();

        tenancies = List.of(tenancies.get(0));
        when(xeroClient.getTrackingCategoriesWithRetry(any(), any())).thenReturn(new TrackingCategories()
                .addTrackingCategoriesItem(new TrackingCategory()
                        .name("Property")
                        .options(List.of(
                                new TrackingOption()
                                        .name("PropertyReference")
                                        .trackingOptionID(propertyTrackingOptionId)))
                        .trackingCategoryID(UUID.randomUUID()))
                .addTrackingCategoriesItem(new TrackingCategory()
                        .name("Contacts")
                        .options(List.of(
                                new TrackingOption()
                                        .name("TenantUser")
                                        .trackingOptionID(tenantTrackingOptionId),
                                new TrackingOption()
                                        .name("LandlordUser")
                                        .trackingOptionID(landlordTrackingOptionId)
                        ))
                        .trackingCategoryID(UUID.randomUUID()))
        );

        var result = autoJournalService.sendMonthlyJournalBills(organisation, tenancies, period, List.of());

        assertThat(result).hasSize(tenancies.size());

        // TrackingCategories are already created so don't call them
        verify(xeroClient, times(0)).createTrackingCategoryWithRetry(any(), any(), any());

        verify(xeroClient, times(0)).createTrackingCategoryOptionWithRetry(any(), any(), any(), any());
    }
}
