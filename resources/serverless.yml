service: integrationresources
frameworkVersion: "3"

# plugins:
#   - serverless-apigw-binary

custom:
  xeroReceiverFunctionName:
    prerelease: ${self:service}-${self:provider.stage}-xero-inbound-receiver
    default: ${self:service}-${self:provider.stage}-xero-integrations-service-receiver
  invoiceLineTriggerFunctionName:
    prerelease:  ${self:service}-${self:provider.stage}-invoice-line-item-trigger
    default:  ${self:service}-${self:provider.stage}-invoice-line-item-trigger-handler
  readParentPropertyFunctionName:
    prerelease: ${self:service}-${self:provider.stage}-read-model-parent-property
    default: ${self:service}-${self:provider.stage}-read-model-parent-property-summary
  xeroCallBackFunctionName:
    prerelease: ${self:service}-${self:provider.stage}-xero-integration-callback
    default: ${self:service}-${self:provider.stage}-xero-integration-callback-handler
  orgPropertyExporterFunctionName:
    prerelease: ${self:service}-${self:provider.stage}-organisation-property-exporter
    default: ${self:service}-${self:provider.stage}-organisation-property-data-exporter
  supplierLandlordReportFunctionName:
    prerelease: ${self:service}-${self:provider.stage}-supplier-landlord-report
    default: ${self:service}-${self:provider.stage}-supplier-landlord-statement-report
  eventBridge:
    eventBusName: "loftyworks-domain-events-${opt:stage}"
    eventSource: "lw.property-management"
  rentancy-domain:
    dev: dev.rentancy.com
    devuk: dev.uk.loftyworks.com
    stageuk: stageuk.rentancy.com
    prod: app.rentancy.com
    produs: produs.rentancy.io
    devus: devus.rentancy.io
    prerelease: todo
    prodeu: todo
  rentancy-primary-organisation-id:
    dev: rentancydevelopment
    devuk: loftyworksdevuk
    stageuk: rentancystageuk
    prod: rentancyinternal2
    produs: rentancyprodus
    devus: rentancydevus
    prerelease: rentancystageuk
    prodeu: rentancyprodeu
  revenue-report-sender-emails:
    dev: <EMAIL>
    devuk: <EMAIL>
    stageuk: <EMAIL>
    prod: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
    produs: <EMAIL>
    devus: <EMAIL>
    prerelease: <EMAIL>
    prodeu: <EMAIL>
  monthly-journal-report-sender-emails:
    dev: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
    devuk: <EMAIL>
    stageuk: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
    prod: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
    produs: <EMAIL>
    devus: <EMAIL>
    prerelease: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
    prodeu: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
  apigwBinary:
    types:
      - 'application/octet-stream'
  rentancySuppMail:
    dev: <EMAIL>
    devuk: <EMAIL>
    stageuk: <EMAIL>
    prod: <EMAIL>
    produs: <EMAIL>
    prerelease: <EMAIL>
    prodeu: <EMAIL>
  appsync:
    api-output:
      prod: 7ghe6kcqrrg37ohck7dmvk7zaa
      dev: o7ndtvliqnfdpouk7ybpefcb7u
      devuk: zc4ddtrzinaxxoqjlv5irtqbh4
      stageuk: hudtpmi3ebcnlfncsbaeqo7nwy
      produs: x533udwbyfdazdcp5t3xv2ghbm
      devus: ru24i6bsbvfozh54idsrvm4eoa
      prerelease: hhmlvxrpevhrdld2jmcx2dr3x4
      prodeu: gkyzrmicdjh55hv7qfs4hsvbvu
  lambda:
    appsync-service: appSyncService-${self:provider.stage}
  cognito:
    user-pool:
      dev: arn:aws:cognito-idp:${self:provider.cognito_region}:#{AWS::AccountId}:userpool/eu-west-2_zL6axz6tQ
      devuk: arn:aws:cognito-idp:${self:provider.cognito_region}:${aws:accountId}:userpool/eu-west-2_Tm05gPOz2
      stageuk: arn:aws:cognito-idp:${self:provider.cognito_region}:${aws:accountId}:userpool/eu-west-2_cQ6Icbk92
      prod: arn:aws:cognito-idp:${self:provider.cognito_region}:#{AWS::AccountId}:userpool/eu-west-2_ye9ZPay62
      produs: arn:aws:cognito-idp:${self:provider.cognito_region}:#{AWS::AccountId}:userpool/us-west-1_8FspwhEXz
      devus: arn:aws:cognito-idp:${self:provider.cognito_region}:#{AWS::AccountId}:userpool/us-west-2_srTkAjYKS
  s3:
    email-bucket: ${file(../config.${opt:stage, 'dev'}.yml):s3.email-bucket}
    email-bucket-prefix:
      dev: dev-inbox
      devuk: dev-inbox
      stageuk: stageuk-inbox
      prod: inbox
      devus: devus-inbox
      prerelease: stageuk-inbox
      prodeu: prodeu-inbox
    rentancy-document-uploads: com-rentancy-documents${self:provider.stage}-${self:provider.stage}
    rentancy-email-attachments-bucket: rentancy-email-attachments-${self:provider.stage}
  sqs:
    integrations-queue: integrations-queue-${self:provider.stage}
    integrations-queue-dlq: integrations-queue-${self:provider.stage}-dlq
    xero-update-queue: xero-update-queue-${self:provider.stage}
    xero-update-queue-dlq: xero-update-queue-${self:provider.stage}-dlq
    xero-contacts-queue: xero-contacts-queue-${self:provider.stage}
    xero-contacts-queue-dlq: xero-contacts-queue-${self:provider.stage}-dlq
    xero-invoices-queue: xero-invoices-queue-${self:provider.stage}
    xero-invoices-queue-dlq: xero-invoices-queue-${self:provider.stage}-dlq
    xero-invoices-queue-outbound: xero-invoices-queue-outbound
    xero-invoices-queue-outbound-dlq: xero-invoices-queue-outbound-dlq
    xero-invoices-callback-queue: xero-invoices-callback-queue
    xero-invoices-callback-queue-dlq: xero-invoices-callback-queue-dlq
    xero-transactions-queue: xero-transactions-queue-${self:provider.stage}
    xero-transactions-queue-dlq: xero-transactions-queue-${self:provider.stage}-dlq
    xero-accounts-queue: xero-accounts-queue-${self:provider.stage}
    xero-accounts-queue-dlq: xero-accounts-queue-${self:provider.stage}-dlq
    xero-over-payment-queue: xero-over-payment-queue-${self:provider.stage}
    xero-over-payment-queue-dlq: xero-over-payment-queue-${self:provider.stage}-dlq
    xero-payments-queue: xero-payments-queue-${self:provider.stage}
    xero-payments-queue-dlq: xero-payments-queue-${self:provider.stage}-dlq
    xero-bank-transfer-queue: xero-bank-transfer-queue-${self:provider.stage}
    xero-bank-transfer-queue-dlq: xero-bank-transfer-queue-${self:provider.stage}-dlq
    xero-journal-queue: xero-journal-queue-${self:provider.stage}
    xero-journal-queue-dlq: xero-journal-queue-${self:provider.stage}-dlq
    mailchimp-queue: mailchimp-queue-${self:provider.stage}
    mailchimp-queue-dlq: mailchimp-queue-${self:provider.stage}-dlq
    tenancy-invoice-queue: tenancy-invoice-queue-${self:provider.stage}
    tenancy-invoice-queue-dlq: tenancy-invoice-queue-${self:provider.stage}-dlq
    commission-invoice-queue: commission-invoice-queue-${self:provider.stage}
    commission-invoice-queue-dlq: commission-invoice-queue-${self:provider.stage}-dlq
    overseas-resident-bill-queue: overseas-resident-bill-queue-${self:provider.stage}
    overseas-resident-bill-queue-dlq: overseas-resident-bill-queue-${self:provider.stage}-dlq
    portfolio-invoice-queue: portfolio-invoice-queue-${self:provider.stage}
    portfolio-invoice-queue-dlq: portfolio-invoice-queue-${self:provider.stage}-dlq
    whats-app-queue: whats-app-queue-${self:provider.stage}
    whats-app-queue-dlq: whats-app-queue-${self:provider.stage}-dlq
    whats-app-integration-queue: whats-app-integration-queue-${self:provider.stage}
    whats-app-integration-queue-dlq: whats-app-integration-queue-${self:provider.stage}-dlq
    email-message-queue: email-message-queue-${self:provider.stage}
    email-message-queue-dlq: email-message-queue-${self:provider.stage}-dlq
    property-tracking-code-queue: property-tracking-code-queue-${self:provider.stage}
    property-tracking-code-queue-dlq: property-tracking-code-queue-${self:provider.stage}-dlq
    landlord-bill-queue: landlord-bill-queue-${self:provider.stage}
    landlord-bill-queue-dlq: landlord-bill-queue-${self:provider.stage}-dlq
    async-property-balance-report-queue: async-property-balance-report-queue-${self:provider.stage}
    async-property-balance-report-queue-dlq: async-property-balance-report-queue-${self:provider.stage}-dlq
    client-balance-report-queue: client-balance-report-queue-${self:provider.stage}
    client-balance-report-queue-dlq: client-balance-report-queue-${self:provider.stage}-dlq
    organisation-property-data-exporter-queue: organisation-property-data-exporter-queue-${self:provider.stage}
    organisation-property-data-exporter-queue-dlq: organisation-property-data-exporter-queue-${self:provider.stage}-dlq
    client-statement-report-queue: client-statement-report-queue-${self:provider.stage}
    client-statement-report-queue-dlq: client-statement-report-queue-${self:provider.stage}-dlq
    client-general-report-queue: client-general-report-queue-${self:provider.stage}
    client-general-report-queue-dlq: client-general-report-queue-${self:provider.stage}-dlq
    cash-balance-report-queue: cash-balance-report-queue-${self:provider.stage}
    cash-balance-report-queue-dlq: cash-balance-report-queue-${self:provider.stage}-dlq
    bacs-report-queue: bacs-report-queue-${self:provider.stage}
    bacs-report-queue-dlq: bacs-report-queue-${self:provider.stage}-dlq
    overseas-resident-report-queue: overseas-resident-report-queue-${self:provider.stage}
    overseas-resident-report-queue-dlq: overseas-resident-report-queue-${self:provider.stage}-dlq
    landlord-commission-queue: landlord-commission-queue-${self:provider.stage}
    landlord-commission-queue-dlq: landlord-commission-queue-${self:provider.stage}-dlq
    tenancy-schedule-queue: tenancy-schedule-queue-${self:provider.stage}
    tenancy-schedule-queue-dlq: tenancy-schedule-queue-${self:provider.stage}-dlq
    xero-webhook-queue: xero-webhook-queue-${self:provider.stage}
    xero-webhook-queue-dlq: xero-webhook-queue-${self:provider.stage}-dlq
    supplier-landlord-statement-report-queue: supplier-landlord-statement-report-queue-${self:provider.stage}
    supplier-landlord-statement-report-queue-dlq: supplier-landlord-statement-report-queue-${self:provider.stage}-dlq
    landlord-bill-update-queue: landlord-bill-update-queue-${self:provider.stage}
    landlord-bill-update-queue-dlq: landlord-bill-update-queue-${self:provider.stage}-dlq
    bulk-payout-queue: bulk-payout-queue-${self:provider.stage}
    bulk-payout-queue-dlq: bulk-payout-queue-${self:provider.stage}-dlq
    journal-bill-sender-queue: journal-bill-sender-queue-${self:provider.stage}
    journal-bill-sender-queue-dlq: journal-bill-sender-queue-${self:provider.stage}-dlq
    #Resources declared in notification repo
    email-notification-queue: email-notification-queue-${self:provider.stage}
    email-template-notification-queue: send-template-email-queue-${self:provider.stage}
    email-invoice-queue: email-invoice-queue-${self:provider.stage}
    read-model-parent-property-summary-queue: read-model-parent-property-summary-queue-${self:provider.stage}
    read-model-parent-property-summary-queue-dlq: read-model-parent-property-summary-queue-${self:provider.stage}-dlq

  dynamodb:
    email-message-table: EmailMessage-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    email-attachment-table: EmailAttachment-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    organisation-table: Organisation-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    tenancy-table: Tenancy-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    tenancy-settings-table: TenancySettings-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    user-table: User-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    user-table-denormalized-emails-index: gsi-ByDenormalizedEmails
    organisation-user-table: OrganisationUser-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    organisation-user-index: gsi-OrgranisationUsers
    address-organisation-index: gsi-OrganisationAddresses
    document-table: Document-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    document-organisation-index: gsi-OrganisationDocuments
    user-email-index: gsi-ByCognitoEmail
    user-xero-index: gsi-ByXeroId
    user-organisation-index: gsi-ByOrganisation
    integration-table: Integration-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    user-id-index: gsi-ByCognitoId
    integration-index: gsi-OrganisationUserIntegration
    integration-organisation-index: gsi-OrganisationIntegrations
    integration-state-index: gsi-ByState
    integration-tenant-index: gsi-TenantIntegration
    invoice-table: Invoice-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    invoice-allocation-table: InvoiceAllocation-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    invoice-organisation-index: gsi-OrganisationInvoices
    invoice-allocation-property-index: gsi-ByPropertyId
    invoice-allocation-index: gsi-InvoiceAllocations
    invoice-index: gsi-ByInvoiceId
    invoice-line-item-table: InvoiceLineItem-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    invoice-line-item-index: gsi-ByLineItemId
    invoice-line-item-invoice-index: gsi-InvoiceLineItems
    invoice-line-item-transaction-index: gsi-TransactionLineItems
    invoice-line-item-organisation-index: gsi-OrganisationLineItems
    transaction-table: Transaction-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    account-table: Account-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    transaction-index: gsi-ByTransactionId
    transaction-organisation-index: gsi-OrganisationTransactions
    account-index: gsi-ByAccountId
    account-organisation-index: gsi-OrganisationAccounts
    tenancy-reference-index: gsi-ByReference
    property-reference-index: gsi-ByReference
    tenancy-invoice-table: InvoiceTenancy-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    property-invoice-table: InvoiceProperty-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    property-table: Property-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    property-organisation-index: gsi-OrganisationProperties
    payment-table: Payment-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    payment-index: gsi-ByPaymentId
    payment-organisation-index: gsi-OrganisationPayments
    transfer-table: Transfer-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    transfer-index: gsi-ByTransferId
    tenancy-organisation-index: gsi-OrganisationTenancies
    tenancy-property-index: gsi-PropertyTenancies
    tenancy-settings-index: gsi-OrganisationSettings
    property-invoice-index: gsi-PropertyInvoices
    property-invoice-invoice-index: gsi-InvoiceProperties
    tenancy-invoice-index: gsi-TenancyInvoices
    tenancy-invoice-invoice-index: gsi-InvoiceTenancies
    statement-table: Statement-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    statement-index: gsi-OrganisationStatements
    statement-landlord-bill-index: gsi-LandlordBillStatements
    statement-property-index: gsi-PropertyStatements
    rent-invoice-history: RentInvoiceHistory-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    rent-invoice-history-tenancy-index: gsi-RentInvoiceHistoryTenancy
    rent-invoice-history-xero-index: gsi-ByXeroId
    address-table: Address-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    address-parent-index: gsi-ByParentIdAndParentType
    task-table: Task-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    task-organisation-index: gsi-OrgranisationTasks
    column-table: Column-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    conversation-table: Conversation-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    message-table: Message-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    message-index: gsi-ByWhatsAppId
    property-budget-table: PropertyBudget-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    property-budget-property-index: gsi-PropertyBudget
    xero-journal-table: XeroJournal-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    xero-journal-index: gsi-JournalId
    landlord-bill-table: LandlordBill-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    landlord-bill-organisation-index: gsi-ByReference
    landlord-bill-invoice-index: gsi-ByOriginalInvoiceId
    invoice-webhook-events-table: InvoiceWebhookEvents-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    invoice-line-item-tracking-name-index: gsi-ByTrackingName
    over-payment-table: OverPayment-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    over-payment-index: gsi-ByOverPaymentId
    over-payment-organisation-index: gsi-OrganisationOverPayments
    invoice-property-table: InvoiceProperty-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    read-model-parent-property-summary: ReadModelParentPropertySummary-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    parent-property-table: ParentPropertyEntity-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    parent-property-property-index: gsi-ParentPropertyEntityProperty
    invoice-tenancy-table: InvoiceTenancy-${self:custom.appsync.api-output.${self:provider.stage}}-${self:provider.stage}
    invoice-tenancy-index: gsi-InvoiceTenancies
  mailchimp:
    api-key:
      prod: ${ssm:/integrations/prod/mailchimp/api-key}
      dev: ${ssm:/integrations/dev/mailchimp/api-key}
      devuk: ${ssm:/integrations/devuk/mailchimp/api-key}
      stageuk: ${ssm:/integrations/dev/mailchimp/api-key}
      produs: todo
      devus: todo
      prerelease: todo
      prodeu: todo
    base-uri:
      prod: https://us10.api.mailchimp.com/3.0
      dev: https://us1.api.mailchimp.com/3.0
      devuk: https://us1.api.mailchimp.com/3.0
      stageuk: https://us1.api.mailchimp.com/3.0
      produs: https://us1.api.mailchimp.com/3.0
      devus: https://us1.api.mailchimp.com/3.0
      prerelease: https://us1.api.mailchimp.com/3.0
      prodeu: https://us1.api.mailchimp.com/3.0
  xero:
    authorization-url: https://login.xero.com/identity/connect/authorize
    token-url: https://identity.xero.com/connect/token
    scopes: openid,email,profile,offline_access,accounting.settings,accounting.transactions,accounting.contacts,accounting.reports.read,accounting.attachments,accounting.journals.read
    openid-scopes: openid,email,profile
    api-root-path: https://api.xero.com
    ui-root-path: https://go.xero.com
    webhooks-secret: ${ssm:/integrations/${self:provider.stage}/xero/webhooks-secret}
    redirect-url: ${ssm:/integrations/${self:provider.stage}/xero/redirect-url}
    app-client-id: ${ssm:/integrations/${self:provider.stage}/xero/app-client-id}
    app-client-secret: ${ssm:/integrations/${self:provider.stage}/xero/app-client-secret}
  jasper-server:
    rootUrl: http://ec2-35-178-161-29.eu-west-2.compute.amazonaws.com
  twilio:
    account-sid:
      prod: AC469d0bf4e62a93e573b31e29560401fe
      dev: ACb31a2a00556b9b12d336ad76ba11fe68
      devuk: ACb31a2a00556b9b12d336ad76ba11fe68
      stageuk: ACb31a2a00556b9b12d336ad76ba11fe68
      produs: ACb31a2a00556b9b12d336ad76ba11fe68
      devus: ACb31a2a00556b9b12d336ad76ba11fe68
      prerelease: ACb31a2a00556b9b12d336ad76ba11fe68
      prodeu: ACb31a2a00556b9b12d336ad76ba11fe68
    auth-token:
      prod: ${ssm:/integrationresources/prod/twilio/auth-token}
      dev: ${ssm:/integrationresources/dev/twilio/auth-token}
      devuk: ${ssm:/integrations/devuk/twilio/auth-token}
      stageuk: ${ssm:/integrationresources/dev/twilio/auth-token}
      produs: ${ssm:/integrationresources/produs/twilio/auth-token}
      devus: ${ssm:/integrationresources/devus/twilio/auth-token}
      prerelease: ${ssm:/integrationresources/dev/twilio/auth-token}
      prodeu: ${ssm:/integrationresources/dev/twilio/auth-token}
  rentancy-error-channel:
    dev: bbc1c110-fd24-11ec-9c8f-41c5115dadc5
    stageuk: bbc1c110-fd24-11ec-9c8f-41c5115dadc5
    prod: 205d1a50-0803-11ed-bac9-fd04947354bd
    produs: bbc1c110-fd24-11ec-9c8f-41c5115dadc5
    devus: bbc1c110-fd24-11ec-9c8f-41c5115dadc5

provider:
  name: aws
  region: ${file(../config.${opt:stage, 'dev'}.yml):region}
  cognito_region: ${file(../config.${opt:stage, 'dev'}.yml):region}
  ddb_region: ${file(../config.${opt:stage, 'dev'}.yml):region}
  lambda-region: ${file(../config.${opt:stage, 'dev'}.yml):region}
  uploads_bucket_region: ${file(../config.${opt:stage, 'dev'}.yml):region}
  notifications_region: ${file(../config.${opt:stage, 'dev'}.yml):region}
  runtime: java11
  versionFunctions: false
  stage: ${opt:stage, 'dev'}
  invoice-line-item-stream-arn: ${file(../config.${opt:stage, 'dev'}.yml):dynamodb.invoice-line-item-stream-arn}
  xero-invoices-queue-inbound-arn: ${file(../config.${opt:stage, 'dev'}.yml):sqs.xero-invoices-queue-inbound-arn}
  xero-invoices-callback-queue-arn: ${file(../config.${opt:stage, 'dev'}.yml):sqs.xero-invoices-callback-queue-arn}

  environment:
    REGION: ${self:provider.region}
    EVENT_SOURCE: ${self:custom.eventBridge.eventSource}
    EVENT_BUS_NAME: ${self:custom.eventBridge.eventBusName}

  iamRoleStatements:
    - Effect: "Allow"
      Action:
        - "sns:*"
      Resource:
        - "*"
    - Effect: "Allow"
      Action:
        - "sqs:*"
      Resource:
        - "*"
    - Effect: "Allow"
      Action:
        - "s3:*"
      Resource:
        - "*"
    - Effect: "Allow"
      Action:
        - "ses:*"
      Resource:
        - "*"
    - Effect: "Allow"
      Action:
        - "appsync:GraphQL"
      Resource:
        - "*"
    - Effect: "Allow"
      Action:
        - "dynamodb:*"
      Resource:
        - "*"
    - Effect: "Allow"
      Action:
        - "lambda:*"
      Resource:
        - "*"
    - Effect: "Allow"
      Action:
        - events:PutEvents
      Resource:
        - Arn:
          Fn::Sub: "arn:aws:events:${AWS::Region}:${AWS::AccountId}:event-bus/${self:custom.eventBridge.eventBusName}"

# you can add packaging information here
package:
  artifact: ../build/distributions/integrations.zip

functions:
#  email-parser:
#    handler: com.rentancy.integrations.handlers.EmailParser
#    timeout: 600
#    events:
#      - s3:
#          bucket: ${self:custom.s3.email-bucket}
#          event: s3:ObjectCreated:Put
#          existing: true
#          rules:
#            - prefix: ${self:custom.s3.email-bucket-prefix.${self:provider.stage}}
#    environment:
#      ENV: ${self:provider.stage}
#      REGION: ${self:provider.region}
#      DDB_REGION: ${self:provider.ddb_region}
#      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
#      EMAIL_BUCKET: ${self:custom.s3.email-bucket}
#      EMAIL_MESSAGE_TABLE: ${self:custom.dynamodb.email-message-table}
#      EMAIL_ATTACHMENT_TABLE: ${self:custom.dynamodb.email-attachment-table}
#      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
#      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
#      USER_TABLE: ${self:custom.dynamodb.user-table}
#      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
#      DOCUMENT_TABLE: ${self:custom.dynamodb.document-table}
#      RENTANCY_DOCUMENT_UPLOADS: ${self:custom.s3.rentancy-document-uploads}
#      LAMBDA_REGION: ${self:provider.lambda-region}
#      CONVERSATION_TABLE: ${self:custom.dynamodb.conversation-table}
#      MESSAGE_TABLE: ${self:custom.dynamodb.message-table}
#      MESSAGE_INDEX: ${self:custom.dynamodb.message-index}
#      USER_COGNITOID_INDEX: ${self:custom.dynamodb.user-id-index}
#      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
#      ORGANISATION_USER_TABLE: ${self:custom.dynamodb.organisation-user-table}
#      ORGANISATION_USER_INDEX: ${self:custom.dynamodb.organisation-user-index}

  xero-integrations-service-receiver:
    handler: com.rentancy.integrations.handlers.XeroIntegrationServiceReceiver
    name: ${self:custom.xeroReceiverFunctionName.${self:provider.stage}, self:custom.xeroReceiverFunctionName.default}
    events:
     - sqs: ${self:provider.xero-invoices-queue-inbound-arn}
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      XERO_INVOICES_QUEUE: ${self:custom.sqs.xero-invoices-queue}
      XERO_INVOICES_CALLBACK_QUEUE: ${self:custom.sqs.xero-invoices-callback-queue}

  invoice-line-item-trigger-handler:
    handler: com.rentancy.integrations.handlers.InvoiceLineItemTriggerHandler
    name: ${self:custom.invoiceLineTriggerFunctionName.${self:provider.stage}, self:custom.invoiceLineTriggerFunctionName.default}
    events:
      - stream:
          type: dynamodb
          arn: ${self:provider.invoice-line-item-stream-arn}
          batchSize: 5  # Adjust as necessary
          maximumBatchingWindowInSeconds: 60
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_AUTHORIZATION_URL: ${self:custom.xero.authorization-url}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      REDIRECT_URL: ${self:custom.xero.redirect-url}
      XERO_SCOPES: ${self:custom.xero.scopes}
      XERO_OPENID_SCOPES: ${self:custom.xero.openid-scopes}
      DDB_REGION: ${self:provider.ddb_region}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      USER_COGNITOID_INDEX: ${self:custom.dynamodb.user-id-index}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      RENTANCY_DOMAIN: ${self:custom.rentancy-domain.${self:provider.stage}}
      TENANCY_INVOICE_TABLE: ${self:custom.dynamodb.tenancy-invoice-table}
      TENANCY_INVOICE_INVOICE_INDEX: ${self:custom.dynamodb.tenancy-invoice-invoice-index}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      READ_MODEL_PARENT_PROPERTY_SUMMARY_QUEUE: ${self:custom.sqs.read-model-parent-property-summary-queue}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      PARENT_PROPERTY_TABLE: ${self:custom.dynamodb.parent-property-table}

  read-model-parent-property-summary:
    handler: com.rentancy.integrations.handlers.ReadModelParentPropertySummaryFillerHandler
    name: ${self:custom.readParentPropertyFunctionName.${self:provider.stage}, self:custom.readParentPropertyFunctionName.default}
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - ReadModelParentPropertySummaryQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      DDB_REGION: ${self:provider.region}
      INVOICE_PROPERTY_TABLE: ${self:custom.dynamodb.invoice-property-table}
      READ_MODEL_PARENT_PROPERTY_SUMMARY_TABLE: ${self:custom.dynamodb.read-model-parent-property-summary}
      PARENT_PROPERTY_TABLE: ${self:custom.dynamodb.parent-property-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      PROPERTY_PARENT_PROPERTY_INDEX: ${self:custom.dynamodb.parent-property-property-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}

  mailchimp-email:
    handler: com.rentancy.integrations.handlers.MailChimpHandler
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - MailchimpQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      MAILCHIMP_API_KEY: ${self:custom.mailchimp.api-key.${self:provider.stage}}
      MAILCHIMP_BASE_URI: ${self:custom.mailchimp.base-uri.${self:provider.stage}}
  xero-webhooks-handler:
    handler: com.rentancy.integrations.handlers.XeroWebHooksHandler
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - XeroWebhookQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_WEBHOOKS_SECRET: ${self:custom.xero.webhooks-secret}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      DDB_REGION: ${self:provider.ddb_region}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      XERO_CONTACTS_QUEUE: ${self:custom.sqs.xero-contacts-queue}
      XERO_INVOICES_QUEUE: ${self:custom.sqs.xero-invoices-queue}
      XERO_INVOICES_QUEUE_OUTBOUND: ${self:custom.sqs.xero-invoices-queue-outbound}
      XERO_INVOICES_CALLBACK_QUEUE: ${self:custom.sqs.xero-invoices-callback-queue}
      ORGANISATION_USER_TABLE: ${self:custom.dynamodb.organisation-user-table}
      ORGANISATION_USER_INDEX: ${self:custom.dynamodb.organisation-user-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      USER_TABLE_DENORMALIZED_EMAILS_INDEX: ${self:custom.dynamodb.user-table-denormalized-emails-index}
  xero-integration-callback-handler:
    handler: com.rentancy.integrations.handlers.XeroIntegrationCallbackHandler
    timeout: 600
    events:
      - sqs: ${self:provider.xero-invoices-callback-queue-arn}
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      TENANCY_INVOICE_QUEUE: ${self:custom.sqs.tenancy-invoice-queue}
      COMMISSION_INVOICE_QUEUE: ${self:custom.sqs.commission-invoice-queue}
      RENT_INVOICE_HISTORY_TABLE: ${self:custom.dynamodb.rent-invoice-history}
      RENT_INVOICE_HISTORY_INDEX: ${self:custom.dynamodb.rent-invoice-history-tenancy-index}
      LANDLORD_COMMISSION_QUEUE: ${self:custom.sqs.landlord-commission-queue}
      OVERSEAS_RESIDENT_BILL_QUEUE: ${self:custom.sqs.overseas-resident-bill-queue}
      TENANCY_SETTINGS_INDEX: ${self:custom.dynamodb.tenancy-settings-index}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_ORGANISATION_INDEX: ${self:custom.dynamodb.user-organisation-index}
      ORGANISATION_USER_TABLE: ${self:custom.dynamodb.organisation-user-table}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      EMAIL_INVOICE_QUEUE: ${self:custom.sqs.email-invoice-queue}
      XERO_INVOICES_QUEUE_OUTBOUND: ${self:custom.sqs.xero-invoices-queue-outbound}
      XERO_INVOICES_CALLBACK_QUEUE: ${self:custom.sqs.xero-invoices-callback-queue}
      XERO_UI_ROOT_PATH: ${self:custom.xero.ui-root-path}
      RENTANCY_DOMAIN: ${self:custom.rentancy-domain.${self:provider.stage}}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      MONTHLY_JOURNAL_REPORT_SENDER_EMAILS: ${self:custom.monthly-journal-report-sender-emails.${self:provider.stage}}
      EMAIL_ATTACHMENT_BUCKET: ${self:custom.s3.rentancy-email-attachments-bucket}
      PRIMARY_ORGANISATION_ID: ${self:custom.rentancy-primary-organisation-id.${self:provider.stage}}
      ORGANISATION_USER_INDEX: ${self:custom.dynamodb.organisation-user-index}
      ACCOUNT_TABLE: ${self:custom.dynamodb.account-table}
      ACCOUNT_INDEX: ${self:custom.dynamodb.account-index}
      ACCOUNT_ORGANISATION_INDEX: ${self:custom.dynamodb.account-organisation-index}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      LANDLORD_BILL_TABLE: ${self:custom.dynamodb.landlord-bill-table}
      LANDLORD_BILL_ORGANISATION_INDEX: ${self:custom.dynamodb.landlord-bill-organisation-index}

  xero-update:
    handler: com.rentancy.integrations.handlers.XeroUpdateHandler
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - XeroUpdateQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
  xero-data-fetcher:
    handler: com.rentancy.integrations.handlers.XeroDataFetcher
    timeout: 600
    events:
      - schedule:
          rate: rate(5 minutes)
          enabled: true
          input:
            type: INVOICE
      - schedule:
          rate: rate(6 minutes)
          enabled: true
          input:
            type: CONTACT
      - schedule:
          rate: rate(7 minutes)
          enabled: true
          input:
            type: ACCOUNT
      - schedule:
          rate: rate(8 minutes)
          enabled: true
          input:
            type: TRANSACTION
      - schedule:
          rate: rate(11 minutes)
          enabled: true
          input:
            type: OVERPAYMENT
      - schedule:
          rate: rate(13 minutes)
          enabled: true
          input:
            type: PAYMENT
      - schedule:
          rate: rate(17 minutes)
          enabled: true
          input:
            type: BANK_TRANSFER
      - schedule:
          rate: rate(19 minutes)
          enabled: false
          input:
            type: JOURNAL
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_TABLE_DENORMALIZED_EMAILS_INDEX: ${self:custom.dynamodb.user-table-denormalized-emails-index}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_INDEX: ${self:custom.dynamodb.invoice-index}
      INVOICE_LINE_ITEM_INDEX: ${self:custom.dynamodb.invoice-line-item-index}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      XERO_CONTACTS_QUEUE: ${self:custom.sqs.xero-contacts-queue}
      XERO_INVOICES_QUEUE: ${self:custom.sqs.xero-invoices-queue}
      XERO_INVOICES_QUEUE_OUTBOUND: ${self:custom.sqs.xero-invoices-queue-outbound}
      XERO_INVOICES_CALLBACK_QUEUE: ${self:custom.sqs.xero-invoices-callback-queue}
      XERO_TRANSACTIONS_QUEUE: ${self:custom.sqs.xero-transactions-queue}
      XERO_ACCOUNTS_QUEUE: ${self:custom.sqs.xero-accounts-queue}
      XERO_PAYMENTS_QUEUE: ${self:custom.sqs.xero-payments-queue}
      XERO_OVER_PAYMENTS_QUEUE: ${self:custom.sqs.xero-over-payment-queue}
      XERO_BANK_TRANSFER_QUEUE: ${self:custom.sqs.xero-bank-transfer-queue}
      XERO_JOURNAL_QUEUE: ${self:custom.sqs.xero-journal-queue}
      ACCOUNT_TABLE: ${self:custom.dynamodb.account-table}
      ACCOUNT_INDEX: ${self:custom.dynamodb.account-index}
      ACCOUNT_ORGANISATION_INDEX: ${self:custom.dynamodb.account-organisation-index}
      RENTANCY_SUPPORT_EMAIL: ${self:custom.rentancySuppMail.${self:provider.stage}}
      EMAIL_TEMPLATE_NOTIFICATION_QUEUE: ${self:custom.sqs.email-template-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      ORGANISATION_USER_TABLE: ${self:custom.dynamodb.organisation-user-table}
      ORGANISATION_USER_INDEX: ${self:custom.dynamodb.organisation-user-index}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}

  xero-contact-loader:
    handler: com.rentancy.integrations.handlers.XeroDataLoader::loadContact
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - XeroContactsQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      USER_ORGANISATION_INDEX: ${self:custom.dynamodb.user-organisation-index}
      ORGANISATION_USER_TABLE: ${self:custom.dynamodb.organisation-user-table}
      ORGANISATION_USER_INDEX: ${self:custom.dynamodb.organisation-user-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
  xero-over-payment-loader:
    handler: com.rentancy.integrations.handlers.XeroDataLoader::loadOverPayment
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - XeroOverPaymentQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      USER_ORGANISATION_INDEX: ${self:custom.dynamodb.user-organisation-index}
      ORGANISATION_USER_TABLE: ${self:custom.dynamodb.organisation-user-table}
      ORGANISATION_USER_INDEX: ${self:custom.dynamodb.organisation-user-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      OVER_PAYMENT_TABLE: ${self:custom.dynamodb.over-payment-table}
      OVER_PAYMENT_INDEX: ${self:custom.dynamodb.over-payment-index}
  xero-invoice-loader:
    handler: com.rentancy.integrations.handlers.XeroDataLoader::loadInvoice
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - XeroInvoicesQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      INVOICE_PROPERTY_TABLE: ${self:custom.dynamodb.invoice-property-table}
      USER_ORGANISATION_INDEX: ${self:custom.dynamodb.user-organisation-index}
      ORGANISATION_USER_TABLE: ${self:custom.dynamodb.organisation-user-table}
      ORGANISATION_USER_INDEX: ${self:custom.dynamodb.organisation-user-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_INDEX: ${self:custom.dynamodb.invoice-index}
      INVOICE_LINE_ITEM_INDEX: ${self:custom.dynamodb.invoice-line-item-index}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_INVOICE_TABLE: ${self:custom.dynamodb.tenancy-invoice-table}
      PROPERTY_INVOICE_INDEX: ${self:custom.dynamodb.property-invoice-index}
      TENANCY_INVOICE_INDEX: ${self:custom.dynamodb.tenancy-invoice-index}
      TENANCY_REFERENCE_INDEX: ${self:custom.dynamodb.tenancy-reference-index}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      PROPERTY_INVOICE_TABLE: ${self:custom.dynamodb.property-invoice-table}
      PROPERTY_REFERENCE_INDEX: ${self:custom.dynamodb.property-reference-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      LANDLORD_BILL_QUEUE: ${self:custom.sqs.landlord-bill-queue}
      LANDLORD_BILL_UPDATE_QUEUE: ${self:custom.sqs.landlord-bill-update-queue}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      TENANCY_INVOICE_INVOICE_INDEX: ${self:custom.dynamodb.tenancy-invoice-invoice-index}
      INVOICE_WEBHOOK_EVENTS_TABLE: ${self:custom.dynamodb.invoice-webhook-events-table}
      PROPERTY_INVOICE_INVOICE_INDEX: ${self:custom.dynamodb.property-invoice-invoice-index}
      LANDLORD_BILL_TABLE: ${self:custom.dynamodb.landlord-bill-table}
      LANDLORD_BILL_INVOICE_INDEX: ${self:custom.dynamodb.landlord-bill-invoice-index}
      PAYMENT_TABLE: ${self:custom.dynamodb.payment-table}
      PAYMENT_ORGANISATION_INDEX: ${self:custom.dynamodb.payment-organisation-index}
      STATEMENT_TABLE: ${self:custom.dynamodb.statement-table}
      STATEMENT_INDEX: ${self:custom.dynamodb.statement-index}
      STATEMENT_PROPERTY_INDEX: ${self:custom.dynamodb.statement-property-index}
      STATEMENT_LANDLORD_BILL_INDEX: ${self:custom.dynamodb.statement-landlord-bill-index}
      COMMISSION_INVOICE_QUEUE: ${self:custom.sqs.commission-invoice-queue}
      USER_TABLE_DENORMALIZED_EMAILS_INDEX: ${self:custom.dynamodb.user-table-denormalized-emails-index}

  xero-transaction-loader:
    handler: com.rentancy.integrations.handlers.XeroDataLoader::loadTransaction
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - XeroTransactionsQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      ORGANISATION_USER_TABLE: ${self:custom.dynamodb.organisation-user-table}
      ORGANISATION_USER_INDEX: ${self:custom.dynamodb.organisation-user-index}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_INDEX: ${self:custom.dynamodb.invoice-index}
      INVOICE_LINE_ITEM_INDEX: ${self:custom.dynamodb.invoice-line-item-index}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      TRANSACTION_TABLE: ${self:custom.dynamodb.transaction-table}
      TRANSACTION_INDEX: ${self:custom.dynamodb.transaction-index}
      INVOICE_LINE_ITEM_TRANSACTION_INDEX: ${self:custom.dynamodb.invoice-line-item-transaction-index}

  xero-account-loader:
    handler: com.rentancy.integrations.handlers.XeroDataLoader::loadAccount
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - XeroAccountsQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      ORGANISATION_USER_TABLE: ${self:custom.dynamodb.organisation-user-table}
      ORGANISATION_USER_INDEX: ${self:custom.dynamodb.organisation-user-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_INDEX: ${self:custom.dynamodb.invoice-index}
      INVOICE_LINE_ITEM_INDEX: ${self:custom.dynamodb.invoice-line-item-index}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      TRANSACTION_TABLE: ${self:custom.dynamodb.transaction-table}
      ACCOUNT_TABLE: ${self:custom.dynamodb.account-table}
      ACCOUNT_INDEX: ${self:custom.dynamodb.account-index}
      TRANSACTION_INDEX: ${self:custom.dynamodb.transaction-index}
      ACCOUNT_ORGANISATION_INDEX: ${self:custom.dynamodb.account-organisation-index}
  xero-payment-loader:
    handler: com.rentancy.integrations.handlers.XeroDataLoader::loadPayment
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - XeroPaymentsQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_INDEX: ${self:custom.dynamodb.invoice-index}
      INVOICE_LINE_ITEM_INDEX: ${self:custom.dynamodb.invoice-line-item-index}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      TRANSACTION_TABLE: ${self:custom.dynamodb.transaction-table}
      ACCOUNT_TABLE: ${self:custom.dynamodb.account-table}
      ACCOUNT_INDEX: ${self:custom.dynamodb.account-index}
      TRANSACTION_INDEX: ${self:custom.dynamodb.transaction-index}
      PAYMENT_TABLE: ${self:custom.dynamodb.payment-table}
      PAYMENT_INDEX: ${self:custom.dynamodb.payment-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
      LANDLORD_BILL_TABLE: ${self:custom.dynamodb.landlord-bill-table}
      LANDLORD_BILL_INVOICE_INDEX: ${self:custom.dynamodb.landlord-bill-invoice-index}
      PAYMENT_ORGANISATION_INDEX: ${self:custom.dynamodb.payment-organisation-index}
      STATEMENT_TABLE: ${self:custom.dynamodb.statement-table}
      STATEMENT_INDEX: ${self:custom.dynamodb.statement-index}
      STATEMENT_LANDLORD_BILL_INDEX: ${self:custom.dynamodb.statement-landlord-bill-index}
      INVOICE_TENANCY_TABLE: ${self:custom.dynamodb.invoice-tenancy-table}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      INVOICE_TENANCY_INDEX: ${self:custom.dynamodb.invoice-tenancy-index}
  xero-transfer-loader:
    handler: com.rentancy.integrations.handlers.XeroDataLoader::loadTransfer
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - XeroBankTransferQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_INDEX: ${self:custom.dynamodb.invoice-index}
      INVOICE_LINE_ITEM_INDEX: ${self:custom.dynamodb.invoice-line-item-index}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      TRANSACTION_TABLE: ${self:custom.dynamodb.transaction-table}
      ACCOUNT_TABLE: ${self:custom.dynamodb.account-table}
      ACCOUNT_INDEX: ${self:custom.dynamodb.account-index}
      TRANSACTION_INDEX: ${self:custom.dynamodb.transaction-index}
      TRANSFER_TABLE: ${self:custom.dynamodb.transfer-table}
      TRANSFER_INDEX: ${self:custom.dynamodb.transfer-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
  xero-journal-loader:
    handler: com.rentancy.integrations.handlers.XeroDataLoader::loadJournal
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - XeroJournalQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_INDEX: ${self:custom.dynamodb.invoice-index}
      INVOICE_LINE_ITEM_INDEX: ${self:custom.dynamodb.invoice-line-item-index}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      TRANSACTION_TABLE: ${self:custom.dynamodb.transaction-table}
      ACCOUNT_TABLE: ${self:custom.dynamodb.account-table}
      ACCOUNT_INDEX: ${self:custom.dynamodb.account-index}
      TRANSACTION_INDEX: ${self:custom.dynamodb.transaction-index}
      TRANSFER_TABLE: ${self:custom.dynamodb.transfer-table}
      TRANSFER_INDEX: ${self:custom.dynamodb.transfer-index}
      JOURNAL_TABLE: ${self:custom.dynamodb.xero-journal-table}
      JOURNAL_INDEX: ${self:custom.dynamodb.xero-journal-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}

  property-tracking-code-handler:
    handler: com.rentancy.integrations.handlers.PropertyTrackingCodeHandler
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - PropertyTrackingCodeQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
  tenancy-invoice-sender:
    handler: com.rentancy.integrations.handlers.TenancyInvoiceSender
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - TenancyInvoiceQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      RENT_INVOICE_HISTORY_TABLE: ${self:custom.dynamodb.rent-invoice-history}
      RENT_INVOICE_HISTORY_INDEX: ${self:custom.dynamodb.rent-invoice-history-tenancy-index}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      EMAIL_INVOICE_QUEUE: ${self:custom.sqs.email-invoice-queue}
      XERO_UI_ROOT_PATH: ${self:custom.xero.ui-root-path}
      RENTANCY_DOMAIN: ${self:custom.rentancy-domain.${self:provider.stage}}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      TENANCY_SETTINGS_INDEX: ${self:custom.dynamodb.tenancy-settings-index}
      EMAIL_TEMPLATE_NOTIFICATION_QUEUE: ${self:custom.sqs.email-template-notification-queue}
      RENTANCY_SUPPORT_EMAIL: ${self:custom.rentancySuppMail.${self:provider.stage}}

  commission-invoice-sender:
    handler: com.rentancy.integrations.handlers.CommissionInvoiceSender
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - CommissionInvoiceQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      EMAIL_ATTACHMENT_BUCKET: ${self:custom.s3.rentancy-email-attachments-bucket}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      RENT_INVOICE_HISTORY_TABLE: ${self:custom.dynamodb.rent-invoice-history}
      RENT_INVOICE_HISTORY_XERO_INDEX: ${self:custom.dynamodb.rent-invoice-history-xero-index}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}

  overseas-resident-bill-sender:
    handler: com.rentancy.integrations.handlers.OverseasResidentBillSender
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - OverseasResidentBillQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      RENT_INVOICE_HISTORY_TABLE: ${self:custom.dynamodb.rent-invoice-history}
      RENT_INVOICE_HISTORY_INDEX: ${self:custom.dynamodb.rent-invoice-history-tenancy-index}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      RENTANCY_SUPPORT_EMAIL: ${self:custom.rentancySuppMail.${self:provider.stage}}

  portfolio-invoice-matcher:
    handler: com.rentancy.integrations.handlers.PortfolioInvoiceHandler
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - PortfolioInvoiceQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      DDB_REGION: ${self:provider.ddb_region}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      EMAIL_ATTACHMENT_BUCKET: ${self:custom.s3.rentancy-email-attachments-bucket}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_INDEX: ${self:custom.dynamodb.invoice-index}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      INVOICE_LINE_ITEM_INDEX: ${self:custom.dynamodb.invoice-line-item-index}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      PROPERTY_INVOICE_INDEX: ${self:custom.dynamodb.property-invoice-index}
      TENANCY_INVOICE_INDEX: ${self:custom.dynamodb.tenancy-invoice-index}
      PROPERTY_INVOICE_TABLE: ${self:custom.dynamodb.property-invoice-table}
      TENANCY_INVOICE_TABLE: ${self:custom.dynamodb.tenancy-invoice-table}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
  whatsapp-response-handler:
    handler: com.rentancy.integrations.handlers.WhatsAppResponseHandler
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - WhatsAppQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      CONVERSATION_TABLE: ${self:custom.dynamodb.conversation-table}
      MESSAGE_TABLE: ${self:custom.dynamodb.message-table}
      MESSAGE_INDEX: ${self:custom.dynamodb.message-index}
      USER_COGNITOID_INDEX: ${self:custom.dynamodb.user-id-index}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      TWILIO_ACCOUNT_SID: ${self:custom.twilio.account-sid.${self:provider.stage}}
      TWILIO_AUTH_TOKEN: ${self:custom.twilio.auth-token.${self:provider.stage}}
      RENTANCY_DOCUMENT_UPLOADS: ${self:custom.s3.rentancy-document-uploads}
  email-message-response-handler:
    handler: com.rentancy.integrations.handlers.EmailMessageResponseHandler
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - EmailMessageQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      CONVERSATION_TABLE: ${self:custom.dynamodb.conversation-table}
      MESSAGE_TABLE: ${self:custom.dynamodb.message-table}
      MESSAGE_INDEX: ${self:custom.dynamodb.message-index}
      USER_COGNITOID_INDEX: ${self:custom.dynamodb.user-id-index}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      RENTANCY_DOCUMENT_UPLOADS: ${self:custom.s3.rentancy-document-uploads}
  landlord-bill-handler:
    handler: com.rentancy.integrations.handlers.LandlordBillHandler
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - LandlordBillQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      LANDLORD_BILL_TABLE: ${self:custom.dynamodb.landlord-bill-table}
      LANDLORD_BILL_ORGANISATION_INDEX: ${self:custom.dynamodb.landlord-bill-organisation-index}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      TENANCY_INVOICE_TABLE: ${self:custom.dynamodb.tenancy-invoice-table}
      TENANCY_INVOICE_INDEX: ${self:custom.dynamodb.tenancy-invoice-index}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      RENT_INVOICE_HISTORY_TABLE: ${self:custom.dynamodb.rent-invoice-history}
  property-balance-report:
    handler: com.rentancy.integrations.handlers.PropertyBalanceReportHandler
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - AsyncPropertyBalanceReportQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      DDB_REGION: ${self:provider.ddb_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      RENTANCY_DOCUMENT_UPLOADS: ${self:custom.s3.rentancy-document-uploads}
      PROPERTY_ORGANISATION_INDEX: ${self:custom.dynamodb.property-organisation-index}
      EMAIL_ATTACHMENT_BUCKET: ${self:custom.s3.rentancy-email-attachments-bucket}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
  client-balance-report:
    handler: com.rentancy.integrations.handlers.ClientBalanceReportHandler
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - ClientBalanceReportQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      DDB_REGION: ${self:provider.ddb_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      RENTANCY_DOCUMENT_UPLOADS: ${self:custom.s3.rentancy-document-uploads}
      PROPERTY_ORGANISATION_INDEX: ${self:custom.dynamodb.property-organisation-index}
      EMAIL_ATTACHMENT_BUCKET: ${self:custom.s3.rentancy-email-attachments-bucket}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      TENANCY_SETTINGS_INDEX: ${self:custom.dynamodb.tenancy-settings-index}
  organisation-property-data-exporter:
    handler: com.rentancy.integrations.handlers.PropertyDataExporterHandler
    name: ${self:custom.orgPropertyExporterFunctionName.${self:provider.stage}, self:custom.orgPropertyExporterFunctionName.default}
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - OrganisationPropertyDataExporterQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      PROPERTY_ORGANISATION_INDEX: ${self:custom.dynamodb.property-organisation-index}
      DDB_REGION: ${self:provider.ddb_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      TENANCY_SETTINGS_INDEX: ${self:custom.dynamodb.tenancy-settings-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      ORGANISATION_USER_TABLE: ${self:custom.dynamodb.organisation-user-table}
      ORGANISATION_USER_INDEX: ${self:custom.dynamodb.organisation-user-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      RENTANCY_DOCUMENT_UPLOADS: ${self:custom.s3.rentancy-document-uploads}
      EMAIL_ATTACHMENT_BUCKET: ${self:custom.s3.rentancy-email-attachments-bucket}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
  client-statement-report:
    handler: com.rentancy.integrations.handlers.ClientStatementReportHandler
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - ClientStatementReportQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      DDB_REGION: ${self:provider.ddb_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      RENTANCY_DOCUMENT_UPLOADS: ${self:custom.s3.rentancy-document-uploads}
      PROPERTY_ORGANISATION_INDEX: ${self:custom.dynamodb.property-organisation-index}
      EMAIL_ATTACHMENT_BUCKET: ${self:custom.s3.rentancy-email-attachments-bucket}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      TENANCY_SETTINGS_INDEX: ${self:custom.dynamodb.tenancy-settings-index}
  client-general-report:
    handler: com.rentancy.integrations.handlers.ClientGeneralReportHandler
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - ClientGeneralReportQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      DDB_REGION: ${self:provider.ddb_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_COGNITOID_INDEX: ${self:custom.dynamodb.user-id-index}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      RENTANCY_DOCUMENT_UPLOADS: ${self:custom.s3.rentancy-document-uploads}
      PROPERTY_ORGANISATION_INDEX: ${self:custom.dynamodb.property-organisation-index}
      EMAIL_ATTACHMENT_BUCKET: ${self:custom.s3.rentancy-email-attachments-bucket}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      STATEMENT_TABLE: ${self:custom.dynamodb.statement-table}
      STATEMENT_INDEX: ${self:custom.dynamodb.statement-index}
      TENANCY_SETTINGS_INDEX: ${self:custom.dynamodb.tenancy-settings-index}
  bacs-report:
    handler: com.rentancy.integrations.handlers.BacsReportSenderHandler
    timeout: 600
    events:
      - schedule:
          rate: rate(5 minutes)
          enabled: true
          input:
            source: WARMUP
      - sqs:
          arn:
            Fn::GetAtt:
              - BacsReportQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      PROPERTY_ORGANISATION_INDEX: ${self:custom.dynamodb.property-organisation-index}
      DDB_REGION: ${self:provider.ddb_region}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_REFERENCE_INDEX: ${self:custom.dynamodb.tenancy-reference-index}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      EMAIL_ATTACHMENT_BUCKET: ${self:custom.s3.rentancy-email-attachments-bucket}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      RENTANCY_DOCUMENT_UPLOADS: ${self:custom.s3.rentancy-document-uploads}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      PROPERTY_REFERENCE_INDEX: ${self:custom.dynamodb.property-reference-index}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      INVOICE_PROPERTY_TABLE: ${self:custom.dynamodb.property-invoice-table}
      PROPERTY_INVOICE_TABLE: ${self:custom.dynamodb.property-invoice-table}
      PROPERTY_INVOICE_INVOICE_INDEX: ${self:custom.dynamodb.property-invoice-invoice-index}
  cash-balance-report:
    handler: com.rentancy.integrations.handlers.CashBalanceSenderHandler
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - CashBalanceReportQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      PROPERTY_ORGANISATION_INDEX: ${self:custom.dynamodb.property-organisation-index}
      DDB_REGION: ${self:provider.ddb_region}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_REFERENCE_INDEX: ${self:custom.dynamodb.tenancy-reference-index}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      EMAIL_ATTACHMENT_BUCKET: ${self:custom.s3.rentancy-email-attachments-bucket}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      RENTANCY_DOCUMENT_UPLOADS: ${self:custom.s3.rentancy-document-uploads}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      PROPERTY_REFERENCE_INDEX: ${self:custom.dynamodb.property-reference-index}
  overseas-resident-report:
    handler: com.rentancy.integrations.handlers.OverseasResidentReportHandler
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - OverseasResidentReportQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      DDB_REGION: ${self:provider.ddb_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      RENTANCY_DOCUMENT_UPLOADS: ${self:custom.s3.rentancy-document-uploads}
      PROPERTY_ORGANISATION_INDEX: ${self:custom.dynamodb.property-organisation-index}
      EMAIL_ATTACHMENT_BUCKET: ${self:custom.s3.rentancy-email-attachments-bucket}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      ORGANISATION_USER_TABLE: ${self:custom.dynamodb.organisation-user-table}
      ORGANISATION_USER_INDEX: ${self:custom.dynamodb.organisation-user-index}
      ADDRESS_ORGANISATION_INDEX: ${self:custom.dynamodb.address-organisation-index}
  supplier-landlord-statement-report:
    handler: com.rentancy.integrations.handlers.SupplierLandlordStatementReportHandler
    name: ${self:custom.supplierLandlordReportFunctionName.${self:provider.stage}, self:custom.supplierLandlordReportFunctionName.default}
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - SupplierLandlordStatementReportQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      DDB_REGION: ${self:provider.ddb_region}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      RENTANCY_DOCUMENT_UPLOADS: ${self:custom.s3.rentancy-document-uploads}
      PROPERTY_ORGANISATION_INDEX: ${self:custom.dynamodb.property-organisation-index}
      EMAIL_ATTACHMENT_BUCKET: ${self:custom.s3.rentancy-email-attachments-bucket}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      ORGANISATION_USER_TABLE: ${self:custom.dynamodb.organisation-user-table}
      ORGANISATION_USER_INDEX: ${self:custom.dynamodb.organisation-user-index}
  landlord-commission-sender:
    handler: com.rentancy.integrations.handlers.LandlordCommissionHandler
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - LandlordCommissionQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      RENT_INVOICE_HISTORY_TABLE: ${self:custom.dynamodb.rent-invoice-history}
      RENT_INVOICE_HISTORY_INDEX: ${self:custom.dynamodb.rent-invoice-history-tenancy-index}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      PROPERTY_REFERENCE_INDEX: ${self:custom.dynamodb.property-reference-index}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
  tenancy-schedule-pdf:
    handler: com.rentancy.integrations.handlers.TenancyScheduleSenderHandler
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - TenancyScheduleQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      PROPERTY_ORGANISATION_INDEX: ${self:custom.dynamodb.property-organisation-index}
      DDB_REGION: ${self:provider.ddb_region}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      RENTANCY_DOCUMENT_UPLOADS: ${self:custom.s3.rentancy-document-uploads}
      EMAIL_ATTACHMENT_BUCKET: ${self:custom.s3.rentancy-email-attachments-bucket}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
  landlord-bill-updater:
    handler: com.rentancy.integrations.handlers.LandlordBillUpdater
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - LandlordBillUpdateQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      LANDLORD_BILL_TABLE: ${self:custom.dynamodb.landlord-bill-table}
      LANDLORD_BILL_ORGANISATION_INDEX: ${self:custom.dynamodb.landlord-bill-organisation-index}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      TENANCY_INVOICE_TABLE: ${self:custom.dynamodb.tenancy-invoice-table}
      TENANCY_INVOICE_INDEX: ${self:custom.dynamodb.tenancy-invoice-index}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      RENT_INVOICE_HISTORY_TABLE: ${self:custom.dynamodb.rent-invoice-history}
      STATEMENT_TABLE: ${self:custom.dynamodb.statement-table}
      STATEMENT_INDEX: ${self:custom.dynamodb.statement-index}
  landlord-bill-bulk-payout:
    handler: com.rentancy.integrations.handlers.LandlordBillBulkPayoutHandler
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - BulkPayoutQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_EMAIL_INDEX: ${self:custom.dynamodb.user-email-index}
      USER_COGNITOID_INDEX: ${self:custom.dynamodb.user-id-index}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      LANDLORD_BILL_TABLE: ${self:custom.dynamodb.landlord-bill-table}
      LANDLORD_BILL_ORGANISATION_INDEX: ${self:custom.dynamodb.landlord-bill-organisation-index}
      INVOICE_TABLE: ${self:custom.dynamodb.invoice-table}
      INVOICE_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-organisation-index}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_INVOICE_INDEX: ${self:custom.dynamodb.invoice-line-item-invoice-index}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      INVOICE_ALLOCATION_TABLE: ${self:custom.dynamodb.invoice-allocation-table}
      INVOICE_ALLOCATION_PROPERTY_INDEX: ${self:custom.dynamodb.invoice-allocation-property-index}
      INVOICE_ALLOCATION_INDEX: ${self:custom.dynamodb.invoice-allocation-index}
      USER_XERO_INDEX: ${self:custom.dynamodb.user-xero-index}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      JASPER_SERVER_URL: ${self:custom.jasper-server.rootUrl}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      TENANCY_INVOICE_TABLE: ${self:custom.dynamodb.tenancy-invoice-table}
      TENANCY_INVOICE_INDEX: ${self:custom.dynamodb.tenancy-invoice-index}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      TENANCY_REFERENCE_INDEX: ${self:custom.dynamodb.tenancy-reference-index}
      RENT_INVOICE_HISTORY_TABLE: ${self:custom.dynamodb.rent-invoice-history}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
      STATEMENT_TABLE: ${self:custom.dynamodb.statement-table}
      STATEMENT_INDEX: ${self:custom.dynamodb.statement-index}
      EMAIL_ATTACHMENT_BUCKET: ${self:custom.s3.rentancy-email-attachments-bucket}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      RENTANCY_DOCUMENT_UPLOADS: ${self:custom.s3.rentancy-document-uploads}
      PROPERTY_INVOICE_TABLE: ${self:custom.dynamodb.property-invoice-table}
      PROPERTY_INVOICE_INDEX: ${self:custom.dynamodb.property-invoice-index}
  tenancy-journal-bill-sender:
    handler: com.rentancy.integrations.handlers.TenancyJournalBillSender
    timeout: 900
    memorySize: 10240
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - JournalBillSenderQueue
              - Arn
    environment:
      ENV: ${self:provider.stage}
      REGION: ${self:provider.region}
      UPLOADS_REGION: ${self:provider.uploads_bucket_region}
      DDB_REGION: ${self:provider.ddb_region}
      LAMBDA_REGION: ${self:provider.lambda-region}
      APPSYNC_LAMBDA_NAME: ${self:custom.lambda.appsync-service}
      USER_TABLE: ${self:custom.dynamodb.user-table}
      USER_ORGANISATION_INDEX: ${self:custom.dynamodb.user-organisation-index}
      ORGANISATION_USER_TABLE: ${self:custom.dynamodb.organisation-user-table}
      ORGANISATION_TABLE: ${self:custom.dynamodb.organisation-table}
      TENANCY_TABLE: ${self:custom.dynamodb.tenancy-table}
      PROPERTY_TABLE: ${self:custom.dynamodb.property-table}
      XERO_API_ROOT_PATH: ${self:custom.xero.api-root-path}
      XERO_APP_CLIENT_ID: ${self:custom.xero.app-client-id}
      XERO_APP_CLIENT_SECRET: ${self:custom.xero.app-client-secret}
      XERO_TOKEN_URL: ${self:custom.xero.token-url}
      TENANCY_SETTINGS_TABLE: ${self:custom.dynamodb.tenancy-settings-table}
      RENT_INVOICE_HISTORY_TABLE: ${self:custom.dynamodb.rent-invoice-history}
      RENT_INVOICE_HISTORY_INDEX: ${self:custom.dynamodb.rent-invoice-history-tenancy-index}
      PROPERTY_BUDGET_TABLE: ${self:custom.dynamodb.property-budget-table}
      PROPERTY_BUDGET_INDEX: ${self:custom.dynamodb.property-budget-property-index}
      INTEGRATION_TABLE: ${self:custom.dynamodb.integration-table}
      INTEGRATION_INDEX: ${self:custom.dynamodb.integration-index}
      INTEGRATION_ORGANISATION_INDEX: ${self:custom.dynamodb.integration-organisation-index}
      INTEGRATION_STATE_INDEX: ${self:custom.dynamodb.integration-state-index}
      INTEGRATION_TENANT_INDEX: ${self:custom.dynamodb.integration-tenant-index}
      EMAIL_NOTIFICATION_QUEUE: ${self:custom.sqs.email-notification-queue}
      EMAIL_NOTIFICATION_QUEUE_REGION: ${self:provider.notifications_region}
      EMAIL_INVOICE_QUEUE: ${self:custom.sqs.email-invoice-queue}
      XERO_INVOICES_QUEUE_OUTBOUND: ${self:custom.sqs.xero-invoices-queue-outbound}
      XERO_INVOICES_CALLBACK_QUEUE: ${self:custom.sqs.xero-invoices-callback-queue}
      XERO_UI_ROOT_PATH: ${self:custom.xero.ui-root-path}
      RENTANCY_DOMAIN: ${self:custom.rentancy-domain.${self:provider.stage}}
      TENANCY_SETTINGS_INDEX: ${self:custom.dynamodb.tenancy-settings-index}
      INVOICE_LINE_ITEM_TABLE: ${self:custom.dynamodb.invoice-line-item-table}
      INVOICE_LINE_ITEM_ORGANISATION_INDEX: ${self:custom.dynamodb.invoice-line-item-organisation-index}
      TENANCY_ORGANISATION_INDEX: ${self:custom.dynamodb.tenancy-organisation-index}
      MONTHLY_JOURNAL_REPORT_SENDER_EMAILS: ${self:custom.monthly-journal-report-sender-emails.${self:provider.stage}}
      EMAIL_ATTACHMENT_BUCKET: ${self:custom.s3.rentancy-email-attachments-bucket}
      PRIMARY_ORGANISATION_ID: ${self:custom.rentancy-primary-organisation-id.${self:provider.stage}}
      ORGANISATION_USER_INDEX: ${self:custom.dynamodb.organisation-user-index}
      ACCOUNT_TABLE: ${self:custom.dynamodb.account-table}
      ACCOUNT_INDEX: ${self:custom.dynamodb.account-index}
      ACCOUNT_ORGANISATION_INDEX: ${self:custom.dynamodb.account-organisation-index}
      TENANCY_PROPERTY_INDEX: ${self:custom.dynamodb.tenancy-property-index}
      ADDRESS_TABLE: ${self:custom.dynamodb.address-table}
      ADDRESS_PARENT_INDEX: ${self:custom.dynamodb.address-parent-index}
      LANDLORD_BILL_TABLE: ${self:custom.dynamodb.landlord-bill-table}
      LANDLORD_BILL_ORGANISATION_INDEX: ${self:custom.dynamodb.landlord-bill-organisation-index}

resources:
  Resources:
    ReadModelParentPropertySummaryQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.read-model-parent-property-summary-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - ReadModelParentPropertySummaryQueueDLQ
              - Arn
          maxReceiveCount: 3
    ReadModelParentPropertySummaryQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.read-model-parent-property-summary-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    MailchimpQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.mailchimp-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - MailchimpDLQ
              - Arn
          maxReceiveCount: 3
    MailchimpDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.mailchimp-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    XeroWebhookQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.xero-webhook-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - XeroWebhookDLQ
              - Arn
          maxReceiveCount: 3
    XeroWebhookDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.xero-webhook-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    XeroUpdateQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.xero-update-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - XeroUpdateDLQ
              - Arn
          maxReceiveCount: 3
    XeroUpdateDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.xero-update-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    XeroContactsQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.xero-contacts-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - XeroContactsDLQ
              - Arn
          maxReceiveCount: 3
    XeroContactsDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.xero-contacts-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    XeroInvoicesQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.xero-invoices-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - XeroInvoicesDLQ
              - Arn
          maxReceiveCount: 3
    XeroInvoicesDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.xero-invoices-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    XeroTransactionsQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.xero-transactions-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - XeroTransactionsDLQ
              - Arn
          maxReceiveCount: 3
    XeroTransactionsDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.xero-transactions-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    XeroAccountsQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.xero-accounts-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - XeroAccountsDLQ
              - Arn
          maxReceiveCount: 3
    XeroAccountsDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.xero-accounts-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    XeroOverPaymentQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.xero-over-payment-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - XeroOverPaymentDLQ
              - Arn
          maxReceiveCount: 3
    XeroOverPaymentDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.xero-over-payment-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    XeroPaymentsQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.xero-payments-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - XeroPaymentsDLQ
              - Arn
          maxReceiveCount: 3
    XeroPaymentsDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.xero-payments-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    XeroBankTransferQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.xero-bank-transfer-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - XeroBankTransferDLQ
              - Arn
          maxReceiveCount: 3
    XeroBankTransferDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.xero-bank-transfer-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    XeroJournalQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.xero-journal-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - XeroJournalDLQ
              - Arn
          maxReceiveCount: 3
    XeroJournalDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.xero-journal-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    IntegrationsQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.integrations-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - IntegrationsDLQ
              - Arn
          maxReceiveCount: 3
    IntegrationsDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.integrations-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    TenancyInvoiceQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.tenancy-invoice-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - TenancyInvoiceDLQ
              - Arn
          maxReceiveCount: 3
    TenancyInvoiceDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.tenancy-invoice-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    CommissionInvoiceQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.commission-invoice-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - CommissionInvoiceDLQ
              - Arn
          maxReceiveCount: 3
    CommissionInvoiceDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.commission-invoice-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    OverseasResidentBillQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.overseas-resident-bill-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - OverseasResidentBillDLQ
              - Arn
          maxReceiveCount: 3
    OverseasResidentBillDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.overseas-resident-bill-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    PortfolioInvoiceQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.portfolio-invoice-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - PortfolioInvoiceDLQ
              - Arn
          maxReceiveCount: 3
    PortfolioInvoiceDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.portfolio-invoice-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    WhatsAppQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.whats-app-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - WhatsAppDLQ
              - Arn
          maxReceiveCount: 3
    WhatsAppDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.whats-app-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    WhatsAppIntegrationQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.whats-app-integration-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - WhatsAppIntegrationDLQ
              - Arn
          maxReceiveCount: 3
    WhatsAppIntegrationDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.whats-app-integration-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    EmailMessageQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.email-message-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - EmailMessageDLQ
              - Arn
          maxReceiveCount: 3
    EmailMessageDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.email-message-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    PropertyTrackingCodeQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.property-tracking-code-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - PropertyTrackingCodeDLQ
              - Arn
          maxReceiveCount: 3
    PropertyTrackingCodeDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.property-tracking-code-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    LandlordBillQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.landlord-bill-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - LandlordBillDLQ
              - Arn
          maxReceiveCount: 3
    LandlordBillDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.landlord-bill-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    AsyncPropertyBalanceReportQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.async-property-balance-report-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - AsyncPropertyBalanceReportDLQ
              - Arn
          maxReceiveCount: 3
    AsyncPropertyBalanceReportDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.async-property-balance-report-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    ClientBalanceReportQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.client-balance-report-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - ClientBalanceReportDLQ
              - Arn
          maxReceiveCount: 3
    ClientBalanceReportDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.client-balance-report-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    OrganisationPropertyDataExporterQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.organisation-property-data-exporter-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - OrganisationPropertyDataExporterDLQ
              - Arn
          maxReceiveCount: 3
    OrganisationPropertyDataExporterDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.organisation-property-data-exporter-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    ClientStatementReportQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.client-statement-report-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - ClientStatementReportDLQ
              - Arn
          maxReceiveCount: 3
    ClientStatementReportDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.client-statement-report-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    ClientGeneralReportQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.client-general-report-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - ClientGeneralReportDLQ
              - Arn
          maxReceiveCount: 3
    ClientGeneralReportDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.client-general-report-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    CashBalanceReportQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.cash-balance-report-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - CashBalanceReportDLQ
              - Arn
          maxReceiveCount: 3
    CashBalanceReportDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.cash-balance-report-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    BacsReportQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.bacs-report-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - BacsReportDLQ
              - Arn
          maxReceiveCount: 3
    BacsReportDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.bacs-report-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    OverseasResidentReportQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.overseas-resident-report-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - OverseasResidentReportDLQ
              - Arn
          maxReceiveCount: 3
    OverseasResidentReportDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.overseas-resident-report-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    LandlordCommissionQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.landlord-commission-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - LandlordCommissionDLQ
              - Arn
          maxReceiveCount: 3
    LandlordCommissionDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.landlord-commission-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    TenancyScheduleQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.tenancy-schedule-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - TenancyScheduleDLQ
              - Arn
          maxReceiveCount: 3
    TenancyScheduleDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.tenancy-schedule-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    SupplierLandlordStatementReportQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.supplier-landlord-statement-report-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - SupplierLandlordStatementReportDLQ
              - Arn
          maxReceiveCount: 3
    SupplierLandlordStatementReportDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.supplier-landlord-statement-report-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    LandlordBillUpdateQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.landlord-bill-update-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - LandlordBillUpdateDLQ
              - Arn
          maxReceiveCount: 3
    LandlordBillUpdateDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.landlord-bill-update-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    BulkPayoutQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.bulk-payout-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - BulkPayoutDLQ
              - Arn
          maxReceiveCount: 3
    BulkPayoutDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.bulk-payout-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention

    JournalBillSenderQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.journal-bill-sender-queue}
        VisibilityTimeout: 600
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - JournalBillSenderDLQ
              - Arn
          maxReceiveCount: 3
    JournalBillSenderDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.sqs.journal-bill-sender-queue-dlq}
        MessageRetentionPeriod: 1209600 # 14 days retention