service: integrationresources-alerts
frameworkVersion: "3"

# plugins:
#   - serverless-apigw-binary

custom:

  sns:
    alert-topic: loftyworks-lambda-dlq-${self:provider.stage}

  rentancy-domain:
    dev: dev.rentancy.com
    stageuk: stageuk.rentancy.com
    prod: app.rentancy.com
    produs: produs.rentancy.io
    devus: devus.rentancy.io

  sqs:
    integrations-queue: integrations-queue-${self:provider.stage}
    integrations-queue-dlq: integrations-queue-${self:provider.stage}-dlq
    xero-update-queue: xero-update-queue-${self:provider.stage}
    xero-update-queue-dlq: xero-update-queue-${self:provider.stage}-dlq
    xero-contacts-queue: xero-contacts-queue-${self:provider.stage}
    xero-contacts-queue-dlq: xero-contacts-queue-${self:provider.stage}-dlq
    xero-invoices-queue: xero-invoices-queue-${self:provider.stage}
    xero-invoices-queue-dlq: xero-invoices-queue-${self:provider.stage}-dlq
    xero-transactions-queue: xero-transactions-queue-${self:provider.stage}
    xero-transactions-queue-dlq: xero-transactions-queue-${self:provider.stage}-dlq
    xero-accounts-queue: xero-accounts-queue-${self:provider.stage}
    xero-accounts-queue-dlq: xero-accounts-queue-${self:provider.stage}-dlq
    xero-over-payment-queue: xero-over-payment-queue-${self:provider.stage}
    xero-over-payment-queue-dlq: xero-over-payment-queue-${self:provider.stage}-dlq
    xero-payments-queue: xero-payments-queue-${self:provider.stage}
    xero-payments-queue-dlq: xero-payments-queue-${self:provider.stage}-dlq
    xero-bank-transfer-queue: xero-bank-transfer-queue-${self:provider.stage}
    xero-bank-transfer-queue-dlq: xero-bank-transfer-queue-${self:provider.stage}-dlq
    xero-journal-queue: xero-journal-queue-${self:provider.stage}
    xero-journal-queue-dlq: xero-journal-queue-${self:provider.stage}-dlq
    mailchimp-queue: mailchimp-queue-${self:provider.stage}
    mailchimp-queue-dlq: mailchimp-queue-${self:provider.stage}-dlq
    tenancy-invoice-queue: tenancy-invoice-queue-${self:provider.stage}
    tenancy-invoice-queue-dlq: tenancy-invoice-queue-${self:provider.stage}-dlq
    commission-invoice-queue: commission-invoice-queue-${self:provider.stage}
    commission-invoice-queue-dlq: commission-invoice-queue-${self:provider.stage}-dlq
    overseas-resident-bill-queue: overseas-resident-bill-queue-${self:provider.stage}
    overseas-resident-bill-queue-dlq: overseas-resident-bill-queue-${self:provider.stage}-dlq
    portfolio-invoice-queue: portfolio-invoice-queue-${self:provider.stage}
    portfolio-invoice-queue-dlq: portfolio-invoice-queue-${self:provider.stage}-dlq
    whats-app-queue: whats-app-queue-${self:provider.stage}
    whats-app-queue-dlq: whats-app-queue-${self:provider.stage}-dlq
    whats-app-integration-queue: whats-app-integration-queue-${self:provider.stage}
    whats-app-integration-queue-dlq: whats-app-integration-queue-${self:provider.stage}-dlq
    email-message-queue: email-message-queue-${self:provider.stage}
    email-message-queue-dlq: email-message-queue-${self:provider.stage}-dlq
    property-tracking-code-queue: property-tracking-code-queue-${self:provider.stage}
    property-tracking-code-queue-dlq: property-tracking-code-queue-${self:provider.stage}-dlq
    landlord-bill-queue: landlord-bill-queue-${self:provider.stage}
    landlord-bill-queue-dlq: landlord-bill-queue-${self:provider.stage}-dlq
    async-property-balance-report-queue: async-property-balance-report-queue-${self:provider.stage}
    async-property-balance-report-queue-dlq: async-property-balance-report-queue-${self:provider.stage}-dlq
    client-balance-report-queue: client-balance-report-queue-${self:provider.stage}
    client-balance-report-queue-dlq: client-balance-report-queue-${self:provider.stage}-dlq
    organisation-property-data-exporter-queue: organisation-property-data-exporter-queue-${self:provider.stage}
    organisation-property-data-exporter-queue-dlq: organisation-property-data-exporter-queue-${self:provider.stage}-dlq
    client-statement-report-queue: client-statement-report-queue-${self:provider.stage}
    client-statement-report-queue-dlq: client-statement-report-queue-${self:provider.stage}-dlq
    client-general-report-queue: client-general-report-queue-${self:provider.stage}
    client-general-report-queue-dlq: client-general-report-queue-${self:provider.stage}-dlq
    cash-balance-report-queue: cash-balance-report-queue-${self:provider.stage}
    cash-balance-report-queue-dlq: cash-balance-report-queue-${self:provider.stage}-dlq
    bacs-report-queue: bacs-report-queue-${self:provider.stage}
    bacs-report-queue-dlq: bacs-report-queue-${self:provider.stage}-dlq
    overseas-resident-report-queue: overseas-resident-report-queue-${self:provider.stage}
    overseas-resident-report-queue-dlq: overseas-resident-report-queue-${self:provider.stage}-dlq
    landlord-commission-queue: landlord-commission-queue-${self:provider.stage}
    landlord-commission-queue-dlq: landlord-commission-queue-${self:provider.stage}-dlq
    tenancy-schedule-queue: tenancy-schedule-queue-${self:provider.stage}
    tenancy-schedule-queue-dlq: tenancy-schedule-queue-${self:provider.stage}-dlq
    xero-webhook-queue: xero-webhook-queue-${self:provider.stage}
    xero-webhook-queue-dlq: xero-webhook-queue-${self:provider.stage}-dlq
    supplier-landlord-statement-report-queue: supplier-landlord-statement-report-queue-${self:provider.stage}
    supplier-landlord-statement-report-queue-dlq: supplier-landlord-statement-report-queue-${self:provider.stage}-dlq
    landlord-bill-update-queue: landlord-bill-update-queue-${self:provider.stage}
    landlord-bill-update-queue-dlq: landlord-bill-update-queue-${self:provider.stage}-dlq
    bulk-payout-queue: bulk-payout-queue-${self:provider.stage}
    bulk-payout-queue-dlq: bulk-payout-queue-${self:provider.stage}-dlq
    journal-bill-sender-queue: journal-bill-sender-queue-${self:provider.stage}
    journal-bill-sender-queue-dlq: journal-bill-sender-queue-${self:provider.stage}-dlq
    #Resources declared in notification repo
    email-notification-queue: email-notification-queue-${self:provider.stage}
    email-template-notification-queue: send-template-email-queue-${self:provider.stage}
    email-invoice-queue: email-invoice-queue-${self:provider.stage}
     

    account-sid:
      prod: AC469d0bf4e62a93e573b31e29560401fe
      dev: ACb31a2a00556b9b12d336ad76ba11fe68
      stageuk: ACb31a2a00556b9b12d336ad76ba11fe68
      produs: ACb31a2a00556b9b12d336ad76ba11fe68
      devus: ACb31a2a00556b9b12d336ad76ba11fe68
    auth-token:
      prod: ${ssm:/integrationresources/prod/twilio/auth-token}
      dev: ${ssm:/integrationresources/dev/twilio/auth-token}
      stageuk: ${ssm:/integrationresources/dev/twilio/auth-token}
      produs: ${ssm:/integrationresources/produs/twilio/auth-token}
      devus: ${ssm:/integrationresources/devus/twilio/auth-token}
  rentancy-error-channel:
    dev: bbc1c110-fd24-11ec-9c8f-41c5115dadc5
    stageuk: bbc1c110-fd24-11ec-9c8f-41c5115dadc5
    prod: 205d1a50-0803-11ed-bac9-fd04947354bd
    produs: bbc1c110-fd24-11ec-9c8f-41c5115dadc5
    devus: bbc1c110-fd24-11ec-9c8f-41c5115dadc5

provider:
  name: aws
  region: ${file(../config.${opt:stage, 'dev'}.yml):region}
  cognito_region: ${file(../config.${opt:stage, 'dev'}.yml):region}
  ddb_region: ${file(../config.${opt:stage, 'dev'}.yml):region}
  lambda-region: ${file(../config.${opt:stage, 'dev'}.yml):region}
  uploads_bucket_region: ${file(../config.${opt:stage, 'dev'}.yml):region}
  notifications_region: ${file(../config.${opt:stage, 'dev'}.yml):region}
  runtime: java11
  versionFunctions: false
  stage: ${opt:stage, 'dev'}

  environment:
    REGION: ${self:provider.region}

  iamRoleStatements:
    - Effect: "Allow"
      Action:
        - "sns:*"
      Resource:
        - "*"
    - Effect: "Allow"
      Action:
        - "sqs:*"
      Resource:
        - "*"
    - Effect: "Allow"
      Action:
        - "s3:*"
      Resource:
        - "*"
    - Effect: "Allow"
      Action:
        - "ses:*"
      Resource:
        - "*"
    - Effect: "Allow"
      Action:
        - "appsync:GraphQL"
      Resource:
        - "*"
    - Effect: "Allow"
      Action:
        - "dynamodb:*"
      Resource:
        - "*"
    - Effect: "Allow"
      Action:
        - "lambda:*"
      Resource:
        - "*"

# you can add packaging information here
package:
  artifact: ../build/distributions/integrations.zip


resources:
  Resources:
    MailchimpDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: mailchimp-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.mailchimp-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    XeroWebhookDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: xero-webhook-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.xero-webhook-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    XeroUpdateDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: xero-update-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.xero-update-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    XeroContactsDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: xero-contacts-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.xero-contacts-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    XeroInvoicesDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: xero-invoices-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.xero-invoices-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    XeroTransactionsDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: xero-transactions-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.xero-transactions-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    XeroAccountsDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: xero-accounts-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.xero-accounts-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    XeroOverPaymentDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: xero-over-payment-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.xero-over-payment-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    XeroPaymentsDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: xero-payments-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.xero-payments-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    XeroBankTransferDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: xero-bank-transfer-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.xero-bank-transfer-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    XeroJournalDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: xero-journal-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.xero-journal-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    IntegrationsDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: integrations-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.integrations-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    TenancyInvoiceDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: tenancy-invoice-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.tenancy-invoice-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    CommissionInvoiceDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: commission-invoice-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.commission-invoice-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    OverseasResidentBillDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: overseas-resident-bill-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.overseas-resident-bill-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    PortfolioInvoiceDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: portfolio-invoice-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.portfolio-invoice-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    WhatsAppDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: whats-app-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.whats-app-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    WhatsAppIntegrationDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: whats-app-integration-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.whats-app-integration-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    EmailMessageDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: email-message-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.email-message-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    PropertyTrackingCodeDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: property-tracking-code-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.property-tracking-code-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    LandlordBillDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: landlord-bill-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.landlord-bill-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    AsyncPropertyBalanceReportDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: async-property-balance-report-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.async-property-balance-report-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    ClientBalanceReportDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: client-balance-report-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.client-balance-report-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    OrganisationPropertyDataExporterDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: organisation-property-data-exporter-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.organisation-property-data-exporter-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    ClientStatementReportDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: client-statement-report-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.client-statement-report-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    ClientGeneralReportDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: client-general-report-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.client-general-report-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    CashBalanceReportDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: cash-balance-report-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.cash-balance-report-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    BacsReportDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: bacs-report-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.bacs-report-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    OverseasResidentReportDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: overseas-resident-report-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.overseas-resident-report-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    LandlordCommissionDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: landlord-commission-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.landlord-commission-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    TenancyScheduleDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: tenancy-schedule-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.tenancy-schedule-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    SupplierLandlordStatementReportDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: supplier-landlord-statement-report-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.supplier-landlord-statement-report-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    LandlordBillUpdateDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: landlord-bill-update-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.landlord-bill-update-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    BulkPayoutDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: bulk-payout-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.bulk-payout-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}

    JournalBillSenderDLQAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: journal-bill-sender-dlq-alarm-${self:provider.stage}
        Namespace: AWS/SQS
        MetricName: ApproximateNumberOfMessagesVisible
        Dimensions:
          - Name: QueueName
            Value: ${self:custom.sqs.journal-bill-sender-queue-dlq}
        Statistic: Sum
        Period: 60
        EvaluationPeriods: 1
        Threshold: 0
        ComparisonOperator: GreaterThanThreshold
        TreatMissingData: notBreaching
        AlarmActions:
          - Fn::Sub: arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${self:custom.sns.alert-topic}