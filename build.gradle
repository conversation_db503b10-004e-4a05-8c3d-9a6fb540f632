plugins {
    id "ru.myweek-end.freemarker" version "0.0.0.6"
    id 'jacoco'
}

apply plugin: 'java'

repositories {
    mavenCentral()
    jcenter()
    maven { url "http://jasperreports.sourceforge.net/maven2/" }
    maven { url "http://jaspersoft.artifactoryonline.com/jaspersoft/third-party-ce-artifacts/" }
    maven { url "https://plugins.gradle.org/m2/" }
}

//test {
//    useJUnitPlatform()
//    environment "ENV", "test"
//    environment "DDB_REGION", "eu-west-2"
//    environment "INTEGRATION_TABLE", "Integration-yjrs4cpporbulapax2qq3sku74-dev"
//    environment "USER_TABLE", "User-yjrs4cpporbulapax2qq3sku74-dev"
//    environment "USER_COGNITOID_INDEX", "gsi-ByCognitoId"
//    environment "INTEGRATION_INDEX", "gsi-OrganisationUserIntegration"
//    finalizedBy jacocoTestReport // report is always generated after tests run
//}

jacocoTestReport {
    dependsOn test // tests are required to run before generating the report
}

sourceCompatibility = 11
targetCompatibility = 11

dependencies {
    implementation('org.codehaus.groovy:groovy-all:2.3.6')
    implementation('org.freemarker:freemarker:2.3.30')
    implementation('com.amazonaws:aws-java-sdk-sqs:1.11.766')
    implementation('com.amazonaws:aws-java-sdk-s3:1.11.766')
    implementation('com.amazonaws:aws-java-sdk-dynamodb:1.11.766')
    implementation('com.amazonaws:aws-java-sdk-ses:1.11.766')
    implementation('com.amazonaws:aws-java-sdk-eventbridge:1.11.766')
    implementation('com.amazonaws:aws-java-sdk-sns:1.11.766')
    implementation('com.amazonaws:aws-java-sdk-lambda:1.11.766')
    implementation('com.amazonaws:aws-java-sdk-api-gateway:1.11.766')
    implementation('com.amazonaws:aws-lambda-java-events:2.2.7')
    implementation('com.amazonaws:aws-lambda-java-core:1.1.0')
    implementation('com.amazonaws:aws-lambda-java-log4j2:1.6.0')
    implementation('software.amazon.awssdk:dynamodb-enhanced:2.27.0')
    implementation('com.google.guava:guava:30.1.1-jre')
    implementation('com.fasterxml.jackson.core:jackson-core:2.9.10')
    implementation('com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.10')
    implementation('com.fasterxml.jackson.core:jackson-databind:2.9.10')
    implementation('com.fasterxml.jackson.core:jackson-annotations:2.9.10')
    implementation('javax.mail:mail:1.4')
    // https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-starter-web
    implementation('org.springframework.boot:spring-boot-starter-web:2.4.2') {
        exclude group: 'org.apache.logging.log4j', module: 'log4j-to-slf4j'
    }
    implementation group: 'org.apache.logging.log4j', name: 'log4j-core', version: '2.22.0'
    implementation group: 'org.apache.logging.log4j', name: 'log4j-api', version: '2.22.0'

    implementation group: 'org.apache.james', name: 'apache-mime4j-core', version: '0.8.3'
    implementation group: 'org.apache.james', name: 'apache-mime4j-dom', version: '0.8.3'
    implementation group: 'org.apache.james', name: 'apache-mime4j', version: '0.8.3', ext: 'pom'
    implementation group: 'com.github.xeroapi', name: 'xero-java', version: '4.13.0'
    implementation 'org.slf4j:slf4j-api:1.7.28'
    implementation 'org.apache.httpcomponents:httpclient:4.5.13'
    implementation 'org.apache.httpcomponents:httpcore:4.4.12'
    implementation 'org.apache.ant:ant:1.10.11'

    implementation 'commons-lang:commons-lang:2.6'
    implementation group: 'io.sentry', name: "sentry", version: "5.4.2"
    implementation group: 'org.apache.poi', name: 'poi', version: '5.3.0'
    implementation group: 'org.apache.poi', name: 'poi-ooxml', version: '5.3.0'
    implementation group: 'commons-io', name: 'commons-io', version: '2.18.0'

    implementation group: 'org.apache.pdfbox', name: 'pdfbox', version: '2.0.25'
    implementation group: 'org.apache.commons', name: 'commons-lang3', version: '3.12.0'

    implementation group: "com.twilio.sdk", name: "twilio", version: "8.18.0"

    compileOnly 'org.projectlombok:lombok:1.18.12'
    annotationProcessor 'org.projectlombok:lombok:1.18.12'

    testCompileOnly 'org.projectlombok:lombok:1.18.12'
    testAnnotationProcessor 'org.projectlombok:lombok:1.18.12'

    // JUnit 5
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.10.2'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.10.2'
    testImplementation 'org.junit.jupiter:junit-jupiter-params:5.10.2'

    // Mockito
    testImplementation 'org.mockito:mockito-core:5.10.0'
    testImplementation 'org.mockito:mockito-junit-jupiter:5.10.0'
    testImplementation 'org.wiremock:wiremock-standalone:3.3.1'
    testImplementation "org.testcontainers:testcontainers:1.20.4"
    testImplementation "org.testcontainers:localstack:1.20.4"

    testImplementation 'org.assertj:assertj-core:3.16.1'
}

// Task for building the zip file for upload
task buildZip(type: Zip) {

    // Using the Zip API from gradle to build a zip file of all the dependencies
    //
    // The path to this zip file can be set in the serverless.yml file for the
    // package/artifact setting for deployment to the S3 bucket
    //
    // Link: https://docs.gradle.org/current/dsl/org.gradle.api.tasks.bundling.Zip.html

    // set the base name of the zip file
    archiveBaseName.set("integrations")
    destinationDirectory.set(file("$buildDir/distributions"))

    from compileJava
    from processResources
    from "$buildDir/classes/java/main"

    into('lib') {
        from configurations.runtimeClasspath
    }
}

build.dependsOn buildZip

task customWrapper(type: Wrapper) {
    gradleVersion = '3.5'
}
