region: eu-west-2
project-region: eu-west-2
s3:
  email-bucket: app-rentancy-com-email-bucket-prodeu
  document-bucket: com-rentancy-documents10616-prodeu
lambda:
  appsync-service: appSyncService
  securityGroup: sg-0ff96156ece24138d
  subnet: subnet-0bdac5a7db23bedef
mainService:
  endpoint: http://api-internal.lettings.loftyworks.systems
dynamodb:
  invoice-line-item-stream-arn: arn:aws:dynamodb:eu-west-2:554391840728:table/InvoiceLineItem-gkyzrmicdjh55hv7qfs4hsvbvu-prodeu/stream/2025-06-30T11:38:40.057
sqs:
  xero-invoices-queue-inbound-arn: arn:aws:sqs:eu-west-2:590184079916:xero-invoices-queue-inbound
  xero-invoices-callback-queue-arn: arn:aws:sqs:eu-west-2:590184079916:xero-invoices-callback-queue