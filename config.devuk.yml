region: eu-west-2
s3:
  email-bucket: app-rentancy-com-email-bucket-dev
  document-bucket: com-rentancy-documents2f036-devuk
lambda:
  appsync-service: appSyncService
  securityGroup: sg-03bea33459fb2f126
  subnet: subnet-0add8740ff64df045
mainService:
  endpoint: http://api.dev.uk.loftyworks.systems
dynamodb:
  invoice-line-item-stream-arn: arn:aws:dynamodb:eu-west-2:686255943894:table/InvoiceLineItem-zc4ddtrzinaxxoqjlv5irtqbh4-devuk/stream/2025-01-08T09:59:41.711
sqs:
  xero-invoices-queue-inbound-arn: arn:aws:sqs:eu-west-2:686255943894:xero-invoices-queue-inbound
  xero-invoices-callback-queue-arn: arn:aws:sqs:eu-west-2:686255943894:xero-invoices-callback-queue