# Integrations
Rentancy integrations

## MailChimp

Below is an example of the request that should be included in the body of the SQS message

```json
{
  "email_address": "<EMAIL>",
  "status": "subscribed",
  "status_if_new": "subscribed",
  "merge_fields": {
    "FNAME": "<PERSON>",
    "LNAME": "<PERSON>"
  },
  "tags": [
    "self-register"
  ],
  "email_type": "html",
  "list_id": "some-list-id"
}
```

## AWS Toolkit

### Prerequisites

1. AWS CLI (https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html)
2. Docker (https://docs.docker.com/get-docker/). For Mac users: it's available via Homebrew, no need to download 
official Mac application 
3. Colima (https://github.com/abiosoft/colima) - to be on the safe side when it comes to licensing. Docker Desktop is 
quite strict about that
4. AWS SAM CLI (https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html)
5. AWS Toolkit (https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/setup-toolkit.html)
6. Mac users: `docker-credential-helper` available via Homebrew

To work with `colima` on a Mac you need to use OSX keychain in order to store docker credentials. Please adjust `~/.docker/config.json` file:
```json
{
    "auths": {},
    "credsStore": "osxkeychain",
    "currentContext": "colima"
}
```
Colima workflow also requires you to properly set `DOCKER_HOST` env var:

`export DOCKER_HOST="unix://$HOME/.colima/docker.sock"`

After that you can start `colima` with: `$ colima start` and create proper configuration with colima as a Docker deamon
(IntelliJ flow):
![intellij-docker-config](docs/intellij-docker-config.png)

### Local Development

https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/invoke-lambda.html

Please copy over lambda environmental variables from AWS console. Example of a lambda run configuration 
(again IntelliJ flow):
![aws-toolkit-lambda-run-config](docs/aws-toolkit-lambda-run-config.png)

### Deploying to DEV

#### Prerequisites
First install yarn. On MacOS it's
```bash
brew install yarn
```

Go to this project with cd
```bash
yarn install
export PATH=$PATH:node_modules/.bin
sls --help
```
#### Deployment
```bash
sls deploy --stage devuk
```
