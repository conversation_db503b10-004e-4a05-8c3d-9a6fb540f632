plugins:
  - - "@semantic-release/commit-analyzer"
    - releaseRules:
        - type: docs
          release: patch
        - type: refactor
          release: patch
        - type: chore
          release: patch
  - "@semantic-release/release-notes-generator"
  - - "@semantic-release/github"
    - successComment: false
      failComment: false

branches:
  - master
  - name: dev
    prerelease: true
  - name: release-*
    prerelease: true
  - name: feat/*
    prerelease: '${name.replace(/^feat\//g, "feat-")}'
  - name: fix/*
    prerelease: '${name.replace(/^fix\//g, "fix-")}'
  - name: docs/*
    prerelease: '${name.replace(/^docs\//g, "docs-")}'
  - name: refactor/*
    prerelease: '${name.replace(/^refactor\//g, "refactor-")}'
  - name: chore/*
    prerelease: '${name.replace(/^chore\//g, "chore-")}'