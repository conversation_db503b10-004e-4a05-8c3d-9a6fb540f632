on:
  push:
    branches:
      - dev
      - devus
      - master
      - produs
      - release*
  pull_request:
    branches:
      - dev
      - devus
      - master
      - produs
      - release*

name: Build and Deploy Serverless to Development

jobs:
  deploy:
    name: test-build-deploy
    runs-on: arc-default-runner-set

    steps:
      - name: Checkout
        uses: actions/checkout@v2

#      - name: Configure AWS credentials
#        uses: aws-actions/configure-aws-credentials@v1
#        with:
#          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
#          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
#          aws-region: eu-west-2

      - name: Java version
        uses: actions/setup-java@v1
        with:
          java-version: 11

      - name: Install fonts
        run: sudo apt-get update && sudo apt-get install -y fonts-dejavu-core ttf-mscorefonts-installer fontconfig

      - name: Test
        run: ./gradlew test -i

      - name: Build
        run: ./gradlew build

#      - name: Pseudo parameters
#        run: npm install serverless-pseudo-parameters
#
#      - name: Api Gateway binary types
#        run: npm install serverless-apigw-binary
#
#      - name: Install Serverless
#        run: npm install -g serverless@2.35.0
#
#      - name: Serverless Authentication
#        run: serverless config credentials --provider aws --key ${{secrets.AWS_ACCESS_KEY_ID}} --secret ${{secrets.AWS_SECRET_ACCESS_KEY}}
#
#      - name: Serverless Deploy
#        run: serverless deploy --stage dev
