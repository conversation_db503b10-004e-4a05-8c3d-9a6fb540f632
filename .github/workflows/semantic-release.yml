on:
  push:
    branches:
      - master

jobs:
  semantic-release:
    if: "!contains(github.event.head_commit.message, '[skip ci]')"

    name: Semantic Release
    runs-on: ubuntu-latest
    permissions: write-all

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          persist-credentials: false

      - uses: actions/setup-node@v3
        with:
          node-version: '20.x'

      - name: Semantic Release
        uses: cycjimmy/semantic-release-action@v2
        id: semantic # Need an `id` for output variables
        with:
          semantic_version: 17
          extra_plugins: |
            @semantic-release/changelog@5
            @semantic-release/exec@5
            @semantic-release/git@9
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Do something when a new release published
        if: steps.semantic.outputs.new_release_published == 'true'
        run: |
          echo ${{ steps.semantic.outputs.new_release_version }}
          echo ${{ steps.semantic.outputs.new_release_major_version }}
          echo ${{ steps.semantic.outputs.new_release_minor_version }}
          echo ${{ steps.semantic.outputs.new_release_patch_version }}