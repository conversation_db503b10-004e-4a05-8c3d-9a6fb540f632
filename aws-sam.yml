Resources:
  landlordBillSender:
    Type: AWS::Serverless::Function
    Properties:
      Timeout: 60
      MemorySize: 1024
      Handler: com.rentancy.integrations.handlers.LandlordBillSender
      Runtime: java11
      CodeUri: .
      Architectures:
        - arm64
      Environment:
        Variables:
          ADDRESS_PARENT_INDEX:
          ADDRESS_TABLE:
          APPSYNC_LAMBDA_NAME:
          DDB_REGION:
          ENV:
          INTEGRATION_INDEX:
          INTEGRATION_ORGANISATION_INDEX:
          INTEGRATION_STATE_INDEX:
          INTEGRATION_TABLE:
          INTEGRATION_TENANT_INDEX:
          INVOICE_ALLOCATION_INDEX:
          INVOICE_ALLOCATION_PROPERTY_INDEX:
          INVOICE_ALLOCATION_TABLE:
          INVOICE_LINE_ITEM_INVOICE_INDEX:
          INVOICE_LINE_ITEM_TABLE:
          INVOICE_ORGANISATION_INDEX:
          INVOICE_TABLE:
          JASPER_SERVER_URL:
          LAMBDA_REGION:
          LANDLORD_BILL_ORGANISATION_INDEX:
          LANDLORD_BILL_TABLE:
          ORGANISATION_TABLE:
          PARENT_PROPERTY_TABLE:
          PROPERTY_BUDGET_INDEX:
          PROPERTY_BUDGET_TABLE:
          PROPERTY_TABLE:
          REGION:
          RENT_INVOICE_HISTORY_TABLE:
          STATEMENT_INDEX:
          STATEMENT_TABLE:
          TENANCY_INVOICE_INDEX:
          TENANCY_INVOICE_TABLE:
          TENANCY_PROPERTY_INDEX:
          TENANCY_REFERENCE_INDEX:
          TENANCY_SETTINGS_TABLE:
          TENANCY_TABLE:
          UPLOADS_REGION:
          USER_COGNITOID_INDEX:
          USER_EMAIL_INDEX:
          USER_TABLE:
          USER_XERO_INDEX:
          XERO_API_ROOT_PATH:
          XERO_APP_CLIENT_ID:
          XERO_APP_CLIENT_SECRET:
          XERO_TOKEN_URL:
          INVOICE_LINE_ITEM_ORGANISATION_INDEX:
          INVOICE_PROPERTY_TABLE:

  xeroDataFetcher:
    Type: AWS::Serverless::Function
    Properties:
      Timeout: 60
      MemorySize: 1024
      Handler: com.rentancy.integrations.handlers.XeroDataFetcher
      Runtime: java11
      CodeUri: .
      Architectures:
        - arm64
      Environment:
        Variables:
          ADDRESS_PARENT_INDEX:
          ADDRESS_TABLE:
          APPSYNC_LAMBDA_NAME:
          DDB_REGION:
          ENV:
          INTEGRATION_INDEX:
          INTEGRATION_ORGANISATION_INDEX:
          INTEGRATION_STATE_INDEX:
          INTEGRATION_TABLE:
          INTEGRATION_TENANT_INDEX:
          INVOICE_ALLOCATION_INDEX:
          INVOICE_ALLOCATION_PROPERTY_INDEX:
          INVOICE_ALLOCATION_TABLE:
          INVOICE_LINE_ITEM_INVOICE_INDEX:
          INVOICE_LINE_ITEM_TABLE:
          INVOICE_ORGANISATION_INDEX:
          INVOICE_TABLE:
          JASPER_SERVER_URL:
          LAMBDA_REGION:
          LANDLORD_BILL_ORGANISATION_INDEX:
          LANDLORD_BILL_TABLE:
          ORGANISATION_TABLE:
          PARENT_PROPERTY_TABLE:
          PROPERTY_BUDGET_INDEX:
          PROPERTY_BUDGET_TABLE:
          PROPERTY_TABLE:
          REGION:
          RENT_INVOICE_HISTORY_TABLE:
          STATEMENT_INDEX:
          STATEMENT_TABLE:
          TENANCY_INVOICE_INDEX:
          TENANCY_INVOICE_TABLE:
          TENANCY_PROPERTY_INDEX:
          TENANCY_REFERENCE_INDEX:
          TENANCY_SETTINGS_TABLE:
          TENANCY_TABLE:
          UPLOADS_REGION:
          USER_COGNITOID_INDEX:
          USER_EMAIL_INDEX:
          USER_TABLE:
          USER_XERO_INDEX:
          XERO_API_ROOT_PATH:
          XERO_APP_CLIENT_ID:
          XERO_APP_CLIENT_SECRET:
          XERO_TOKEN_URL:
          INVOICE_LINE_ITEM_ORGANISATION_INDEX:
          INVOICE_PROPERTY_TABLE:
          INVOICE_INDEX:
          INVOICE_LINE_ITEM_INDEX:
          XERO_CONTACTS_QUEUE:
          XERO_INVOICES_QUEUE:
          XERO_INVOICES_QUEUE_OUTBOUND:
          XERO_INVOICES_CALLBACK_QUEUE:
          XERO_TRANSACTIONS_QUEUE:
          XERO_ACCOUNTS_QUEUE:
          XERO_PAYMENTS_QUEUE:
          XERO_OVER_PAYMENTS_QUEUE:
          XERO_BANK_TRANSFER_QUEUE:
          XERO_JOURNAL_QUEUE:
          ACCOUNT_TABLE:
          ACCOUNT_INDEX:
          ACCOUNT_ORGANISATION_INDEX:
          RENTANCY_SUPPORT_EMAIL:
          EMAIL_TEMPLATE_NOTIFICATION_QUEUE:
          EMAIL_NOTIFICATION_QUEUE_REGION:
          EMAIL_NOTIFICATION_QUEUE:
          ORGANISATION_USER_TABLE:
          ORGANISATION_USER_INDEX:

  depositManagementHandler:
    Type: AWS::Serverless::Function
    Properties:
      Timeout: 60
      MemorySize: 1024
      Handler: com.rentancy.integrations.handlers.DepositManagementHandler
      Runtime: java11
      CodeUri: .
      Architectures:
        - arm64
      Environment:
        Variables:
          ADDRESS_PARENT_INDEX:
          ADDRESS_TABLE:
          APPSYNC_LAMBDA_NAME:
          DDB_REGION:
          ENV:
          INTEGRATION_INDEX:
          INTEGRATION_ORGANISATION_INDEX:
          INTEGRATION_STATE_INDEX:
          INTEGRATION_TABLE:
          INTEGRATION_TENANT_INDEX:
          INVOICE_ALLOCATION_INDEX:
          INVOICE_ALLOCATION_PROPERTY_INDEX:
          INVOICE_ALLOCATION_TABLE:
          INVOICE_LINE_ITEM_INVOICE_INDEX:
          INVOICE_LINE_ITEM_TABLE:
          INVOICE_ORGANISATION_INDEX:
          INVOICE_TABLE:
          JASPER_SERVER_URL:
          LAMBDA_REGION:
          LANDLORD_BILL_ORGANISATION_INDEX:
          LANDLORD_BILL_TABLE:
          ORGANISATION_TABLE:
          PARENT_PROPERTY_TABLE:
          PROPERTY_BUDGET_INDEX:
          PROPERTY_BUDGET_TABLE:
          PROPERTY_TABLE:
          REGION:
          RENT_INVOICE_HISTORY_TABLE:
          STATEMENT_INDEX:
          STATEMENT_TABLE:
          TENANCY_INVOICE_INDEX:
          TENANCY_INVOICE_TABLE:
          TENANCY_PROPERTY_INDEX:
          TENANCY_REFERENCE_INDEX:
          TENANCY_SETTINGS_TABLE:
          TENANCY_TABLE:
          UPLOADS_REGION:
          USER_COGNITOID_INDEX:
          USER_EMAIL_INDEX:
          USER_TABLE:
          USER_XERO_INDEX:
          XERO_API_ROOT_PATH:
          XERO_APP_CLIENT_ID:
          XERO_APP_CLIENT_SECRET:
          XERO_TOKEN_URL:
          INVOICE_LINE_ITEM_ORGANISATION_INDEX:
          INVOICE_PROPERTY_TABLE:
          TENANCY_ORGANISATION_INDEX: gsi-OrganisationTenancies
          PROPERTY_ORGANISATION_INDEX: gsi-OrganisationProperties

  parentPropertySummary:
    Type: AWS::Serverless::Function
    Properties:
      Timeout: 60
      MemorySize: 1024
      Handler: com.rentancy.integrations.handlers.ParentPropertySummary
      Runtime: java11
      CodeUri: .
      Architectures:
        - arm64
      Environment:
        Variables:
          ADDRESS_PARENT_INDEX:
          ADDRESS_TABLE:
          APPSYNC_LAMBDA_NAME:
          DDB_REGION:
          ENV:
          INTEGRATION_INDEX:
          INTEGRATION_ORGANISATION_INDEX:
          INTEGRATION_STATE_INDEX:
          INTEGRATION_TABLE:
          INTEGRATION_TENANT_INDEX:
          INVOICE_ALLOCATION_INDEX:
          INVOICE_ALLOCATION_PROPERTY_INDEX:
          INVOICE_ALLOCATION_TABLE:
          INVOICE_LINE_ITEM_INVOICE_INDEX:
          INVOICE_LINE_ITEM_TABLE:
          INVOICE_ORGANISATION_INDEX:
          INVOICE_TABLE:
          JASPER_SERVER_URL:
          LAMBDA_REGION:
          LANDLORD_BILL_ORGANISATION_INDEX:
          LANDLORD_BILL_TABLE:
          ORGANISATION_TABLE:
          PARENT_PROPERTY_TABLE:
          PROPERTY_BUDGET_INDEX:
          PROPERTY_BUDGET_TABLE:
          PROPERTY_TABLE:
          REGION:
          RENT_INVOICE_HISTORY_TABLE:
          STATEMENT_INDEX:
          STATEMENT_TABLE:
          TENANCY_INVOICE_INDEX:
          TENANCY_INVOICE_TABLE:
          TENANCY_PROPERTY_INDEX:
          TENANCY_REFERENCE_INDEX:
          TENANCY_SETTINGS_TABLE:
          TENANCY_TABLE:
          UPLOADS_REGION:
          USER_COGNITOID_INDEX:
          USER_EMAIL_INDEX:
          USER_TABLE:
          USER_XERO_INDEX:
          XERO_API_ROOT_PATH:
          XERO_APP_CLIENT_ID:
          XERO_APP_CLIENT_SECRET:
          XERO_TOKEN_URL:
          INVOICE_LINE_ITEM_ORGANISATION_INDEX:
          INVOICE_PROPERTY_TABLE:
          TENANCY_ORGANISATION_INDEX: gsi-OrganisationTenancies
          PROPERTY_ORGANISATION_INDEX: gsi-OrganisationProperties
          PROPERTY_PARENT_PROPERTY_INDEX: gsi-ParentPropertyEntityProperty
          READ_MODEL_PARENT_PROPERTY_SUMMARY_QUEUE:
          READ_MODEL_PARENT_PROPERTY_SUMMARY_TABLE:

  bacsReportSender:
    Type: AWS::Serverless::Function
    Properties:
      Timeout: 60
      MemorySize: 1024
      Handler: com.rentancy.integrations.handlers.BacsReportSenderHandler
      Runtime: java11
      CodeUri: .
      Architectures:
        - arm64
      Environment:
        Variables:
          ENV:
          REGION:
          UPLOADS_REGION:
          ORGANISATION_TABLE:
          PROPERTY_TABLE:
          PROPERTY_ORGANISATION_INDEX:
          DDB_REGION:
          TENANCY_TABLE:
          TENANCY_REFERENCE_INDEX:
          TENANCY_PROPERTY_INDEX:
          USER_TABLE:
          TENANCY_SETTINGS_TABLE:
          JASPER_SERVER_URL:
          LAMBDA_REGION:
          APPSYNC_LAMBDA_NAME:
          EMAIL_ATTACHMENT_BUCKET:
          EMAIL_NOTIFICATION_QUEUE:
          EMAIL_NOTIFICATION_QUEUE_REGION:
          INVOICE_LINE_ITEM_TABLE:
          RENTANCY_DOCUMENT_UPLOADS:
          INVOICE_LINE_ITEM_ORGANISATION_INDEX:
          USER_XERO_INDEX:
          PROPERTY_REFERENCE_INDEX:
          INVOICE_TABLE:
          INVOICE_ORGANISATION_INDEX:
          INVOICE_PROPERTY_TABLE:
          PROPERTY_INVOICE_TABLE:
          PROPERTY_INVOICE_INVOICE_INDEX:


  tenancyJournalBillSender:
    Type: AWS::Serverless::Function
    Properties:
      Timeout: 60
      MemorySize: 1024
      Handler: com.rentancy.integrations.handlers.TenancyJournalBillSender
      Runtime: java11
      CodeUri: .
      Architectures:
        - arm64
      Environment:
        Variables:
          ACCOUNT_INDEX:
          ACCOUNT_ORGANISATION_INDEX:
          ACCOUNT_TABLE:
          ADDRESS_PARENT_INDEX:
          ADDRESS_TABLE:
          APPSYNC_LAMBDA_NAME:
          DDB_REGION:
          EMAIL_ATTACHMENT_BUCKET:
          EMAIL_INVOICE_QUEUE:
          EMAIL_NOTIFICATION_QUEUE:
          EMAIL_NOTIFICATION_QUEUE_REGION:
          ENV:
          INTEGRATION_INDEX:
          INTEGRATION_ORGANISATION_INDEX:
          INTEGRATION_STATE_INDEX:
          INTEGRATION_TABLE:
          INTEGRATION_TENANT_INDEX:
          INVOICE_LINE_ITEM_ORGANISATION_INDEX:
          INVOICE_LINE_ITEM_TABLE:
          LAMBDA_REGION:
          LANDLORD_BILL_ORGANISATION_INDEX:
          LANDLORD_BILL_TABLE:
          MONTHLY_JOURNAL_REPORT_SENDER_EMAILS:
          ORGANISATION_TABLE:
          ORGANISATION_USER_INDEX:
          ORGANISATION_USER_TABLE:
          PRIMARY_ORGANISATION_ID:
          PROPERTY_BUDGET_INDEX:
          PROPERTY_BUDGET_TABLE:
          PROPERTY_TABLE:
          REGION:
          RENTANCY_DOMAIN:
          RENT_INVOICE_HISTORY_INDEX:
          RENT_INVOICE_HISTORY_TABLE:
          TENANCY_ORGANISATION_INDEX:
          TENANCY_PROPERTY_INDEX:
          TENANCY_SETTINGS_INDEX:
          TENANCY_SETTINGS_TABLE:
          TENANCY_TABLE:
          UPLOADS_REGION:
          USER_ORGANISATION_INDEX:
          USER_TABLE:
          XERO_API_ROOT_PATH:
          XERO_APP_CLIENT_ID:
          XERO_APP_CLIENT_SECRET:
          XERO_TOKEN_URL:
          XERO_UI_ROOT_PATH:

  propertyLedgersSummary:
    Type: AWS::Serverless::Function
    Properties:
      Timeout: 60
      MemorySize: 1024
      Handler: com.rentancy.integrations.handlers.PropertyLedgersSummary
      Runtime: java11
      CodeUri: .
      Architectures:
        - arm64
      Environment:
        Variables:
          ADDRESS_PARENT_INDEX:
          ACCOUNT_TABLE:
          ACCOUNT_ORGANISATION_INDEX:
          ADDRESS_TABLE:
          APPSYNC_LAMBDA_NAME:
          DDB_REGION:
          ENV:
          INTEGRATION_ORGANISATION_INDEX:
          INTEGRATION_TABLE:
          INVOICE_ALLOCATION_INDEX:
          INVOICE_ALLOCATION_PROPERTY_INDEX:
          INVOICE_ALLOCATION_TABLE:
          INVOICE_LINE_ITEM_INVOICE_INDEX:
          INVOICE_LINE_ITEM_ORGANISATION_INDEX:
          INVOICE_LINE_ITEM_TABLE:
          INVOICE_LINE_ITEM_TRACKING_NAME_INDEX:
          INVOICE_ORGANISATION_INDEX:
          INVOICE_TABLE:
          JASPER_SERVER_URL:
          LAMBDA_REGION:
          MAIN_SERVICE_ENDPOINT:
          ORGANISATION_TABLE:
          ORGANISATION_USER_INDEX:
          ORGANISATION_USER_TABLE:
          OVER_PAYMENT_ORGANISATION_INDEX:
          OVER_PAYMENT_TABLE:
          PROPERTY_BUDGET_INDEX:
          PROPERTY_BUDGET_TABLE:
          PROPERTY_ORGANISATION_INDEX:
          PROPERTY_TABLE:
          REGION:
          RENTANCY_DOCUMENT_UPLOADS:
          TENANCY_INVOICE_INDEX:
          TENANCY_INVOICE_TABLE:
          TENANCY_ORGANISATION_INDEX:
          TENANCY_PROPERTY_INDEX:
          TENANCY_SETTINGS_TABLE:
          TENANCY_TABLE:
          TRANSACTION_ORGANISATION_INDEX:
          TRANSACTION_TABLE:
          UPLOADS_REGION:
          USER_TABLE:
          USER_XERO_INDEX: