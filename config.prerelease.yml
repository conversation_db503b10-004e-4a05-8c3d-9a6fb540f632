region: eu-west-2
project-region: eu-west-2
s3:
  email-bucket: app-rentancy-com-email-bucket-prerelease
  document-bucket: com-rentancy-documents152145-prerelease
lambda:
  appsync-service: appSyncService
  securityGroup: sg-0f1121ccd8c5b12e6
  subnet: subnet-04823f5c2cbad8d7a
mainService:
  endpoint: http://api-internal.lettings.stage.eu.loftyworks.systems
dynamodb:
  invoice-line-item-stream-arn: arn:aws:dynamodb:eu-west-2:590184079916:table/InvoiceLineItem-hhmlvxrpevhrdld2jmcx2dr3x4-prerelease/stream/2025-05-20T14:08:06.532
sqs:
  xero-invoices-queue-inbound-arn: arn:aws:sqs:eu-west-2:590184079916:xero-invoices-queue-inbound
  xero-invoices-callback-queue-arn: arn:aws:sqs:eu-west-2:590184079916:xero-invoices-callback-queue